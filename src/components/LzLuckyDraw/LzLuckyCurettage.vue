<!--刮刮乐-->
<script setup lang="ts">
import { ref } from 'vue';

const myLucky = ref();

const emit = defineEmits(['scratchAll', 'scratchEnd', 'scratchStart']);

const scratchStart = async () => {
  emit('scratchStart');
};

const scratchEnd = () => {
  emit('end');
};

const scratchAll = () => {
  emit('scratchAll');
};

const initCurettage = () => {
  myLucky.value.initCurettage();
};
defineExpose({
  initCurettage,
});

</script>

<template>
  <lucky-curettage
    ref="myLucky"
    v-bind="$attrs"
    @scratchEnd="scratchEnd"
    @scratchAll="scratchAll"
    @scratchStart="scratchStart" />
</template>

<style scoped lang="scss">

</style>
