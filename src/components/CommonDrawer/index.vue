<template>
  <div
      class="bg-[#f2f2f2] rounded-xl w-screen">
    <div
        :style="{backgroundImage: `url(${backgroundImage || defaultBackgroundImage})`}"
        class="relative title bg-no-repeat flex bg-contain  justify-center items-center h-16 text-white text-lg">
      <div class="w-10 h-1 bg-gradient-to-r from-white to-[#ff6153] mr-2"></div>
      <div class="text-xl">{{title}}</div>
      <div class="w-10 h-1 bg-gradient-to-l from-white to-[#ff8c4a] ml-2"></div>
      <img  src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png"
            class="absolute top-3 right-3 w-3" @click="close" />
    </div>
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String,
    default: '',
    require: true,
  },
  backgroundImage: {
    type: String,
    default: '',
    require: false,
  },
});

const defaultBackgroundImage = '//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png';
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>
