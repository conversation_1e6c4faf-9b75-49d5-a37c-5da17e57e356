<template>
  <div id="container">
    <Transition enter-active-class="animate__animated animate__fadeIn" mode="out-in">
      <component :is="curComponent" @canToggleComponent="handleToggleComponent" />
    </Transition>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable */
/* 快捷打开项目命令  npm run serve src/pages/1000016561/1829472001870397442 */
/* 本地访问  http://douguoqing.isvjcloud.com:8080/test/cc/custom/1000016561/1829472001870397442/?fromShopId=1000016561 */
/* 测试链接 https://lzkjdz-isv.isvjcloud.com/test/cc/custom/1000016561/1829472001870397442/?fromShopId=1000016561 */
/* 线上链接 https://lzkjdz-isv.isvjcloud.com/prod/cc/custom/1000016561/1829472001870397442/?fromShopId=1000016561 */
import { inject, shallowRef, Component } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import Home from './components/Home.vue';

const componentList: Component = {
  Home,
};
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const pathParams: any = inject('pathParams');
const baseUserInfo: any = inject('baseUserInfo');
console.log('基础信息', baseInfo);
console.log('页面路径拆解', pathParams);
console.log('基础用户数据', baseUserInfo);

// 当前显示的组件
const curComponent = shallowRef(Home);

// 处理切换组件
const handleToggleComponent = (componentName: string) => {
  curComponent.value = componentList[componentName];
};
</script>

<style lang="scss" scoped>
#container {
  width: 100vw;
  min-height: 100vh;
  max-width: 100vw;
  line-height: 1;
}
</style>

<style lang="scss">
/* 隐藏Webkit内核浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}

// 禁止页面回弹行为
html,
body {
  overscroll-behavior-y: none;
  overscroll-behavior-x: none;
}

.popup.van-popup {
  background: none;
  overflow-y: unset;
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
