<template>
  <div id="woman-due-date" v-if="isShow">
    <div class="due-date-container">
      <div class="title-wrapper">
        <img src="https://img10.360buyimg.com/imgzone/jfs/t1/112049/33/49486/1042/66dfe8d7F99fa760a/37a9f7934bb2c77f.png" alt="" />
      </div>
      <div class="date-wrapper" @click="isShowPopup = true">
        <div class="due-date-part">
          <span :class="{ active: dueDate !== '请选择宝宝预产期' }">{{ dueDate }}</span>
        </div>
        <!-- <div class="due-date-part2">
          <span v-if="dueDate !== '请选择宝宝预产期'">恭喜你，已经怀孕{{ pregnantDays }}！</span>
        </div> -->
        <img class="arrow-wrapper" src="https://img10.360buyimg.com/imgzone/jfs/t1/91588/2/51270/199/66dfed03Fafb9ec57/274876e4415542f6.png" alt="" />
      </div>
    </div>
    <van-popup v-model:show="isShowPopup" position="bottom">
      <van-date-picker v-model="currentDate" title="选择日期" :min-date="minDate" :max-date="maxDate" @cancel="handleClosePopup" @confirm="handlePopupConfirm" :formatter="formatter" />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable */
import { ref, onMounted, nextTick, Ref } from 'vue';
import dayjs from 'dayjs';
import { formatDate } from '../../common';

interface Props {
  backShowData: any;
}
const props = withDefaults(defineProps<Props>(), {
  backShowData: () => {},
});

// const { isAnswered } = toRefs(props);

type DefineEmits = {
  (e: 'can-toggle-component', componentName: string): void;
  (e: 'open-popup', type: string): void;
};
const emits = defineEmits<DefineEmits>();

const isShow = ref(false);
const isShowPopup = ref(false);
const currentDate: any = ref([]);
const minDate = ref(new Date());
const maxDate = ref(new Date(dayjs().add(279, 'day').toDate()));
const dueDate = ref('请选择宝宝预产期');
const pregnantDays: Ref<string> = ref('');
const handleClosePopup = (): void => {
  isShowPopup.value = false;
};

const afterFormatDate = ref('');
const handlePopupConfirm = (data: any): void => {
  const { selectedOptions, selectedValues } = data;
  // set日期
  dueDate.value = `${selectedOptions[0].text}${selectedOptions[1].text}${selectedOptions[2].text}`;
  afterFormatDate.value = `${selectedValues[0]}/${selectedValues[1]}/${selectedValues[2]}`;
  // 计算周和天
  const dateStr = `${selectedValues[0]}/${selectedValues[1]}/${selectedValues[2]}`;
  handleHowManyDate(dateStr);
  // 关闭弹窗
  isShowPopup.value = false;
};
// 计算孕周
const handleHowManyDate = (dateStr: string) => {
  let dueDate = dayjs(dateStr).toDate().getTime();
  let currentDate = dayjs().toDate().getTime();
  const timeDiff = dueDate - currentDate;
  // 计算预产期和当前日期之间的天数差
  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  // 计算孕周和剩余天数
  const weeks = Math.floor((279 - days) / 7);
  const daysRemainder = (279 - days) % 7;
  // 根据周、剩余天数处理显示
  if (weeks === 0) {
    pregnantDays.value = `${daysRemainder}天`;
  } else if (daysRemainder === 0) {
    pregnantDays.value = `${weeks}周`;
  } else {
    pregnantDays.value = `${weeks}周${daysRemainder}天`;
  }
};
const formatter = (type: any, option: any) => {
  if (type === 'year') {
    option.text += '年';
  }
  if (type === 'month') {
    option.text += '月';
  }
  if (type === 'day') {
    option.text += '日';
  }
  return option;
};

// 使用 defineExpose 暴露属性和方法
defineExpose({
  dueDate,
  afterFormatDate,
});
onMounted(() => {
  nextTick(() => {
    isShow.value = true;
    // 回显数据
    if (JSON.stringify(props.backShowData) !== '{}' && props.backShowData.hasOwnProperty('expectedDate') && props.backShowData.expectedDate !== '') {
      // 预产期显示
      dueDate.value = formatDate(new Date(props.backShowData.expectedDate));
      // 预产期换算天数
      handleHowManyDate(props.backShowData.expectedDate);
      // 下次更新所需要的日期
      afterFormatDate.value = props.backShowData.expectedDate;
      // 时间选择组件
      let date = props.backShowData.expectedDate.split('/');
      currentDate.value = [date[0], date[1], date[2]];
    }
  });
});
</script>

<style lang="scss" scoped>
#woman-due-date {
  width: 7.1rem;
  .due-date-container {
    width: 100%;
    height: 1.7rem;
    background-color: #fff;
    border-radius: 0.2rem;
    padding: 0.25rem;
    box-sizing: border-box;
    position: relative;
    .title-wrapper {
      width: 0.71rem;
      height: 0.24rem;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .date-wrapper {
      width: 6.6rem;
      display: flex;
      justify-content: flex-start;
      align-items: flex-end;
      margin-top: 0.48rem;
      .due-date-part {
        width: 2.61rem;
        .active {
          color: #000000;
        }
        span {
          font-size: 0.32rem;
          color: #a1a1a1;
        }
      }
      .due-date-part2 {
        width: 2.9rem;
        margin-left: 0.94rem;
        text-align: right;
        span {
          font-size: 0.22rem;
          color: #c01f3e;
        }
      }
    }
    .arrow-wrapper {
      width: 0.14rem;
      height: 0.17rem;
      position: absolute;
      right: 0.25rem;
      top: 1.07rem;
    }
  }
}
</style>
