<!-- 上传完图片的抽奖结果 -->
<template>
  <popup v-model:show="showPopup" class="top-popup" :close-on-click-overlay="false" @opened="openedDialog">
    <div class="content">
      <icon class="close-icon" name="close" color="#d6b377" size="50" @click="closeDialog" />
      <div class="dialog">
        <div class="rule-box">
          <div class="gift">获得参与奖{{ giftInfo.prizeName }}</div>
          <div class="tips">
            已发放到您的账户中<br />
            您还可通过投票排行赢取亲子好礼
          </div>
          <div class="dialog-btn" @click="closeDialog">知道了</div>
        </div>
      </div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, PropType, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import type { DrawGift } from '../scripts/types';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  giftInfo: {
    type: Object as PropType<DrawGift>,
    required: true,
    default: () => ({
      prizeName: '',
      prizeType: '',
    }),
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  emits('closeDialog');
};
const openedDialog = async () => {
  console.log('🚀 ~ openedDialog ~ openedDialog:');
};
</script>
<style lang="scss" scoped>
.content {
  box-sizing: border-box;
  position: relative;
  padding-top: 0.25rem;
  .title {
    min-width: 2.5rem;
    max-width: 5.6rem;
    text-align: center;
    padding: 0.18rem 0.7rem;
    box-sizing: border-box;
    white-space: nowrap;
    line-height: 1;
    font-weight: bold;
    font-size: 0.48rem;
    color: #fff;
    background-image: linear-gradient(to top, #fb9b0e 0%, #fecb48 100%);
    border-radius: 0.12rem 0.12rem 0.25rem 0.25rem;
    clip-path: polygon(0 0%, 100% 0, 97% 100%, 3% 100%);
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
  }
  .dialog {
    overflow: hidden;
    width: 7.5rem;
    height: 7.89rem;
    background: url(../assets/draw-result.png) no-repeat;
    background-size: contain;
    padding: 3rem 1.5rem;
    box-sizing: border-box;
    text-align: center;

    .rule-box {
      margin-top: 0.2rem;
      .gift {
        font-size: 0.34rem;
        font-weight: bold;
        margin-bottom: 0.2rem;
      }
      .tips {
        font-size: 0.24rem;
        margin-bottom: 0.7rem;
      }
    }
  }

  .close-icon {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
  }
}
</style>
