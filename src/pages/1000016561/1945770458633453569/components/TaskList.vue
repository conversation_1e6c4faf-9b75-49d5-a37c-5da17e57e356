<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="openedDialog">
    <div class="content">
      <icon class="close-icon" name="close" color="#d6b377" size="50" @click="closeDialog" />
      <div class="title">做任务得更多投票机会</div>
      <div class="dialog">
        <div class="task">
          <div v-for="(task, index) in taskList" :key="index" class="task-item">
            <img :src="task?.info?.icon" alt="任务图标" class="task-icon" />
            <div>
              <div class="task-title">{{ task?.info?.title }}</div>
              <div class="task-info">{{ task?.info?.info }}</div>
            </div>
            <div class="task-btn" :class="{ 'btn-disable': task.finishCnt }" @click="handleClick(task)">{{ task?.info?.text }}</div>
            <!-- <template v-if="index === taskList.length - 1 && task.title.includes('邀请')">
            </template> -->
          </div>
          <div class="friend" v-if="taskList?.length">
            <div class="friends" v-for="item in list">
              <img :src="item.helpAvatar" alt="" />
              <div>{{ item.helpNick || '' }}</div>
            </div>
            <div class="my-friend" @click="(isShowFriendPopup = true), closeDialog()">我的好友&gt;</div>
          </div>
        </div>
      </div>
    </div>
  </popup>
  <MyFriends :isShowPopup="isShowFriendPopup" @closeDialog="isShowFriendPopup = false"></MyFriends>
</template>

<script setup lang="ts">
import { Popup, Icon, showToast, GridItem } from 'vant';

import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import MyFriends from './MyFriends.vue';
import { getTaskList, doTask } from '../scripts/ajax';
import type { FriedItem, TaskItem, ActInfo } from '../scripts/types';
import { callShare } from '@/utils/platforms/share';

const baseInfo = inject('baseInfo') as BaseInfo;
const isShowFriendPopup = ref(false);
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  actInfo: {
    type: Object as () => ActInfo,
    required: true,
    default: () => ({}),
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog', 'doTaskSuccess']);
const closeDialog = () => {
  emits('closeDialog');
};
const taskList = ref<TaskItem[]>();
const friendsList = ref<FriedItem[]>([]);
const emptyFriend = {
  helpPin: '',
  helpNick: '',
  helpTime: '',
  helpAvatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/309201/18/17895/5256/687a1b48Fdcd8d5c1/5686e191454cbb8d.png',
};
const list = computed(() => {
  return [
    // 处理现有项
    ...friendsList.value.map((f) => ({
      helpPin: f.helpPin || '',
      helpNick: f.helpNick || '',
      helpTime: f.helpTime || '',
      helpAvatar: f.helpAvatar || emptyFriend.helpAvatar,
    })),
    // 补充空位
    ...Array(Math.max(0, 5 - friendsList.value.length))
      .fill(null)
      .map(() => ({ ...emptyFriend })),
  ].slice(0, 5);
});
const openedDialog = async () => {
  console.log('🚀 ~ openedDialog ~ openedDialog:');
  const result = (await getTaskList()) as TaskItem[];
  friendsList.value = result.find((item) => item.taskType === 15)?.helpList || [];
  taskList.value = result.map((item) => {
    return {
      ...item,
      info: JSON.parse(item.showJson),
    };
  });
  console.log('🚀 ~ openedDialog ~ result:', result);
};
// 针对不同任务类型定义操作
const taskTypeMap = {
  14: async (task: TaskItem) => {
    if (task.finishCnt) {
      showToast('任务已完成，请明天再试~');
      return;
    }
    await doTask(task.taskId);
    task.finishCnt++;
    showToast('签到成功~');
    emits('doTaskSuccess');
  },
  8: () => {
    window.location.href = 'https://pro.m.jd.com/mall/active/42PVYK3e4TV5LrQQzFuLb4VoHnGe/index.html';
  },
  4: async (task: TaskItem) => {
    if (!task.finishCnt) {
      await doTask(task.taskId);
      task.finishCnt++;
      emits('doTaskSuccess');
    }
    window.location.href = 'https://pro.m.jd.com/mall/active/29cENEGMosTUkkesRHTU1UWSBajB/index.html';
  },
  15: () => {
    if (!props.actInfo.upload) {
      showToast('请先上传亲子默契时刻图片~');
      return;
    }
    const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
    const shareUrl = `${window.location.origin}${window.location.pathname}?pictureNo=${props.actInfo.minePicture?.pictureNo}`;
    console.log('🚀 ~ shareUrl:', shareUrl);
    callShare({
      title: shareConfig.shareTitle,
      content: shareConfig.shareContent,
      imageUrl: shareConfig.shareImage,
      shareUrl: shareUrl,
    });
  },
};

const handleClick = (task: TaskItem) => {
  if (taskTypeMap[task.taskType as keyof typeof taskTypeMap]) {
    taskTypeMap[task.taskType as keyof typeof taskTypeMap](task);
  }
};
</script>
<style lang="scss" scoped>
.content {
  padding-bottom: 1.2rem;
  box-sizing: border-box;
  position: relative;
  padding-top: 0.25rem;
  .title {
    min-width: 2.5rem;
    max-width: 5.6rem;
    text-align: center;
    padding: 0.18rem 0.3rem;
    box-sizing: border-box;
    white-space: nowrap;
    line-height: 1;
    font-weight: bold;
    font-size: 0.48rem;
    color: #fff;
    background-image: linear-gradient(to top, #fb9b0e 0%, #fecb48 100%);
    border-radius: 0.12rem 0.12rem 0.25rem 0.25rem;
    clip-path: polygon(0 0%, 100% 0, 97% 100%, 3% 100%);
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
  }
  .dialog {
    overflow: hidden;
    width: 5.62rem;
    height: 6.7rem;
    background: url(../assets/dialog-bg.png) no-repeat;
    background-size: contain;
    padding: 0.65rem 0.35rem;
    box-sizing: border-box;
    text-align: center;

    .task {
      margin-top: 0.2rem;
      height: 5.6rem;
      background-color: #ffffff;
      border-radius: 0.26rem;
      padding: 0.15rem;
      text-align: left;
      font-size: 0.22rem;
      box-sizing: border-box;
      color: #000;
      word-break: keep-all;
      .task-item {
        display: flex;
        align-items: center;
        padding: 0.1rem 0;
        box-sizing: border-box;
        border-bottom: 2px dashed #c8c8c8;
        &:last-child {
          border-bottom: none;
        }
        .task-icon {
          width: 0.8rem;
          margin-right: 0.1rem;
          flex-shrink: 0;
        }
        .task-title {
          font-size: 0.26rem;
          font-weight: bold;
          margin-bottom: 0.05rem;
        }
        .task-info {
          font-size: 0.18rem;
          line-height: 0.3rem;
          width: 100%;
          word-break: break-all;
          margin-right: 0.05rem;
        }
        .task-btn {
          margin-left: auto;
          background: linear-gradient(90deg, #ff7d00 0%, #ffb74d 100%);
          color: white;
          border-radius: 0.5rem;
          padding: 0.08rem 0.15rem; /* 调整按钮内边距匹配1.25rem高度 */
          font-size: 0.24rem;
          font-weight: 500;
          box-shadow: 0 3px 6px rgba(255, 125, 0, 0.25); /* 增强按钮阴影效果 */
        }
      }
    }
    .friend {
      width: 100%;
      display: flex;
      align-items: center;
      justify-self: start;
      gap: 0.1rem;
      margin-top: 0.1rem;
      img {
        max-width: 1.2rem;
        width: 100%;
        background: #c8c8c8;
        border-radius: 50%;
        margin-bottom: 0.05rem;
      }
    }
    .my-friend {
      color: #2582e0;
      text-decoration: underline;
      white-space: nowrap;
    }
  }

  .close-icon {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
  }
}
</style>
