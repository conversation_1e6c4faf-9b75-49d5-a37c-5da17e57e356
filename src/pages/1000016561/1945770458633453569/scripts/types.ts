export interface PictureResponse {
  /** 头像 */
  avatar: string;
  /** 助力pin */
  helpPin: string;
  /** 点赞数 */
  likes: number;
  /** 昵称 */
  nick: string;
  /** 图片编号 */
  pictureNo: number;
  /** 图片链接 */
  pictureUrl: string;
}

export interface ActInfo {
  /** 活动状态 0-进行中 1-已开奖 */
  activityStatus: number;
  /** 抽奖总次数 */
  drawTotal: number;
  /** 抽奖已用次数 */
  drawUsed: number;
  /** 是否处于分享链接 0-否 1-是 */
  isShare: number;
  /** 点赞总次数 */
  likeTotal: number;
  /** 点赞已用次数 */
  likeUsed: number;
  /** 开卡状态 0-未开卡 1-开卡 */
  openStatus: number;
  sharePicture: PictureResponse;
  /**  是否上传过图片 0-否 1-是 */
  upload: number;
  minePicture: PictureResponse;
}
export interface PictureItem {
  /** 头像 */
  avatar: string;
  /** 创建时间 */
  createTime: string;
  /** 用户加密pin */
  encryptPin: string;
  /** 最后一次点赞时间 */
  lastLikeTime: string;
  /** 点赞数 */
  likes: number;
  /** 昵称 */
  nick: string;
  /** 编号 */
  pictureNo: number;
  /** 图片链接 */
  pictureUrl: string;
}
export interface DrawGift {
  prizeName: string;
  prizeType: string;
}
export interface FriedItem {
  /** 助力人昵称 */
  helpNick: string;
  /** 助力人的头像 */
  helpAvatar: string;
  /** 助力人的pin */
  helpPin: string;
  /** 助力时间 */
  helpTime: string;
}
export interface TaskShowJson {
  /** 图标 */
  icon: string;
  /** 标题 */
  title: string;
  /** 任务信息 */
  info: string;
  /** 按钮文案 */
  text: string;
}

export interface TaskItem {
  /** 完成次数 */
  finishCnt: number;
  /** 助力列表 */
  helpList: any[];
  /** 展示信息JSON */
  showJson: string;
  /** 任务ID */
  taskId: string;
  /** 任务类型
   * 14 签到
   * 8 下单
   * 4 浏览
   *  15 邀请
   */
  taskType: number;
  info: TaskShowJson;
}

export interface DayResponse {
  /** 头像 */
  avatar: string;
  /** 昵称 */
  nick: string;
  /** 排名 */
  rank: number;
  /** 奖品图片 */
  userPrizeImg: string;
  /** 奖品名称 */
  userPrizeName: string;
  /** 中奖ID（实物有） */
  userPrizeId: number;
}
export interface RankDetails {
  /** 头像 */
  avatar: string;
  /** 总票数 */
  likes: number;
  /** 昵称 */
  nick: string;
  /** 排名 */
  rank: number;
}
export interface TotalResponse {
  /** 排名 */
  rank: number;
  /** 中奖ID（实物有） */
  userPrizeId: number;
  /** 奖品图片 */
  userPrizeImg: string;
  /** 奖品名称 */
  userPrizeName: string;
}

export interface RankInfo {
  /** 总榜弹窗状态
   * 0-不弹出
   * 1-弹出中奖 */
  totalStatus: number;
  /** 日榜弹窗状态
   * 0-不弹出
   *  1-弹出中奖
   *  2-弹出未中奖 */
  dayStatus: number;
  dayResponse: DayResponse;
  totalResponse: TotalResponse;
  details: RankDetails[];
}
export interface PrizeItem {
  /** 中奖id */
  activityPrizeId: number;
  /** 详细地址 */
  address: string;
  /** 是否可以填写地址 */
  canAddress: boolean;
  /** 市 */
  city: string;
  /** 区 */
  county: string;
  /** 创建时间 */
  createTime: string;
  /** 发货状态:1-已发货；2-待发货 */
  deliveryStatus: number;
  /** 是否填写地址 */
  isAddress: boolean;
  /** 电话 */
  mobile: string;
  /** 资产发放内容 */
  prizeContent: string;
  /** 资产名称 */
  prizeName: string;
  /** 奖品类型 */
  prizeType: number;
  /** 省 */
  province: string;
  /** 收件人 */
  realName: string;
  /** 中奖状态：1-中奖；2-未中奖 */
  status: number;
  /** 礼品卡领取攻略图 */
  tipImg: string;
}
