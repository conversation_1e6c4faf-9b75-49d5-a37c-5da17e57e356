<template>
    <div class="main-view">
        <div v-for="(step, index) in qustionList" :key="index">
            <div class="main-view" v-if="index == currentStep">
              <div v-if="step.title == '您狗狗的名字是?'">
                <div class="qustion-slise-box" :style="`background-color: ${PAGE_CONFIG.questionSlideBg};`">
                    <div class="qustion-slise-item" :style="`background-color: ${PAGE_CONFIG.quuetionSlideColor};width: ${ Math.floor(((index+1) / (PAGE_CONFIG.problemList.length || 1)) * 100) }%;`"></div>
                </div>
                <div class="qustion-step-text">{{ `(${index+1}/${PAGE_CONFIG.problemList.length})` }}</div>
                <div class="qustion-text" v-html="step.title"></div>
                <div class="qustion-no-btn">
                  <input type="text" v-model="step.name" placeholder="请填写宠物名字" maxlength="6" />
                </div>
              </div>
              <div v-else>
                <div class="qustion-slise-box" :style="`background-color: ${PAGE_CONFIG.questionSlideBg};`">
                    <div class="qustion-slise-item" :style="`background-color: ${PAGE_CONFIG.quuetionSlideColor};width: ${ Math.floor(((index+1) / (PAGE_CONFIG.problemList.length || 1)) * 100) }%;`"></div>
                </div>
                <div class="qustion-step-text">{{ `(${index+1}/${PAGE_CONFIG.problemList.length})` }}</div>
                <div class="qustion-text" v-html="step.title"></div>
                <div :class="{'qustion-yes-btn': step.status == 1, 'qustion-no-btn': step.status != 1}" @click="step.status = 1">有</div>
                <div :class="{'qustion-yes-btn': step.status == 0, 'qustion-no-btn': step.status != 0}" @click="step.status = 0">没有</div>
                <div class="qustion-step-tips" :style="`color: ${PAGE_CONFIG.mainBgColor}`">{{ step.desc }}</div>
              </div>
            </div>
            <div class="common-btn" v-if="currentStep === 0" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor};opacity: ${step.status == -1 || step.name == '' ? 0.1 : 1}`" @click="nextStep">{{ nextButtonText }}</div>
            <div v-else class="photo-bottom">
                <div class="item-btn" style="width: 2.8rem;" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor}`" @click="bacckStep">上一步</div>
                <div class="item-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor};opacity: ${step.status == -1 ? 0.1 : 1};width: ${currentStep == qustionList.length - 1 ? '3.9rem': '2.8rem'}`" @click="nextStep">
                    {{ nextButtonText }}
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted, reactive } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { useStore } from 'vuex';
import { RootState } from '../store/state';
import { saveQaInfo } from '../config/api';
import { emit } from 'process';

const store = useStore<RootState>();

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const checkId = computed(() => store.state.checkId);
const qustionList = reactive(JSON.parse(JSON.stringify(PAGE_CONFIG.problemList)));
const currentStep = ref(0);
const nextButtonText = ref('下一步');
const emits = defineEmits(['toggleComponent']);

const nextStep = () => {
  if (qustionList[currentStep.value].status === -1) {
    return;
  }
  if (currentStep.value < qustionList.length - 1) {
    currentStep.value++;
    setTimeout(() => {
      if (currentStep.value === qustionList.length - 1) {
        nextButtonText.value = '提交记录';
      } else {
        nextButtonText.value = '下一步';
      }
    }, 200);
  } else {
    const formatDate = {
      checkId: '',
      ifBleeding: false,
      ifDiscomfort: false,
      ifHalitosis: false,
      petNick: '',
    };
    qustionList.forEach((element: any) => {
      switch (element.type) {
        case 'petNick':
          formatDate.petNick = element.name;
          break;
        case 'ifBleeding':
          formatDate.ifBleeding = element.status === 1;
          break;
        case 'ifDiscomfort':
          formatDate.ifDiscomfort = element.status === 1;
          break;
        case 'ifHalitosis':
          formatDate.ifHalitosis = element.status === 1;
          break;
        default:
          break;
      }
    });
    formatDate.checkId = checkId.value;
    saveQaInfo(formatDate).then((res) => {
      // console.log(res);
      if (res) {
        emits('toggleComponent', 'DetectionResult');
      } else {
        emits('toggleComponent', 'PhotoError');
      }
    });
  }
};
const bacckStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
  setTimeout(() => {
    if (currentStep.value === qustionList.length - 1) {
      nextButtonText.value = '提交记录';
    } else {
      nextButtonText.value = '下一步';
    }
  }, 200);
};
const init = () => {
  console.log(PAGE_CONFIG);
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
<style lang="scss">
.swiper-box {
  width: 7.5rem;
  height: auto;
  background: #fff;
  overflow: hidden;
  position: relative;
}
</style>
