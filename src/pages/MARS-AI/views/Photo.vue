<template>
  <div class="main-view">
  <div v-if="uploadStep === 1">
    <img class="photo-desc" :src="require(`../asset/${baseInfo.shopId}/AIdesc.png`)"/>
    <img class="common-dog" :src="require(`../asset/${baseInfo.shopId}/photoDog.png`)"/>
    <div class="photo-bottom">
      <div class="item-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor}`" @click="handlePhotoSelect">从相册选择</div>
      <div class="item-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor}`" @click="openCamera">拍照</div>
    </div>
  </div>
  <div v-if="uploadStep === 2">
    <div v-if="uploadStatus === 'upload'">
      <div class="photo-review" :style="`background-image: url(${needHandleImageSrc})`"></div>
      <img class="retake-photo" :src="require(`../asset/${baseInfo.shopId}/retakePhoto.png`)" @click="retakePhoto"/>
      <div class="common-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor}`" @click="uploadImmg">上传照片</div>
    </div>
    <div v-else-if="uploadStatus === 'loadding'">
      <Loading :pageNo="1"></Loading>
    </div>
  </div>
  <div v-if="uploadStep === 3">
    <div v-if="uploadStatus === 'success'">
      <img class="upload-error-img" :src="require(`../asset/${baseInfo.shopId}/uploadSucces.png`)"/>
      <div class="upload-info" v-for="(it, index) in PAGE_CONFIG.uploadSuceesTips" :key="index">
        {{ it }}
      </div>
      <img class="common-dog" :src="require(`../asset/${baseInfo.shopId}/photoDog.png`)"/>
      <div class="common-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor}`" @click="handleOpenQuestion">去答题</div>
    </div>
    <div v-else-if="uploadStatus === 'error1'">
      <div class="upload-error-title">{{ baseInfo.activityName }}</div>
      <img class="upload-error-img" :src="require(`../asset/${baseInfo.shopId}/notdog.png`)"/>
      <img class="upload-error-desc" :src="require(`../asset/${baseInfo.shopId}/errorDesc.png`)"/>
      <img class="common-dog" :src="require(`../asset/${baseInfo.shopId}/photoErrorDog.png`)"/>
      <div class="common-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor}`" @click="retakePhoto">重新上传</div>
    </div>
    <div v-else-if="uploadStatus === 'error2'">
      <div class="upload-error-title">{{ baseInfo.activityName }}</div>
      <img class="upload-error-img" :src="require(`../asset/${baseInfo.shopId}/nottooth.png`)"/>
      <img class="upload-error-desc" :src="require(`../asset/${baseInfo.shopId}/errorDesc.png`)"/>
      <img class="common-dog" :src="require(`../asset/${baseInfo.shopId}/photoErrorDog.png`)"/>
      <div class="common-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor}`" @click="retakePhoto">重新上传</div>
    </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted } from 'vue';
import { showLoadingToast, showToast, closeToast, DropdownMenu, DropdownItem } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import { ruleFormat, formatDate, getMobileModel, iosFace, androidFace } from '../config/common';
import { backgroundImage } from 'html2canvas/dist/types/css/property-descriptors/background-image';
import { Url } from 'url';
import { httpRequest } from '@/utils/service';
import { useStore } from 'vuex';
import { RootState } from '../store/state';
import { uploadToothImg } from '../config/api';
import Loading from '../components/Loading.vue';

const store = useStore<RootState>();

const PetInfo = computed(() => store.state.petInfo);

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const needHandleImageSrc = ref('');
// 1:选照片，2：上传前确认，3：上传结果，失败：不是狗/失败：不是牙/成功
const uploadStep = ref(1);
// success: 成功，error1: 不是狗，error2：不是牙
const uploadStatus = ref('loadding');
const compressorQuality = ref(1);
const fileBlob = ref<Blob>();
const retakePhoto = () => {
  uploadStep.value = 1;
  needHandleImageSrc.value = '';
  fileBlob.value = undefined;
};
const emits = defineEmits(['toggleComponent']);
const handleCompressor = (canvas: any) => {
  canvas.toBlob(
    (blob: Blob) => {
      // 使用FileReader来读取Blob
      const reader = new FileReader();
      reader.onload = () => {
        console.log(compressorQuality.value, blob.size);
        if (blob.size < 500 * 1024) {
          closeToast();
          // showToast('图像不符合要求。请上传大小为500 KB到10 MB 之间，格式为PNG、JPG、JPEG或WebP的图像。 请再试一次!');
          emits('toggleComponent', 'PhotoError');
          return;
        }
        if (blob.size >= 10 * 1024 * 1024) {
          compressorQuality.value = Number((compressorQuality.value - 0.1).toFixed(1));
          if (compressorQuality.value > 0) {
            handleCompressor(canvas);
          } else {
            closeToast();
            showToast('图像不符合要求。请上传大小为500 KB到10 MB 之间，格式为PNG、JPG、JPEG或WebP的图像。 请再试一次!');
            uploadStep.value = 1;
            uploadStatus.value = 'loadding';
          }
        } else {
          console.log(blob.size, '裁剪成功');
          console.log(compressorQuality.value, '此时的质量');
          closeToast();
          needHandleImageSrc.value = URL.createObjectURL(blob);
          fileBlob.value = blob;
          compressorQuality.value = 1;
          uploadStep.value = 2;
          uploadStatus.value = 'upload';
        }
      };
      // 读取Blob为DataURL，同时指定图片格式和质量
      reader.readAsDataURL(blob);
    },
    'image/jpeg',
    compressorQuality.value,
  );
};
const handleAvatarValidate = (file: File) => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  const reader = new FileReader();
  reader.onload = (e) => {
    const dataUrl = e.target?.result;
    const image = new Image();
    image.onload = () => {
      // 创建一个 canvas 元素
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      const width0 = image.width;
      const height0 = image.height;

      canvas.width = width0;
      canvas.height = height0;

      // 将图片绘制到 canvas 上
      ctx?.drawImage(image, 0, 0, width0, height0);
      handleCompressor(canvas);
    };
    if (typeof dataUrl === 'string') {
      image.src = dataUrl;
    }
  };
  reader.readAsDataURL(file);
};

/**
 * 选择照片
 */
const handlePhotoSelect = (): void => {
  const app = document.getElementById('container') as HTMLElement;
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  const system = getMobileModel();
  if (system === 'iPhone') {
    input.accept = 'image/jpeg,image/png,image/jpg';
  } else {
    input.accept = 'image/*';
  }
  input.style.display = 'none';
  app.appendChild(input);
  input.onchange = async (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target && target.files && target.files.length > 0) {
      const file = target.files[0];
      // 进一步处理 file
      if (file) {
        handleAvatarValidate(file);
      }
    }
  };
  input.click();
};

const openCamera = async () => {
  const app = document.getElementById('container') as HTMLElement;
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  const system = getMobileModel();
  if (system === 'iPhone') {
    input.accept = 'image/jpeg,image/png,image/jpg';
  } else {
    input.accept = 'image/*';
  }
  input.capture = 'camera';
  input.style.display = 'none';
  app.appendChild(input);
  input.onchange = async (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target && target.files && target.files.length > 0) {
      const file = target.files[0];
      // 进一步处理 file
      if (file) {
        handleAvatarValidate(file);
      }
    }
  };
  input.click();
};

const uploadImmg = async () => {
  uploadStatus.value = 'loadding';
  const formData = new FormData();
  fileBlob.value && formData.append('file', fileBlob.value, 'babyAvatar.png');
  formData.append('petId', String(PetInfo.value.petId));
  uploadToothImg(formData).then((res) => {
    console.log(res);
    if (res.data) {
      // checkId: "1904050760441040898"
      // dogBodyPart: "mouth"
      // successful: true
      // topClass: "zoom_out_dog"
      store.commit('setCheckId', res.data.checkId);
      uploadStep.value = 3;
      if (res.data.successful) {
        uploadStatus.value = 'success';
        store.commit('setToothImg', res.data.imgUrl);
      } else {
        uploadStatus.value = `error${res.data.errorType}`;
      }
    } else {
      emits('toggleComponent', 'PhotoError');
    }
  });
};
const handleOpenQuestion = () => {
  emits('toggleComponent', 'Question');
};
const init = () => {
  console.log(PetInfo.value);
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
