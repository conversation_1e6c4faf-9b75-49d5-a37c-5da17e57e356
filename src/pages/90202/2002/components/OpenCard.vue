<template>
  <div class="rule-bk">
    <div class="btn" @click="join"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import { inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

const emits = defineEmits(['close']);

const join = () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('../assets/openCard.png');
  background-size: 100% 100%;
  width: 6.15rem;
  height: 7.79rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.1rem;
  padding-left: 0.3rem;
  padding-right: 0.3rem;

  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: 0.6rem;
    height: 0.6rem;
    cursor: pointer;
  }

  .btn {
    position: absolute;
    top: 6.1rem;
    left: 50%;
    transform: translate(-50%);
    width: 3.6rem;
    height: 0.6rem;
    cursor: pointer;
  }
}
</style>
