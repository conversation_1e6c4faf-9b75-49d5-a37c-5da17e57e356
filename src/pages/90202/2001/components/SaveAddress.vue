<template>
  <div class="rule-bk">
    <!-- <div class="title">
      <div class="leftLineDiv"></div>
      <div>邮寄地址</div>
      <div class="rightLineDiv"></div>
      <img alt=""
        data-v-705393a4=""
        src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png"
        class="close"
        @click="close" />
    </div> -->
    <div class="content">
      <div class="form">
        <VanField label-align="right" v-model="form.realName" label="姓名：" maxlength="20"></VanField>
        <VanField label-align="right" v-model="form.mobile" label="电话：" maxlength="11" type="number"></VanField>
        <VanField label-align="right" v-model="addressCode" label="省市区：" readonly @click="addressSelects = true"></VanField>
        <VanField label-align="right" v-model="form.address" label="详细地址：" maxlength="100"></VanField>
      </div>
      <!-- <div class="tip">请注意：地址填写简略、手机号填写错误皆会影响派单，导致您无法收到商品！（超过1小时未填写收货地址信息，视为放弃）</div> -->
      <div class="submit" @click="checkForm"></div>
    </div>
    <div class="close" @click="close" />
  </div>

  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { reactive, ref, computed, PropType, onMounted } from 'vue';
import { areaList } from '@vant/area-data';
import { FormType } from '../ts/type';
import { httpRequest } from '@/utils/service';

const props = defineProps({
  userPrizeId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const addressSelects = ref(false);

const form: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '提交中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90202/saveAddress', {
      ...form,
      userPrizeId: props.userPrizeId,
    });
    closeToast();
    emits('close', true);
  } catch (error: any) {
    console.error(error);
    showToast(error.message);
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的电话');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/264868/1/22544/24190/67b828d9F772c13d0/7957e31e44ebeb18.png');
  background-size: 100%;
  width: 6.15rem;
  height: 7.79rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.2rem;
  // background-color: #f2f2f2;
  // border-radius: 0.2rem 0.2rem 0 0;
  // width: 100vw;

  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: 0.6rem;
    height: 0.6rem;
    cursor: pointer;
  }

  .content {
    padding: 0 0.5rem;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    width: 100%;
    .form {
      .van-cell {
        border-radius: 0.08rem;
        margin-bottom: 0.4rem;
        background: transparent;
        padding: 0;
        color: #b25a08;

        &::after {
          display: none;
        }
      }
    }

    .tip {
      font-size: 0.18rem;
      color: #b3b3b3;
    }

    .submit {
      text-align: center;
      font-size: 0.35rem;
      line-height: 0.8rem;
      color: #fff;
      width: 3.5rem;
      height: 0.8rem;
      margin: 1rem auto 0;
    }
  }
}
</style>
<style>
.van-field__control {
  height: 0.65rem;
  background-color: #fff;
  border-radius: 0.15rem;
  border: 0.01rem solid #f7ca97;
  padding-left: .2rem;
}
.van-field__label {
  color: #b25a08;
  font-size: 0.26rem;
  font-weight: bold;
  line-height: 0.65rem;
  width: 1.4rem;
}
</style>
