<template>
  <div class="bg" :style="furnishStyles.actBgColor.value" v-if="isLoadingFinish">
    <img alt="" :src="furnishStyles.kvImg.value.src"  class="kv-img"/>
    <div :style="furnishStyles.shopNameColor.value" class="shop-name-text">
      <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
    </div>
    <div class="header-btn-box">
      <div class="header-btn ruleBtn" @click="showRule = true"/>
      <div class="header-btn myPrizeBtn"  @click="showMyPrize = true"/>
      <div class="header-btn myOrderBtn" @click="showMyOrder = true"/>
      <div class="header-btn awardUserList" @click="toast"/>
    </div>
    <img class="steps" :src="furnish.steps" alt="">
    <div class="step1" :style="furnishStyles.step1Bg.value">
      <div class="toLock" @click="toast"></div>
    </div>
    <img class="step2Title" :src="furnish.step2TitleImg" alt="">
    <div v-for="(item, index) in furnish.prizeBgList" :key="index" class="prize-container">
      <img class="prizeBg" :src="item.bg" alt="">
      <div class="prize-list" v-if="prizeList && prizeList.length > 0">
        <div v-for="(prize, index2) in getPrizesByType(item.type)" :key="index2" class="prize-item-wrapper">
          <img class="itemBg" :src="item.itemBg" alt="">
          <img class="prize-img" :src="prize.prizeImg" :alt="prize.prizeName">
          <img class="get-btn" @click="toast" src="//img10.360buyimg.com/imgzone/jfs/t1/306324/15/1591/9461/68231698F292c5b79/6865464e4411c278.png">
        </div>
      </div>
      <img class="more" v-if="getPrizesByType(item.type).length === 3" src="//img10.360buyimg.com/imgzone/jfs/t1/295553/14/6266/2444/68231697F619dea55/d3731fd117dbea6a.png" alt=""/>
      <div class="checkMyOrder" @click="showMyOrder = true"/>
    </div>
    <div class="hotZoneBox">
      <!-- 动态生成的热区按钮 -->
      <HotZone :width="7.21" :data="furnish.hotZoneSetting" @hotClick="toast" reportKey="" />
    </div>
    <!-- 获奖规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!-- 我的订单弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyOrder">
      <MyOrder v-if="showMyOrder" @close="showMyOrder = false"></MyOrder>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, nextTick } from 'vue';
import furnishStyles, { furnish, bgImg } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import RulePopup from '../components/RulePopup.vue';
import { showToast } from 'vant';
import MyPrize from '../components/MyPrize.vue';
import MyOrder from '../components/MyOrder.vue';
import 'swiper/swiper.min.css';
import dayjs from 'dayjs';
import HotZone from '../components/HotZone.vue';

console.log(bgImg);
interface Prize {
  prizeImg: string;
  prizeName: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  rule: number;
}

const { registerHandler } = usePostMessage();
const activityData = inject('activityData') as any;
const isLoadingFinish = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const shopName = ref('xxx旗舰店');
const showRule = ref(false);
const showMyPrize = ref(false);
const showMyOrder = ref(false);
const prizeList = ref([]);

type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuListPreview = ref<Sku[]>([]);
const total = ref(0);
const prizeInfo = ref([
  {
    prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png',
    prizeName: 'XX积分',
    rank: '排名为第XX名~第XX名',
    sendTotalCount: 2,
  },
]);
const assignGoodsFlag = ref(1);
// const skuList = ref([]);
const numImg = [
  '//img10.360buyimg.com/imgzone/jfs/t1/214317/31/16906/3536/624f9d31E2e45d8cf/8363020f60f115ff.png',
  'https://img10.360buyimg.com/imgzone/jfs/t1/219322/7/17032/3636/624f9d31E79f6ea41/442e2f2451d5e1f3.png',
  'https://img10.360buyimg.com/imgzone/jfs/t1/145169/18/26369/3813/624f9d31E2fd9a567/021e24c4f043c941.png',
];
const btnBg = [
  'https://img10.360buyimg.com/imgzone/jfs/t1/64447/8/23367/3758/63ac0870F0f838c7d/c92b8348342d6265.png',
  'https://img10.360buyimg.com/imgzone/jfs/t1/221465/27/20660/3871/6311a58dE6005779c/deb8fb3b695d76c3.png',
  'https://img10.360buyimg.com/imgzone/jfs/t1/114536/39/29768/3766/6311a58dE47ada9f3/f9df3917305ca197.png',
  'https://img10.360buyimg.com/imgzone/jfs/t1/240802/20/6644/4676/66053856Fbc71a957/c7f34bc042465ec0.png'];
const ruleTest = ref('');
// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  await useHtmlToCanvas(document.getElementById('interact-c'));
};
const orderEndTime = ref(dayjs().valueOf());
let mySwiper2;
const setDataInfo = (data:any) => { // 数据赋值
  if (data.prizeList && data.prizeList.length > 0) {
    prizeList.value = data.prizeList;
  }
  console.log(data,'555CCCCCCC');
  orderEndTime.value = dayjs(data.orderEndTime).valueOf();
  if (data.rules) {
    ruleTest.value = data.rules;
  }
};
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    // if (data.consumptionActivityType || data.consumptionActivityType === 0) {
    //   consumptionActivityType.value = data.consumptionActivityType;
    //   // activityTypeUnitText.value = ['元', '件', '单'][data.consumptionActivityType];
    // }
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    console.log(res, 'www');
    shopName.value = data.shopName;
    setDataInfo(data);
  } else if (type === 'task') {
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
const toast = () => {
  showToast('活动预览，仅供查看');
};

// 根据类型获取对应的奖品列表
const getPrizesByType = (type: number) => {
  if (!prizeList.value || prizeList.value.length === 0) return [];
  return prizeList.value.filter(prize => prize.type === type);
};
// 截图监听
registerHandler('screen', () => {
  createImg();
});
//
registerHandler('activity', (data) => {
  setDataInfo(data);
  console.log(data, 'activity1111');
});
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  if (activityData) {
    shopName.value = activityData.shopName;
    setDataInfo(activityData);
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    console.log(decoData, 'decoData');
    isLoadingFinish.value = true;
  }
});

/**
 * 计算进度条
 * @param total 节点个数
 * @param userPrice 用户当前订单完成金额
 * @param list 金额列表
 */
const computedProgress = (total: number, userPrice: number, list:Prize[]) => {
  const pre = 1 / total; // 一份占比
  let price = 0; // 当前节点金额
  let nextPrice = 0; // 下一个节点金额
  let num = 0; // 完成的节点个数
  for (let i = 0; i < list.length; i++) {
    if (list[i].prizeMoney < userPrice) {
      price = +list[i].prizeMoney;
      num = i + 1;
      nextPrice = +list[i + 1].prizeMoney;
    }
    if (nextPrice > userPrice) {
      break;
    }
  }
  // console.log(num, userPrice, price, nextPrice, userPrice - price, nextPrice - userPrice, 'price');
  let y = 0;
  const a = userPrice - price;
  const b = nextPrice - price;
  if (num === 0) {
    nextPrice = +list[0].prizeMoney;
    // console.log(a, price, nextPrice, pre * (a / nextPrice), 'a');
    return (a / nextPrice) * pre;
  }
  if (userPrice > price) {
    y = num + a / b;
    // console.log(pre * y, 'y');
    return pre * y;
  } if (userPrice === price) {
    return pre * num;
  }
  return 0;
};

// onUnmounted(() => {
//   window.removeEventListener('message', receiveMessage);
// });
</script>

<style scoped lang="scss">
img[src=""]
,img:not([src])
{
  opacity:0;
}
.more-btn-all {
  width:6.9rem;
  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
    background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 0.3rem;
  }
}
.bg {
  position: relative;
  padding-bottom: 1rem;
  min-height: 100vh;
  max-width: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  .kv-img {
    position: relative;
    width: 7.22rem;
    margin: 0 auto;
  }
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .header-btn-box {
    position: absolute;
    top: 0.7rem;
    right: 0;
    .header-btn {
      width: 1.1rem;
      height: 0.37rem;
      margin-bottom: 0.1rem;
      cursor: pointer;
      background-size: 100%;
      background-size: 100%!important;
    }
    .ruleBtn{
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/299249/37/7020/3973/682436edF322a12e9/d655bbba64241bc5.png) no-repeat;
    }
    .myPrizeBtn{
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/316627/5/494/4063/682436edF446e1ef5/4d4ebeaf9d891c56.png) no-repeat;
    }
    .myOrderBtn{
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/304675/17/1767/3820/682436edF2c49ba2f/a54a5847ef07930e.png) no-repeat;
    }
    .awardUserList{
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/286309/37/11679/3056/685d032cF038c9b4b/ffea6eb3b6465fed.png) no-repeat;
    }

  }
  .steps{
    width: 7.22rem;
    margin: 0.28rem auto 0;
  }
  .step1{
    width: 7.23rem;
    height: 2.92rem;
    margin: 0.28rem auto 0;
    background-size: 100%;
    background-repeat: no-repeat;
    padding: 2.1rem 0 0 2.6rem;
    .toLock {
      width: 1.67rem;
      height: 0.42rem;
      margin: 0 auto;
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/292936/27/4495/9139/68231697Fd54ea534/81f5c2aa060da4b4.png) no-repeat;
      background-size: 100%;
    }
  }
  .step2Title{
    width: 6.16rem;
    margin: 0.28rem auto 0;
  }
  .prize-container {
    position: relative;
    margin: 0.28rem auto 0;
  }
  .prizeBg{
    width: 7.25rem;
    margin: 0 auto;
  }
  .prize-list {
    position: absolute;
    top: 1rem;
    left: calc(50% - 7rem / 2);
    width: 7rem;
    display: flex;
    overflow-x: scroll;
  }
  .prize-item-wrapper {
    position: relative;
    display: inline-block;
    margin: 0 0.1rem;
  }
  .itemBg {
    height: 2.73rem;
    display: block;
  }
  .prize-img {
    object-fit: contain;
    position: absolute;
    top: 0.1rem; /* 调整这个值可以控制图片在itemBg中的垂直位置 */
    left: 50%;
    transform: translateX(-50%);
    width: 2rem;
  }
  .get-btn {
    position: absolute;
    top: 2.1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2rem;
  }
  .more {
    position: absolute;
    bottom: 0.95rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2.66rem;
  }
  .checkMyOrder {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/291247/17/6034/5327/6824062dF8411c998/cd959d80b166219d.png) no-repeat;
    background-size: 100%;
    width: 2.22rem;
    height: .32rem;
    /* margin: 0 auto; */
    position: absolute;
    bottom: 0.6rem;
    left: 50%;
    transform: translateX(-50%);
  }
  .hotZoneBox{
    width: 7.21rem;
    margin: 0 auto;
    position: relative;
  }
  .hotZone{
    width: 100%;
    margin: 0 0 0.2rem 0;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
