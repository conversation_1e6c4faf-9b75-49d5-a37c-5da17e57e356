<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <div class="content">
      <div class="prize-name">
        <div>恭喜您获得</div>
        <div>{{ prize.prizeName }}</div>
      </div>
      <img :src="prize.prizeImg" alt="" class="prize-img" />
      <div class="btn-list">
        <!--实物-->
        <div>
          <div class="btn2" v-if="prize.prizeType === 3" @click="close"/>
        </div>
        <!--京豆和E卡-->
        <div v-if="prize.prizeType === 2 || prize.prizeType === 8">
          <div class="btn2" @click="close"/>
          <div class="tips">确认收货{{prize.awardDays}}天后无退货，可发放到账</div>
        </div>
        <!--礼品卡-->
        <div v-if="prize.prizeType === 7">
          <div class="tips">{{prize.awardDays}}日内无退货后，可获得礼品</div>
        </div>
      </div>
    </div>
    <div class="close" @click="close"></div>
  </div>
  <div class="thanks-join" v-else>
    <div class="close" @click="close"></div>
    <div class="btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { gotoShopPage } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

const props = defineProps(['prize']);

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.bk {
  height: 8.92rem;
  width: 6.5rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/304477/20/1968/19526/6824879dF72ce2100/cb42b8a74ab625a5.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 1rem;
  .prize-img {
    height: 2rem;
    width: 2rem;
    margin: 0.8rem auto 0;
  }
  .content {
    width: 5.6rem;
    height: 3.5rem;
    border-radius: 0.2rem;
    margin: 0.2rem auto 0;
    padding: 0.3rem 0;
    .prize-name {
      font-size: 0.3rem;
      font-weight: bold;
      margin: 0 0 0.3rem;
      text-align: center;
    }
    .btn-list {
      display: flex;
      justify-content: center;
      .btn2 {
        height: 0.77rem;
        width: 3.22rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/291018/36/6693/12675/68248cd7F5f208693/314b8c23601be2c7.png);
        background-repeat: no-repeat;
        background-size: 100%;
        margin: 0 auto;
      }
      .btn3 {
        height: 1.03rem;
        width: 3.22rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/318014/13/2340/13632/682c552aFaaf513d9/98fe9fad078b9b59.png);
        background-repeat: no-repeat;
        background-size: 100%;
        margin: 0 auto;
      }
      .tips{
        font-size: 0.2rem;
      }
    }
  }
  .close {
    width: 0.6rem;
    /* background: #000; */
    height: 0.6rem;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
  }
}
.thanks-join {
  width: 6rem;
  height: 6.3rem;
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/192914/3/22012/24033/623985b9E8508c48b/019e54628504b2dc.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 4.5rem;
  .close {
    height: 0.24rem;
    width: 0.24rem;
    position: absolute;
    right: 0.34rem;
    top: 0.34rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/143521/26/18654/387/5fd9e706E8f1594e3/ae5cc06440559585.png) no-repeat;
    background-size: 100% 100%;
  }
}
</style>
