import { showLoadingToast, showToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';

export const getActUIConfig = async () => {
  try {
    const { data } = await httpRequest.post('/common/getActivityConfig');
    return data || '';
  } catch (error: any) {
    console.error(error);
  }
  return '';
};
// 活动主接口
export const mainAct = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
      // message: '倒计时 3 秒',
    });
    const { data } = await httpRequest.post('/1000087025/lock/rights/loadMainPage');
    closeToast();
    return data;
  } catch (error: any) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 锁权
export const lockRights = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
      // message: '倒计时 3 秒',
    });
    const data = await httpRequest.post('/1000087025/lock/rights/lockRights');
    closeToast();
    return data;
  } catch (error: any) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 领取
export const recieve = async (rightsId:number) => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
      // message: '倒计时 3 秒',
    });
    const data = await httpRequest.post('/1000087025/lock/rights/addToken', { rightsId });
    closeToast();
    return data;
  } catch (error: any) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
