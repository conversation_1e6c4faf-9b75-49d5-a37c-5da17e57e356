<!--
 * 确认兑换弹窗
 * @author: wuhao
 * @since: 2024-09-24
 * ExchangeConfirmPopup.vue
-->
<template>
  <Popup teleport="body" v-model:show="isShowPopup">
    <div class="container">
      <div class="bg">
        <div class="confirm-btn" @click="addToken"></div>
      </div>
      <img class="close-btn" src="//img10.360buyimg.com/imgzone/jfs/t1/228668/36/25189/1605/66f2730dF1d833fda/36b51fc2b705daeb.png" alt="" @click="closePopup" />
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed } from 'vue';
import { Popup, showToast } from 'vant';
import { recieve } from '../ajax/ajax';

const props = defineProps({
  showPopup: {
    type: Boolean,
    default: false,
  },
  rightsId: {
    type: Number,
    default: 0,
  },
});
const isShowPopup = computed(() => props.showPopup);
const emit = defineEmits(['closePopup', 'refresh']);
const closePopup = () => {
  emit('closePopup');
};
const addToken = async () => {
  const result = await recieve(props.rightsId);
  if (result.code === 200) {
    closePopup();
    showToast('领取成功');
    setTimeout(() => {
      emit('refresh');
    }, 1000);
  } else {
    closePopup();
  }
};
</script>

<style scoped lang="scss">
.container {
  height: 8rem;

  .bg {
    width: 5.7rem;
    height: 6.85rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/279215/4/27281/35263/681193bbFb604da29/4889e2c103902d75.png) no-repeat;
    background-size: 100%;
    padding-top: 5.45rem;

    .confirm-btn {
      width: 2.2rem;
      height: 0.5rem;
      margin: 0 auto;
    }
  }

  .close-btn {
    width: 0.7rem;
    height: 0.7rem;
    position: absolute;
    top: 7rem;
    left: 2.5rem;
  }
}
</style>
