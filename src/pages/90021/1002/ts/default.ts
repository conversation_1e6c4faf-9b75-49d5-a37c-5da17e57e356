export const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};
export const defaultStateList = [
  {
    // 资产封面图
    prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/176585/24/10488/6916/60a4cb50E562734ab/f9ab956ec09a4146.png',
    // 资产名称
    prizeName: 'xx京豆',
    // 资产类型
    prizeType: 2,
    // 剩余份数
    remainCount: 0,
    sendTotalCount: 0,
    type: 1, // 1 规格1 2 规格2
  },
];

export const IMAGE_MAP = {
  // 倒计时背景
  COUNT_DOWN_BG: 'https://img10.360buyimg.com/imgzone/jfs/t1/249344/31/1668/23181/6593bcc5Fc9cca6bd/2da62e837e4ff0f9.png',
  // 立即下单图
  IMMEDIATELY_PAY: 'https://img10.360buyimg.com/imgzone/jfs/t1/226966/6/11196/24408/6593bcc4Fb181614a/507fef69f187dce7.png',
  // 步骤tag
  STEP_ICON: 'https://img10.360buyimg.com/imgzone/jfs/t1/229749/8/10382/818/6593bcc5F44a7689a/600eddbd71ae6fd8.png',
  // 奖品背景
  PRIZE_BORDER: 'https://img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png',
  // 爆款商品背景
  HOT_ITEM_BG: 'https://img10.360buyimg.com/imgzone/jfs/t1/226687/36/9528/90302/6593bcc6Fd3f4b982/57f4f5986afece13.png',
};
export const defaultSeriesList = [
  {
    seriesName: '',
    skuList1: [],
    skuList2: [],
    trialSkuList: [{
      showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/107696/16/54872/19248/680212ceFc8b5e201/570e4468a2f676be.png',
      specs: 1,
    }, {
      showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/107696/16/54872/19248/680212ceFc8b5e201/570e4468a2f676be.png',
      specs: 2,
    }],
    formalSkuList: [{
      showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/107696/16/54872/19248/680212ceFc8b5e201/570e4468a2f676be.png',
      specs: 1,
    }],
  },
];
