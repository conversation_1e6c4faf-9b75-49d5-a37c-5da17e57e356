<template>
  <!-- 确认领取弹窗 互斥 -->
  <div class='box-Hc' v-if="isHc">
    <div class='btn-group'>
      <div class='btn-item' @click='closeDialog()'></div>
      <div class='btn-item' @click='sureDraw()'></div>
    </div>
  </div>

  <!-- 确认领取弹窗 不互斥 -->
  <div class='box' v-else>
    <div class='btn-group'>
      <div class='btn-item' @click='closeDialog()'></div>
      <div class='btn-item' @click='sureDraw()'></div>
    </div>
  </div>

</template>

<script lang='ts' setup>
import { defineEmits, defineProps, ref } from 'vue';

const props = defineProps({ isHc: Boolean });
const emits = defineEmits(['closeDialog', 'sureDraw']);
// console.log(props.isHc, '=========');
const closeDialog = () => {
  emits('closeDialog');
};

const sureDraw = () => {
  emits('sureDraw');
};
</script>

<style lang='scss' scoped>
.box-Hc {
  background-image: url("../assets/img/dialog-sure.png");
  width: 7.32rem;
  height: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  .btn-group {
    width: 4.3rem;
    height: 0.7rem;
    position: absolute;
    bottom: 1.2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn-item {
      width: 1.9rem;
      height: 100%;
    }
  }

  .close-btn {
    position: absolute;
    bottom: 0;
    left: 1.3rem;
    width: 3.5rem;
    height: 1rem;
    overflow: auto;
    word-break: break-all;
  }
}
.box{
  background-image: url("../assets/img/dialog-sure-no.png");
  width: 7.32rem;
  height: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  .btn-group {
    width: 4.3rem;
    height: 0.7rem;
    position: absolute;
    bottom: 1.2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn-item {
      width: 1.9rem;
      height: 100%;
    }
  }

  .close-btn {
    position: absolute;
    bottom: 0;
    left: 1.3rem;
    width: 3.5rem;
    height: 1rem;
    overflow: auto;
    word-break: break-all;
  }
}
</style>
