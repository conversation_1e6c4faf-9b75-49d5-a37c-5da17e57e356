* {
  box-sizing: border-box;
}

html {
  max-width: 750px;
  margin: 0 auto;

  #app {
    position: relative;
  }

  img {
    margin: 0 auto;
  }
}

.gray {
  /*grayscale(val):val值越大灰度就越深*/
  -webkit-filter: grayscale(100%) brightness(1);
  -moz-filter: grayscale(100%) brightness(1);
  -ms-filter: grayscale(100%) brightness(1);
  -o-filter: grayscale(100%) brightness(1);
  filter: grayscale(100%) brightness(1);
  filter: gray brightness;
}

#app .van-popup {
  background-color: transparent;
}

.box {
  width: 7rem;
  height: 7.3rem;
  position: relative;
  background: {
    repeat: no-repeat;
    size: contain;
  };
}
