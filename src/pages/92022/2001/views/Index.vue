<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <img
        :src="
          furnish.actBg ??
          'https://img10.360buyimg.com/imgzone/jfs/t1/335078/26/1205/74802/68a569e3Fec1f12b9/20664b77af26dcfe.png'
        "
        alt=""
        class="kv-img"
      />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1">{{ shopName }}</span>
        </div>
        <div>
          <div
            class="header-btn"
            :style="furnishStyles.headerBtn.value"
            v-for="(btn, index) in btnList"
            :key="index"
            @click="btn.event"
          >
            {{ btn.name }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="!isDraw">
      <div
        class="ruleDiv"
        :style="{ backgroundImage: 'url(' + furnish.rulePageBg + ')' }"
      >
        <div class="noRuleTextDiv" v-html="ruleTest"></div>
      </div>
      <div
        class="drawBtnDiv"
        :style="{ backgroundImage: 'url(' + furnish.drawBtnDiv + ')' }"
        @click="getDrawClick()"
      >
        立即申领
      </div>
    </div>
    <div v-else>
      <div
        class="drawSuccBtnDiv"
        :style="{ backgroundImage: 'url(' + furnish.drawSuccBtnDiv + ')' }"
        @click="gotoSkuPage(skuId)"
      >
        申领成功
      </div>
      <div class="promptDiv1">需在15天内使用并领取</div>
      <div class="promptDiv">现在下单立享折扣超低价</div>
      <div class="sku">
        <div class="sku-list">
          <div class="sku-item" v-for="(item, index) in skuList" :key="index">
            <img :src="item.skuMainPicture" alt="" />
            <div class="sku-text">{{ item.skuName }}</div>
            <div class="sku-btns" @click="gotoSkuPage(item.skuId)"></div>
          </div>
          <!-- <div class="more-btn-all">
            <div
              class="more-btn"
              v-if="skuListPreview.length && skuListPreview.length !== total"
              @click="ShowToast()"
            >
              点我加载更多
            </div>
          </div> -->
        </div>
      </div>
    </div>
    <!-- 活动门槛 -->
    <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
    <VanPopup
      teleport="body"
      v-model:show="showOpenCard"
      position="center"
      :closeOnClickOverlay="false"
    >
      <OpenCard @close="showOpenCard = false"></OpenCard>
    </VanPopup>
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule" position="center">
      <RulePopup :rule="ruleTest" @close="showRule = false" :repurchaseTime='repurchaseTime'></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showNotJoinAc" position="center">
      <NotJoinAc
        :thresholdResponseListArr="thresholdResponseListArr"
        v-if="showNotJoinAc"
        @close="showNotJoinAc = false"
      ></NotJoinAc>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showNoPrize" position="center">
      <NoPrize v-if="showNoPrize" @close="showNoPrize = false"></NoPrize>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { inject, nextTick, reactive, ref, watchEffect } from "vue";
import furnishStyles, { furnish } from "../ts/furnishStyles";
import RulePopup from "../components/RulePopup.vue";
import { DecoData } from "@/types/DecoData";
import { BaseInfo } from "@/types/BaseInfo";
import { closeToast, showLoadingToast, showToast } from "vant";
import { httpRequest } from "@/utils/service";
import { gotoShopPage, gotoSkuPage } from "@/utils/platforms/jump";
import dayjs from "dayjs";
import OpenCard from "../components/OpenCard.vue";
import NotJoinAc from "../components/NotJoinAc.vue";
import NoPrize from "../components/NoPrize.vue";
import Threshold2 from "@/components/Threshold2/index.vue";
import useThreshold from "@/hooks/useThreshold";

const skuId = ref("");
// 展示门槛显示弹框
const showLimit = ref(false);
const showNoPrize = ref(false);
const showNotJoinAc = ref(false);
const showOpenCard = ref(false);
const isDraw = ref(false);
const decoData = inject("decoData") as DecoData;
const baseInfo: BaseInfo = inject("baseInfo") as BaseInfo;
// 0-全部商品 1-指定商品  2-排除
const orderSkuisExposure = ref(0);
const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const thresholdResponseListArr = ref<any>([]);
// 先进行会员校验 然后订单检验 再通用校验
const thresholdResponseList1 = baseInfo.thresholdResponseList.filter(
  (item) => item.thresholdCode === 4 || item.thresholdCode === 8
);
if (thresholdResponseList1.length > 0) {
  showOpenCard.value = true;
} else {
  // 2501 有前置订单 2502 报名后取消订单 2503 没有订单
  const thresholdResponseList2 = baseInfo.thresholdResponseList.filter(
    (item) => item.thresholdCode === 801
  );
  if (thresholdResponseList2.length > 0) {
    showNotJoinAc.value = true;
    thresholdResponseListArr.value = thresholdResponseList2;
  } else {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList.filter(
        (item) =>
          item.thresholdCode !== 4 &&
          item.thresholdCode !== 8 &&
          item.thresholdCode !== 801 &&
          item.thresholdCode !== 802 &&
          item.thresholdCode !== 803 &&
          item.thresholdCode !== 804 &&
          item.thresholdCode !== 805 &&
          item.thresholdCode !== 806 &&
          item.thresholdCode !== 807 &&
          item.thresholdCode !== 808
      ),
    });
  }
}
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

// 店铺名称
const shopName = ref(baseInfo.shopName);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref("");
const repurchaseTime = ref("");
// 奖品列表
const prizeList = ref({
  prizeId: 0,
  prizeImg: "",
  prizeName: "",
  prizeType: 0,
  remainCount: 0,
  sendTotalCount: 0,
  // 奖品状态 -1 不满足条件 0 未领取 1 领取成功 2 取消报名 3 发放奖奖品 4剩余份数不足
  status: 0,
  receivePrizeId: 0,
});
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
// 活动规则相关
const showRulePopup = async (type: number) => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get("/common/getRule");
      ruleTest.value = data;
    }
    if (type === 1) {
      showRule.value = true;
    }
  } catch (error) {
    console.error();
  }
};
// 按钮列表
const btnList: {
  name: string;
  clickCode: string;
  event?: () => void;
}[] = [
  {
    name: "活动规则 >",
    clickCode: "hdgz",
    event: () => {
      showRulePopup(1);
    },
  },
];
const hasOrdBefore = ref(false);
const receivePrizeId = ref("");
// 获取阶梯信息(
const getActivity = async () => {
  try {
    const { data } = await httpRequest.post("/92022/activity");
    hasOrdBefore.value = data.hasOrdBefore;
    if (!hasOrdBefore.value) {
      if (furnish.isShowJump && furnish.jumpUrl) {
        window.location.href = furnish.jumpUrl;
        return;
      }
      showLimit.value = true;
    }
    prizeList.value = data.prize;
    repurchaseTime.value = data.repurchaseTime;
    skuId.value = data.skuId;
    isDraw.value = data.prize.status === 1 ? true : false;
    receivePrizeId.value = data.receivePrizeId;
  } catch (error) {
    console.error(error);
  }
};

// 领奖
const getDrawClick = async () => {
  // 先进行会员校验 然后订单检验 再通用校验
  const thresholdResponseList1 = baseInfo.thresholdResponseList.filter(
    (item) => item.thresholdCode === 4 || item.thresholdCode === 8
  );
  if (thresholdResponseList1.length > 0) {
    showOpenCard.value = true;
    return;
  } else {
    // 不满足首购身份
    const thresholdResponseList2 = baseInfo.thresholdResponseList.filter(
      (item) => item.thresholdCode === 801
    );
    if (thresholdResponseList2.length > 0) {
      showNotJoinAc.value = true;
      thresholdResponseListArr.value = thresholdResponseList2;
      return;
    } else {
      const thresholdResponseList3 = baseInfo.thresholdResponseList.filter(
        (item) =>
          item.thresholdCode !== 4 &&
          item.thresholdCode !== 8 &&
          item.thresholdCode !== 801 &&
          item.thresholdCode !== 802 &&
          item.thresholdCode !== 803 &&
          item.thresholdCode !== 804 &&
          item.thresholdCode !== 805 &&
          item.thresholdCode !== 806 &&
          item.thresholdCode !== 807 &&
          item.thresholdCode !== 808
      );
      if (thresholdResponseList3.length > 0) {
        showLimit.value = useThreshold({
          thresholdList: thresholdResponseList3,
        });
        return;
      } else {
        const thresholdResponseList4 = baseInfo.thresholdResponseList.filter(
          (item) =>
            item.thresholdCode === 802 ||
            item.thresholdCode === 803 ||
            item.thresholdCode === 804 ||
            item.thresholdCode === 805 ||
            item.thresholdCode === 806 ||
            item.thresholdCode === 807 ||
            item.thresholdCode === 808
        );
        if (thresholdResponseList4.length > 0) {
          // showToast("抱歉，您暂不满足复购订单~");
          showToast(thresholdResponseList4[0].thresholdContent);
          return;
        }
      }
    }
  }
  if (prizeList.value.status === -1 || prizeList.value.status === 2) {
    showToast("抱歉，您暂不满足复购订单~");
    return;
  }
  if (prizeList.value.status === 4) {
    showNoPrize.value = true;
    return;
  }
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post("/92022/receivePrize", {
      prizeId: prizeList.value.prizeId,
      receivePrizeId: receivePrizeId.value,
    });
    await getActivity();
    if (data.status === 2) {
      showToast('发放失败');
    } else {
      closeToast();
    }
  } catch (error: any) {
    console.error(error);
    if (error.message === "手慢了，奖品已领光~") {
      showNoPrize.value = true;
      await getActivity();
    } else {
      showToast({
        message: error.message,
        duration: 2000,
        onClose: () => {
          getActivity();
        },
      });
    }
  }
};
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

// 获取曝光商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post("/92022/getExposureSku", {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    skuList.value.push(...data);
    // total.value = data.total;
    // pagesAll.value = data.pages;
  } catch (error) {
    console.error(error);
  }
};

const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivity(), showRulePopup(2), getSkuList()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
// 领取奖品
const saveReceivePrize = async (prizeId: number, receivePrizeId: number) => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post("/10077/receivePrize", {
      prizeId,
      receivePrizeId,
    });
    closeToast();
    award.value = data;
    showAward.value = true;
    await init();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
    console.log(error);
  }
};
watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();
</script>

<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.32rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    font-size: 0.35rem;
    cursor: pointer;
    font-weight: 500;
  }
}

.ruleDiv {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/330473/19/1201/3586/68a568e3Fead669a1/03c43db4b06760ab.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.07rem;
  height: 2.56rem;
  margin-left: 50%;
  transform: translateX(-50%);
  margin-top: 0.34rem;
  padding: 0.52rem 0.22rem 0.3rem;
  color: #44423e;
  font-size: 0.18rem;
  .noRuleTextDiv {
    height: 1.8rem;
    overflow-y: scroll;
    // background-color: red;
  }
}
.drawBtnDiv {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/326310/15/8074/13663/68a568e3F6c77f9c3/0c7d96f3a60aab77.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 3.03rem;
  height: 0.69rem;
  margin-left: 50%;
  transform: translateX(-50%);
  font-size: 0;
  margin-top: 0.35rem;
}
.drawSuccBtnDiv {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/329909/28/1217/16087/68a568e1Fdb96526e/34caa99d50e78bef.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 3.84rem;
  height: 0.69rem;
  margin-left: 50%;
  transform: translateX(-50%);
  font-size: 0;
  margin-top: 0.35rem;
}
.promptDiv1 {
  color: #000;
  font-size: 0.24rem;
  text-align: center;
  margin-top: 0.24rem;
}
.promptDiv {
  color: #000;
  font-size: 0.3rem;
  text-align: center;
}
.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  .title-img {
    //width: 2.82rem;
    //height: 0.4rem;
    width: 6.9rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    //margin: 0.2rem auto 0.1rem auto;
    width: 6.9rem;
    place-content: flex-start space-between;
    padding: 0.2rem;
    position: relative;
    overflow-y: scroll;
  }
  .sku-item {
    // width: 3.15rem;
    margin-bottom: 0.1rem;
    // background: rgb(255, 255, 255);
    // border-radius: 0.2rem;
    overflow: hidden;
    // background-color: #44423e;
    margin-bottom: 0.24rem;
    img {
      background-color: #44423e;
      display: block;
      width: 2.8rem;
      height: 2.8rem;
      object-fit: contain;
    }
    .sku-text {
      width: 2.8rem;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      font-size: 0.27rem;
      color: #262626;
      // padding: 0 0.2rem;
      margin: 0.1rem 0 0.1rem 0;
      box-sizing: border-box;
      text-align: center;
    }
    .sku-btns {
      width: 2.51rem;
      height: 0.69rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      // margin: 0.14rem auto;
      margin-left: 50%;
      transform: translateX(-50%);
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/323985/15/7959/12333/68a568e1Feb54929f/7b3e40ad53d77add.png);
    }
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.bottom-shop-share {
  //display: flex;
  position: fixed;
  bottom: 0;
  left: 0.3rem;
  margin: 0 auto;
  .to-shop {
    //flex: 1;
    height: 0.8rem;
    width: 6.9rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .share-friends {
    flex: 1;
    height: 0.88rem;
    width: 3.75rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
.more-btn-all {
  width: 6.9rem;
  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
    background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 0.3rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
