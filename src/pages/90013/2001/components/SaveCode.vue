<template>
  <div class="code-bg" :style="furnishStyles.codeBg.value">
    <input v-model="verificationCode" type="text" class="text" maxlength="15" />
    <div class="btn" @click="toCheck"></div>
  </div>
</template>

<script lang="ts" setup>
import { inject, ref } from 'vue';
import furnishStyles from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';

const isPreview = inject('isPreview') as boolean;
const baseInfo = inject('baseInfo') as BaseInfo;

const verificationCode = ref('');

const toCheck = async () => {
  if (isPreview) {
    console.log('预览');
    showToast('活动预览，仅供查看');
    return;
  }
  if (!verificationCode.value) {
    showToast('请输入核销码');
    return;
  }
  try {
    showLoadingToast({
      message: '核销中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90013/checkCode', {
      verificationCode: verificationCode.value,
    });
    closeToast();
    showToast({
      message: '核销成功',
      forbidClick: true,
    });
    setTimeout(() => {
      window.location.href = baseInfo.openCardLink;
    }, 2000);
  } catch (error: any) {
    console.error(error);
    closeToast();
    showToast({
      message: error.message,
      forbidClick: true,
    });
  }
};
</script>

<style scoped lang="scss">
.code-bg {
  width: 5.25rem;
  height: 5.15rem;
  background-size: 100%;
  background-repeat: no-repeat;
  padding-top: 2.25rem;
  .text {
    display: block;
    width: 4.72rem;
    height: 0.84rem;
    margin: 0 auto;
    background: transparent;
    border: none;
    padding: 0 0.3rem;
    text-align: center;
    font-size: 0.3rem;
  }
  .btn {
    width: 3.15rem;
    height: 0.76rem;
    margin: 0.7rem auto 0;
  }
}
</style>
