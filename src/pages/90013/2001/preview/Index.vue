<template>
  <div v-if="isLoadingFinish">
    <div v-if="!hasWriteOff" class="bg" :style="furnishStyles.pageBg.value">
      <img :src="furnish.joinBtn" alt="" class="join-btn" @click="toast" />
    </div>
    <div v-else class="bg" :style="furnishStyles.successBg.value">
      <img :src="furnish.successBtn" alt="" class="success-btn" @click="toast" />
    </div>
  </div>
  <van-popup v-model:show="codePopup" teleport="body">
    <SaveCode></SaveCode>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, inject, watch } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import html2canvas from 'html2canvas';
import { showToast } from 'vant';
import SaveCode from '../components/SaveCode.vue';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const shopName = ref('xxx自营旗舰店');

const isLoadingFinish = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
const hasWriteOff = ref(false);
const codePopup = ref(false);

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const drawPrize = () => {
  showToast('活动预览，仅供查看');
};

const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;

  showSelect.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    showSelect.value = true;
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    ruleTest.value = data.rules;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'shop') {
    shopName.value = data;
  } else if (type === 'pageType') {
    if (data === '1') {
      hasWriteOff.value = false;
      codePopup.value = false;
    } else if (data === '2') {
      hasWriteOff.value = true;
      codePopup.value = false;
    } else if (data === '3') {
      hasWriteOff.value = false;
      codePopup.value = true;
    }
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    shopName.value = activityData.shopName;

    ruleTest.value = activityData.rules;
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  height: 100vh;
  background-repeat: no-repeat;
  // padding-bottom: 0.5rem;
  // padding-top: 14.95rem;
  .kv {
    width: 100%;
  }
  .join-btn {
    position: fixed;
    bottom: 1.2rem;
    left: 50%;
    transform: translateX(-50%);
    width: 3.14rem;
    margin: 0 auto;
  }
  .success-btn {
    position: fixed;
    bottom: 1.2rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4.6rem;
    margin: 0 auto;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
