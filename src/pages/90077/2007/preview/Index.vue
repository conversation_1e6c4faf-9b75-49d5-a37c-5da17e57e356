<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv ">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img"/>
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
<!--          {{ shopName }}-->
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtnRules.value" @click="showRulePop"/>
          <div class="header-btn" :style="furnishStyles.headerBtnMyPrizes.value" @click="showMyPrizePop"/>
        </div>
      </div>
    </div>
    <div class="hotZoneBox">
<!--      <img class="hotZone" :src="furnish.prizeBg" alt="">-->
<!--      <div class="hotBtn" v-for="(item, index) in showData" :key="index" :style="item.style" @click="ShowToast"></div>-->
      <!-- 动态生成的热区按钮 -->
      <HotZone :width="7.21" :data="furnish.hotZoneSetting" @hotClick="ShowToast" reportKey="" />
      <div class="getLeftBtn" :style="furnishStyles.getPrizeBtn.value" @click="ShowToast"/>
      <div class="getRightBtn" :style="furnishStyles.getPrizeBtn.value" @click="ShowToast"/>
    </div>

    <div class="sku" v-if="isExposure === 1">
      <img class="sku-list-img" :src="furnish.showSkuBg" alt="">
      <div class="sku-list">
        <div class="sku-item" v-for="(item,index) in skuList" :key="index" @click="ShowToast">
          <img v-if="item.showSkuImage" :src="item?.showSkuImage" alt="" />
<!--          <div class="sku-text" :style="furnishStyles.priceColor.value"></div>-->
        </div>
      </div>
    </div>
    <div class="bottom-shop-share">
      <div class="to-shop" :style="furnishStyles.btnToShop.value" @click="ShowToast"/>
    </div>
    <VanPopup teleport="body" v-model:show="showRule" >
      <RulePopup :rule="ruleTest" @close="showRule = false"/>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" >
      <MyPrize @close="showMyPrize = false"/>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, nextTick } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';
import html2canvas from 'html2canvas';
import HotZone from '../components/HotZone.vue';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const total = ref(0);

const isLoadingFinish = ref(false);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
}

const couponPrizeList = ref<Prize>([defaultStateList]);
const prizeList = ref<Prize>([defaultStateList]);
const multiplePrizeList = ref<Prize>([defaultStateList, defaultStateList]);

type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuList = ref<Sku[]>([
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png',
  },
]);
const orderSkuListPreview = ref<Sku[]>([]);
const nextStateAmount = ref(0);
const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const showGoods = ref(false);

const showRulePop = () => {
  showRule.value = true;
};

const showMyPrizePop = () => {
  showMyPrize.value = true;
};

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

// const toSaveAddress = (id: string) => {
//   addressId.value = id;
//   showSaveAddress.value = true;
// };

// const orderSkuisExposure = ref(1);
const isExposure = ref(1);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;
    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);
    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;
    const blob = dataURLToBlob(croppedBase64);
    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  const list1 = data.couponPrizeList;
  const list2 = data.prizeList;
  const list3 = data.multiplePrizeList;
  if (list1.prizeType !== 0) {
    couponPrizeList.value = list1;
  }
  if (list2.prizeType !== 0) {
    prizeList.value = list2;
  }
  if (list3.prizeType !== 0) {
    multiplePrizeList.value = list3;
  }
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuList) {
    skuList.value = data.skuList;
  }
  if (data.orderSkuListPreview) {
    orderSkuListPreview.value = data.orderSkuListPreview;
  }
  ruleTest.value = data.rules;
  // orderSkuisExposure.value = data.orderSkuisExposure;
  isExposure.value = data.isExposure;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    const list1 = activityData.couponPrizeList;
    const list2 = activityData.prizeList;
    const list3 = activityData.multiplePrizeList;
    if (list1.prizeType !== 0) {
      couponPrizeList.value = list1;
    }
    if (list2.prizeType !== 0) {
      prizeList.value = list2;
    }
    if (list3.prizeType !== 0) {
      multiplePrizeList.value = list3;
    }
    ruleTest.value = activityData.rules;
    orderSkuListPreview.value = activityData.orderSkuListPreview;
    shopName.value = activityData.shopName;
    skuList.value = activityData.skuList;
    // orderSkuisExposure.value = activityData.orderSkuisExposure;
    isExposure.value = activityData.isExposure;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style lang="scss" scoped>

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  //margin-bottom: 9rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 0.43rem;
    height: 1.42rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat:no-repeat;
    background-size: 100%;
  }
}

.hotZoneBox{
  width: 7.21rem;
  margin: 0 auto;
  position: relative;
}
.hotZone{
  width: 100%;
  margin: 0 0 0.2rem 0;
}

//.hotBtn {
//  background-color: #000;
//}

.getCouponBtn {
  position: absolute;
  bottom: 10.7rem;
  left: 3.9rem;
  width: 1.81rem;
  height: 0.51rem;
  background-repeat:no-repeat;
  background-size: 100%;
}

.getCardBtn{
  position: absolute;
  bottom: 8.12rem;
  left: 3rem;
  width: 1.43rem;
  height: 0.35rem;
  background-repeat:no-repeat;
  background-size: 100%;
}

.getLeftBtn{
  width: 0.5rem;
  height: 0.5rem;
  background-repeat: no-repeat;
  background-size: 100%;
  //background-color: #000;
  position: absolute;
  top: 3.7rem;
  z-index: 9;
  left: 2.5rem;
}

.getRightBtn{
  width: 0.5rem;
  height: 0.5rem;
  background-repeat: no-repeat;
  background-size: 100%;
  //background-color: #000;
  position: absolute;
  top: 3.7rem;
  z-index: 9;
  right: 0.86rem;
}

.remainBox1 {
  position: absolute;
  bottom: 8.7rem;
  left: 4.5rem;
  font-size: 0.18rem;
}
.remainBox2 {
  position: absolute;
  bottom: 6.15rem;
  left: 0.85rem;
  font-size: 0.18rem;
  //background-color: #000;
  width: 2.44rem;
  text-align: center;
}
.remainBox3 {
  position: absolute;
  bottom: 6.15rem;
  left: 4.18rem;
  font-size: 0.18rem;
  //background-color: #000;
  width: 2.44rem;
  text-align: center;
}

.sku{
  width: 7.21rem;
  padding: 0.2rem 0;
  position: relative;
  margin: 0.2rem auto 0.1rem auto;
  .sku-list-img {
    width: 7.21rem;
    height: auto;
  }
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    width: 7.21rem;
    place-content: flex-start space-between;
    padding: 1.18rem 0.2rem 0;
    height: auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
  }
  .sku-item{
    width: 3.3rem;
    overflow: hidden;
    margin-bottom: 0.2rem;
    .sku-text{
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      font-size: 0.32rem;
      height: 0.8rem;
      margin: 0.4rem auto 0;
      box-sizing: border-box;
      .go-sku-btn {
        width: 1.4rem;
        height: 0.3rem;
        //background-color: #000;
      }
    }
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.bottom-shop-share{
  margin: 0 auto;
  .to-shop{
    margin: 0 auto;
    height: 1rem;
    width: 3.5rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
.more-btn-all {
  width:6.9rem;
  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
    background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 0.3rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
