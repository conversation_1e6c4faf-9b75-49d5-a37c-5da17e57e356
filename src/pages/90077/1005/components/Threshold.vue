<template>
  <VanPopup teleport="body" v-model:show="isShowPopup" @click-overlay='closeDialog'>
    <div class="box">
      <div class='dialog'>
        <div class="title">- 很抱歉 -</div>
        <div class="dialog_rule">
          {{props.data[0].thresholdContent}}
        </div>
      </div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, defineEmits, defineProps } from 'vue';

const props = defineProps(['data', 'canNotCloseJoin', 'showPopup']);
const isShowPopup = computed(() => props.showPopup);
console.log(props.data);
const emits = defineEmits(['closeDialog']);

const closeDialog = () => {
  // B端配置1为可关闭，2为不可关闭
  if (props.canNotCloseJoin !== '2') {
    emits('closeDialog');
  }
};
</script>

<style scoped lang="scss">
.box {

  .dialog {
    width: 6.9rem;
    margin: 0 auto;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/227345/13/27509/102377/66da7777F85c25ba6/d10d7399c5fa1b61.png) no-repeat;
    height: 8rem;
    background-size: cover;
    box-sizing: border-box;
    padding: 1.1rem 0.85rem;
    .title {
      font-size: 0.37rem;
      text-align: center;
      margin: 0.5rem 0;
      color: #1b3f7d;
    }

    .dialog_rule {
      max-height: 3.73rem;
      overflow-y: auto;
      font-size: 0.28rem;
      font-weight: normal;
      letter-spacing: 0.01rem;
      color: #1b3f7d;
      margin: 0.1rem auto;
      text-align: left;
      white-space: pre-wrap;
      width: 90%;
    }
  }

}
</style>
