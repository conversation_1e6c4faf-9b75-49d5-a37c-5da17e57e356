<template>
  <div class="box">
    <div class='dialog'>
      <div class="title">- 填写信息 -</div>
      <div class="dialog_rule">
        <div class="px-4 leading-5 text-[#33333] whitespace-pre-wrap text-xs pb-5">
          <div>
            <VanField class="fieldItem" v-model="form.realName" required label="姓名：" maxlength="20"></VanField>
            <VanField class="fieldItem" v-model="form.mobile" required label="电话：" maxlength="11" type="number"></VanField>
            <VanField class="fieldItem" v-model="addressCode" required label="省市区：" readonly @click="addressSelects = true"></VanField>
            <VanField class="fieldItem" v-model="form.address" required label="详细地址：" maxlength="100"></VanField>
          </div>
<!--          <div class="text-gray-400 mt-2 text-xs">请注意：地址填写简略、手机号填写错误皆会影响派单，导致您无法收到商品！（超过1小时未填写收货地址信息，视为放弃）</div>-->
          <div class="confirm" @click="checkForm"/>
        </div>
      </div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { reactive, ref, computed, PropType, onMounted } from 'vue';
import { areaList } from '@vant/area-data';
import { FormType } from '../ts/type';
import { httpRequest } from '@/utils/service';

const props = defineProps({
  addressId: {
    type: String,
    required: true,
  },
  echoData: {
    type: Object as PropType<any>,
    default: () => ({
      realName: '',
      mobile: '',
      province: '',
      city: '',
      county: '',
      address: '',
    }),
  },
});

const emits = defineEmits(['close']);

const addressSelects = ref(false);

const form: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

onMounted(() => {
  // 回显地址
  Object.keys(form).forEach((key: string) => {
    form[key] = props.echoData[key];
  });
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});
// const addressCode = ref('');

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90077/userAddressInfo', {
      addressId: props.addressId,
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的电话');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};
</script>
<style scoped>
.box {

  .dialog {
    width: 6.9rem;
    margin: 0 auto;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/227345/13/27509/102377/66da7777F85c25ba6/d10d7399c5fa1b61.png) no-repeat;
    height: 8rem;
    background-size: cover;
    box-sizing: border-box;
    padding: 1.1rem 0.85rem;
    .title {
      font-size: 0.42rem;
      text-align: center;
      font-weight: bold;
      letter-spacing: 0.08rem;
      color: #1b3f7d;
      margin: 0.1rem 0 0.1rem;
    }
    .dialog_rule {
      max-height: 3.73rem;
      font-size: 0.21rem;
      font-weight: normal;
      letter-spacing: 0.01rem;
      color: #1b3f7d;
      margin-top: 0.1rem;
      text-align: left;
    }
    .fieldItem {
      background-color: #f0aa4a;
      border-radius: 0.2rem;
      margin: 0.1rem 0 0;
      font-weight: bold;
      font-size: 0.24rem;
      height: 0.6rem;
      line-height: 0.3rem;
    }
    .confirm {
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/232777/14/24981/9192/66de5f6cFd88ab96e/404146b7df73b396.png) no-repeat;
      background-size: 100% 100%;
      width: 2.16rem;
      height: 0.66rem;
      margin: 0.2rem auto;
    }
  }
}
.van-cell{
  --van-cell-text-color :#fff;
  --van-field-label-color :#fff;
  --van-field-input-text-color :#fff;
  --van-cell-border-color: rgba(255, 255, 255, 0);
  background-color: transparent;
  color:#fff;
}
</style>
