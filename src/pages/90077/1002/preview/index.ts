import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const a = {
  actBg: '',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/106862/8/48866/2451226/66ebbfedF4ac27f92/8a8acf3080e050c6.png',
  actBgColor: '#8fbe91',
  shopNameColor: '#000000',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/95503/13/50244/8193/66ebbff5F7a6d6698/17df53b4b35fdbf4.png',
  myPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/239217/9/17700/8407/66ebbfedF546b6408/58d72d219ff60be4.png',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/173765/18/45122/1663760/66ebc146Fb0f4d495/f6c66a390ae998bd.png',
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/230361/3/26836/16340/66ebbff0F35a9a19b/2ee42c9ce3e9694f.png',
  getPrizeGrayBtn: '//img10.360buyimg.com/imgzone/jfs/t1/130813/17/46824/13629/66ebbff1F247af0b2/c86d13efd762458d.png',
  prizeNameColor: '#fff',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/185227/12/48315/2385737/66ebbff0F9e16db21/41257a30428773a6.png',
  priceColor: '#ffffff',
  btnToShop: '//img10.360buyimg.com/imgzone/jfs/t1/234930/12/26042/37464/66ebbff6F421acaa8/95f6b50f616497bc.png',
  goMoreUrl: '',
  canNotCloseJoinPopup: '1',
  jumpUrl: '',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/236821/21/23217/36235/66ebc876Fb8e5c768/ce354bdc6c326c26.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/244464/37/18838/52605/66ebc874F24916a19/0a8e11e0d63f05a1.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/5192/1/27146/76016/66ebc876F5124bc47/c5895330a3c5dac4.png',
  hotZoneList: [],
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '新单有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  // app.provide('decoData', a);
  app.provide('isPreview', true);
  app.mount('#app');
});
