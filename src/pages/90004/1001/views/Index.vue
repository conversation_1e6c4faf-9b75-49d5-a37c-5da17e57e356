<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="btn-list">
      <img :src="furnish.ruleImg" v-click-track="'wdjp'" alt="" @click="showMyPrize = true"/>
    </div>
    <div class="levelDivAll">
      <div class="levelDiv">
        <div class="levelItemDiv" v-for="(item,index) in levelArr" :key="index">
          <div class="levelItemDiv2"  :class="{ 'levelItemDiv1' : currentLevelIndex === index+1 }" @click.stop="selectLevel(item,index)">{{item.name}}</div>
        </div>
      </div>
      <div class="levelPrizeDivALL">
        <div class="levelPrizeBg" :style="furnishStyles.levelPrizeBgArr.value[currentLevelIndex - 1].backgroundImage ? furnishStyles.levelPrizeBgArr.value[currentLevelIndex - 1] : levelArr[currentLevelIndex - 1].styleData"></div>
      </div>
      <div v-if="currentLevelIndex === 1" class="levelTextDiv1"></div>
      <div v-if="currentLevelIndex === 2" class="levelTextDiv2"></div>
      <div class="bottomBtnDiv" @click.stop="getPrizeClick(levelArr[currentLevelIndex - 1])"></div>
      <div class="levelRuleDiv">
        <div class="contentDiv" v-html="levelArr[currentLevelIndex - 1].taskRule"></div>
      </div>
    </div>
  </div>
  <!-- 活动门槛 -->
  <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :itemData="selectItemData" :addressId="addressId" :activityPrizeId="activityPrizeId" :userPrizeId="userPrizeId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" :isEnd="isEnd">
    </MyPrize>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, inject } from 'vue';
import furnishStyles, { furnish, taskRequestInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import SaveAddress from '../components/SaveAddress.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import Threshold2 from '@/components/Threshold2/ThresholdCPB.vue';
import useThreshold from '@/hooks/useThreshold';
import { gotoSkuPage } from '@/utils/platforms/jump';
import '@/components/Threshold2/CPBStyle.scss';
import MyPrize from '../components/MyPrize.vue';

const levelArr = reactive([
  {
    name: '银卡会员',
    value: 1,
    gradeValue: 1,
    id: '',
    skuId: '',
    prizeType: 11,
    styleData: {
      width: '4.35rem',
      height: '4.64rem',
      backgroundImage: 'url(//img10.360buyimg.com/imgzone/jfs/t1/89606/12/39598/43908/65ea76ccFc2323caf/dba8162d0b9d69be.png)',
    },
    taskRule: '',
  },
  {
    name: '金卡及以上会员',
    value: 2,
    gradeValue: 2,
    id: '',
    skuId: '',
    prizeType: 11,
    styleData: {
      width: '4.30rem',
      height: '4.63rem',
      backgroundImage: 'url(//img10.360buyimg.com/imgzone/jfs/t1/100986/32/47751/68203/65ea76cdF2fb18563/d5148d51949265fd.png)',
    },
    taskRule: '',
  },
]);

const showMyPrize = ref(false);

// 当前选择对的会员等级，默认为等级1
const currentLevelIndex = ref(1);
const closeOnClickOverlay = ref(false);
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const isEnd = ref(false);
const endTime = ref(0);

const getTime = async () => {
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > endTime.value) {
    isEnd.value = true;
  }
};

// 保存实物地址相关
const userPrizeId = ref('');
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const selectItemData = ref(null);

// 展示门槛显示弹框
const showLimit = ref(false);
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
  className: 'common-message-cpb',
});
// 获取页面数据
const getAllData = async () => {
  try {
    const { data } = await httpRequest.post('/90004/activityInfo');
    taskRequestInfo.splice(0);
    taskRequestInfo.push(...data.list);
    levelArr.forEach((item, index) => {
      taskRequestInfo.forEach((item1, index1) => {
        if (item.gradeValue === Number(item1.level)) {
          item.taskRule = item1.taskRule;
          item.id = item1.id;
          item.skuId = item1.skuId;
        }
      });
    });
  } catch (error: any) {
    console.error();
  }
};
// 选择等级
const selectLevel = (itemData: any, index: any) => {
  currentLevelIndex.value = Number(itemData.value);
};
// 调用接口获取地址信息
const getAddressInfo = async (itemData:any) => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/90004/getAddressInfo', {
      activityPrizeId: 0,
      taskId: itemData.id,
    });
    closeToast();
    showSaveAddress.value = true;
    addressId.value = data.addressId;
    activityPrizeId.value = data.activityPrizeId;
    userPrizeId.value = data.userPrizeId;
  } catch (e) {
    closeToast();
    console.log(e, '调用接口获取地址信息');
  }
};
// 调用领取奖品接口，根据接口返回字段判断接下来的操作
const getPrizeClick = async (itemData:any) => {
  console.log(itemData, '领取奖品');
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
      className: 'common-message-cpb',
    });
    return;
  }
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  selectItemData.value = itemData;
  try {
    const { data } = await httpRequest.post('/90004/sendPrize', {
      activityPrizeId: '',
      taskId: itemData.id,
    });
    closeToast();
    console.log(data, '调用领取奖品接口');
    if (data.status === 0) {
      showToast('申请成功');
      if (data.prizeType === 3) {
        // 实物成功 关闭弹窗 显示填写地址弹窗
        showSaveAddress.value = true;
        // addressId.value = data.result.result;
        activityPrizeId.value = data.activityPrizeId;
        userPrizeId.value = data.userPrizeId;
      } else {
        const timeOutName = setTimeout(async () => {
          // 令牌领取成功则跳转商详页面
          await gotoSkuPage(itemData.skuId);
          clearTimeout(timeOutName);
        }, 2000);
      }
    } else {
      showToast('领取失败');
    }
  } catch (errMag) {
    console.log(errMag, '调用领取奖品接口错误信息');
    showToast(errMag.message);
    if (errMag.message === '已申领过实物奖品啦！请填写地址~') {
      showToast(errMag.message);
      setTimeout(() => {
        closeToast();
        // 调用接口获取地址信息
        getAddressInfo(itemData);
      }, 1000);
    } else if (errMag.message === '您已领取过奖品啦~') {
      showToast(errMag.message);
      console.log(itemData.skuId, 'itemData.skuId');
      console.log(itemData, 'itemData');
      // 有skuId则表示为令牌;
      if (itemData.prizeType === 11 && itemData.skuId) {
        const timeOutName = setTimeout(async () => {
          // 令牌已经领取过toast提示后跳转商详页面，不管令牌是否已经使用
          await gotoSkuPage(itemData.skuId);
          clearTimeout(timeOutName);
        }, 2000);
      }
    } else {
      showToast(errMag.message);
    }
  }
};

const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getAllData(), getTime()]);
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};
init();
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  .levelDivAll{
    position: relative;
    padding-top: 2.5rem;
    .levelDiv{
      display: flex;
      justify-content: center;
      align-items: center;
      width: 5.25rem;
      height: 0.61rem;
      margin-left: calc(50% - 5.25rem / 2);
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/239167/35/5568/5347/65ea73abF042c8923/42b65af5c1b5892a.png) no-repeat;
      background-size: 100%;
      cursor: pointer;
      .levelItemDiv{
        flex: 1;
        display:flex;
        justify-content: center;
        .levelItemDiv2{
          color: #ffffff;
          font-size: 0.24rem;
        }
        .levelItemDiv1{
          background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/245977/40/5659/2010/65ea73abFed5e12f5/6ae71b9e410a2ef7.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 2.58rem;
          height: 0.48rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #634b36;
          font-size: 0.24rem;
        }
      }
    }
  }
  .levelPrizeDivALL{
    margin-top: 0.2rem;
    display: flex;
    justify-content:center;
    align-items:center;
    flex-direction: column;
    .levelPrizeBg{
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .levelPrizeTitle{
      width: 80%;
      font-size: 0.18rem;
      min-height: 0.8rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .levelRuleDiv{
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/240815/4/2279/7956/65960d8fFb7fb05fa/01a02c35c6ec80a3.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 6.51rem;
    height: 3.06rem;
    margin-left: calc(50% - 6.51rem / 2);
    padding:0.5rem 0.2rem;
    color:#876634;
    .contentDiv{
      height: 2.06rem;
      overflow-y: scroll;
      white-space: pre-wrap;
    }
  }
  .levelTextDiv1{
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/163562/36/39630/5385/65f3fa52Fda9f9626/7aff8de5ea1107e4.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 3rem;
    height: 0.46rem;
    margin-left: calc(50% - 3rem / 2);
    margin-top: 0.2rem;
  }
  .levelTextDiv2{
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/163082/4/43149/6883/65f3fa52F84d124cc/0c049e496f166c56.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 4.53rem;
    height: 0.45rem;
    margin-left: calc(50% - 4.53rem / 2);
    margin-top: 0.2rem;
  }
  .bottomBtnDiv{
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/228586/36/13868/4640/65e97f21Fc3a0c5aa/2a223c38864f93ea.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 2.40rem;
    height: 0.63rem;
    margin-left: calc(50% - 2.40rem / 2);
    margin-top: 0.24rem;
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.btn-list {
  position: absolute;
  top: 3rem;
  right: 0;
  z-index:999;
  img {
    width: 0.47rem;
    margin-bottom: 0.18rem;
  }
}
</style>
