<template>
  <div class="rule-bk">

    <div class="content" v-html="rule"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 5.74rem;
  height: 10.02rem;
  background-image:url('//img10.360buyimg.com/imgzone/jfs/t1/253195/37/28964/51121/67c6927dFa2c8f880/b0e10cb3c1d731b1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding:5.00rem 0.7rem 0 0.8rem;
  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .content {
    height: 30vh;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
  }
}
</style>
