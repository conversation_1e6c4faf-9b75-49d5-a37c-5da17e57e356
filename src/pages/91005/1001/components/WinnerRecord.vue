<template>
  <div class="winnerRecordDivAll">
    <div class="activityName">活动名称：{{subActivityName}}</div>
    <div class="titleListDiv" v-if="currentTab === 2">
      <div class="titleItem" v-for="(item,index) in showArr" :key="index">{{item.aliasName}}</div>
    </div>
    <div class="titleListDiv" v-else>
      <div class="titleItem" v-for="(item,index) in selectShowArr" :key="index">{{item.aliasName}}</div>
    </div>
    <div class="listDivAll" v-if="currentTab === 2 && titleList.length > 0">
      <div class="listDiv">
        <div class="itemDiv" v-for="(item1, index1) in showArr" :key="index1">
          <div class="itemDiv11" v-for="(item2, index2) in titleList" :key="index2">
            <div class="textDiv" v-if="item1.columnValue === 'grantTime'">{{item2[item1.columnValue] ? dayjs(item2[item1.columnValue]).format('YYYY/MM/DD') : '--'}}</div>
            <div class="textDiv" v-else-if="item1.columnValue === 'pin' || item1.columnValue === 'mobile' || item1.columnValue === 'realName' || item1.columnValue === 'nickName'">{{item2[item1.columnValue] ? encryptStr(item2[item1.columnValue]) : '--'}}</div>
            <div class="textDiv" v-else>{{item2[item1.columnValue] ? item2[item1.columnValue] : '--'}}</div>
          </div>
        </div>
      </div>
      <div class="more-btn-all">
        <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
      </div>
    </div>
    <div class="noDataDiv" v-else-if="currentTab === 2 && titleList.length <= 0">
      暂无数据
    </div>
   <div class="listDivAll" v-else-if="currentTab === 1 && titleList.length > 0">
     <div class="listDiv">
       <div class="itemDiv" v-for="(item1, index1) in selectShowArr" :key="index1">
         <div class="itemDiv11" v-for="(item2, index2) in titleList" :key="index2">
           <div class="textDiv" v-if="item1.columnValue === 'grantTime'">{{item2[item1.columnValue] ? dayjs(item2[item1.columnValue]).format('YYYY/MM/DD') : '--'}}</div>
           <div class="textDiv" v-else-if="item1.columnValue === 'pin' || item1.columnValue === 'mobile' || item1.columnValue === 'realName' || item1.columnValue === 'nickName'">{{item2[item1.columnValue] ? encryptStr(item2[item1.columnValue]) : '--'}}</div>
           <div class="textDiv" v-else>{{item2[item1.columnValue] ? item2[item1.columnValue] : '--'}}</div>
         </div>
       </div>
     </div>
     <div class="more-btn-all">
       <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
     </div>
   </div>
    <div class="noDataDiv" v-else-if="currentTab === 1 && titleList.length <= 0">
      暂无数据
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';

const props = defineProps({
  showArr: {
    type: Array,
    default: () => [],
  },
  currentTab: {
    type: Number,
    default: 0,
  },
  subActivityId: {
    type: String,
    default: '',
  },
  subActivityName: {
    type: String,
    default: '',
  },
});
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

const selectShowArr = ref([{
  aliasName: '昵称',
  columnName: '昵称',
  columnValue: 'nickName',
  show: true,
}, {
  aliasName: '奖品名称',
  columnName: '奖品名称',
  columnValue: 'prizeName',
  show: true,
}, {
  aliasName: '中奖时间',
  columnName: '中奖时间',
  columnValue: 'grantTime',
  show: true,
}]);
console.log(props.currentTab, props.showArr, '导入活动的查询条件');
// 加密
const encryptStr = (pin: string): string => {
  const first = pin.slice(0, 1);
  const last = pin.slice(-1);
  return `${first}****${last}`;
};
const titleList = ref([]);
const titleListPage = ref([]);
const initData = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    if (props.currentTab === 2) {
      const { data } = await httpRequest.post('/91005/exportGrantList', {
        type: 2,
        activityId: props.subActivityId,
        pageNum: pageNum.value,
        pageSize: 30,
      });
      let keys = [];
      titleListPage.value = [];
      data.records.forEach((itemData1) => {
        const objData = {};
        keys = Object.keys(itemData1);
        props.showArr.forEach((itemData) => {
          // //   // 如果你想要逐个打印键，而不是打印整个键数组
          if (keys.includes(itemData.columnValue)) {
            objData[itemData.columnValue] = itemData1[itemData.columnValue];
          }
        });
        titleListPage.value.push(objData);
      });
      // console.log(titleListPage, 'titleListPage');
      total.value = data.total;
      pagesAll.value = data.pages;
      titleList.value.push(...titleListPage.value);
    } else {
      const { data } = await httpRequest.post('/91005/grantList', {
        type: 1,
        activityId: props.subActivityId,
        pageNum: pageNum.value,
        pageSize: 30,
      });
      let keys = ['nickName', 'prizeName', 'grantTime'];
      titleListPage.value = [];
      data.records.forEach((itemData1) => {
        const objData = {};
        keys = Object.keys(itemData1);
        selectShowArr.value.forEach((itemData) => {
        // 如果你想要逐个打印键，而不是打印整个键数组
          if (keys.includes(itemData.columnValue)) {
            objData[itemData.columnValue] = itemData1[itemData.columnValue];
          }
        });
        titleListPage.value.push(objData);
        // titleList.value.push(objData);
      });
      total.value = data.total;
      pagesAll.value = data.pages;
      titleList.value.push(...titleListPage.value);
    }
    // console.log(data, '中奖名单查询');
    closeToast();
  } catch (error: any) {
    console.error(error);
    closeToast(error.message);
  }
};
const loadMore = async () => {
  pageNum.value++;
  await initData();
};
initData();
</script>
<style lang="scss" scoped>
.winnerRecordDivAll{
  width: 5.74rem;
  height: 10.02rem;
  background-image:url('//img10.360buyimg.com/imgzone/jfs/t1/226184/12/30185/50964/67c6927cFc29faaef/9fc7c2198e132fba.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding:5.10rem 0.6rem 0 0.8rem;
  .activityName{
    font-size:0.24rem;
    color:#000;
    text-align: center;
    padding: 0 0.24rem;
    margin-bottom:0.2rem;
  }
  .more-btn-all {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      //background: -webkit-gradient(linear, left top, right top, from(#1890ff), to(#1890ff));
      background: #77c92e;
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .titleListDiv{
    display: flex;
    .titleItem{
      font-size:0.2rem;
      color: #000;
      flex:1;
      display: flex;
      flex-direction: column;
      //align-items: center;
    }
  }
  .listDivAll{
    overflow-y: scroll;
    max-height: 3.2rem;
    margin-top:0.12rem;
  }
  .listDiv{
    display: flex;
  }
  .noDataDiv{
    font-size: 0.24rem;
    height: 25vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .itemDiv{
    font-size:0.2rem;
    color: #000;
    flex:1;
    display: flex;
    flex-direction: column;
    align-items: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    min-width: 0;
    .itemDiv11{
      width:100%;
      //display: flex;
      //align-items: center;
      //justify-content: center;
      //text-align: center;
      white-space: nowrap; /* 禁止文本换行 */
      text-overflow: ellipsis; /* 当内容超出时，使用省略号表示 */
    }
    .textDiv{
      line-height: 0.24rem;
      //max-width: 1rem;
      width:100%;
      white-space: nowrap; /* 禁止文本换行 */
      overflow: hidden;    /* 隐藏超出容器的内容 */
      text-overflow: ellipsis; /* 当内容超出时，使用省略号表示 */
    }
    //.messageItemDiv{
    //  width: 100%;
    //  white-space: nowrap; /* 禁止文本换行 */
    //  overflow: hidden;    /* 隐藏超出容器的内容 */
    //  text-overflow: ellipsis; /* 当内容超出时，使用省略号表示 */
    //  div{
    //    width: 100%;
    //    white-space: nowrap;
    //    overflow: hidden;
    //    text-overflow: ellipsis;
    //  }
    //}
  }
}
</style>
