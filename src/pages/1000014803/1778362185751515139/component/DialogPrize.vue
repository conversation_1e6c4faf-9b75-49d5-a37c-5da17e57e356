<script setup lang="ts">
import { ref, defineEmits } from 'vue';
import useRequest from '../hooks/useRequest';
import dayjs from 'dayjs';

const { request } = useRequest();
const prizeList = ref<any[]>([]);
const prizeInfo = ref<any>({});

const emits = defineEmits(['handleDelivery']);

const fetchData = async () => {
  const { data } = await request('/live/userPrizes');
  prizeList.value = data;
};

const handleDelivery = (item: any) => {
  if (['填写地址', '查看地址'].includes(item.deliveryStatus)) {
    prizeInfo.value = item;
    emits('handleDelivery', prizeInfo.value);
  }
};
const formatTime = (value: any) => dayjs(value).format('MM-DD HH:mm:ss');

fetchData();
</script>

<template>
  <div>
    <div class="noData" v-if="!prizeList.length">暂无数据</div>
    <div class="container" v-else>
      <div class="container-content" v-for="(item, index) in prizeList" :key="index">
        <div>{{formatTime(item.createTime)}}</div>
        <div>{{item.prizeName}}</div>
        <div
          :class="item.deliveryStatus === '已发放'
           ? 'container-content-btn-reverse'
           : 'container-content-btn'"
          @click="handleDelivery(item)"
        >
          {{ item.deliveryStatus }}
        </div>
      </div>
    </div>

  </div>
</template>

<style scoped lang="scss">
.noData {
  position: absolute;
  left: 50%;
  top: 4.2rem;
  transform: translateX(-50%);
  color: #9d9d9d;
}
.container {
  position: absolute;
  top: 2.6rem;
  height: 4.5rem;
  overflow-y: scroll;
  width: 100%;
  &-content {
    display: grid;
    padding: 0 .4rem ;
    grid-template-columns: 1.21fr 1.55fr 1.17fr;
    color: #a81e1d;
    margin-bottom: .2rem;

    &-btn {
      background: #a81e1d;
      color: white;
      border-radius: .1rem;
      width: 64%;
      margin-left: .4rem;

      &-reverse {
        @extend .container-content-btn;
        background: transparent;
        border: 1px solid #a81e1d;
        color: #a81e1d;
      }
    }
    > div {
      text-align: center;
      font-size: .20rem;
      &:first-child {
        text-align: left;
      }
    }
  }
}

</style>
