<template>
  <div class="address-popup">
    <div class="address-content">
      <div class='close' @click="close"></div>
      <div>
        <VanField disabled v-model="form.realName" required label="收货人：" maxlength="20" input-align="right"></VanField>
        <VanField disabled v-model="form.mobile" required label="手机号：" maxlength="11" type="number" input-align="right"></VanField>
        <VanField disabled v-model="addressCode" required label="所在地区：" readonly input-align="right"></VanField>
        <VanField disabled v-model="form.address" required label="详细地址：" maxlength="100" input-align="right"></VanField>
      </div>
      <!-- <div class="text-gray-400 mt-2" style="font-size: .22rem;width: 80%;">请注意：地址填写简略、手机号填写错误皆会影响派单，导致您无法收到商品！（超过1小时未填写收货地址信息，视为放弃）</div> -->
      <!-- <div class="save-btn" @click="checkForm">提交</div> -->
    </div>
    <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
      <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { reactive, ref, computed, PropType, onMounted } from 'vue';
import { areaList } from '@vant/area-data';
import { FormType } from '../ts/type';
import { httpRequest } from '@/utils/service';
import CommonDrawer from '@/components/CommonDrawer/index.vue';

const props = defineProps({
  userReceiveRecordId: {
    type: String,
    required: true,
  },
  activityPrizeId: {
    type: String,
    required: true,
  },
  echoData: {
    type: Object as PropType<any>,
    default: () => ({
      realName: '',
      mobile: '',
      province: '',
      city: '',
      county: '',
      address: '',
    }),
  },
});

const emits = defineEmits(['close']);
const close = (): void => {
  emits('close');
};
const addressSelects = ref(false);

const form: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

onMounted(() => {
  // 回显地址
  Object.keys(form).forEach((key: string) => {
    form[key] = props.echoData[key];
  });
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});
// const addressCode = ref('');

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/99202/userAddressInfo', {
      userReceiveRecordId: props.userReceiveRecordId,
      activityPrizeId: props.activityPrizeId,
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的电话');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};
</script>

<style scoped lang='scss'>

.address-popup {
  width: 6.27rem;
  height: 8.5rem;
  position: relative;
  padding-top: 1rem;

  .address-content {
    width: 6.27rem;
    height: 7.26rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/238025/17/28324/7161/67307e5eFf28bc5dc/62e74ba4dc8857a1.png);
    background-repeat: no-repeat;
    background-size: 100%;
    padding-top: 1.6rem;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .close {
    width: .4rem;
    height: .4rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/156491/25/50723/319/672acd28Fbda234d2/2910e6697479f05f.png");
      repeat: no-repeat;
      size: contain;
    };
    position: absolute;
    top: .3rem;
    right: 0;
  }

  .save-btn {
    width: 3.6rem;
    height: 0.6rem;
    line-height: 0.6rem;
    text-align: center;
    color: white;
    font-size: 0.32rem;
    border-radius: 0.3rem;
    margin-top: 1rem;
    background-color: #CA8970;
  }
}

</style>
