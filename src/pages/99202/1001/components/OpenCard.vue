<template>
  <div class='box' @click="close">
    <div class='close'></div>
    <div class='rule-bk'>
      <div class="btn" @click="openCard"></div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { BaseInfo } from '@/types/BaseInfo';
import { inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;
const emits = defineEmits(['close']);
const close = (): void => {
  emits('close');
};
const openCard = () => {
  const returnUrl = encodeURIComponent(window.location.href);
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${returnUrl}&isJoin=1`;
};
</script>

<style scoped lang='scss'>

.box {
  width: 6.27rem;
  height: 8.5rem;
  position: relative;

  .rule-bk {
    width: 6.27rem;
    height: 7.26rem;
    position: absolute;
    top: 1rem;
    left: 0;
    padding-top: 1.4rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/183117/25/52972/91404/673f1916F33905302/abb33f6c2bded687.png");
      repeat: no-repeat;
      size: contain;
    };
    .btn{
      width: 3.6rem;
      height: .6rem;
      position: absolute;
      right: 1.3rem;
      bottom: 2.7rem;
    }
  }

  .close {
    width: .4rem;
    height: .4rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/156491/25/50723/319/672acd28Fbda234d2/2910e6697479f05f.png");
      repeat: no-repeat;
      size: contain;
    };
    position: absolute;
    top: .3rem;
    right: 0;
  }

  .content {
    height: 5rem;
    border: 0.3rem solid transparent;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
  }
}

</style>
