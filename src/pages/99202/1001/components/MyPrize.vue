<template>
  <div class='box'>
    <div class='close' @click='close'></div>
    <div class='box-bk'>
      <div class='content'>
        <ul>
          <li>日期</li>
          <li>奖品</li>
          <li>状态</li>
        </ul>
        <div class='prize-view' v-if='prizes.length'>
          <ul v-for='(item, index) in prizes' :key='index'>
            <li>{{ dayjs(item.createTime).format('YYYY.MM.DD') }}</li>
            <li>{{ item.prizeName }}</li>
            <!-- <li v-if='item.prizeType == "3" && item.nickName !== ""'>已发货 ></li> -->
            <li @click='handlePrize(item)'>{{ getPrizeType(item) }}</li>
          </ul>
        </div>

        <div v-else class='no-data'>暂无获奖记录哦~</div>
      </div>
    </div>
  </div>

  <!-- <VanPopup teleport='body' v-model:show='showSaveAddress' position='center'>
    <SaveAddress v-if='showSaveAddress' :userReceiveRecordId="userReceiveRecordId" :activityPrizeId="activityPrizeId" :echoData='echoData' @close='closeSaveAddress'></SaveAddress>
  </VanPopup>
  <VanPopup teleport='body' v-model:show='showViewAddress' position='center'>
    <ViewAddress v-if='showViewAddress' :userReceiveRecordId="userReceiveRecordId" :activityPrizeId="activityPrizeId" :echoData='echoData' @close='closeViewAddress'></ViewAddress>
  </VanPopup> -->
</template>

<script lang='ts' setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
// import SaveAddress from './SaveAddress.vue';
// import ViewAddress from './ViewAddress.vue';
import { httpRequest } from '@/utils/service';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import Clipboard from 'clipboard';

const emits = defineEmits(['close', 'saveAddress', 'viewAddress']);
const close = (): void => {
  emits('close');
};
interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  dealStatus: number;
  realName: string;
  deliverName: string;
  deliverNo: string;
  id: string;
}

const prizes = reactive([] as Prize[]);

const getPrizeType = (prize: Prize) => {
  switch (prize.prizeType) {
    case 3:
      if (prize.realName === '' || prize.realName === null) {
        return '填写地址 >';
      }
      return '查看地址 >';
    case 9:
    case 10:
      return '立即兑换';
    default:
      return '已发放';
  }
};

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/99202/getReceive');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
getUserPrizes();
const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制快递单号成功');
  })
  .on('error', () => {
    showToast('复制快递单号失败');
  });

const showSaveAddress = ref(false);
const showViewAddress = ref(false);
const userReceiveRecordId = ref('');
const addressId = ref('');
const activityPrizeId = ref('');
const userPrizeId = ref('');
const echoData = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 修改地址
const changAddress = (item: any) => {
  userReceiveRecordId.value = item.userReceiveRecordId;
  activityPrizeId.value = item.userReceiveRecordId;
  // Object.keys(echoData).forEach((key) => {
  //   echoData[key] = item[key];
  // });
  if (item.realName === '' || item.realName === null) {
    close();
    emits('saveAddress', userReceiveRecordId.value, userReceiveRecordId.value);
    // showSaveAddress.value = true;
  } else {
    close();
    emits('viewAddress', userReceiveRecordId.value, item);
    // showViewAddress.value = true;
  }
};
// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};
// 关闭收货地址
const closeViewAddress = (type: boolean | undefined) => {
  showViewAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};
const handlePrize = (prize: Prize) => {
  if (prize.prizeType === 3) {
    changAddress(prize);
  }
};

</script>

<style scoped lang='scss'>
.box {
  width: 6rem;
  height: 8rem;
  position: relative;

  .box-bk {
    width: 6rem;
    height: 7rem;
    position: absolute;
    top: 1rem;
    left: 0;
    padding-top: 1.4rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/162595/29/51512/5779/672c5d73F6e5a3099/d4f85cb79a13db81.png");
      repeat: no-repeat;
      size: contain;
    };
  }

  .close {
    width: .4rem;
    height: .4rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/156491/25/50723/319/672acd28Fbda234d2/2910e6697479f05f.png");
      repeat: no-repeat;
      size: contain;
    };
    position: absolute;
    top: .3rem;
    right: 0;
  }

  .content {
    height: 5.5rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    ul {
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      margin-bottom: .15rem;
      line-height: .5rem;

      li {
        font-size: .22rem;
        color: #7C7C7C;
        text-align: center;
        width: 33%;
      }
    }

    .prize-view {
      height: 4rem;
      overflow-y: auto;
    }

    .no-data {
      text-align: center;
      line-height: 4.2rem;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
  }
}

.deliver-dialog {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 0.2rem 0.8rem;
  width: 5.88rem;
  height: 2.76rem;
  background-color: #fff;
  border-radius: 0.2rem;
  align-items: center;

  .deliver-btn {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/249881/20/551/18047/65894308F93be030b/8a3bbbb31e4294cc.png') no-repeat;
    background-size: 100% 100%;
    width: 4.5rem;
    height: 0.76rem;
  }
}

.right1btn {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/236543/30/8550/5618/6585328aF1f8cc84b/dd057198e10bd3ef.png) no-repeat;
  background-size: 100% 100%;
  width: 1.2rem;
  height: 0.44rem;
  margin-left: 0.3rem;
  text-align: center;
  line-height: 0.44rem;
  color: #ffffff;
  font-size: 0.24rem;
}

</style>
