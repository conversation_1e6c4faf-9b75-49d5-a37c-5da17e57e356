<template>
  <div class="buySkuDivAll">
    <div class="closeDiv" @click="closeClick()"></div>
    <div class="skuListDiv">
      <div class="skuItemDiv" v-for="(item, index) in skuListData" :key="index">
        <div class="skuMainPictureDiv">
          <img :src="item.skuMainPicture" alt=""></img>
        </div>
        <div class="skuNameDiv">{{item.skuName}}</div>
        <div class="btnDivAll">
          <div class="btnDiv" @click="goBuSkuClick(item)">点击下单</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { isPreview } from "@/utils";
import { gotoSkuPage } from "@/utils/platforms/jump";
import { httpRequest } from "@/utils/service";
import dayjs from "dayjs";
import { closeToast, showLoadingToast, showToast } from "vant";
import { inject, onMounted, onUnmounted, ref } from "vue";
import { BaseInfo } from '@/types/BaseInfo';

const props = defineProps(['itemTaskData']);
const baseInfo = inject('baseInfo') as BaseInfo;
const skuListData = ref<any[]>([]);
const emits = defineEmits(["close"]);
const closeClick = () => {
  emits("close", null);
};
const getSkuListData = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post("/96012/getTaskAddSku");
    // const res = {
    //   data: [
    //     {
    //       skuId: "10097246839573",
    //       skuMainPicture: "//img10.360buyimg.com/imgzone/jfs/t1/299839/3/18149/102195/690306e2F0e1e5587/c9df023271b44363.jpg",
    //       isOperated: false,
    //       jdPrice: 0,
    //       skuName: "测试1测试1测试1测试1测试1测试1测试1测试1测试1测试1测试1",
    //     },
    //     {
    //       skuId: "10124918367321",
    //       skuMainPicture: "//img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png",
    //       isOperated: false,
    //       jdPrice: 0,
    //       skuName: "测试1",
    //     },
    //     {
    //       skuId: "10113475064168",
    //       skuMainPicture: "//img10.360buyimg.com/imgzone/jfs/t1/224067/12/35156/30740/68fb26e7Ff81ffbf6/9ac3a7d1875871f4.png",
    //       isOperated: false,
    //       jdPrice: 0,
    //       skuName: "测试1",
    //     },
    //   ],
    // };
    console.log(res, "浏览商品列表==========");
    closeToast();
    skuListData.value = res.data;
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
const goBuSkuClick = (itemData:any) => {
  gotoSkuPage(itemData.skuId);
}
const initData = () => {
  if (!isPreview) {
    getSkuListData();
  }
};

initData();
</script>
<style lang="scss" scoped>
.buySkuDivAll {
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/359213/7/3652/7699/690aac60F816dd40c/7a79c8929e0e9be6.png);
  background-size: cover;
  width: 7.5rem;
  height: 9.08rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.42rem;
  .closeDiv {
    position: absolute;
    right: 0.38rem;
    top: 0.44rem;
    width: 0.3rem;
    height: 0.3rem;
    // background-color: red;
  }
  .skuListDiv {
    height: 7.4rem;
    overflow-y: scroll;
    overflow-x: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 6.9rem;
    margin-left: 50%;
    transform: translateX(-50%);
    .skuItemDiv {
      width: 2.2rem;
      border-radius: 0.15rem;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      height: 3.6rem;
      position: relative;
      .skuMainPictureDiv{
        // width: 2.0rem;
        height: 2.0rem;
        margin-top: 0.02rem;
        width: 100%;
        display: flex;
        justify-content: center;
        // border: 0.02rem solid #000;
        box-sizing: border-box;
        img{
          width: 2rem;
          height: 2rem;
          object-fit: contain;
        }
      }
      .skuNameDiv{
        margin: 0.1rem 0.2rem 0 0.2rem;
        font-size: 0.24rem;
        color: #000;
        display: -webkit-box;        /* 必须结合 -webkit-box 布局 */
        -webkit-box-orient: vertical; /* 设置垂直方向排列 */
        -webkit-line-clamp: 2;       /* 限制显示行数为2行 */
        overflow: hidden;           /* 隐藏超出内容 */
        text-overflow: ellipsis;    /* 显示省略号 */
        text-align: center;
      }
      .btnDivAll{
        position: absolute;
        bottom: 0.19rem;
        margin-top: 0.15rem;
        width: 100%;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        .btnDiv{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 1.4rem;
          height: 0.5rem;
          border-radius: 0.4rem;
          color: #fff;
          font-size: 0.24rem;
          // padding: 0.2rem 0.3rem;
          background-color: #000;
        }
        .btnGrayDiv{
          width: 1.4rem;
          height: 0.5rem;
          border-radius: 0.4rem;
          color: #fff;
          font-size: 0.24rem;
          // padding: 0.2rem 0.3rem;
          background-color: gray;
        }
      }
    }
  }
}
</style>
