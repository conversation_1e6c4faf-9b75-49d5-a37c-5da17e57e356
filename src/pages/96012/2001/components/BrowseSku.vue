<template>
  <div class="browseSkuDivAll">
    <div class="closeDiv" @click="closeClick()"></div>
    <div class="skuListDiv">
      <div class="skuItemDiv" v-for="(item, index) in skuListData" :key="index">
        <div class="skuMainPictureDiv">
          <img :src="item.skuMainPicture" alt=""></img>
        </div>
        <div class="skuNameDiv">{{item.skuName}}</div>
        <div class="btnDivAll">
          <div class="btnDiv" v-if="!item.isOperated" @click="goBrowseClick(item)">去浏览</div>
          <div class="btnGrayDiv" v-else>已浏览</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { isPreview } from "@/utils";
import { gotoSkuPage } from "@/utils/platforms/jump";
import { httpRequest } from "@/utils/service";
import dayjs from "dayjs";
import { closeToast, showLoadingToast, showToast } from "vant";
import { inject, onMounted, onUnmounted, ref } from "vue";
import { BaseInfo } from '@/types/BaseInfo';

const props = defineProps(['itemTaskData']);
const baseInfo = inject('baseInfo') as BaseInfo;
const skuListData = ref<any[]>([]);
const emits = defineEmits(["close"]);
const closeClick = () => {
  emits("close", null);
};
const getSkuListData = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post("/96012/browseSku/getSkuList");
    console.log(res, "浏览商品列表==========");
    closeToast();
    skuListData.value = res.data;
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
const goBrowseClick = (itemData:any) => {
  const oldTime = dayjs().valueOf();
  localStorage.setItem('doTask', JSON.stringify({ taskType: props.itemTaskData ? props.itemTaskData.taskType : 28, time: oldTime, skuId: props.itemTaskData.skuId, taskSeconds: Number(props.itemTaskData.taskSeconds) * 1000 }));
  gotoSkuPage(itemData.skuId);
}
const initData = () => {
  if (!isPreview) {
    getSkuListData();
  }
};
 initData();
// const reportTask = async (taskId: number, skuId = '') => {
//     const emitData = {
//       skuId: skuId
//     }
//     emits('browseSuccess', emitData);
// };

// 检查任务状态
// const initTask = async () => {
//   const task = window.localStorage.getItem('doTask');
//   if (!task) return;
//   window.localStorage.removeItem('doTask');
//   const newItem = JSON.parse(task);
//   const newTime: number = dayjs().valueOf(); // 当前时间戳
//   const oldTime: number = newItem.time; // 做任务的时间
//   const { taskType, skuId, taskSeconds } = newItem;
//   const num = ref(0); // 需要做任务满足时长
//   if (taskType === 28) {
//     // num.value = taskSeconds.value * 1000;
//     const downTime = newTime - oldTime >= taskSeconds;
//     if (downTime) {
//       reportTask(taskType, skuId);
//     } else {
//       showToast('浏览时间不足，不能获得奖励哦~');
//     }
//   }
// };
// const handleVisiable = async (e: any) => {
//   console.log(document.visibilityState,'browseSku如果缓存中存在flag再执行判断visibilityState=======');
//   // 如果缓存中存在flag再执行判断visibilityState
//   if (document.visibilityState !== 'visible') return;
//   await initTask();
// };
// onMounted(() => {
//   initData();
//   initTask();
//   document.addEventListener('visibilitychange', handleVisiable);
// });
// onUnmounted(() => {
//   document.removeEventListener('visibilitychange', handleVisiable);
// });
</script>
<style lang="scss" scoped>
.browseSkuDivAll {
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/344305/34/22320/8646/690aac61F733ed329/48181298c31dc62a.png);
  background-size: cover;
  width: 7.5rem;
  height: 9.08rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.42rem;
  .closeDiv {
    position: absolute;
    right: 0.38rem;
    top: 0.44rem;
    width: 0.3rem;
    height: 0.3rem;
    // background-color: red;
  }
  .skuListDiv {
    height: 7.4rem;
    overflow-y: scroll;
    overflow-x: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 6.9rem;
    margin-left: 50%;
    transform: translateX(-50%);
    .skuItemDiv {
      width: 2.2rem;
      border-radius: 0.15rem;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      height: 3.6rem;
      position: relative;
      .skuMainPictureDiv{
        // width: 2.0rem;
        height: 2.0rem;
        margin-top: 0.02rem;
        width: 100%;
        display: flex;
        justify-content: center;
        // border: 0.02rem solid #000;
        box-sizing: border-box;
        img{
          width: 2rem;
          height: 2rem;
          object-fit: contain;
        }
      }
      .skuNameDiv{
        margin: 0.1rem 0.2rem 0 0.2rem;
        font-size: 0.24rem;
        color: #000;
        display: -webkit-box;        /* 必须结合 -webkit-box 布局 */
        -webkit-box-orient: vertical; /* 设置垂直方向排列 */
        -webkit-line-clamp: 2;       /* 限制显示行数为2行 */
        overflow: hidden;           /* 隐藏超出内容 */
        text-overflow: ellipsis;    /* 显示省略号 */
        text-align: center;
      }
      .btnDivAll{
        position: absolute;
        bottom: 0.19rem;
        margin-top: 0.15rem;
        width: 100%;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        .btnDiv{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 1.4rem;
          height: 0.5rem;
          border-radius: 0.4rem;
          color: #fff;
          font-size: 0.24rem;
          // padding: 0.2rem 0.3rem;
          background-color: #000;
        }
        .btnGrayDiv{
          width: 1.4rem;
          height: 0.5rem;
          border-radius: 0.4rem;
          color: #fff;
          font-size: 0.24rem;
          // padding: 0.2rem 0.3rem;
          background-color: gray;
        }
      }
    }
  }
}
</style>
