<template>
  <div class="images-list">
    <div
      v-for="(column, columnIndex) in columns"
      :key="columnIndex"
      class="column"
      :class="columnClass(columnIndex)"
    >
      <div
        class="images-item"
        v-for="(item, index) in column"
        :key="index"
      >
        <div class="imgBox" @click="showImgDetail(item)">
          <img v-if="item.imgList[0].imgUrl" :src="item.imgList[0].imgUrl" alt="">
          <img v-else :src="item.imgList[0]" alt="">
          <img class="palyBtn" v-if="item.imgList[0].videoUrl" src="https://img10.360buyimg.com/imgzone/jfs/t1/331179/15/21235/2725/68e7c8f7F7840cef6/2e410c6677ce5029.png" alt="">
          <div class="rankNum" v-if="showRank">{{item.rank}}</div>
        </div>
        <div class="titleBox">{{item.title}}</div>
        <div class="contentBox">{{item.content}}</div>
        <div class="userInfoBox" v-if="showUserInfo">
          <div class="userInfo">
            <div class="avatar">
              <img :src="item.avatar" alt="">
            </div>
            <div class="nickName">{{item.nickName}}</div>
          </div>
          <div class="fansLikeBox">
            <img
              @click="likeThisPost(item)"
              class="like"
              :src="item.liked ? 'https://img10.360buyimg.com/imgzone/jfs/t1/345820/4/20072/571/69044b4eF37e1c8dd/0ed93cb5a7c0f514.png':'https://img10.360buyimg.com/imgzone/jfs/t1/344172/34/18852/1210/69044b4fF93ebc1d1/c1129ab8ca68f022.png'"
              alt=""
            >
            <div class="fans">{{item.fans}}</div>
          </div>
        </div>
        <!--修改分享以及发布时间区域-->
        <div v-if="showChangeShare">
          <div class="changeShareBtn">
            <div></div>
            <div></div>
          </div>
          <div class="createTime">发布时间：{{item.createTime}}</div>
        </div>
      </div>
    </div>
  </div>
  <VanPopup v-model:show="imgDetailPopup" position="center">
    <ShowImagesDetail v-if="imgDetailPopup" :imgDetail="imgDetail" @closePop="closeImgDetail"/>
  </VanPopup>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { showToast } from "vant";
import { httpRequest } from "@/utils/service";
import ShowImagesDetail from './ShowImagesDetail.vue';

// 定义图片项的类型
interface ImageObject {
  imgUrl: string;
}

interface ImageItem {
  imgList: (string | ImageObject)[];
  title: string;
  content: string;
  avatar: string;
  nickName: string;
  fans: number;
  liked: boolean;
  dynamicId: string;
  rank?: number;
  createTime?: string;
}

const props = defineProps(['imgList', 'showUserInfo', 'showRank', 'showChangeShare']);
const emits = defineEmits(['likeThisPost']);

// 计算左侧列的元素（奇数索引：0, 2, 4...）
const leftColumnItems = computed(() => {
  return props.imgList?.filter((item: ImageItem, index: number) => index % 2 === 0) || [];
});

// 计算右侧列的元素（偶数索引：1, 3, 5...）
const rightColumnItems = computed(() => {
  return props.imgList?.filter((item: ImageItem, index: number) => index % 2 === 1) || [];
});

// 创建列数组用于循环渲染
const columns = computed(() => {
  return [leftColumnItems.value, rightColumnItems.value];
});

// 计算列的类名
const columnClass = (index: number) => {
  return index === 0 ? 'left-column' : 'right-column';
};

// 点赞
const likeThisPost = async (item: ImageItem) => {
  try {
    await httpRequest.post('/96012/likeDynamic', {
      dynamicId: item.dynamicId,
    });
    item.liked = true;
    emits('likeThisPost', item)
  } catch (error) {
    showToast(error.message);
  }
};

const imgDetailPopup = ref(false);
const imgDetail = ref<ImageItem | null>(null);
// 显示图片详情
const showImgDetail = (item: ImageItem) => {
  imgDetail.value = item;
  imgDetailPopup.value = true;
};
const closeImgDetail = () => {
  imgDetailPopup.value = false;
};
</script>

<style scoped lang="scss">
.images-list{
  display: flex;
  gap: 0.1rem; // 列间距
  overflow: hidden;
  overflow-y: scroll;
  max-height: 11.4rem;
  &::-webkit-scrollbar {
    display: none;
  }

  .column {
    flex: 1; // 每列占据相等宽度

    .images-item{
      width: 100%;
      height: auto;
      background-color: #fff;
      padding: 0.2rem 0.1rem 0.14rem;
      box-sizing: border-box;
      margin-bottom: 0.2rem; /* 行间距 */
      border-radius: 0.2rem;

      &:last-child {
        margin-bottom: 0; /* 最后一个元素不需要下边距 */
      }

      .imgBox{
        position: relative;
        img{
          width: 100%;
          height: auto;
          border-radius: 0.2rem;
        }
        .palyBtn{
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 1.6rem;
          height: 1.6rem;
          z-index: 9;
        }
        .rankNum{
          position: absolute;
          top: -0.03rem;
          left: -0.03rem;
          background: url(https://img10.360buyimg.com/imgzone/jfs/t1/354654/16/1543/1166/68ff0914F9f0d777d/af84330388c65dd4.png) no-repeat;
          background-size: 100%;
          width: 0.94rem;
          height: 0.94rem;
          padding: 0.16rem 0.49rem 0 0.095rem;
          color: #dca45c;
          font-size: 0.2rem;
          font-weight: bold;
          text-align: center;
        }
      }

      .titleBox{
        font-size: 0.22rem;
        margin: 0.1rem 0;
      }
      .contentBox{
        font-size: 0.2rem;
        color: #666;
        line-height: 0.24rem;
        word-wrap: break-word;
        width: 3.2rem;
      }
      .userInfoBox{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 0.1rem;
        .userInfo{
          width: 2rem;
          align-items: center;
          display: flex;
          .avatar{
            width: 0.5rem;
            height: 0.5rem;
            overflow: hidden;
            border-radius: 50%;
            img{
              width: 0.5rem;
            }
          }
          .nickName{
            font-size: 0.2rem;
            margin-left: 0.1rem;
          }
        }
        .fansLikeBox{
          font-size: 0.2rem;
          margin-left: 0.1rem;
          display: flex;
          align-items: center;
          .like{
            width: 0.36rem;
            height: 0.3rem;
            margin-right: 0.05rem;
          }
          .fans{
            color: #666;
          }
        }
      }
      .changeShareBtn{
        display: flex;
        align-items: center;
        background: url(https://img10.360buyimg.com/imgzone/jfs/t1/343527/11/22212/3108/69085d75Fc76ba2c9/b97d3b56b84e0edf.png) no-repeat;
        background-size: 100%;
        width: 3rem;
        height: 0.68rem;
        margin-top: 0.09rem;
        div {
          flex: 1;
          width: 0.15rem;
          height: 0.68rem;
        }
      }
      .createTime{
        font-size: 0.18rem;
        color: #8f8f8f;
        margin-top: 0.15rem;
      }
    }
  }
}
</style>
