<template>
  <div class="bg">
    <div class="mineInfoBox" :style="furnishStyles.mineBg.value">
      <img class="mineAvatar" :src="mineInfo.avatar" alt="">
      <div class="nickName">{{mineInfo.nickName}}</div>
      <div class="progressLineBox">
        <div>我的人气值：</div>
        <div class="progress">
          <div class="bubble" :style="{ width: progressWidth }"/>
        </div>
        <div class="rate">{{mineInfo.fansTotal}}</div>
      </div>
    </div>
    <div class="mainTabArea">
      <div class="mainTabWrapper" v-for="(tab, index) in tabList" :key="index">
        <div class="mainTab" :style="index === activeMineTab ? 'color: #000' : 'color:#666'" @click="switchMineTab(index)">{{tab.title}}</div>
        <!-- 选中指示器 -->
        <div :class="index === activeMineTab ? 'activeTabIndicator' : 'tabIndicator'"/>
      </div>
    </div>
    <div v-if="activeMineTab === 0" class="mineMainBox">
      <div v-if="minePublish.length" class="showImgArea" >
        <ImagesList :imgList="minePublish" :showUserInfo="true" :showRank="false" :showChangeShare="true"/>
      </div>
      <div v-else class="noData">
        <div class="noDataTitle">你还未发布内容哦~</div>
        <div class="noDataBtn">点击发布</div>
      </div>
    </div>
    <div v-else class="mineMainBox">
      <div v-if="mineLiked.length" class="showImgArea">
        <ImagesList :imgList="mineLiked" :showUserInfo="true" :showRank="false" :showChangeShare="false"/>
      </div>
      <div v-else class="noData">
        <div class="noDataTitle">你还未点赞内容哦~</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import furnishStyles, { furnish } from '../ts/furnishStyles';
import {ref, onMounted, nextTick, computed, defineProps, inject} from 'vue';
import ImagesList from "../components/ImagesList.vue";
import {closeToast, showLoadingToast} from "vant";
import {checkActTime} from "../ts/logic";
import { httpRequest } from "@/utils/service";
import { BaseInfo } from "@/types/BaseInfo";

const props = defineProps(['mineInfo']);

// 进度条
const progressBar = ref(0);
const progressWidth = ref('');
progressBar.value = props.mineInfo.fansTotal / 100000;
progressWidth.value = `${progressBar.value * 100}%`;

const tabList = ref([
  {
    title: "我发布的",
  },
  {
    title: "我点赞的",
  },
]);
// 添加tab选中状态
const activeMineTab = ref(0);
// 主tab切换
const switchMineTab = (index: number) => {
  activeMineTab.value = index;
};

const mineLiked = ref([
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
    ],
    title: '标题1',
    content: '描述1',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户1',
    fans: 1000,
    createTime: '2025-11-20',
    liked: true,
    rank: 1,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题2',
    content: '描述2',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户2',
    fans: 10009,
    createTime: '2025-11-20',
    liked: true,
    rank: 2,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/291461/39/27351/11615/69046e4cF2e58da62/e0ba47c13aa9d89d.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题3',
    content: '描述3',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户3',
    fans: 1000,
    createTime: '2025-11-20',
    liked: true,
    rank: 3,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题4',
    content: '描述4',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户4',
    fans: 1000,
    createTime: '2025-11-20',
    liked: true,
    rank: 4,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题5',
    content: '描述5',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户5',
    fans: 1000,
    createTime: '2025-11-20',
    liked: true,
    rank: 5,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题6',
    content: '描述6',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户6',
    fans: 1000,
    createTime: '2025-11-20',
    liked: true,
    rank: 6,
  },
]);

const minePublish = ref([
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
    ],
    title: '标题1',
    content: '描述1',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户XXX',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
    rank: 1,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题2',
    content: '描述2',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户XXX',
    fans: 10009,
    createTime: '2025-11-20',
    liked: false,
    rank: 2,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/291461/39/27351/11615/69046e4cF2e58da62/e0ba47c13aa9d89d.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题3',
    content: '描述3',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户XXX',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
    rank: 3,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题4',
    content: '描述4',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户XXX',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
    rank: 4,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题5',
    content: '描述5',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户XXX',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
    rank: 5,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题6',
    content: '描述6',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户XXX',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
    rank: 6,
  },
]);
</script>

<style scoped lang="scss">
.bg {
  width: 7.5rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding: 0 0 3rem 0;
  .mineInfoBox{
    width: 7.5rem;
    height: 5.19rem;
    background-size: 100%;
    background-repeat: no-repeat;
    padding: 1.12rem 0.5rem 0;
    .mineAvatar{
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
      margin: 0 auto;
    }
    .nickName{
      font-size: 0.36rem;
      color: #000000;
      margin: 0.34rem auto 0;
      text-align: center;
    }
    .progressLineBox {
      display: flex;
      margin: 0 auto;
      width: 6rem;
      position: relative;
      top: 0.6rem;
      justify-content: center;
      font-size: 0.24rem;
      color: #666666;
      .progress {
        background: url(https://img10.360buyimg.com/imgzone/jfs/t1/349283/21/20840/344/69085083Ff1bca5cf/9edd8ffa50d547db.png) no-repeat;
        background-size: 100%;
        width: 3rem;
        height: 0.15rem;
        position: relative;
        border-radius: 0.08rem;
        overflow: hidden;
        transform: translateY(25%);
        .bubble {
          margin-top: 0.01rem;
          height: 0.15rem;
          position: relative;
          border-radius: 0.08rem;
          background-color: #415fff;
        }
      }
      .rate {
        text-align: right;
        font-size: 0.24rem;
        margin: 0 0 0 0.1rem;
        color: #415fff;
      }
    }
  }
  .mainTabArea{
    width: 6.9rem;
    height: 0.6rem;
    margin: 0.3rem auto 0.2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    overflow-x: scroll;
    background-color: #fff;
    border-radius: 0.24rem;
    font-size: 0.24rem;
    padding: 0.1rem 0 0 0;
    &::-webkit-scrollbar {
      display: none;
    }

    .mainTabWrapper {
      display: flex;
      flex-direction: column;
      align-items: center;

      .mainTab {
        width: 3.45rem;
        height: 0.4rem;
        line-height: 0.4rem;
        text-align: center;
        font-weight: bold;
        cursor: pointer;
      }

      .tabIndicator {
        width: 0.53rem;
        height: 0.06rem;
        margin-top: 0.05rem;
        border-radius: 0.03rem;
        background-color: transparent;
      }
      .activeTabIndicator {
        width: 0.53rem;
        height: 0.06rem;
        margin-top: 0.05rem;
        border-radius: 0.03rem;
        background-color: #415fff;
      }
    }
  }
  .mineMainBox{
    width: 7.5rem;
    .showImgArea{
      width: 7.1rem;
      margin: 0 auto;
      padding: 0 0 0.73rem;
    }
    .noData{
      width: 7.1rem;
      margin: 0 auto;
      height: 4rem;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .noDataTitle{
        font-size: 0.24rem;
        color: #666666;
        margin: 0.5rem auto 0.3rem;
      }
      .noDataBtn {
        line-height: 0.6rem;
        text-align: center;
        margin: 0 auto;
        color: #fff;
        width: 3.08rem;
        height: 0.88rem;
        background-color: #000000;
        font-size: 0.24rem;
        line-height: 0.88rem;
        border-radius: 0.44rem;
      }
    }
  }
}
</style>
