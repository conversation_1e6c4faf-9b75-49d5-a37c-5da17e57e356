<template>
  <div class="uploadBg" :style="furnishStyles.pageBg.value">
    <div class="backButton" @click="goBack">< 返回</div>
    <div class="inputBox">
      <div class="imagesBox">
        <!-- 图片预览区域 -->
        <div class="imagePreviewContainer">
          <div
            v-for="(image, index) in previewImages"
            :key="index"
            class="imagePreviewItem"
          >
            <img class="previewImage" :src="image.url" alt="预览图片" />
            <img class="removeButton" @click="removeImage(index)" src="https://img10.360buyimg.com/imgzone/jfs/t1/355161/17/3346/988/6909affaF3309e575/967a330d6bc2a4b4.png" alt=""/>
          </div>

          <!-- 添加图片按钮 -->
          <div
            v-if="previewImages.length < 9"
            class="addImageButton"
            @click="triggerFileInput"
          >
            <div class="plusIcon">+</div>
            <div class="imageCount">{{ previewImages.length }}/9</div>
          </div>
        </div>

        <!-- 隐藏的文件输入框 -->
        <input
          ref="fileInput"
          type="file"
          accept="image/*"
          multiple
          class="hidden-input"
          @change="handleFileChange"
        />
      </div>
      <input class="inputTitle" type="text" maxlength="20" name="title" placeholder="点击添加标题" />
      <textarea class="inputContent" maxlength="200" name="content" placeholder="点击添加正文"></textarea>
    </div>

    <div class="chooseTitleBox">
      <van-field v-model="mainTitleValue" is-link readonly @click="showMainTitle = true" placeholder="选择主分区" />
      <van-field v-model="secondaryTitleValue" is-link readonly @click="showSecondaryTitle = true" placeholder="选择分区"/>
    </div>

    <div class="imgBox">
      <img :src="furnish.uploadPageTextImg" alt="">
    </div>

    <div class="publishBtn" @click="uploadImages">发布照片</div>

    <VanPopup teleport="body" v-model:show="showMainTitle" position="bottom">
      <van-picker
        title="选择主分区"
        :columns="mainColumns"
        @confirm="onMainConfirm"
        @cancel="onCancel"
        @change="onMainChange"
      />
    </VanPopup>

    <VanPopup teleport="body" v-model:show="showSecondaryTitle" position="bottom">
      <van-picker
        title="选择分区"
        :columns="secondaryColumns"
        @confirm="onSecondaryConfirm"
        @cancel="onCancel"
        @change="onSecondaryChange"
      />
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted} from "vue";
import furnishStyles, { furnish } from "../ts/furnishStyles";
import { httpRequest } from "@/utils/service";
import { showToast } from "vant";

const emits = defineEmits(['goBack']);

// 添加返回方法
const goBack = () => {
  emits('goBack');
};

// 弹窗显示控制
const showMainTitle = ref(false);
const showSecondaryTitle = ref(false);

// 显示值
const mainTitleValue = ref('');
const secondaryTitleValue = ref('');

// 选择器数据
const mainColumns = ref<{text: string, value: number}[]>([]);
const secondaryColumns = ref<{text: string, value: number}[]>([]);

// 接口数据存储
const sectionData = ref<SectionInfo[]>([]);

// 当前选中的主分区ID
const selectedMainId = ref<number | null>(null);

// 图片预览数组
const previewImages = ref<{file: File, url: string}[]>([]);

// 定义接口返回的数据结构类型
interface ChildSection {
  childSectionId: number;
  childSectionName: string;
}

interface SectionInfo {
  childSectionList: ChildSection[];
  fatherSectionId: number;
  fatherSectionName: string;
}

// 获取分区信息
const getSectionInfo = async () => {
  try {
    const { data } = await httpRequest.post('/96012/getSectionInfo');
    sectionData.value = data;

    // 设置主分区列数据
    mainColumns.value = data.map((item: SectionInfo) => ({
      text: item.fatherSectionName,
      value: item.fatherSectionId
    }));
  } catch (error) {
    console.error(error);
    showToast('获取分区信息失败');
  }
};

// 主分区确认选择
const onMainConfirm = ({ selectedOptions }: { selectedOptions: any[] }) => {
  showMainTitle.value = false;
  if (selectedOptions && selectedOptions.length > 0) {
    const selected = selectedOptions[0];
    mainTitleValue.value = selected.text;
    selectedMainId.value = selected.value;

    // 根据选中的主分区更新子分区数据
    const selectedSection = sectionData.value.find((item: SectionInfo) => item.fatherSectionId === selected.value);
    if (selectedSection && selectedSection.childSectionList) {
      secondaryColumns.value = selectedSection.childSectionList.map((child: ChildSection) => ({
        text: child.childSectionName,
        value: child.childSectionId
      }));
    }

    // 清空之前选中的子分区
    secondaryTitleValue.value = '';
  }
};

// 子分区确认选择
const onSecondaryConfirm = ({ selectedOptions }: { selectedOptions: any[] }) => {
  showSecondaryTitle.value = false;
  if (selectedOptions && selectedOptions.length > 0) {
    secondaryTitleValue.value = selectedOptions[0].text;
  }
};

// 取消选择
const onCancel = () => {
  showMainTitle.value = false;
  showSecondaryTitle.value = false;
};

// 主分区变化（用于联动效果）
const onMainChange = (val: any) => {
  // 可以在这里处理主分区变化的逻辑
};

// 子分区变化
const onSecondaryChange = (val: any) => {
  // 可以在这里处理子分区变化的逻辑
};

const fileInput = ref<HTMLInputElement | null>(null);
// 触发文件选择
const triggerFileInput = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

// 删除图片
const removeImage = (index: number) => {
  previewImages.value.splice(index, 1);
};

// 处理文件选择
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;

  if (!files || files.length === 0) return;

  // 检查总图片数量是否超过9张
  if (previewImages.value.length + files.length > 9) {
    showToast(`最多只能上传9张图片，当前还能上传${9 - previewImages.value.length}张`);
    target.value = ''; // 清空input值
    return;
  }

  // 验证文件类型并处理每张图片
  for (let i = 0; i < files.length; i++) {
    const file = files[i];

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      showToast('请选择图片文件');
      continue;
    }

    // 读取文件并生成预览URL
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target && e.target.result) {
        previewImages.value.push({
          file: file,
          url: e.target.result as string
        });
      }
    };
    reader.readAsDataURL(file);
  }

  // 清空input值，以便下次选择相同文件也能触发change事件
  target.value = '';
};

// 上传图片到后端
const uploadImages = async () => {
  if (previewImages.value.length === 0) {
    showToast('请至少选择一张图片');
    return;
  }

  // 创建FormData对象
  const formData = new FormData();

  // 添加图片文件到FormData
  previewImages.value.forEach((image, index) => {
    formData.append('images', image.file);
  });

  // 添加其他表单数据（如果需要）
  // formData.append('title', titleValue.value);
  // formData.append('content', contentValue.value);
  // formData.append('mainSectionId', selectedMainId.value);
  // formData.append('secondarySectionId', selectedSecondaryId.value);

  try {
    // 发送上传请求
    const response = await httpRequest.post('/96012/upLoadImg', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    if (response.data.success) {
      showToast('图片上传成功');
      // 清空已上传的图片
      previewImages.value = [];
    } else {
      showToast('图片上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    showToast('图片上传失败');
  }
};

// 组件挂载时获取分区数据
onMounted(() => {
  getSectionInfo();
});
</script>

<style scoped lang="scss">
.uploadBg{
  width: 7.5rem;
  min-height: 100vh;

  /* 添加返回按钮样式 */
  .backButton {
    color: #666;
    font-size: 0.24rem;
    width: 7.5rem;
    padding: 0.2rem 0.3rem;
  }

  .inputBox{
    background: #fff;
    width: 6.9rem;
    padding: 0.3rem;
    border-radius: 0.3rem;
    margin: 0 auto 0.3rem;
    .imagesBox{
      margin-bottom: 0.3rem;
      .hidden-input {
        display: none;
      }
      .imagePreviewContainer {
        display: flex;
        flex-wrap: wrap;
        gap: 0.1rem;

        .imagePreviewItem {
          position: relative;
          width: 2rem;
          height: 2rem;

          .previewImage {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 0.1rem;
          }

          .removeButton {
            position: absolute;
            top: 0;
            right: 0;
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 0 0.15rem 0 0;
          }
        }

        .addImageButton {
          width: 2rem;
          height: 2rem;
          border: 1px dashed #ccc;
          border-radius: 0.1rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          .plusIcon {
            font-size: 0.4rem;
            color: #999;
          }

          .imageCount {
            font-size: 0.2rem;
            color: #999;
            margin-top: 0.05rem;
          }
        }
      }
    }
    .inputTitle{
      width: 100%;
      border: none;
      font-size: 0.24rem;
      border-bottom: 1px #ccc dashed;
      padding-bottom: 0.3rem;
    }
    .inputContent{
      width: 100%;
      min-height: 1.24rem;
      border: none;
      font-size: 0.24rem;
      resize: vertical; // 允许垂直调整大小
      overflow-y: auto; // 垂直滚动
      font-family: inherit; // 使用继承的字体
      padding: 0.2rem 0 0;
    }
  }
  .chooseTitleBox{
    background: #fff;
    width: 6.9rem;
    margin: 0 auto;
    border-radius: 0.3rem;
    overflow: hidden;
    font-size: 0.24rem;
  }
  .imgBox{
    width: 6.85rem;
    margin: 0.3rem auto 0;
    img{
      width: 100%;
    }
  }
  .publishBtn{
    width: 6.9rem;
    height: 1rem;
    background-color: #000000;
    border-radius: 0.5rem;
    margin: 1rem auto 0;
    color: #fff;
    text-align: center;
    line-height: 1rem;
    font-size: 0.3rem;
  }
}
:deep(.van-icon-arrow:before) {
  font-size: 0.24rem !important;
}
:deep(.van-field__control) {
  font-size: 0.24rem !important;
}
:deep(.van-ellipsis) {
  font-size: 0.24rem !important;
}
:deep(.van-picker__confirm) {
  font-size: 0.24rem !important;
}
:deep(.van-picker__cancel) {
  font-size: 0.24rem !important;
}
</style>
