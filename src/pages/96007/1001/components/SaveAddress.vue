<template>
  <div class="rule-bk">
    <div class="content">
      <div class="form">
        <VanField :readonly="isLookEd" v-model="form.realName" placeholder="请输入收货人" required label="收货人：" maxlength="20"></VanField>
        <VanField :readonly="isLookEd" v-model="form.mobile" placeholder="请输入手机号" required label="手机号：" maxlength="11" type="number"></VanField>
        <VanField  v-model="addressCodeProvince" placeholder="请选择>" required label="所在省：" readonly @click="addressSelects = !isLookEd"></VanField>
        <VanField  v-model="addressCodeCity" placeholder="请选择>" required label="所在市：" readonly @click="addressSelects = !isLookEd"></VanField>
        <VanField  v-model="addressCodeCounty" placeholder="请选择>" required label="所在区：" readonly @click="addressSelects = !isLookEd"></VanField>
        <VanField :readonly="isLookEd" placeholder="请输入详细地址" v-model="form.address" required label="详细地址：" maxlength="100"></VanField>
      </div>
      <div v-if="!isLookEd" class="submit" @click="checkForm"/>
    </div>
  </div>
  <div class="close" @click="close"/>

  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { reactive, ref, computed, onMounted, inject } from 'vue';
import { areaList } from '@vant/area-data';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  userPrizeId: {
    type: String,
    required: true,
  },
  addressId: {
    type: String,
    required: true,
  },
  echoData: {
    type: Object,
    default: () => ({
      realName: '',
      mobile: '',
      province: '',
      city: '',
      county: '',
      address: '',
    }),
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const addressSelects = ref(false);

const form = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
  addressId: '',
});
const isLookEd = ref(false);
const addressCodeProvince = computed(() => {
  if (form.province) {
    return `${form.province}`;
  }
  return '';
});

const addressCodeCity = computed(() => {
  if (form.city) {
    return `${form.city}`;
  }
  return '';
});

const addressCodeCounty = computed(() => {
  if (form.county) {
    return `${form.county}`;
  }
  return '';
});

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  if (baseInfo.status === 3) {
    showToast('活动已结束');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/96007/userAddressInfo', {
      addressId: props.addressId,
      userPrizeId: props.userPrizeId,
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (isLookEd.value) {
    showToast('已提交过地址，不可修改');
    return;
  }
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入手机号');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的手机号');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};
onMounted(() => {
  // 回显地址
  Object.keys(form).forEach((key: string) => {
    form[key] = props.echoData[key];
  });
  if (props.echoData.realName) {
    isLookEd.value = true;
  }
});
</script>

<style scoped lang="scss">
.rule-bk {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.5rem;
  height: 8.92rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/286734/5/12830/19525/6847cffbF7d7c0631/3841707067c93375.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 1.5rem 0 0 0;
  display: flex;
  align-items: center;

  .content {
    height: 6.6rem;
    width: 6rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #002d81;
    white-space: pre-wrap;
    cursor: pointer;
    margin: 0 auto;
    border-radius: 0.2rem;
    /*background-color: #000;*/
    padding: 0 0.4rem;

    .form {
      .van-cell {
        border-radius: 0.08rem;
        margin-bottom: 0.1rem;

        &::after {
          display: none;
        }
      }
    }

    .tip {
      font-size: 0.18rem;
      color: #b3b3b3;
    }

    .submit {
      text-align: center;
      width: 2.55rem;
      height: 0.63rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/305534/22/9583/5604/6847d0d5Fd694f18b/9681ec0e5a47b898.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.7rem auto 0;
    }
    .tips{
      font-size: 0.2rem;
      margin: 0 auto;
      text-align: center;
    }
  }
}
.close {
  width: 0.6rem;
  /* background: #000; */
  height: 0.6rem;
  margin: 0.6rem auto 0;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/310192/5/8413/1274/6847c989F1f4b5203/48aff4a323b38052.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
<style lang="scss" scoped>
::v-deep .van-cell {
  background: rgba(255, 255, 255, 0);
  font-size: 0.24rem;
  margin: 0.2rem 0 0;
  padding: 0;
  height: 0.5rem;
  line-height: 0.5rem;
}
::v-deep .van-field__value {
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/312234/40/8499/819/6847d375F4cf98168/3d3da43580835401.png') no-repeat;
  background-size: 100% 100%;
  padding: 0 0 0 0.2rem;
}

::v-deep .van-field__label{
  color: #002d81;
}

::v-deep .van-field__control::placeholder {
  color: #fff !important;
}
::v-deep .van-field__control{
  color: #002d81;
}
</style>
