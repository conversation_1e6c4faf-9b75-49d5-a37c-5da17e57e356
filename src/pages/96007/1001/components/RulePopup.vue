<template>
  <div class="rule-bk">
    <div class="content" v-html="rule"></div>
  </div>
  <div class="close" @click="close"/>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.5rem;
  height: 8.92rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/288362/24/14316/19917/6847c988Feed8cdcb/836c52ef0d35806a.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 1.5rem 0 0 0;

  .content {
    height: 6.8rem;
    width: 5.4rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #002d81;
    white-space: pre-wrap;
    cursor: pointer;
    margin: 0 auto;
    border-radius: 0.2rem;
  }
}
.close {
  width: 0.6rem;
  /* background: #000; */
  height: 0.6rem;
  margin: 0.6rem auto 0;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/310192/5/8413/1274/6847c989F1f4b5203/48aff4a323b38052.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
