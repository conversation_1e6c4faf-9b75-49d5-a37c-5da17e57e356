import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

// const _decoData = {
//   pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/119441/11/44972/904176/661d1f29F44833198/7a0bd8215d3fe094.png',
//   actBg: '',
//   actBgColor: '',
//   ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/108757/40/44301/3168/661d1f29F4ed1fa7a/c5fa2c61f2071010.png',
//   successPageBg: '',
//   noPrizePop: '',
//   noPrizePopLink: '',
//   canNotPop: '',
//   canNotPopLink: '',
//   rulePop: '',
//   cmdImg: '',
//   h5Img: '',
//   mpImg: '',
// };

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员二次回购礼';
  console.log(document.title, 'activityData======');
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
