<template>
  <div class="rule-bk">
    <div class="bg">
      <div class="content">
        <div v-html="rule"/>
      </div>
      <div class="backBtn" @click="close"/>
    </div>
  </div>
</template>

<script lang="ts" setup>

const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>
<style>
@font-face {
  font-family: 'FZLTHJW';
  src: url("../font/fzlthjwgb10.TTF") format('TTF');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'FZLTHJW';
}
</style>
<style scoped lang="scss">
.rule-bk {
  width: 100vw;
  margin: 0;
  padding: 0;
  .bg{
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/72912/40/25372/193621/669dbd13F05d1ea45/bbca9253c29cbde7.png);
    background-repeat: no-repeat;
    background-size: 100%;
    min-height: 100vh;
    margin: 0 auto;
    padding: 4.2rem 0 0;
    .content {
      width: 6.89rem;
      height: 6.6rem;
      margin: 0.3rem auto;
      padding: 0 0.4rem;
      font-size: 0.24rem;
      white-space: pre-wrap;
      overflow-y: scroll;
      word-wrap: break-word;
    }
    .backBtn{
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/243593/3/15090/6283/669a2339Fbea6ce8f/837b05430d7d5b20.png);
      background-repeat: no-repeat;
      background-size: 100%;
      width: 1.91rem;
      height: 0.58rem;
      margin: 0 auto;
    }
  }
}
.van-popup--center {
  max-width: 100%;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
