<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <img :src="prize.showImg" alt="" class="prize-img" />
    <div class="success">
      <img class="textImg" src="https://img10.360buyimg.com/imgzone/jfs/t1/336522/36/13637/11885/68d0e86eF080d2423/cd02dd86d8cb54ec.png" alt="">
      <img v-if="prize.prizeType === 3" class="addressTextImg" src="https://img10.360buyimg.com/imgzone/jfs/t1/336878/1/13578/8296/68d0e8cdF0c69401b/919a92cab42b09b3.png" alt="">
      <div class="btn">
        <img v-if="prize.prizeType === 3" @click="saveAddress" src="https://img10.360buyimg.com/imgzone/jfs/t1/338523/28/13410/2382/68d0e92cF0b26519f/b87277bbc6d454ab.png" alt="">
        <img v-else-if="prize.prizeType === 7" class="copy-btn" :copy-text="copyText" src="https://img10.360buyimg.com/imgzone/jfs/t1/339890/8/13618/4223/68d0ea3fF74fd5f67/671b0030f3dd62ad.png" alt="">
        <img v-else @click="close" src="https://img10.360buyimg.com/imgzone/jfs/t1/326944/38/23005/4061/68d0ea46F459e845d/a7b5fd22cd5be60a.png" alt="">
      </div>
    </div>
  </div>
  <div class="thanks-join" v-else>
    <div class="btn" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject, computed } from 'vue';
import Clipboard from 'clipboard';
import { showToast } from 'vant';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  showImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
  prizeRecordId?: string; // 领奖成功后返回的记录ID，用于实物奖品填写地址
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const saveAddress = () => {
  // 如果是实物奖品，需要传递prizeRecordId给填写地址接口
  const addressParams = {
    result: props.prize.result.result,
    activityPrizeId: props.prize.activityPrizeId,
    prizeRecordId: props.prize.prizeRecordId // 领奖成功接口返回的data字符串
  };
  emits('saveAddress', addressParams);
};

const showCardNum = () => {
  emits('showCardNum', { ...props.prize.result, showImg: props.prize.showImg, prizeName: props.prize.prizeName });
};

const copyText = computed(() => {
  if (props.prize.result.cardNumber && props.prize.result.cardPassword) {
    return `卡号：${props.prize.result.cardNumber}\n卡密：${props.prize.result.cardPassword}`;
  }
  if (props.prize.result.cardNumber) {
    return `卡号：${props.prize.result.cardNumber}`;
  }
  if (props.prize.result.cardPassword) {
    return `卡密：${props.prize.result.cardPassword}`;
  }
  return '';
});

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bk {
  width: 6.89rem;
  height: 8.06rem;
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/165105/5/53212/11604/68d0e770F12b16ace/81dbcdf4ef395481.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 1rem;
  .prize-img {
    height: 2rem;
    width: 2rem;
    margin: 0 auto;
  }
  .success{
    margin: 0 auto;
    .textImg {
      width: 1.65rem;
      margin: 0.6rem auto 0;
    }
    .addressTextImg{
      width: 4.35rem;
      margin: 0 auto;
    }
    .btn{
      margin: 0.6rem 0 0;
      img{
        margin: 0 auto;
        width: 2.28rem;
      }
    }
  }
}
.thanks-join {
  width: 7rem;
  height: 8rem;
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/323427/9/22949/39912/68d0ebb9F5bf3aa67/31cf40565f3016e0.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 5.5rem;
  .btn {
    display: block;
    margin: 0 auto;
    width: 2.28rem;
    height: 0.74rem;
    background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/329429/7/16051/2981/68d0ec4dF7f73db22/a30afc74e24968f6.png);
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
</style>
