<template>
    <div class="main-view" style="overflow-y: scroll;">
        <img v-if="imgurl" style="width: 7.5rem;height: auto;margin-bottom: 1rem;" :src="imgurl">
        <div class="bottom-tip">长按保存</div>
        <!-- <div class="common-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor};top: 25.4rem;`"  @click="router.back()">返回</div> -->
        <canvas id="poster" style="display: none;"></canvas>
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted, reactive } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import VueQrcode from '@chenfengyuan/vue-qrcode';
import html2canvas from 'html2canvas';
import dayjs from 'dayjs';
import { httpRequest } from '@/utils/service';
import { saveCheckResultImg } from '../../config/api';
import { useRouter, useRoute } from 'vue-router';
import { drawRoundedRect, fillTextWithWrap, fillTextWithWrap2 } from '../../config/common';

const route = useRoute();
const router = useRouter();
const store = useStore<RootState>();

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const PetInfo = reactive({
  petNick: window.localStorage.getItem('1000001706-petNick'),
  petAvatar: window.localStorage.getItem('1000001706-petAvatar'),
});
const toothImg = computed(() => sessionStorage.getItem('toothImg'));
const checkId = computed(() => store.state.checkId);
const weightType = computed(() => store.state.weightType);

const text2List = [
  '低于90%的同龄同体型幼犬',
  '在同龄同体型幼犬的平均范围内',
  '高于90%的同龄同体型幼犬',
];

const resimgList = [
  {
    yes: 'https://img10.360buyimg.com/imgzone/jfs/t1/273366/8/23402/2781/68089076Fe081a9f0/84860cb7d541ba6a.png',
    no: 'https://img10.360buyimg.com/imgzone/jfs/t1/275176/21/26572/2794/68089076Fe547c5be/cc8100978a070482.png',
  },
  {
    yes: 'https://img10.360buyimg.com/imgzone/jfs/t1/279886/13/25553/2890/68089075Fec4a5d4f/d8589c135b5a8b45.png',
    no: 'https://img10.360buyimg.com/imgzone/jfs/t1/274837/16/26568/2888/68089076Fb6611ae9/dfd7b20cd1391780.png',
  },
  {
    yes: 'https://img10.360buyimg.com/imgzone/jfs/t1/284005/1/24747/2589/68089075Fe1bcad3b/65f18be083f555f8.png',
    no: 'https://img10.360buyimg.com/imgzone/jfs/t1/276655/34/26479/2608/68089076F49e20805/48215bd8c76950bd.png',
  },
];

const getHost = window.location.href.split('/custom')[0];
const qustionRes = computed(() => store.state.qustionRes);
const aiRes = computed(() => store.state.aiRes);
const qrCodeActUrl = `${getHost}/custom/1000001706/1899006146263613442/Home/?adsource=poster`;
const imgurl = ref('');
const config = {
  headers: {
    'Content-Type': 'multipart/form-data',
  },
};
// 定义符合 BlobCallback 类型的回调函数
function handleBlob(blob: Blob | null): void {
  if (!blob) return;
  const formData = new FormData();
  formData.append('file', blob, 'babyAvatar.png');
  (async () => {
    try {
      const res = await httpRequest.post('/common/uploadImgOss', formData, config);
      console.log('上传成功:', res.data);
      saveCheckResultImg({
        checkId: checkId.value,
        checkResultImg: res.data,
      });
    } catch (error) {
      console.error('上传失败:', error);
    }
  })();
}
const takephotos = async () => {
  const canvas = document.getElementById('poster') as HTMLCanvasElement;
  const ctx = canvas.getContext('2d')!;
  const posterWidth = 750; // 7.5rem 转换为像素
  const posterHeight = 1800; // 示例高度
  canvas.width = posterWidth;
  canvas.height = posterHeight;

  // 绘制背景
  ctx.fillStyle = '#fff';
  ctx.fillRect(0, 0, posterWidth, posterHeight);
  const img = new Image();
  img.crossOrigin = 'Anonymous';
  img.src = 'https://img10.360buyimg.com/imgzone/jfs/t1/290159/8/1015/27522/680f88c7Fe3181305/37b12a30187bbde8.png';
  await new Promise((resolve) => {
    img.onload = resolve;
  });
  ctx.drawImage(img, 38, 0, 674, 959);
  const timeText = `分析时间：${dayjs().format('YYYY-MM-DD')}`;
  ctx.font = '28px Arial';
  ctx.fillStyle = '#808285';
  ctx.textAlign = 'center';
  ctx.fillText(timeText, posterWidth / 2, 274);
  ctx.font = '28px Arial';
  ctx.fillStyle = '#ec001a';
  ctx.textAlign = 'center';
  ctx.fillText(`与同龄狗狗相比，【${PetInfo.petNick}】的体重`, posterWidth / 2, 520);

  const resimg1 = new Image();
  resimg1.crossOrigin = 'Anonymous';
  resimg1.src = weightType.value === 1 ? resimgList[0].yes : resimgList[0].no;
  await new Promise((resolve) => {
    resimg1.onload = resolve;
  });
  ctx.drawImage(resimg1, 95, 560, 560, 90);
  const resimg2 = new Image();
  resimg2.crossOrigin = 'Anonymous';
  resimg2.src = weightType.value === 2 ? resimgList[1].yes : resimgList[1].no;
  await new Promise((resolve) => {
    resimg2.onload = resolve;
  });
  ctx.drawImage(resimg2, 95, 683, 560, 90);
  const resimg3 = new Image();
  resimg3.crossOrigin = 'Anonymous';
  resimg3.src = weightType.value === 3 ? resimgList[2].yes : resimgList[2].no;
  await new Promise((resolve) => {
    resimg3.onload = resolve;
  });
  ctx.drawImage(resimg3, 95, 806, 560, 90);

  const tipbgimg = new Image();
  tipbgimg.crossOrigin = 'Anonymous';
  tipbgimg.src = 'https://img10.360buyimg.com/imgzone/jfs/t1/281673/13/25409/7887/68088508Fa010816b/bb07c7f8599fd64d.png';
  await new Promise((resolve) => {
    tipbgimg.onload = resolve;
  });
  ctx.drawImage(tipbgimg, 22, 986, 706, 554);

  ctx.font = '28px Arial';
  ctx.fillStyle = '#000';
  ctx.textAlign = 'left';
  const text = `要确定健康的体重，我们需要不止一个时间点的数据，建议您在多个时间点跟踪${PetInfo.petNick}的体重，并与兽医讨论。`;
  const x = 95;
  const y = 1054;
  const maxWidth = 540; // 文本的最大宽度
  const lineHeight = 48; // 行高
  fillTextWithWrap2(ctx, text, x, y, maxWidth, lineHeight);

  ctx.fillStyle = '#ffefed';
  // 绘制圆角矩形背景
  drawRoundedRect(ctx, 82, 1260, 586, 220, 24); // 20 是圆角半径

  const text2 = `根据您今天分享的信息，${PetInfo.petNick}的体重${text2List[weightType.value - 1]}。这可能由多种因素导致，比如它的基因、活动量以及饮食。`;
  const y2 = 1320;
  ctx.fillStyle = '#ec001a';
  fillTextWithWrap2(ctx, text2, x, y2, maxWidth, lineHeight);

  ctx.fillStyle = '#808285';
  ctx.fillText('识别右侧二维码', 75, 1660);
  ctx.fillText('定期追踪幼犬体重，', 75, 1690);
  ctx.fillText('助力狗狗茁壮成长', 75, 1720);

  const qrcodeimg = new Image();
  qrcodeimg.crossOrigin = 'Anonymous';
  qrcodeimg.src = 'https://img10.360buyimg.com/imgzone/jfs/t1/309419/10/523/17222/68231bccF86c5a067/c4696f9c642848a2.png';
  await new Promise((resolve) => {
    qrcodeimg.onload = resolve;
  });
  ctx.drawImage(qrcodeimg, 502, 1572, 210, 210);

  // 在这里画带圆角的圆形图片 放在最后
  ctx.fillStyle = '#ffefed';
  drawRoundedRect(ctx, 282.5, 300, 185, 185, 185 / 2); // 20 是圆角半径
  ctx.clip();
  const iconimg = new Image();
  iconimg.crossOrigin = 'Anonymous';
  iconimg.src = PetInfo.petAvatar || 'https://img10.360buyimg.com/imgzone/jfs/t1/270347/12/26858/11453/68088110F56d77624/9face4967d47dca2.png';
  await new Promise((resolve) => {
    iconimg.onload = resolve;
  });
  ctx.drawImage(iconimg, 282.5, 300, 185, 185);
  // 恢复绘图区域
  ctx.restore();

  const src64 = canvas.toDataURL('image/png');
  imgurl.value = src64;
  canvas.toBlob(handleBlob, 'image/png');
};

const init = () => {
  console.log(store.state.qustionRes.value);
  takephotos();
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
