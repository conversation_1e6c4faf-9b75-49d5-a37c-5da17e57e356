<template>
    <div class="main-view">
        <div class="result-content-box">
            <img class="logo-img" :src="require('../../asset/result/logo.png')" alt="">
            <VanSwipe ref="swiper" :loop="false">
                <van-swipe-item>
                    <div class="result-content">
                      <img class="bg" :src="require('../../asset/result/aiResBg.png')"/>
                      <img class="icon" :src="PetInfo.petAvatar || require('../../asset/result/icon.png')"/>
                      <canvas id="res1Title" style="width: 7.33rem;height: 0.77rem;position: absolute;left: 0;top: 4.44rem;z-index: 5 !important;"></canvas>
                      <div class="weight-type-box">
                        <div v-for="(item, index) in [1,2,3]" :key="index">
                          <img class="res-img" v-if="item == weightType" :src="require(`../../asset/result/petweight/${item}-yes.png`)"/>
                          <img class="res-img" v-else :src="require(`../../asset/result/petweight/${item}-no.png`)"/>
                        </div>
                      </div>
                    </div>
                </van-swipe-item>
                <van-swipe-item>
                  <div class="result-content">
                    <img class="bg" :src="require('../../asset/result/aiResBg2.png')"/>
                    <canvas id="res2content" style="width: 6.2rem;height: 5rem;position: absolute;left: 0.565rem;top: 1.65rem;z-index: 5 !important;"></canvas>
                    <div class="weit-bottom-box">
                      <img class="common-btn" style="position: unset;" :src="require('../../asset/result/toEnd.png')" @click="toEnd"/>
                      <img class="common-btn" style="position: unset;" :src="require('../../asset/result/toShare.png')" @click="toShare"/>
                    </div>
                  </div>
                </van-swipe-item>
                <template #indicator="{ active, total }">
                    <div class="page-no-box" :style="{color: `${PAGE_CONFIG.mainBgColor}`}">
                        <div>结果{{ `${active + 1}/${total}`}}</div>
                        <div class="bottom-div">
                            <img class="direction-icon" :src="require(`../../asset/result/direction-left.png`)" @click="swiper.prev()"/>
                            <div style="display: flex;flex-direction: row;">
                                <div class="page-point" :style="{background: `${PAGE_CONFIG.mainBgColor}`, opacity: active == 0 ? 1 : 0.2}"></div>
                                <div class="page-point" :style="{background: `${PAGE_CONFIG.mainBgColor}`, opacity: active == 1 ? 1 : 0.2}"></div>
                            </div>
                            <img class="direction-icon" :src="require(`../../asset/result/direction-right.png`)" @click="swiper.next()"/>
                        </div>
                    </div>
                </template>
            </VanSwipe>
        </div>
        <Disclaimers2 :isShow="isShowDisclaimers" @closePopup="isShowDisclaimers = false"/>
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted, reactive } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import Loading from '../../components/Loading.vue';
import Disclaimers2 from '../../components/Disclaimers2.vue';
import { getPetBreed, getPetList, getWeightCheckResult, getQaInfo, uploadToothImg, saveQaInfo, savePetInfo } from '../../config/api';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { useRouter, useRoute } from 'vue-router';
import { drawRoundedRect, fillTextWithWrap } from '../../config/common';

const route = useRoute();
const router = useRouter();
const store = useStore<RootState>();
const swiper = ref();

const weightType = computed(() => store.state.weightType);

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const PetInfo = reactive({
  petNick: window.localStorage.getItem('1000001706-petNick'),
  petAvatar: window.localStorage.getItem('1000001706-petAvatar'),
});
const toothImg = computed(() => store.state.toothImg);
const checkId = computed(() => store.state.checkId);
const isShowDisclaimers = ref(false);

const text2List = [
  '低于90%的同龄同体型幼犬',
  '在同龄同体型幼犬的平均范围内',
  '高于90%的同龄同体型幼犬',
];
const addTitle1 = () => {
  const canvas = document.getElementById('res1Title') as HTMLCanvasElement;
  const ctx = canvas.getContext('2d')!;
  const posterWidth = 733; // 7.5rem 转换为像素
  const posterHeight = 77; // 示例高度
  canvas.width = posterWidth;
  canvas.height = posterHeight;
  // ctx.fillStyle = '#fff';
  // ctx.fillRect(0, 0, posterWidth, posterHeight);
  ctx.font = '28px Arial';
  ctx.fillStyle = '#333333';
  ctx.textAlign = 'center';
  ctx.fillText(`与同龄狗狗相比，${PetInfo.petNick}的体重`, posterWidth / 2, 28);
};
const addContent2 = () => {
  const canvas = document.getElementById('res2content') as HTMLCanvasElement;
  const ctx = canvas.getContext('2d')!;
  const posterWidth = 620; // 7.5rem 转换为像素
  const posterHeight = 500; // 示例高度
  canvas.width = posterWidth;
  canvas.height = posterHeight;
  ctx.fillStyle = '#ffefed';
  // 绘制圆角矩形背景
  drawRoundedRect(ctx, 0, 0, posterWidth, posterHeight, 24); // 20 是圆角半径
  ctx.font = '28px Arial';
  ctx.fillStyle = '#ec001a';
  ctx.textAlign = 'left';
  const text = `要确定健康的体重，我们需要不止一个时间点的数据，建议您在多个时间点跟踪${PetInfo.petNick}的体重，并与兽医讨论。`;
  const x = 30;
  const y = 64;
  const text2 = `根据您今天分享的信息，${PetInfo.petNick}的体重${text2List[weightType.value - 1]}。这可能由多种因素导致，比如它的基因、活动量以及饮食。`;
  const y2 = 286;
  const maxWidth = 540; // 文本的最大宽度
  const lineHeight = 56; // 行高
  fillTextWithWrap(ctx, text, x, y, maxWidth, lineHeight);
  fillTextWithWrap(ctx, text2, x, y2, maxWidth, lineHeight);
};

const openPopup: any = inject('openPopup');
const qustionRes = ref();
const aiRes = ref();

const isLoadding = ref(true);
const pageStep = ref(0);
const resNo = ref(0);
const toEnd = () => {
  lzReportClick('continueClick');
  router.push({
    path: '/end',
  });
};
const toShare = () => {
  lzReportClick('clickShare');
  router.push({
    path: '/post',
  });
};
const timeNow = ref(Date.now());
const getAiRes = (params: any) => {
  getWeightCheckResult(params).then((ressult: any) => {
    console.log(ressult);
    if (ressult.status) {
      store.commit('setWeightType', ressult.res.data.checkResult);
      aiRes.value = ressult.res.data;
      setTimeout(() => {
        addTitle1();
        addContent2();
        openPopup();
      }, 500);
    } else {
      router.push('/pageerror');
    }
  });
};
const init = () => {
  let params;
  if (checkId.value) {
    params = {
      bodySize: store.state.step1Select,
      checkId: checkId.value,
      petBirth: store.state.petInfo.petBirth || '',
      weight: store.state.petWeight,
    };
    window.localStorage.setItem('1000001706-params', JSON.stringify(params));
  } else {
    params = JSON.parse(window.localStorage.getItem('1000001706-params') || '{}');
  }
  getAiRes(params);
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  lzReportClick('resultPage');
  init();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
