<template>
    <div class="main-view" style="padding: unset;">
        <Loading :pageNo="2" />
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted, reactive } from 'vue';
import Loading from '../../components/Loading.vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();

onMounted(() => {
  setTimeout(() => {
    router.replace('/result');
  }, 3000);
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
