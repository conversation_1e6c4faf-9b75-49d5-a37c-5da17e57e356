<template>
  <div id="app">
    <div class="title">抢好礼</div>
    <img class="bg" src="https://img10.360buyimg.com/imgzone/jfs/t1/137480/25/16969/117226/5fbb2c2aE84189391/b88fec356fa46dec.png" alt="">

    <!--    <div class="btn">-->
    <!--      <img  src="https://img10.360buyimg.com/imgzone/jfs/t1/139306/29/14998/20836/5fb63035E64c90a8f/017f7a95830a86c9.png" alt="">-->
    <!--    </div>-->

    <!-- 使用微信开放标签替换原按钮 -->
    <wx-open-launch-weapp
      id="launch-btn"
      appid="wx72c89b4ce4b41b39"
      :path=url>
      <component :is="'script'" type="text/wxtag-template">
        <component :is="'style'">.btn {
          width: 100%;
          }</component>
        <img class="btn" src="https://img10.360buyimg.com/imgzone/jfs/t1/139306/29/14998/20836/5fb63035E64c90a8f/017f7a95830a86c9.png" alt="">
      </component>
    </wx-open-launch-weapp>
    <div class="text">微信环境下仅支持京东购物小程序打开</div>
  </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { useStore } from 'vuex';
import { RootState } from '../store/state';
import { getPetBreed, getPetList, getCheckUpResult, getQaInfo, uploadToothImg, saveQaInfo, savePetInfo } from '../config/api';

const store = useStore<RootState>();

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const PetInfo = computed(() => store.state.petInfo);
const toothImg = computed(() => store.state.toothImg);
const checkId = computed(() => store.state.checkId);

const emits = defineEmits(['toggleComponent']);
const url = ref('pages/goods/goods?id=7551');

// 定义请求签名的函数
const requestSignature = async (url: any) => {
  try {
    const response = await fetch(`${process.env.VUE_APP_API_HOST}/common/wx/signature`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: url || window.location.href,
      }),
    });
    return await response.json();
  } catch (error: any) {
    console.error('请求签名失败:', error);
    throw error;
  }
};

// 重构后的 wxSdkConfig 函数
const wxSdkConfig = (url:any, debug:any) => new Promise((resolve) => {
  try {
    console.log('获取微信签名-开始', window.location.href);

    // 使用 async/await 包装的请求函数
    requestSignature(url)
      .then((data) => {
        console.log('获取微信签名-完成', data);
        const wxConfig = {
          debug: false,
          appId: data.data.appid,
          timestamp: data.data.timestamp,
          nonceStr: data.data.nonceStr,
          signature: data.data.signature,
          openTagList: ['wx-open-launch-weapp'],
          jsApiList: ['updateAppMessageShareData'],
        };

        console.log('微信config-开始', window.location.href);
        window.wx.config(wxConfig);
        console.log('微信config-完成', wxConfig);

        window.wx.ready(() => {
          console.log('wx sdk ready');
        });

        window.wx.error((res:any) => {
          console.error('wx sdk error', res);
        });

        resolve(true);
      })
      .catch((e) => {
        console.log('获取微信签名-失败', e);
        resolve(true);
      });
  } catch (e) {
    console.log('获取微信签名-失败', e);
    resolve(true);
  }
});

function getParams(key:any) {
  const result = new URLSearchParams(window.location.search);
  return result.get(key);
}
const mpUrl = getParams('mpUrl')?.replace('isvjd', 'isvjcloud');
console.log(mpUrl, 'mpUrl');
url.value = `pages/goods/goods?id=7551&encode_url=${mpUrl}`;

const init = () => {
  console.log(PAGE_CONFIG);
  // wxSdkConfig(mpUrl, true);
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
<style scoped>
#app {
  background: #ec453f;
  height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.title {
  font-size: .6rem;
  color: rgb(252, 236, 197);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 1.8rem;

}

.bg {
  width: 100%;
  display: block;
}

wx-open-launch-weapp {
  position: absolute;
  width: 5.5rem;
  height: .8rem;
  left: 50%;
  transform: translateX(-50%);
  top: 9.9rem;
  display: block;
  overflow: visible;
  z-index: 10;
}

.text {
  position: absolute;
  width: 100%;
  text-align: center;
  color: #fff;
  font-size: .25rem;
  top: 10.9rem;
  z-index: 5;
}
</style>
