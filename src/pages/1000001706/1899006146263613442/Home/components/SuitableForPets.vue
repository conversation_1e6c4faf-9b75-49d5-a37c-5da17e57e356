<template>
    <VanPopup teleport="body" v-model:show="show" position="center" :close-on-click-overlay="true">
        <div>
            <img class="pet-close-icon close-icon" :src="require('../asset/closeIcon.png')" @click="closePopup()"/>
            <div class="common-text-popup">
                <!-- <img class="bg-img" :src="require('../asset/适用宠物popup.png')"/> -->
                <img class="bg-img" src="//img10.360buyimg.com/imgzone/jfs/t1/283101/20/16013/59505/67f49d0aFd2e6a58d/be88cd6fd3b23313.png"/>
            </div>
        </div>
    </VanPopup>
</template>
<script lang="ts" setup>
import { FLAGS } from 'html2canvas/dist/types/dom/element-container';
import { emit } from 'process';
import { defineProps, computed, ref } from 'vue';

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
  },
});

const show = computed(() => props.isShow);
const emits = defineEmits(['closePopup']);
const closePopup = () => {
  emits('closePopup');
};

</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
