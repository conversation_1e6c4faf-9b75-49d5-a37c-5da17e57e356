<template>
  <div class="rectangle-loader">
    <img :src="require('../asset/loadding.png')"/>
    <div class="tips-box">正在提交照片进行审核...</div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, computed } from 'vue';
</script>

<style scoped>
.rectangle-loader {
  width: 7.5rem;
  height: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.rectangle-loader img {
  width: 1.28rem;
  height: 1.28rem;
  animation: rotate 2s cubic-bezier(0.1, 0.25, 0.75, 0.9) infinite;
}

.tips-box {
  word-break: keep-all;
  text-align: center;
  display: flex;
  justify-content: center;
  font-size: 0.28rem;
  color: #333333;
  margin-top: 0.3rem;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
