<template>
  <div class="count-down-time" :style="furnishStyles.cutDownBg.value">
    <div v-if="props.isStart">
      <span class="text" v-if="hasEligible" :style="furnishStyles.cutDownColor.value">距领奖结束还有：</span>
      <span class="text" v-else :style="furnishStyles.cutDownColor.value">距可领奖时间还有：</span>
      <van-count-down :time="hasEligible ? props.endTime - new Date().getTime() : props.receiveStartTime - new Date().getTime()" format="DD:HH:mm:ss">
        <template #default="timeData">
          <div class="contentSpan">
            <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.days }}</div>
            <span>天</span>
            <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.hours }}</div>
            <span>时</span>
            <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.minutes }}</div>
            <span>分</span>
            <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.seconds }}</div>
            <span>秒</span>
          </div>
        </template>
      </van-count-down>
    </div>
    <div v-else>
      <span class="text" :style="furnishStyles.cutDownColor.value">距离活动开始还有：</span>
      <van-count-down :time="props.isStart ? props.endTime - new Date().getTime() : props.startTime - new Date().getTime()" format="DD:HH:mm:ss">
        <template #default="timeData">
          <div class="contentSpan">
            <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.days }}</div>
            <span>天</span>
            <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.hours }}</div>
            <span>时</span>
            <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.minutes }}</div>
            <span>分</span>
            <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.seconds }}</div>
            <span>秒</span>
          </div>
        </template>
      </van-count-down>
    </div>
  </div>
</template>

<script setup lang="ts">
import furnishStyles, { furnish } from '../ts/furnishStyles';

const props = defineProps({
  isStart: {
    type: Boolean,
    default: false,
    required: true,
  },
  startTime: {
    type: Number,
    default: 0,
    required: true,
  },
  endTime: {
    type: Number,
    default: 0,
    required: true,
  },
  receiveStartTime: {
    type: Number,
    default: 0,
    required: true,
  },
  hasEligible: {
    type: Boolean,
    default: false,
    required: true,
  },
});

console.log(props.receiveStartTime, 'receiveStartTimereceiveStartTime');
console.log(props.endTime, 'endTimeendTime');
</script>

<style scoped lang="scss">
.count-down-time {
  position: relative;
  top: 0.25rem;
  left: 0.2rem;
  width: 6.9rem;
  height: 0.78rem;
  font-size: 0.25rem;
  background-repeat: no-repeat;
  background-size: 100%;
  //margin: 0 auto;
  //color: #f2270c;
  .text {
    position: absolute;
    top: 0.23rem;
    left: 0.7rem;
    height: 0.78rem;
    font-size: 0.25rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .contentSpan {
    //margin-left: 0rem;
    display: flex;
    position: absolute;
    top: 0.15rem;
    left: 3.32rem;

    .acblockStyleStyle {
      //color: #f2270c;
      border-radius: 0.05rem;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
      //background-color: #8b4004;
      //background: url("https://img10.360buyimg.com/imgzone/jfs/t1/77902/16/19674/1310/63070b2eEbcbdb636/bf6602f462367c3f.png") no-repeat;
      //background-size: 100%;
      width: 0.44rem;
      height: 0.44rem;
    }
    span {
      width: 0.4rem;
      height: 0.44rem;
      color: #ffffff;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
