import { computed, reactive } from 'vue';

export const furnish = reactive({
  // 奖品背景图
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/177178/18/49870/3451/672acd28F492bd16c/f1868282ddb943d6.png',
  // 页面背景颜色
  actBgColor: '#eae4df',
  // 按钮
  btnTextColor: '',
  btnBg: '',
  btnBorderColor: '',
});
// 页面背景颜色
const actBgColor = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.actBg ? `url(${furnish.actBg})` : '',
  width: '7.5rem',
}));
// 操作按钮
const operateBtn = computed(() => ({
  color: furnish.btnTextColor ?? '',
  backgroundColor: furnish.btnBg ?? '',
  borderColor: furnish.btnBorderColor ?? '',
}));

export default {
  actBgColor,
  operateBtn,
};
