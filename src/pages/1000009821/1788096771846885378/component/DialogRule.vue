<script setup lang="ts">
import useRequest from '../hooks/useRequest';
import { ref } from 'vue';

const { getRequest } = useRequest();
const rule = ref('');

const getRule = async () => {
  const { data } = await getRequest('/common/getRule');
  rule.value = data;
};
getRule();
</script>

<template>
<div >
  <pre class="rule">{{rule}}</pre>
</div>
</template>

<style scoped lang="scss">
.rule {
  position: absolute;
  top: 0.9rem;
  padding: 0 .5rem;
  height: 5.2rem;
  overflow: scroll;
  white-space: pre-wrap;
  word-wrap: break-word;
}

</style>
