<script setup lang="ts">
import { ref, defineProps, defineExpose } from 'vue';
import { areaList } from '@vant/area-data';

const addressSelects = ref(false);
const props: any = defineProps(['data']);

const userInfo = ref({
  realName: '',
  mobile: '',
  areaAll: '',
  province: '',
  city: '',
  county: '',
  address: '',
  addressCode: '',
  // todo
  // userPrizeId: props.data.userPrizeId || 0,
  ...props.data,
});

const confirmAddress = (addressItemList: any) => {
  userInfo.value.province = addressItemList.selectedOptions[0].text;
  userInfo.value.city = addressItemList.selectedOptions[1].text;
  userInfo.value.county = addressItemList.selectedOptions[2].text;
  userInfo.value.areaAll = userInfo.value.province + userInfo.value.city + userInfo.value.county;
  addressSelects.value = false;
};
defineExpose({
  userInfo: userInfo.value,
});

</script>

<template>
    <div class="info">
      <div class="info-content">
        <VanField placeholder="收货人姓名" v-model="userInfo.realName" type="text" class="van-field__control" maxlength="11"/>
        <VanField placeholder="收货人手机号" v-model="userInfo.mobile" type="number"  class="van-field__control" maxlength="11"/>
        <VanField placeholder="选择省/市/区" v-model="userInfo.areaAll" readonly @click="addressSelects = true" class="van-field__control"></VanField>
        <VanField placeholder="街道门牌号" v-model="userInfo.address" type="text" class="van-field__control address"/>
        <VanField placeholder="邮政编码" maxlength="6" type="number" v-model="userInfo.addressCode"  class="van-field__control addressCode"/>
      </div>

      <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
        <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
      </VanPopup>
    </div>
</template>

<style scoped lang="scss">

.info {
  position: absolute;
  top: 1.72rem;
  left: 2.4rem;

  &-content {
    display: flex;
    flex-direction: column;
    gap: .42rem;
    .van-cell:after {
      border: none;
    }
    //:deep(.van-cell:after) {
    //  border: none;
    //}
    .van-field__control{
      width: 3.5rem;
      height: 100%;
      color: black;
      background-color: transparent;
      font-size: 0.23rem;
      display: flex;
      align-items: center;
      line-height: 0.28rem;

      ::placeholder {
        color: #8c8880;
        font-size: .25rem;
        opacity: 1;
      }
    }

  }
}

</style>
