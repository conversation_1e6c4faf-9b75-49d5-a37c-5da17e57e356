<template>
  <VanPopup v-model:show="show" :closeable="false">
    <div class="popup-content">
      <div class="prize-title">
        <div class="prize-text">礼品</div>
        <div class="prize-text">领取时间</div>
      </div>

      <div class="prize-content">
        <div class="prize-text">{{ prize?.rightsName }}</div>
        <div class="prize-text">{{ prize?.createTime }}</div>
      </div>
      <div class="close-btn" @click="closePopup"></div>
    </div>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed } from 'vue';

const props = defineProps({
  showPopup: {
    type: Boolean,
    default: false,
  },
  prize: {
    type: Object,
    default: () => ({}),
  },
});
const emits = defineEmits(['closePopup']);
const show = computed(() => props.showPopup);
const closePopup = () => {
  emits('closePopup');
};
</script>

<style scoped lang="scss">
.popup-content {
  width: 6.5rem;
  height: 8.5rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/268212/40/14351/33287/678fa325Fea14f79a/d1bd80df9a55e6c5.png') no-repeat;
  background-size: 100%;
  position: relative;
  padding-top: 1.5rem;
  .prize-title {
    width: 4.4rem;
    height: 0.5rem;
    display: flex;
    justify-content: center;
    margin: 0 auto;
    .prize-text {
      width: 50%;
      text-align: center;
      font-size: 0.26rem;
      font-weight: bold;
    }
  }
  .prize-content {
    width: 4.4rem;
    height: 2.5rem;
    display: flex;
    justify-content: center;
    margin: 0 auto;
    .prize-text {
      font-size: 0.26rem;
      width: 50%;
      text-align: center;
    }
  }
  .close-btn {
    width: 3rem;
    height: 0.5rem;
    position: absolute;
    top: 7.5rem;
    left: 1.8rem;
  }
}
</style>
<style>
.van-popup {
  background: none;
  overflow: hidden;
}
/* .van-toast {
    background-color: rgba(0, 0, 0, 0.7);
    flex-wrap: nowrap;
    width: auto;
    min-width: var(--van-toast-text-min-width);
  } */
</style>
