<template>
  <VanPopup v-model:show="show" :closeable="false" :close-on-click-overlay="true" @click-overlay="closePopup">
    <div class="popup-content">
      <div class="title">请您务必填写真实信息</div>

      <div class="section">
        <div class="form-group">
          <div class="form-group-title">收货人：</div>
          <input v-model="viewerInfo.receiver" type="text" placeholder="请输入收货人姓名" />
        </div>
        <div class="form-group">
          <div class="form-group-title">联系电话：</div>
          <input v-model="viewerInfo.receiverPhone" type="tel" placeholder="请输入联系电话" />
        </div>
        <div class="form-group">
          <div class="form-group-title">省市区：</div>
          <input type="text" placeholder="请选择省市区" v-model="addressCode" readonly="true" @click="addressSelects = true" />
        </div>
        <div class="form-group">
          <div class="form-group-title">详细地址：</div>
          <textarea v-model="viewerInfo.address" placeholder="请输入详细地址" rows="3"> </textarea>
        </div>
      </div>

      <img class="close-btn" @click="showconfirm = true" src="//img10.360buyimg.com/imgzone/jfs/t1/286700/16/19709/7803/6889bf52Fab659925/5007ed0e5753a7f6.png" alt="" />
      <!-- 底部提示 -->
      <div class="bottom-tip">地址确认之后不可更改<br>9月8日23:59后不得填写收货信息</div>
    </div>
    <!--地址选择-->
    <VanPopup v-model:show="addressSelects" teleport="#app" position="bottom">
      <VanArea title="请选择省市区" :area-list="areaList" @confirm="confirmAddress" @cancel="onCancel" />
    </VanPopup>
  </VanPopup>
  <VanPopup v-model:show="showconfirm" :closeable="false">
    <div class="confirm-content">
      <div class="close-btn" @click="showconfirm = false"></div>
      <div class="confirm-btn" @click="submit"></div>
    </div>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, reactive, watch } from 'vue';
import { showToast } from 'vant';
import { areaList } from '@vant/area-data';

const props = defineProps({
  showPopup: {
    type: Boolean,
    default: false,
  },
  viewerInfo: {
    type: Object,
    default: () => ({
      receiver: '',
      receiverPhone: '',
      province: '',
      city: '',
      district: '',
      address: '',
    }),
  },
});
const addressCode = ref('');
const addressSelects = ref(false);
const emits = defineEmits(['closePopup', 'submit', 'restore']);
const showconfirm = ref(false);

// 保存初始数据的副本
let initialData = {};

// 监听弹窗打开，保存初始数据
watch(
  () => props.showPopup,
  (newVal) => {
    if (newVal) {
      // 弹窗打开时，深拷贝保存初始数据
      initialData = JSON.parse(JSON.stringify(props.viewerInfo));
      const { province, city, district  } = props.viewerInfo;
      addressCode.value = province ? `${province}${city}${district}` : '';
    }
  },
);
const show = computed(() => props.showPopup);

// 校验函数
const validateRequired = (value: string, fieldName: string): boolean => {
  if (!value || value.trim() === '') {
    showToast(`请填写${fieldName}`);
    return false;
  }
  return true;
};

// 校验表情符号
const validateEmoji = (value: string, fieldName: string): boolean => {
  const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
  if (emojiRegex.test(value)) {
    showToast(`${fieldName}不能包含表情符号`);
    return false;
  }
  return true;
};

// 校验姓名
const validateName = (value: string, fieldName: string): boolean => {
  if (!validateRequired(value, fieldName)) return false;
  if (!validateEmoji(value, fieldName)) return false;

  // 长度校验 (2-20个字符)
  if (value.length < 2 || value.length > 20) {
    showToast(`${fieldName}长度应在2-20个字符之间`);
    return false;
  }

  // 特殊字符校验 (只允许中文、英文、数字)
  const nameRegex = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
  if (!nameRegex.test(value)) {
    showToast(`${fieldName}只能包含中文、英文和数字`);
    return false;
  }

  return true;
};

// 校验电话
const validatePhone = (value: string, fieldName: string): boolean => {
  if (!validateRequired(value, fieldName)) return false;
  if (!validateEmoji(value, fieldName)) return false;

  // 长度校验 (11位手机号)
  if (value.length !== 11) {
    showToast(`${fieldName}必须为11位数字`);
    return false;
  }

  // 特殊字符校验 (只允许数字)
  const phoneRegex = /^[0-9]+$/;
  if (!phoneRegex.test(value)) {
    showToast(`${fieldName}只能包含数字`);
    return false;
  }

  // 手机号格式校验
  const mobileRegex = /^1[3-9]\d{9}$/;
  if (!mobileRegex.test(value)) {
    showToast(`请输入正确的${fieldName}格式`);
    return false;
  }

  return true;
};

// 确认三联地址信息
const confirmAddress = (addressItemList: any) => {
  const { viewerInfo } = props;
  viewerInfo.province = addressItemList?.selectedOptions[0].text;
  viewerInfo.city = addressItemList.selectedOptions[1].text;
  viewerInfo.district = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
  addressCode.value = addressItemList.selectedOptions.map((item: any) => item.text).join('/');
};
// 关闭三联地址框
const onCancel = () => {
  addressSelects.value = false;
};
const submit = () => {
  showconfirm.value = false;
  const { viewerInfo } = props;
  // 校验收货信息
  if (!validateName(viewerInfo.receiver, '收货人姓名')) return;
  if (!validatePhone(viewerInfo.receiverPhone, '联系电话')) return;
  if (!validateRequired(viewerInfo.province, '省份')) return;
  if (!validateRequired(viewerInfo.city, '城市')) return;
  if (!validateRequired(viewerInfo.district, '区县')) return;
  if (!validateRequired(viewerInfo.address, '详细地址')) return;

  // 所有校验通过，提交表单
  emits('submit', viewerInfo);
  emits('closePopup');
};

const closePopup = () => {
  // 发出还原事件，传递初始数据
  emits('restore', initialData);
  emits('closePopup');
};
</script>

<style scoped lang="scss">
.confirm-content {
  width: 6.5rem;
  height: 4rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/294308/36/22041/63904/68a7e378F6fb769d5/24f20276fb204bc4.png') no-repeat;
  background-size: 100%;
  position: relative;
  .close-btn {
    width: 2rem;
    height: 0.6rem;
    position: absolute;
    top: 2.5rem;
    left: 1.2rem;
  }
  .confirm-btn {
    width: 2rem;
    height: 0.6rem;
    position: absolute;
    top: 2.5rem;
    right: 1.2rem;
  }
}
.popup-content {
  width: 6.5rem;
  height: 8.5rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/303677/23/22199/71861/6888848dF9d41ab2c/7ce38b93edc85cd9.png') no-repeat;
  background-size: 100%;
  position: relative;
  padding-top: 2.2rem;
  .title {
    padding-left: 0.5rem;
    text-align: center;
    font-size: 0.26rem;
    color: #dd0022;
    margin-bottom: 0.3rem;
  }
  .close-btn {
    width: 2.5rem;
    // height: 0.5rem;
    position: absolute;
    top: 6.6rem;
    left: calc(50% + 0.18rem);
    transform: translateX(-50%);
  }
  .bottom-tip {
    width: 100%;
    padding-left: 0.2rem;
    text-align: center;
    font-size: 0.2rem;
    color: #dd0022;
    position: absolute;
    top: 5.8rem;
  }

  .section {
    width: 4rem;
    border-radius: 0.2rem;
    margin: 0 auto 0.3rem;
    padding-left: 0.2rem;

    .section-title {
      font-size: 0.22rem;
      color: #333;
      font-weight: bold;
      margin-bottom: 0.1rem;
    }

    .form-group {
      width: 4rem;
      display: flex;
      align-items: center;
      margin-bottom: 0.3rem;

      &:last-child {
        margin-bottom: 0;
      }

      .form-group-title {
        font-size: 0.2rem;
        color: #333;
        width: 1.1rem;
        white-space: nowrap;
      }

      input,
      textarea {
        width: 2.9rem;
        height: 0.3rem;
        background: rgba(255, 192, 203, 0.3);
        border: none;
        border-radius: 0.1rem;
        padding: 0 0.2rem;
        font-size: 0.24rem;
        color: #333;

        &::placeholder {
          color: #999;
        }

        &:focus {
          outline: none;
          background: rgba(255, 192, 203, 0.5);
        }
      }
      textarea {
        height: auto;
      }
    }
  }
}
</style>
