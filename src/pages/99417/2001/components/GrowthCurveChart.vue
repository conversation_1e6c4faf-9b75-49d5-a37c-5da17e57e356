<!-- components/GrowthCurveChart.vue -->
<template>
  <div class="curve-wrap">
    <canvas ref="canvasRef" class="f2-canvas"></canvas>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch, computed, nextTick, PropType } from 'vue';
import F2 from '@antv/f2';
import dayjs from 'dayjs';

type XUnit = 'weeks' | 'months';
type MetricKey = 'weights' | 'heights' | 'heads';

interface CurvePoint {
  XValue: number | string;
  YValue: number;
  date: string;
  type: '3%' | '15%' | '50%' | '85%' | '97%';
}
interface CurveBundle {
  YConfig: { YMax: number; YMin: number };
  dataList: CurvePoint[];
  points: CurvePoint[];
}
interface XConfig {
  XDays: number;
  XTickCount: number;
  XTicks: number[];
  XUnit: XUnit;
}
interface BabyChartData {
  XConfig: XConfig;
  weights: CurveBundle;
  heights: CurveBundle;
  heads: CurveBundle;
}
interface RecordItem {
  babyId: number;
  head?: number | null;
  height?: number | null;
  weight?: number | null;
  recordDate: string; // 'YYYY-MM-DD' | 'YYYY-MM-DD HH:mm:ss'
  recordId: number;
}

const props = defineProps({
  /** /99417/main -> babyList[i].data */
  dataSource: Object as PropType<BabyChartData | null>,
  /** 当前指标：weights / heights / heads */
  metric: { type: String as PropType<MetricKey>, required: true },
  /** y 轴单位：weights=kg，其余=cm */
  unit: { type: String as PropType<'kg' | 'cm'>, default: 'cm' },
  width: Number,
  height: Number,
  /** 当前宝宝的填写记录 */
  records: { type: Array as PropType<RecordItem[]>, default: () => [] },
  /** 当前宝宝生日，用于换算月龄/周龄 */
  birthday: { type: String, default: '' },
  showYMinMax: { type: Boolean, default: false },
});

const canvasRef = ref<HTMLCanvasElement | null>(null);
let chart: any = null;

const colorMap: Record<string, string> = {
  '3%': '#BFBFBF',
  '15%': '#E48C8C',
  '50%': '#6DBB8E',
  '85%': '#E48C8C',
  '97%': '#BFBFBF',
  record: '#18C07A', // 记录点/线的绿色
};

const unitText = computed(() => `单位(${props.unit})`);
const xAxisSuffix = computed(() => (props.dataSource?.XConfig?.XUnit === 'weeks' ? '年龄(周)' : '年龄(月)'));

function getBundle(): CurveBundle | null {
  const d = props.dataSource;
  if (!d) return null;
  return d[props.metric];
}

function parseXY(v: number | string) {
  if (typeof v === 'number') return v;
  const n = Number(v);
  return Number.isFinite(n) ? n : 0;
}

function buildStdDataset(bundle: CurveBundle) {
  // 标准曲线展开为 [{x, y, type}, ...]
  return bundle.dataList.map((p) => ({
    x: parseXY(p.XValue),
    y: p.YValue,
    type: p.type, // 3% | 15% | 50% | 85% | 97%
  }));
}

function buildYTicks(min: number, max: number, step = 2) {
  const ticks: number[] = [];
  // 先推最小值
  ticks.push(min);
  // 从 >= min 的下一个 step 整点开始往上推
  const start = Math.ceil((min - 1e-8) / step) * step;
  for (let v = start; v < max - 1e-8; v += step) {
    if (v > min + 1e-8) ticks.push(Number(v.toFixed(4)));
  }
  // 最后推最大值（避免重复）
  if (Math.abs(ticks[ticks.length - 1] - max) > 1e-8) {
    ticks.push(max);
  }
  return ticks;
}

function buildRecordSeries() {
  const xUnit = props.dataSource?.XConfig?.XUnit; // 'weeks' | 'months'
  if (!xUnit || !props.birthday) return [];

  const b = dayjs(props.birthday);
  const valueKey = props.metric === 'weights' ? 'weight' : props.metric === 'heights' ? 'height' : 'head';

  const arr = (props.records || [])
    .map((r) => {
      const val = r[valueKey as 'weight' | 'height' | 'head'];
      if (val == null) return null;
      const rd = dayjs(r.recordDate);
      if (!rd.isValid() || !b.isValid()) return null;

      let x = 0;
      if (xUnit === 'weeks') {
        const days = rd.diff(b, 'day');
        x = days / 7;
      } else {
        x = rd.diff(b, 'month', true);
      }

      return { x, y: Number(val), type: 'record' as const };
    })
    .filter(Boolean) as Array<{ x: number; y: number; type: 'record' }>;

  // 关键：按 x 升序，连线才正确
  return arr.sort((a, b) => a.x - b.x);
}

function destroyChart() {
  if (chart) {
    chart.destroy();
    chart = null;
  }
}

function drawGuides(bundle: CurveBundle, xMax: number) {
  // 右侧 3%/15%/50%/85%/97% 文案
  const endYs = bundle.points.map((p) => ({ type: p.type, y: p.YValue }));
  endYs.forEach(({ type, y }) => {
    chart.guide().text({
      position: [xMax, y],
      content: type,
      offsetX: 10,
      style: { fill: colorMap[type], textAlign: 'left', fontSize: 10 },
    });
  });
  // 左上角：单位
  chart.guide().text({
    position: ['min', 'max'],
    content: unitText.value,
    offsetX: -12,
    offsetY: -25,
    style: { fill: '#B5B5B5', fontSize: 10, textAlign: 'left' },
  });
  // 底部中间：年龄(周/月)
  // 底部中间：年龄(周/月)
  const xMid = (0 + xMax) / 2; // 中间的 x 值
  chart.guide().text({
    position: [xMid, 'min'],
    content: xAxisSuffix.value,
    offsetY: 30,
    style: { fill: '#9E9E9E', fontSize: 10, textAlign: 'center' },
  });
}

// 让步长落在 1 / 2 / 2.5 / 5 × 10^k 这类“好看”的数
function niceStep(raw: number) {
  const pow10 = Math.pow(10, Math.floor(Math.log10(raw)));
  const n = raw / pow10;
  let m = 1;
  if (n <= 1) m = 1;
  else if (n <= 2) m = 2;
  else if (n <= 2.5) m = 2.5;
  else if (n <= 5) m = 5;
  else m = 10;
  return m * pow10;
}

/** 自适应 y 轴刻度（保证包含 min/max） */
function buildAutoYTicks(min: number, max: number, canvasHeightPx: number) {
  if (!(isFinite(min) && isFinite(max)) || min === max) return [min, max];

  const range = max - min;

  // 依据可视高度估算刻度数量（每格 ~24~32px）
  const approxCount = Math.round(canvasHeightPx / 28);
  const target = Math.min(12, Math.max(5, approxCount)); // 限 5~12 条刻度线

  const rawStep = range / (target - 1);
  const step = niceStep(rawStep);

  // 从 min 向下对齐到 step 的整数倍，再往上推
  const start = Math.floor(min / step) * step;
  const ticks: number[] = [];
  for (let v = start; v <= max + 1e-8; v += step) {
    if (v >= min - 1e-8) ticks.push(Number((Math.round(v / 1e-6) * 1e-6).toFixed(6)));
    if (ticks.length > 100) break; // 保险
  }

  // 确保包含 min/max
  if (Math.abs(ticks[0] - min) > 1e-8) ticks.unshift(min);
  if (Math.abs(ticks[ticks.length - 1] - max) > 1e-8) ticks.push(max);

  // 去重并排序（避免上面 unshift/push 造成重复/乱序）
  const uniq = Array.from(new Set(ticks.map((v) => +v.toFixed(6)))).sort((a, b) => a - b);
  return uniq;
}

function render() {
  const bundle = getBundle();
  const xConf = props.dataSource?.XConfig;
  if (!bundle || !xConf || !canvasRef.value) return;

  const stdData = buildStdDataset(bundle);
  const recData = buildRecordSeries();
  const data = stdData.concat(recData);

  const { YMin, YMax } = bundle.YConfig;

  destroyChart();

  const containerHeight = document.querySelector('.chart-container')?.clientHeight || 360;
  chart = new F2.Chart({
    el: canvasRef.value!,
    pixelRatio: window.devicePixelRatio || 1,
    width: props.width,
    height: containerHeight - 10,
    padding: [40, 40, 40, 40],
  });

  // ✅ 动态 y 轴刻度（保证含 min/max）
  let yTicks = buildAutoYTicks(YMin, YMax, chart.get('height') - 80 /*除去上下 padding 的近似可绘高度*/);
  // 根据 showYMinMax 过滤
  if (!props.showYMinMax && yTicks.length > 2) {
    yTicks = yTicks.filter((t) => t !== YMin && t !== YMax);
  }

  const xMax = xConf.XTicks.length ? xConf.XTicks[xConf.XTicks.length - 1] : Math.max(...data.map((d) => d.x));

  chart.legend(false);
  chart.source(data, {
    x: { min: 0, max: xMax, ticks: xConf.XTicks, tickCount: xConf.XTickCount, nice: false },
    y: { min: YMin, max: YMax, ticks: yTicks, nice: false }, // ← 用自适应 ticks
  });

  chart.axis('x', {
    label: () => ({ fill: '#9E9E9E', fontSize: 10 }),
    grid: () => ({ stroke: 'rgba(0,0,0,0.08)', lineDash: [2, 2] }),
    line: { stroke: 'rgba(0,0,0,0.15)' },
  });
  chart.axis('y', {
    label: () => ({ fill: '#9E9E9E', fontSize: 10 }),
    grid: () => ({ stroke: 'rgba(0,0,0,0.12)', lineDash: [2, 2] }),
    line: { stroke: 'rgba(0,0,0,0.15)' },
  });

  // ===== 折线：标准曲线（虚线）=====
  chart
    .line()
    .position('x*y')
    .color('type', (t: string) => colorMap[t] || '#ccc')
    .size('type', (t: string) => (t === 'record' ? 0 : 2)) // 隐藏记录线
    .style({ lineDash: [6, 6] }) // 标准曲线统一虚线
    .shape('smooth');

  // ===== 折线：记录曲线（绿色实线）=====
  chart
    .line()
    .position('x*y')
    .color('type', (t: string) => (t === 'record' ? colorMap.record : 'transparent'))
    .size('type', (t: string) => (t === 'record' ? 2 : 0)) // 只显示记录线
    .shape('smooth');

  // ===== 记录点（绿色）=====
  chart
    .point()
    .position('x*y')
    .color('type', (t: string) => (t === 'record' ? colorMap.record : 'transparent'))
    .size('type', (t: string) => (t === 'record' ? 3.5 : 0))
    .style('type', (t: string) => (t === 'record' ? { lineWidth: 1 } : {}))
    .shape('circle');

  chart.tooltip(false);

  drawGuides(bundle, xMax);

  chart.render();
}

onMounted(() => nextTick(render));
onBeforeUnmount(destroyChart);

watch(
  () => [props.metric, props.dataSource, props.unit, props.records, props.birthday],
  () => nextTick(render),
  { deep: true },
);
</script>

<style scoped>
.curve-wrap {
  width: 100%;
  height: 100%;
}

.f2-canvas {
  display: block;
  width: 100%;
  height: 100%;
}
</style>
