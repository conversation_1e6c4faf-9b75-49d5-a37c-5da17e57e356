<template>
  <van-popup class="my-dialog my-popup" :show="show" @click.stop>
    <div class="dialog-container animate__animated animate__faster" :class="{ animate__bounceInUp: show, animate__zoomOut: !show }" @click.stop>
      <div class="record-list-dialog">
        <img class="title" src="https://img10.360buyimg.com/imgzone/jfs/t1/293810/40/24470/3724/689ab096F4527dd8b/4c237374645562ed.png" />

        <!-- 时间轴列表 -->
        <div class="timeline">
          <span v-if="normalized.length === 0" class="empty-text">暂无记录</span>
          <div v-else v-for="(r, idx) in normalized" :key="idx" class="node">
            <div class="dot-wrap">
              <img class="dot-icon" src="https://img10.360buyimg.com/imgzone/jfs/t1/302113/1/20914/5748/689ab757Fb554f59b/956296dc9f9d9378.png" />
              <i class="line"></i>
            </div>

            <div class="card">
              <div class="head">
                <div class="time">{{ r.relative }}（{{ r.hm }}）</div>
                <van-icon name="ellipsis" class="more" @click="onMore(r)" />
              </div>

              <div class="title-text" :class="{ emphasis: r.showTitle }">
                {{ r.title }}
              </div>

              <div class="desc" v-if="r.desc">
                {{ r.desc }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-close-btn-bottom" @click="$emit('update:show', false)"></div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';
import { showConfirmDialog } from 'vant';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const props = defineProps<{
  show: boolean;
  records?: Array<{
    recordDate: string; // 'YYYY-MM-DD HH:mm:ss'
    height?: number | null;
    weight?: number | null;
    head?: number | null;
  }>;
}>();

const emit = defineEmits<{
  (e: 'update:show', v: boolean): void;
  (e: 'remove', index: number): void;
}>();

// 规整/派生展示字段
const normalized = computed(() => {
  return (props.records || [])
    .slice() // 不改原数组
    .sort((a, b) => dayjs(b.recordDate).valueOf() - dayjs(a.recordDate).valueOf()) // 按时间倒序
    .map((r) => {
      const when = dayjs(r.recordDate);
      console.log(when.format('YYYY-MM-DD HH:mm:ss'));
      const filled: { key: 'height' | 'weight' | 'head'; label: string; unit: string; val: number }[] = [];
      if (r.height ?? null) filled.push({ key: 'height', label: '身高', unit: 'cm', val: r.height as number });
      if (r.weight ?? null) filled.push({ key: 'weight', label: '体重', unit: 'kg', val: r.weight as number });
      if (r.head ?? null) filled.push({ key: 'head', label: '头围', unit: 'cm', val: r.head as number });

      // 标题逻辑：一项 => 该项名称；两项及以上 => 生长发育
      const title = filled.length <= 1 ? filled[0]?.label || '生长发育' : '生长发育';
      const showTitle = title === '生长发育';

      // 描述文案
      let desc = '';
      if (filled.length === 1) {
        const f = filled[0];
        desc = `${f.val}${f.unit}`;
      } else if (filled.length >= 2) {
        const parts = [r.height != null ? `身高：${r.height}cm` : '', r.weight != null ? `体重：${r.weight}kg` : '', r.head != null ? `头围：${r.head}cm` : ''].filter(Boolean);
        desc = parts.join('，');
      }

      return {
        raw: r,
        title,
        showTitle,
        desc,
        relative: when.fromNow(), // 例如：18分钟前
        hm: when.format('HH:mm'), // 括号里的时:分
      };
    });
});

// 右上角三点 -> 删除确认
function onMore(r) {
  showConfirmDialog({
    title: '提示',
    message: '是否删除该条记录？删除后不可恢复',
    confirmButtonText: '删除',
    confirmButtonColor: '#ff4d4f',
    cancelButtonText: '取消',
  })
    .then(() => emit('remove', r))
    .catch(() => {});
}
</script>

<style scoped lang="scss">
.record-list-dialog {
  position: relative;
  display: flex;
  justify-content: center;
  width: 7.37rem;
  height: 10.94rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/307106/19/25410/58425/689a9ad9F58226194/1dbfe5a4ea176918.png');
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;

  .title {
    position: absolute;
    top: 3.22rem;
    left: 0.7rem;
    width: 1.4rem;
  }

  .timeline {
    position: absolute;
    top: 4.3rem;
    left: 0;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 6.35rem;
    padding: 0 0.4rem 0 0.6rem;
    overflow: auto;

    .empty-text {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding-right: 0.3rem;
      padding-bottom: 0.3rem;
      color: #bbb;
      font-size: 0.25rem;
    }
  }

  .node {
    position: relative;
    display: flex;
    margin-bottom: 0.36rem;

    .dot-wrap {
      position: absolute;
      top: 50%;
      left: -0.4rem;
      width: 0.44rem;
      transform: translateY(-50%);

      .dot-icon {
        position: relative;
        z-index: 2;
        width: 0.44rem;
        height: 0.44rem;
      }

      .line {
        position: absolute;
        top: -0.6rem;
        left: 0.21rem;
        width: 0.04rem;
        height: calc(100% + 1.7rem);
        background-color: #7bca5c;
        content: '';
      }
    }

    .card {
      width: 6.2rem;
      margin-left: 0.24rem;
      padding: 0.24rem;
      background: #fff;
      border-radius: 0.16rem;
      box-shadow: 0 0.08rem 0.22rem rgb(255, 94, 94, 25%);

      .head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #7a7a7a;
        font-size: 0.24rem;

        .more {
          color: #b8b8b8;
          font-size: 0.32rem;
        }
      }

      .title-text {
        margin-top: 0.14rem;
        color: #ff6b7b;
        font-weight: 600;
        font-size: 0.3rem;

        &.emphasis {
          color: #ff6b7b;
        }
      }

      .desc {
        margin-top: 0.12rem;
        color: #4a4a4a;
        font-size: 0.28rem;
      }
    }
  }

  .node:last-child .line {
    height: calc(100% + 1.3rem);
  }
}
</style>
