<template>
  <div id="page">
    <div class="activity-container">
      <!-- 背景图 -->
      <img class="bg-image" src="https://img10.360buyimg.com/imgzone/jfs/t1/315309/17/25346/782565/689a8f5cF6a51c1f1/7d8940da0dbd635a.png" alt="bg" />

      <!-- 顶部栏 -->
      <div class="top-bar">
        <div class="title">生长发育</div>
      </div>

      <!-- 顶部栏下方：切换宝宝按钮 -->
      <div class="baby-switch" @click="openBabyPicker">
        {{ babyLabel }}
      </div>

      <button class="all-records" @click="onOpenAll">全部记录</button>
      <button class="my-prize" @click="onClickMyPrize">我的奖品</button>

      <!-- 一级Tabs -->
      <div class="tabs">
        <div class="tab-item" :class="{ active: activeTab === 0 }" @click="activeTab = 0">
          生长记录
          <i class="underline" v-if="activeTab === 0"></i>
        </div>
        <div class="tab-item" :class="{ active: activeTab === 1 }" @click="activeTab = 1">
          评估与建议
          <i class="underline" v-if="activeTab === 1"></i>
        </div>
      </div>

      <!-- 内容区 -->
      <div class="content-wrap">
        <!-- 生长记录 -->
        <template v-if="activeTab === 0">
          <div class="card">
            <!-- 二级Tabs：体重/身高/头围 -->
            <div class="sub-tabs">
              <div class="sub-tab" :class="{ active: subTab === 0 }" @click="subTab = 0">体重</div>
              <div class="sub-tab" :class="{ active: subTab === 1 }" @click="subTab = 1">身高</div>
              <div class="sub-tab" :class="{ active: subTab === 2 }" @click="subTab = 2">头围</div>
            </div>

            <div class="chart-container">
              <GrowthCurveChart v-if="currentBaby && currentBaby.data" :data-source="currentBaby.data" :metric="currentMetric" :unit="unitMap[currentMetric]" :records="currentBaby?.records || []" :birthday="currentBaby?.birthday" :key="`${currentBaby?.babyId || 'none'}-${currentMetric}`" />
            </div>
          </div>

          <!-- 曲线说明入口 -->
          <div class="hint">
            <span class="link" @click="onWhatCurve">什么是生长发育曲线</span>
            <van-icon name="question-o" />
          </div>

          <!-- 添加记录按钮 -->
          <div class="footer">
            <button class="primary-btn" @click="onAddRecord">添加记录</button>
          </div>
        </template>

        <!-- 评估与建议 -->
        <template v-else>
          <div class="advice-card" v-if="evaluationText">
            <div class="advice-header">
              <span class="badge">小冠建议</span>
            </div>
            <div class="advice-content" v-html="evaluationText"></div>
          </div>
          <div class="empty-panel" v-else></div>
        </template>
      </div>
    </div>
  </div>
  <!-- 添加记录弹窗 -->
  <AddRecordDialog v-model:show="showAddRecordDialog" :saving="isSaving99417" @save="onSaveRecord" />
  <!-- 记录列表弹窗 -->
  <RecordListDialog v-model:show="showRecordListDialog" :records="currentBaby?.records || []" @remove="onRemoveRecord" />
  <!-- 选择宝宝弹层 -->
  <van-popup v-model:show="showBabyPicker" position="bottom" round>
    <van-picker :columns="babyColumns" title="选择宝宝" @confirm="onBabyConfirm" @cancel="showBabyPicker = false" />
  </van-popup>
  <!-- 填写地址弹窗 -->
  <AddressDialog v-model:show="showAddressDialog" @submitSuccess="onSubmitAddressSuccess" @submit="onSubmitAddress" />
  <!-- 抽奖结果弹窗 -->
  <PrizeResultDialog v-model:show="showPrizeDialog" :prizes="prizeList" />
  <!-- 我的奖品弹窗 -->
  <MyPrizeDialog v-model:show="showMyPrizeDialog" :my-prize-list="myPrizeList" />
  <!-- 暂无宝宝弹窗 -->
  <NoBabyTipDialog v-model:show="showNoBabyTip" @back="NoBabyTipDialogBack" />
  <!-- 未出生宝宝弹窗 -->
  <UnbornBabyTipDialog v-model:show="showUnbornTip" />
  <!-- 性别为空弹窗 -->
  <NoGenderTipDialog v-model:show="showNoGenderTip" @back="NoGenderTipDialogBack" />
</template>

<script lang="ts" setup>
/* eslint-disable */
import { inject, reactive, provide, computed, ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { closeToast, showLoadingToast, showToast, Swipe as VanSwipe, SwipeItem as VanSwipeItem, Popup as VanPopup, CountDown as VanCountDown } from 'vant';
import { debounce } from 'lodash';
import dayjs from 'dayjs';
import { areaList } from '@vant/area-data';
import { useRouter } from 'vue-router';

import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { gotoShopPage, addSkuToCart, gotoSkuPage } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import openCard from '@/utils/openCard';

import AddRecordDialog from '../components/AddRecordDialog.vue';
import RecordListDialog from '../components/RecordListDialog.vue';
import GrowthCurveChart from '../components/GrowthCurveChart.vue';
import AddressDialog from '../components/AddressDialog.vue';
import PrizeResultDialog from '../components/PrizeResultDialog.vue';
import MyPrizeDialog from '../components/MyPrizeDialog.vue';
import NoBabyTipDialog from '../components/NoBabyTipDialog.vue';
import UnbornBabyTipDialog from '../components/UnbornBabyTipDialog.vue';

import NoGenderTipDialog from '../components/NoGenderTipDialog.vue';

const router = useRouter();

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('baseInfo', baseInfo);

const pathParams: any = inject('pathParams');
console.log('pathParams', pathParams);

const baseUserInfo: any = inject('baseUserInfo');
console.log('baseUserInfo', baseUserInfo);

const decoData: any = inject('decoData');
console.log('decoData', decoData);

const delayToast = (e: string, time = 1000): Promise<void> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      showToast(e);
      resolve();
    }, time);
  });
};

function checkActivityTime(): Promise<void> {
  return new Promise((resolve, reject) => {
    const now = dayjs();
    const start = dayjs(baseInfo.startTime);
    const end = dayjs(baseInfo.endTime);

    if (now.isBefore(start)) {
      showToast('活动未开始');
      reject(new Error('活动未开始'));
      return;
    }
    if (now.isAfter(end)) {
      showToast('活动已结束');
      reject(new Error('活动已结束'));
      return;
    }
    resolve();
  });
}

const isSaving99417 = ref(false);

// 指标映射：subTab 0/1/2 -> weights/heights/heads
const metricMap = ['weights', 'heights', 'heads'] as const;
const unitMap: Record<'weights' | 'heights' | 'heads', 'kg' | 'cm'> = {
  weights: 'kg',
  heights: 'cm',
  heads: 'cm',
};
// 中文标签 & 单位映射
const metricLabelMap = { weights: '体重', heights: '身高', heads: '头围' } as const;
// 按曲线 YConfig 校验数值是否在 [YMin, YMax] 区间
function validateByYConfig(val: number | null | undefined, ycfg: { YMin: number; YMax: number } | undefined, label: string, unit: 'kg' | 'cm') {
  if (val == null || !ycfg || typeof ycfg.YMin !== 'number' || typeof ycfg.YMax !== 'number') return true;
  const { YMin, YMax } = ycfg;
  if (val < YMin || val > YMax) {
    showToast(`${label}超出可填写范围（${YMin}~${YMax}${unit}）`);
    return false;
  }
  return true;
}

// 当前宝宝、当前指标
const currentBaby = computed(() => babyList.value[currentBabyIdx.value] || null);
const currentMetric = computed(() => metricMap[subTab.value]);

const homeData: any = ref({});
// 当前选择的宝宝索引
const currentBabyIdx = ref(0);
// baby 列表（从 /99417/main 的 data.babyList 获取）
const babyList = computed(() => (homeData.value as any)?.babyList || []);
// 顶部按钮显示文案
const babyLabel = computed(() => {
  const list = babyList.value;
  if (!list.length) return '您还未添加宝宝信息';
  return `当前宝宝：${list[currentBabyIdx.value]?.nickname ?? ''}`;
});
// Picker 列数据
const babyColumns = computed(() => babyList.value.map((b: any, i: number) => ({ text: b.nickname, value: i })));

// 打开弹层
const showBabyPicker = ref(false);
async function openBabyPicker() {
  await checkHasBaby();
  showBabyPicker.value = true;
}
// 确认选择
function onBabyConfirm(option: { selectedValues: any[]; selectedOptions: any[] }) {
  const picked = option.selectedOptions?.[0];
  if (picked && typeof picked.value === 'number') {
    currentBabyIdx.value = picked.value;
    // 切换后如果是未出生宝宝，直接提示
    const b = babyList.value[currentBabyIdx.value];
    if (b && dayjs(b.birthday).isAfter(dayjs(), 'day')) {
      showUnbornTip.value = true;
    }
  }
  showBabyPicker.value = false;
}

const getHomeData = async (options = { showLoading: true }) => {
  const { showLoading = true } = options;

  try {
    if (showLoading) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
    }

    // 查询用户宝宝列表
    const { data } = await httpRequest.post('/99417/main');
    homeData.value = data;

    // 没有宝宝则弹提示
    if (!((homeData.value as any)?.babyList?.length > 0)) {
      showNoBabyTip.value = true;
    }
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  } finally {
    if (showLoading) {
      closeToast();
    }
  }
};

// 拉取接口后，若有宝宝默认选第一个
onMounted(async () => {
  await getHomeData();
  if (babyList.value.length) {
    currentBabyIdx.value = 0;
    // 如果第一个宝宝未出生，提示一次
    if (dayjs(babyList.value[0].birthday).isAfter(dayjs(), 'day')) {
      showUnbornTip.value = true;
    }
  }
});

const activeTab = ref(0); // 0: 生长记录  1: 评估与建议
const subTab = ref(0); // 0: 体重 1: 身高 2: 头围

const onWhatCurve = () => {
  router.push({ name: 'explain' });
};

// 抽奖结果弹窗
const showAddRecordDialog = ref(false);
const onAddRecord = async () => {
  await checkActivityTime();
  await ensureReadyBaby(); // 有宝宝且已出生
  showAddRecordDialog.value = true;
};

// 记录列表弹窗
const showRecordListDialog = ref(false);
const onOpenAll = async () => {
  await ensureReadyBaby();
  showRecordListDialog.value = true;
};
async function onRemoveRecord(item: any) {
  const payload = item?.raw;
  try {
    showLoadingToast({ message: '删除中...', forbidClick: true, duration: 0 });
    await httpRequest.post('/99417/remove', payload);
    // 删除成功后刷新主接口数据
    await getHomeData({ showLoading: false });
    delayToast('删除成功');
  } catch (error: any) {
    console.error('[删除失败]', error);
    showToast(error?.message || '删除失败，请稍后重试');
  } finally {
    closeToast();
  }
}

const hasAnyBabySubmittedBefore = computed(() => {
  return Array.isArray(babyList.value) && babyList.value.some((b) => Array.isArray(b.records) && b.records.length > 0);
});

// 添加记录弹窗点击确定
async function onSaveRecord(payload: { recordDate: string; height?: number | null; weight?: number | null; head?: number | null }) {
  console.log(
    '[记录检查]',
    '所有宝宝是否有过提交：',
    hasAnyBabySubmittedBefore.value,
    '详情：',
    babyList.value?.map((b) => ({ babyId: b.babyId, nickname: b.nickname, recordsCount: b.records?.length ?? 0 })),
  );

  console.log('收到子组件数据', payload);

  if (!homeData.value.drawState) return showToast('奖品无库存，请联系客服');

  // 新增校验：不能添加宝宝生日之前的信息
  const baby = currentBaby.value;
  if (!baby || !baby.birthday) {
    showToast('未找到宝宝信息');
    return;
  }

  const recordDay = dayjs(payload.recordDate);
  const birthdayDay = dayjs(baby.birthday);
  if (recordDay.isBefore(birthdayDay, 'day')) {
    showToast('不能添加宝宝出生之前的记录');
    return;
  }

  // ====== 区间校验：体重/身高/头围（来自对应曲线的 YConfig）======
  const ds = baby?.data; // /99417/main 返回的该宝宝的曲线数据
  const okHeight = validateByYConfig(payload.height, ds?.heights?.YConfig, metricLabelMap.heights, unitMap.heights);
  if (!okHeight) return;

  const okWeight = validateByYConfig(payload.weight, ds?.weights?.YConfig, metricLabelMap.weights, unitMap.weights);
  if (!okWeight) return;

  const okHead = validateByYConfig(payload.head, ds?.heads?.YConfig, metricLabelMap.heads, unitMap.heads);
  if (!okHead) return;
  // ====== 结束：区间校验 =======

  recordFormData.value = payload;

  // 如果有提交记录，就说明领过奖了，就直接保存数据
  if (hasAnyBabySubmittedBefore.value) {
    if (isSaving99417.value) return; // ✅ 防连击
    isSaving99417.value = true;

    console.log('有提交记录，直接保存数据');
    try {
      showLoadingToast({ message: '提交中...', forbidClick: true, duration: 0 });
      const params = {
        record: {
          ...recordFormData.value,
          babyId: currentBaby.value.babyId,
        },
      };
      const { data } = await httpRequest.post('/99417/save', params);
      delayToast('提交成功');
    } catch (error: any) {
      showToast(error.message);
      console.error(error);
    } finally {
      closeToast();
      await getHomeData({ showLoading: false });
      isSaving99417.value = false; // ✅ 解锁
      showAddRecordDialog.value = false;
    }
  } else {
    // 如果有实物奖品，则需要填写地址
    if (homeData.value.hasGoods) {
      console.log('有实物奖品，需要填写地址');
      showAddRecordDialog.value = false;
      showAddressDialog.value = true;
    } else {
      if (isSaving99417.value) return; // ✅ 防连击
      isSaving99417.value = true;
      console.log('无实物奖品，直接提交保存并领奖');
      // 否则直接提交保存并领奖
      try {
        showLoadingToast({ message: '提交中...', forbidClick: true, duration: 0 });
        const params = {
          record: {
            ...recordFormData.value,
            babyId: currentBaby.value.babyId,
          },
        };
        const { data } = await httpRequest.post('/99417/save', params);
        if (data && data.length > 0) {
          showPrizeDialog.value = true;
          prizeList.value = data;
        }
      } catch (error: any) {
        showToast(error.message);
        console.error(error);
      } finally {
        closeToast();
        await getHomeData({ showLoading: false });
        isSaving99417.value = false; // ✅ 解锁
        showAddRecordDialog.value = false;
      }
    }
  }
}

// 抽奖结果弹窗
const showPrizeDialog = ref(false);
const prizeList = ref([]);

// 填写地址弹窗
const recordFormData = ref({});
const showAddressDialog = ref(false);
const onSubmitAddressSuccess = async () => {};
const onSubmitAddress = async (address) => {
  if (isSaving99417.value) return; // ✅ 防连击
  isSaving99417.value = true;
  try {
    showLoadingToast({ message: '提交中...', forbidClick: true, duration: 0 });
    const params = {
      address: address,
      record: {
        ...recordFormData.value,
        babyId: currentBaby.value.babyId,
      },
    };
    const { data } = await httpRequest.post('/99417/save', params);
    if (data && data.length > 0) {
      showAddressDialog.value = false;
      showPrizeDialog.value = true;
      prizeList.value = data;
    }
  } catch (error: any) {
    showAddressDialog.value = false;
    showToast(error.message);
    console.error(error);
    await getHomeData({ showLoading: false });
  } finally {
    closeToast();
    await getHomeData({ showLoading: false });
    isSaving99417.value = false; // ✅ 解锁
  }
};

// 我的奖品弹窗
const showMyPrizeDialog = ref(false);
const myPrizeList = ref([]);
const onClickMyPrize = async () => {
  await checkHasBaby();
  try {
    showLoadingToast({ message: '正在查询...', forbidClick: true, duration: 0 });
    const { data } = await httpRequest.post('/99417/prize/query');
    myPrizeList.value = data;
    showMyPrizeDialog.value = true;
  } finally {
    closeToast();
  }
};

const evaluationText = computed(() => (currentBaby.value?.evaluation || '').trim());

const showNoBabyTip = ref(false);
const NoBabyTipDialogBack = async () => {
  showNoBabyTip.value = false;
  window.jmfe.toAny('https://lzkjdz-isv.isvjcloud.com/m/1000003570/99/2208100000357002/');
};
function checkHasBaby(): Promise<void> {
  return new Promise((resolve, reject) => {
    // 这里假设 babyList 是你的宝宝列表数据
    if (babyList.value && babyList.value.length > 0) {
      resolve(); // 有宝宝，直接 resolve
    } else {
      // 没有宝宝，弹出提示
      showNoBabyTip.value = true;
      reject(new Error('no-baby')); // 可以 reject 一个标识错误
    }
  });
}

const showUnbornTip = ref(false);
// 检查“当前宝宝是否已出生”
function checkCurrentBabyBorn(): Promise<void> {
  return new Promise((resolve, reject) => {
    const b = currentBaby.value;
    if (!b) {
      // 没有宝宝的情况交给 checkHasBaby 去处理
      reject(new Error('no-baby'));
      return;
    }
    const bd = dayjs(b.birthday);
    const unborn = bd.isAfter(dayjs(), 'day'); // 生日在今天之后 => 未出生
    if (unborn) {
      showUnbornTip.value = true;
      reject(new Error('unborn-baby'));
    } else {
      resolve();
    }
  });
}

const showNoGenderTip = ref(false);
const NoGenderTipDialogBack = () => {
  showNoGenderTip.value = false;
  window.jmfe.toAny('https://lzkjdz-isv.isvjcloud.com/m/1000003570/99/2208100000357002/');
};
function checkGenderSelected(): Promise<void> {
  return new Promise((resolve, reject) => {
    const b = currentBaby.value;
    if (!b) {
      // 交给 checkHasBaby 处理
      reject(new Error('no-baby'));
      return;
    }
    // 已出生？
    const isBorn = !dayjs(b.birthday).isAfter(dayjs(), 'day');

    // 未选择性别：按你的约定 gender === 2；兼容后端可能为 null/undefined 的情况
    const unselected = b.gender === '2' || b.gender === 2 || b.gender === null || b.gender === undefined;

    if (isBorn && unselected) {
      console.log('未选择性别');
      showNoGenderTip.value = true;
      reject(new Error('no-gender'));
      return;
    }
    resolve();
  });
}

// 组合检查：需要“有宝宝 & 已出生 & 性别已选”才放行
async function ensureReadyBaby() {
  await checkHasBaby();
  await checkCurrentBabyBorn();
  await checkGenderSelected();
}
</script>

<style scoped lang="scss">
/* 设计稿 750px => 页面宽 7.5rem；此页面使用 rem */
#page {
  width: 100vw;
  height: 100vh;
  overflow: auto;
}

.activity-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 7.5rem;
  min-height: 100vh;
  margin: 0 auto;
  background-color: #ffb88a;

  .bg-image {
    position: absolute;
    inset: 0 0 auto;
    z-index: -1;
    width: 100%;
    height: 100vh;
    object-fit: cover;
  }
}

/* 顶部栏 */
.top-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 7.5rem;
  height: 0.88rem;
  margin-bottom: 0.8rem;

  .title {
    color: #fff;
    font-weight: 600;
    font-size: 0.36rem;
    letter-spacing: 0.01rem;
  }
}

.all-records {
  position: fixed;
  top: 1rem;
  right: 0;
  height: 0.64rem;
  padding: 0 0.28rem;
  color: #9c6b18;
  font-size: 0.24rem;
  background: #fff;
  border: none;
  border-radius: 0.32rem 0 0 0.32rem;
}

.my-prize {
  position: fixed;
  top: 1.75rem;
  right: 0;
  height: 0.64rem;
  padding: 0 0.28rem;
  color: #9c6b18;
  font-size: 0.24rem;
  background: #fff;
  border: none;
  border-radius: 0.32rem 0 0 0.32rem;
}

/* 一级 Tabs */
.tabs {
  display: flex;
  justify-content: center;
  margin-top: 0.18rem;

  .tab-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 0.3rem;
    padding: 0 0.2rem 0.2rem;
    color: #fff;
    font-size: 0.28rem;

    &.active {
      color: #000;
      font-weight: 600;
    }

    .underline {
      position: absolute;
      bottom: -0.08rem;
      left: 0;
      display: block;
      width: 100%;
      height: 0.06rem;
      background: #f0553a;
      border-radius: 0.03rem;
      content: '';
    }
  }
}

/* 内容区 */
.content-wrap {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* 卡片区 */
.card {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  width: 6.88rem;
  margin-top: 0.4rem;
  padding: 0.24rem 0 0;
  background-color: #fff;
  border-radius: 0.07rem;
  box-shadow: 0 0.04rem 0.24rem 0 rgb(222, 160, 134, 48%);

  .sub-tabs {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 6.27rem;
    height: 0.67rem;
    background-color: #f2f2f2;
    border-radius: 0.07rem;

    .sub-tab {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2.03rem;
      height: 0.49rem;
      color: #999595;
      border-radius: 0.07rem;

      &.active {
        color: #443a3a;
        background-color: #fff;
      }
    }
  }

  .chart-container {
    flex: 1;
    width: 100%;
    margin-top: 0.24rem;
  }
}

/* 提示与按钮 */
.hint {
  width: 7.02rem;
  margin: 0.52rem 0 0.28rem;
  color: #fff;
  font-size: 0.24rem;
  text-align: center;

  .link {
    text-decoration: none;
    cursor: pointer;
  }

  .dot {
    margin-left: 0.06rem;
    opacity: 0.85;
  }
}

.footer {
  display: flex;
  justify-content: center;
  width: 7.02rem;
  margin-bottom: 0.4rem;

  .primary-btn {
    width: 3.9rem;
    height: 0.9rem;
    color: #fff;
    font-weight: 600;
    font-size: 0.32rem;
    letter-spacing: 0.02rem;
    background: #e93a2e;
    border: none;
    border-radius: 0.45rem;
    box-shadow: 0 0.08rem 0.18rem rgb(233, 58, 46, 25%);
  }
}

.baby-switch {
  position: absolute;
  top: 0.98rem; /* 贴合你标题下的位置，可微调 */
  left: 0.24rem;
  z-index: 2;
  max-width: 4.2rem;
  height: 0.48rem;
  padding: 0 0.24rem;
  overflow: hidden;
  color: #fff;
  font-size: 0.22rem;
  line-height: 0.48rem;
  white-space: nowrap;
  text-overflow: ellipsis;
  background: #9a612d; /* 棕色 */
  border-radius: 0.24rem; /* 胶囊 */
  box-shadow: 0 0.04rem 0.08rem rgb(0, 0, 0, 12%);
}

/* 评估与建议 */
.advice-card {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 6.88rem;
  margin-top: 0.4rem;
  padding: 0.24rem 0 0;
  background-color: #fff;
  border-radius: 0.07rem;
  box-shadow: 0 0.04rem 0.24rem 0 rgb(222, 160, 134, 48%);
}

.advice-header {
  display: flex;
  width: 100%;
  margin-bottom: 0.16rem;

  .badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.11rem;
    height: 0.59rem;
    color: #fff;
    font-size: 0.34rem;
    background-color: #ff8381;
    border-radius: 0 0.27rem 0.27rem 0;
  }
}

.advice-content {
  max-height: calc(100vh - 4.3rem);
  padding: 0 0.3rem 0.3rem;
  overflow: auto;
  color: #6e6e6e;
  font-size: 0.24rem;
  line-height: 2;
  white-space: break-spaces;
}

.empty-panel {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 6.88rem;
  height: 8rem;
  margin-top: 0.4rem;
  padding: 0.24rem 0 0;
  background-color: #fff;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/317656/24/23941/74138/689b4dcdF552c9ebf/a8d1edfe3ed4d5ea.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 65% auto;
  border-radius: 0.07rem;
  box-shadow: 0 0.04rem 0.24rem 0 rgb(222, 160, 134, 48%);
}
</style>
