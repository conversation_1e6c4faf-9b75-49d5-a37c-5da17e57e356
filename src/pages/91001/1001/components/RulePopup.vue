<template>
  <div class="rule">
    <div v-html="rule"></div>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref } from 'vue';

const rule = ref('');

const getRule = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    closeToast();
    const res = await httpRequest.get('/common/getRule');
    rule.value = res.data;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
getRule();
</script>

<style scoped lang="scss">
.rule {
  height: 100%;
  padding: 0.35rem;
  font-size: 0.24rem;
  color: #000;
  white-space: pre-wrap;
  word-break: break-all;
  div {
    height: 100%;
    overflow-y: scroll;
  }
}
</style>
