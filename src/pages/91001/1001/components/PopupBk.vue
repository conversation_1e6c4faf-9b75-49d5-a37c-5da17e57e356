<template>
  <VanPopup teleport="body" v-model:show="limitPopup" :closeOnClickOverlay="false">
    <div class="popup-bk">
      <img :src="title" alt="" class="title" />
      <div class="content">
        <slot></slot>
      </div>
    </div>
    <img src="../assets/close.png" alt="" class="close" @click="close" />
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';

const props = defineProps(['show', 'title']);
const emits = defineEmits(['update:show']);

const limitPopup = ref(props.show);
const close = () => {
  limitPopup.value = false;
  emits('update:show', false);
};

watch(
  () => props.show,
  (val) => {
    limitPopup.value = val;
  },
);
</script>

<style scoped lang="scss">
.popup-bk {
  background: url('../assets/popupBk.png') no-repeat;
  background-size: 100%;
  width: 6.5rem;
  height: 8.15rem;
  padding-top: 0.3rem;
  .title {
    margin: 0 auto 0.25rem;
    height: 0.39rem;
  }
  .content {
    height: 7.2rem;
    border-radius: 0 0 0.6rem 0.6rem;
    overflow: hidden;
  }
}
.close {
  width: 0.6rem;
  margin: 0.23rem auto 0;
}
</style>
