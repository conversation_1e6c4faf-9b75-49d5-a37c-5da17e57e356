<template>
  <vanPopup v-model:show="show">
    <div class="operation-guide-container">
      <div class="text" v-html="rule.replaceAll('\n', '<br>')"></div>
      <div class="close" @click="closePopup"></div>
    </div>
  </vanPopup>
</template>
<script setup lang="ts">
import { defineProps, computed, defineEmits } from 'vue';

const props = defineProps({
  showPopup: {
    type: Boolean,
    default: false,
  },
  rule: {
    type: String,
    default: '',
  },
});
const show = computed(() => props.showPopup);
const emit = defineEmits(['closePopup']);
const closePopup = () => {
  emit('closePopup');
};
</script>

<style scoped lang="scss">
.operation-guide-container {
  width: 6rem;
  height: 9rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/339256/7/3455/11576/68b158c0Fc5a07a95/ecfebc4edbaf90a5.png') no-repeat;
  background-size: 100%;
  padding-top: 1.8rem;
  position: relative;
  .text {
    color: #000;
    padding: 0 0.3rem 0 0.3rem;
    height: 6rem;
    font-size: 0.24rem;
    color: #222222;
    overflow: auto;
  }
  .close {
    width: 0.5rem;
    height: 0.5rem;
    position: absolute;
    bottom: 0.2rem;
    left: 50%;
    transform: translate(-50%);
  }
}
.van-popup {
  background: none;
  overflow: hidden;
}
</style>
