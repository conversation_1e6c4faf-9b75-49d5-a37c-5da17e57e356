<template>
  <div :class="(userType === 'fail' || userType === 'default') ? 'result-white' : 'result'">
    <img class="logo" src="../assets/img/logo-icon.png" alt="">
    <img :class="[userType === 'pass' ? 'top1' : 'top']" v-if="userType === 'pass' || userType === 'fail'" src="../assets/img/new-upload-title.png" alt="">

    <img class="result-title" :src="showTitle" alt="">
    <div :class="secondaryHead">
      <span v-if="userType === 'pass'">
        点击下方【立即购买】拍下<br/>
        您专属的新客权益
      </span>
      <span v-else-if="userType === 'old' || userType === 'new'">
        您已获得{{prizeData.prizeName}}
      </span>
    </div>
    <div class="result-img-bg" v-if="userType === 'pass' || userType === 'old' || userType === 'new'">
      <img class="result-banner" :src="showBanner" alt="">
    </div>
    <img v-if="userType !== 'pass' && userType !== 'new' && userType !== 'old'" class="result-banner1" :src="showBanner" alt="">

    <img class="result-btn" :src="showBtn" @click="handleResult(btnRes)" alt="">
    <img class="bottom" src="../assets/img/bottom-text.png" alt="">
  </div>
  <VanPopup teleport="body" v-model:show="showToUpload" :close-on-click-overlay="false">
    <ToUpLoadDialog :actType="actType" :uploadName="uploadName" @closeDialog="showToUpload = false"/>
  </VanPopup>
</template>

<script lang='ts' setup>
import { ref, inject, onMounted, computed } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { useRoute } from 'vue-router';
import { furnish } from '../ts/furnishStyles';
import { useStore } from 'vuex';
import ToUpLoadDialog from '../components/ToUpLoadDialog.vue';
import { closeToast } from 'vant';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const route = useRoute();
const store = useStore();

// 通过 getters 获取状态
// B端配置的活动类型 0-不开启 1-上传出生证明 2-上传宝宝照片
const actType = computed(() => store.getters.getActType);
// 用户类型 new-新客 old-老客 pass-通过资格校验 fail-未通过资格校验 default-不满足领取规则
const userType = computed(() => store.getters.getUserType);
// 是否已领取新客权益2
const userReceivedGift2 = computed(() => store.getters.getUserReceivedGift2);
// 是否上传过出生证号
const userBornBindStatus = computed(() => store.getters.getUserBornBindStatus);
// 上传照片名称
const uploadName = computed(() => store.getters.getUploadName);
// 奖品信息
const prize = computed(() => store.getters.getPrizeList);

const prizeData = ref([]);
const showTitle = ref('');
const showBtn = ref('');
const btnRes = ref('');
const secondaryHead = ref('');
const showToUpload = ref(false);

/* -------------------------------------------------------------------------- */
const showBanner = ref('//img10.360buyimg.com/imgzone/jfs/t1/281799/28/4708/120190/67d94048F24e9b5d0/295702578ec8c9c9.png');

const imgTitle = [
  // 不满足领取规则
  '//img10.360buyimg.com/imgzone/jfs/t1/285040/8/4989/34293/67da3421F2a403e5f/730f8f51d8dbef38.png',
  // 未通过资格校验
  '//img10.360buyimg.com/imgzone/jfs/t1/270423/26/6340/34789/67da3033Ffa632182/ae21c5700708501a.png',
  // 满足新客条件
  '//img10.360buyimg.com/imgzone/jfs/t1/283207/1/4791/31957/67da3032F2799c66e/dcfbf94c31186021.png',
  // 满足老客条件
  '//img10.360buyimg.com/imgzone/jfs/t1/281689/18/5123/31862/67da3032Feb528aa1/a13302f750f707a9.png',
  // 通过资格校验
  '//img10.360buyimg.com/imgzone/jfs/t1/279172/9/5733/34799/67da3033F514ab2b1/8d8547be23ca3ef5.png',
];

const imgBtn = [
  // 返回店铺
  '//img10.360buyimg.com/imgzone/jfs/t1/280870/25/19947/23109/67fe0e28F86d4f7c7/a59113fca81c02c3.png',
  // 立即购买
  '//img10.360buyimg.com/imgzone/jfs/t1/282195/29/5096/19028/67da30a4Fa973c856/abb074937358ed6e.png',
];

const handleResult = (res: string) => {
  if (btnRes.value === 'back') {
    window.location.href = furnish.jumpLink;
  } else {
    gotoSkuPage(prizeData.value?.skuId);
  }
};

const initResult = () => {
  const resultMap = {
    new: { // 满足新客条件
      title: imgTitle[2],
      banner: prizeData.value?.prizeImg,
      btn: imgBtn[1],
      secondaryHead: 'secondaryNewOldHead',
    },
    old: { // 满足老客条件
      title: imgTitle[3],
      banner: prizeData.value?.prizeImg,
      btn: imgBtn[1],
      secondaryHead: 'secondaryNewOldHead',
    },
    pass: { // 通过资格校验
      title: imgTitle[4],
      banner: prizeData.value?.prizeImg,
      btn: imgBtn[1],
      secondaryHead: 'secondaryPassHead',
    },
    fail: { // 未通过资格校验
      title: imgTitle[1],
      banner: furnish.canNotJoinKv,
      btn: imgBtn[0],
    },
    default: { // 不满足领取规则
      title: imgTitle[0],
      banner: furnish.canNotJoinKv,
      btn: imgBtn[0],
    },
  };

  const result = resultMap[userType.value] || resultMap.default;

  showTitle.value = result.title;
  showBanner.value = result.banner;
  showBtn.value = result.btn;
  secondaryHead.value = result.secondaryHead || '';
  if (userType.value !== 'new' && userType.value !== 'old' && userType.value !== 'pass') {
    btnRes.value = 'back';
  }
  closeToast();
};

onMounted(() => {
  // 如果校验通过
  if (userType.value === 'pass' || userType.value === 'old' || userType.value === 'new') {
    prizeData.value = prize.value;
  }
  initResult();
  if (userType.value === 'new') {
    if (actType.value === 2) {
      // 如果没领取过新客权益2
      if (!userReceivedGift2.value) {
        showToUpload.value = true;
      }
      return;
    }
    if (actType.value === 1) {
      // 如果没领取过新客权益2且没有上传过出生证明
      if (!userReceivedGift2.value && !userBornBindStatus.value) {
        showToUpload.value = true;
      }
    }
  }
});
</script>

<style lang='scss'>
.result {
  width: 100%; /* 宽度自适应 */
  min-height: 100vh;
  padding-top: 2rem;
  position: relative;
  text-align: center;
  padding-bottom: 0.2rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/281303/24/4269/45429/67d9437eF2a2c7784/9fe50b4d988ce4ac.png");
    repeat: no-repeat;
    size: 100% 100%;
  };
}
.result-white {
  width: 100%; /* 宽度自适应 */
  min-height: 100vh;
  padding-top: 2.2rem;
  position: relative;
  text-align: center;
  padding-bottom: 0.2rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/275441/17/5440/12168/67d94363F00c7f13c/f450f1130ab2c27f.png");
    repeat: no-repeat;
    size: 100% 100%;
  };
}

  .logo {
    width: 1.54rem;
    position: absolute;
    top: .5rem;
    left: 50%;
    transform: translateX(-50%);
  }

  .top {
    width: 3.05rem;
    /* height: 0.53rem; */
    margin: 0 auto 1.5rem;
  }
.top1 {
  width: 3.05rem;
  /* height: 0.53rem; */
  margin: 0 auto 0.48rem;
}

  .result-title {
    width: 4rem;
    margin: 0 auto;
  }

  .secondaryPassHead{
    margin: 0.89rem auto 0.47rem;
    text-align: center;
    font-size: 0.3rem;
    letter-spacing: 0.02rem;
    color: #9d5322;
    font-weight: 600;
  }

  .secondaryNewOldHead{
    margin: 1.39rem auto 0.58rem;
    text-align: center;
    font-size: 0.3rem;
    letter-spacing: 0.02rem;
    color: #9d5322;
  }

  .result-img-bg{
    width: 6.36rem;
    height: 6.36rem;
    background: url(../assets/img/banner-bg.png) no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
  }
  .result-banner {
    width: 6.36rem;
    margin: 0.4rem auto 0;
  }
.result-banner1 {
  width: 6.36rem;
  margin: 1.2rem auto 0;
}
  .result-btn {
    height: 0.86rem;
    margin: 1.3rem auto 0;
  }
  .bottom{
    margin: 3rem auto 0;
    width: 7.2rem;
  }

</style>
