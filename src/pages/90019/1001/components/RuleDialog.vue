<template>
  <!-- 活动规则弹窗 -->
  <div class="box">
    <!-- 规则 -->
    <div class="rule-view">
      <div class="rule" v-html="rule"></div>
    </div>
    <!-- 关闭按钮 -->
    <div class="close-btn"
         @click="closeDialog()"></div>
  </div>
</template>

<script lang='ts' setup>
import { defineEmits, defineProps, inject } from 'vue';

const emit = defineEmits(['closeDialog']);
const props = defineProps({ rule: String });
const decoData = inject('decoData', {}) as any;

const closeDialog = () => {
  emit('closeDialog', 'ruleDialog');
};
</script>

<style lang='scss' scoped>
.box {
  align-items: center;
  justify-content: center;
  width: 6.36rem;

  img {
    margin: 0 auto;
  }

  .popup {
    width: 6rem;
  }

  .rule-view {
    margin: 0 auto;
    position: relative;
    padding: 1.2rem 0.7rem 0.7rem;
    box-sizing: border-box;
    background-image: url(../assets/img/dialog-rule.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: 1;
    width: 6.36rem;
    height: 9.49rem;

    .rule {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      font-size: .26rem;
      text-align: left;
      white-space: break-spaces;
      color: #333;
    }
  }

  .close-btn {
    width: 0.6rem;
    height: 0.6rem;
    background: url(../assets/img/dialog-close-icon.png) no-repeat;
    background-size: contain;
    cursor: pointer;
    z-index: 10;
    margin: 0 auto;
  }
}
</style>
