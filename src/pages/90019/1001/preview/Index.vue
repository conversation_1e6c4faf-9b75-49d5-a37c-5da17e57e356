<template>
  <!-- background -->
  <div class="home pageBg" :style="furnishStyles.pageBg.value" >
    <!-- 活动规则按钮 -->
    <div class="handle-side-btn" @click="showRule = true"></div>
    <!-- 我的奖品按钮 -->
    <div class="handle-side-btn" style="top: 6.5rem" @click="showMyPrize = true"></div>
    <!-- 手机号验证码信息收集 -->
    <div class="phone-info-view">
      <div class="phone-input">
        <input type="tel" maxlength="11" placeholder="请输入您的手机号码">
      </div>
      <div class="phone-input">
        <input type="tel" maxlength="6" placeholder="请输入短信验证码">
        <div class="send-verification-btn" :class="{gray:false}" @click="toast()">
          发送验证码
        </div>
      </div>
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/255901/2/29262/25125/67c7f149F079238d6/88d90b2ddd0eb96e.png"
           @click="toast()" class="commit-check-btn" alt="">
    </div>
    <div class="step-view">
      <img :src="furnish.stepImg" style="width: 7.18rem;margin: 0 auto" alt="">
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="showRule" :close-on-click-overlay="false">
    <RuleDialog @closeDialog="showRule = false" :rule="rule"/>
    </VanPopup>
  <VanPopup teleport="body" v-model:show="showMyPrize" :close-on-click-overlay="false">
    <MyRecordDialog @closeDialog="showMyPrize = false"/>
  </VanPopup>
</template>
<script setup lang="ts">
import { ref, inject, onMounted } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useCountdown from '@/hooks/useCountdown';
import { showToast } from 'vant';
import { dialogName } from '../ts/dialog';
import RuleDialog from '../components/RuleDialog.vue';
import MyRecordDialog from '../components/MyRecordDialog.vue';

const activityData = inject('activityData') as any;

const isPreview = ref(false);
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();
// 默认设置结束时间戳为1小时后
const endTime = ref(dayjs().add(30, 'day').valueOf());
const countdownTime = useCountdown(endTime);

const isLoadingFinish = ref(true);

const isExposure = ref(0);

const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const rule = ref('');
const showMyPrize = ref(false);

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  console.log(data);
  // const efficientPrizeList = data.ladderList.filter((e: { prizeType: number }) => e.prizeType);
  // if (efficientPrizeList.length) {
  //   ladderInfoList.value = efficientPrizeList;
  //   nextPotCount.value = ladderInfoList.value[0].nextPotCount || 0;
  // } else {
  //   ladderInfoList.value = defaultStateList;
  // }
  isExposure.value = data.isExposure;
  rule.value = data.rules;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    rule.value = activityData.rules;
    endTime.value = dayjs(activityData.endTime).valueOf();
    shopName.value = activityData.shopName;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style scoped lang="scss">
.pageBg {
  background-size: 100%;
  min-height:100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.2rem;
}
.header-kv{
  padding: 0 0 11rem 0;
  .btnAllClass{
    margin-top: 4rem;
    .btnClass{
      width: 0.61rem;
      height: 1.61rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.5rem 0 0 0;
      background-color: rgba(154, 79, 79, 0.41);
    }
  }
}
.stepView{
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 7.18rem;
  height: 9.09rem;
  margin: 0 auto;
  padding: .35rem 0 0 0;
}
.home {
  width: 100%; /* 宽度自适应 */
  min-height: 25.8rem;
  padding-top: 11.86rem;
  position: relative;
  //background-image: url('../assets/img/home-kv.jpg'), /* 上方图片 */
  //url('../assets/img/home-bg.jpg'); /* 下方图片 */
  background-position: top left, bottom left;
  background-size: contain, 100% 100%; /* 强制等比缩放填满容器 */
  background-repeat: no-repeat;

  .handle-side-btn {
    width: 0.6rem;
    height: 1.8rem;
    position: absolute;
    right: 0;
    top: 4.4rem;
  }

  .phone-info-view {
    width: 7.18rem;
    height: 2.9rem;
    margin: 0rem auto 0;
    padding: .35rem 0;
    text-align: center;
    background: {
      image: url("../assets/img/phone-info-view.png");
      repeat: no-repeat;
      size: contain;
    };

    .phone-input {
      width: 4rem;
      height: .6rem;
      line-height: .56rem;
      padding-left: .15rem;
      margin: 0 auto .2rem;
      border: 1px solid #1d2e81;
      border-radius: .5rem;
      position: relative;

      input {
        width: 3.8rem;
        font-size: .28rem;
        line-height: .56rem;
        color: #434343;
        letter-spacing: .01rem;
        background-color: transparent !important;
        border: transparent !important;
      }

      input::placeholder {
        color: #999; /* 占位文字颜色 */
        font-size: .24rem; /* 字体大小 */
        letter-spacing: -.01rem;
      }

      .send-verification-btn {
        width: 1.7rem;
        height: .587rem;
        line-height: .61rem;
        text-align: center;
        font-size: .26rem;
        color: #FFFFFF;
        position: absolute;
        right: -.01rem;
        top: -.01rem;
        cursor: pointer;
        background: {
          image: url("//img10.360buyimg.com/imgzone/jfs/t1/254993/26/29046/1831/67c7eb98F7e1c6b00/f7a7f44f7518d57f.png");
          size: contain;
          repeat: no-repeat;
        };
      }
    }

    .commit-check-btn {
      width: 3.04rem;
      margin: .1rem auto 0;
    }
  }

  .step-view {
    text-align: center;
    margin-top: .3rem;
  }
}
</style>
