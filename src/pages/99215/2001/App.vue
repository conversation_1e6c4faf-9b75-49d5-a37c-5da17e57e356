<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="kv">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="btn-list">
        <img :src="furnish.ruleBtn" alt="" @click="rulePop = true" />
        <img :src="furnish.myPrizeBtn" alt="" @click="myPrizePop = true" />
      </div>
      <div class="main-box">
        <div class="big-btn btn-common" :style="furnishStyles.bigBtn.value" @click="inviteFriend">立即邀请</div>
      </div>
    </div>
    <div class="prize-box" :style="furnishStyles.prizeBox.value" v-if="taskType === 0">
      <div class="tip-text" v-if="isInvited">{{ hasAssistNick }}给你赠送了超值的优惠券</div>
      <div class="tip-text" v-else>邀请好友领券，可得奖励</div>

      <div class="scroll-box">
        <div class="content">
          <div class="item" v-for="item in couponList">
            <div class="show-img" :style="furnishStyles.prizeBg.value">
              <img :src="item.img" alt="" />
            </div>
            <div class="name-box">
              <div class="name">{{ item.prizeName }}</div>
            </div>
            <div class="btn gary" :style="furnishStyles.getCouponBtn.value" v-if="item.status !== 1" @click="getCoupon(item)">立即领券</div>
            <div class="btn" :style="furnishStyles.getCouponBtn.value" v-else @click="getCoupon(item)">立即领券</div>
          </div>
        </div>
      </div>
    </div>
    <div class="prize-box" :style="furnishStyles.prizeBox.value" v-else-if="taskType === 1">
      <div class="tip-text" v-if="isInvited">{{ hasAssistNick }}觉得这些产品不错，邀请你购买</div>
      <div class="tip-text" v-else>邀请好友成功体验完整服务，即可获得奖励</div>

      <div class="scroll-box">
        <div class="content">
          <div class="item" v-for="item in skuList">
            <div class="show-img" :style="furnishStyles.prizeBg.value">
              <img :src="item.skuMainPicture" alt="" @click="toDaojiaSku(item.skuId)" />
            </div>
            <div class="name-box">
              <div class="name" @click="toDaojiaSku(item.skuId)">{{ item.skuName }}</div>
            </div>
            <div class="bnts">
              <div class="btn-buy" :style="furnishStyles.bugBtn.value" @click="toDaojiaSku(item.skuId)">购买</div>
              <div class="btn-share" :style="furnishStyles.shareBtn.value" @click="shareSku(item.skuId)">分享</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="rule-img">
      <img :src="furnish.ruleImg" alt="" />
    </div>
    <div class="share-box" :style="furnishStyles.shareBox.value">
      <!-- <div class="tip-text">
        <p>APP内参与邀请好友，体验到家服务</p>
        <p v-if="prizeType === 0 || prizeType === 2">
          即刻获得<span>{{ prizeList.find((item) => item.type === 0)?.prizeName }}</span
          >奖励
        </p>
      </div> -->
      <div class="big-btn" :style="furnishStyles.bigBtn.value" @click="inviteFriendUseImage">分享海报邀友体验</div>
    </div>
    <div class="inviter-box" :style="furnishStyles.inviterBox.value">
      <div class="big-btn" :class="{ gary: !canGetPrize }" :style="furnishStyles.bigBtn.value" @click="getPrize">
        领取奖励
        <img v-if="canGetPrize" src="//img10.360buyimg.com/imgzone/jfs/t1/328999/3/24295/2248/68db3d4fFbd43166a/d39239acf80e9c29.png" alt="" class="icon" />
      </div>
      <div class="time-tip">应风控管理要求，所得京豆每月10日/20日/30日发放奖励</div>
      <div class="num-box">
        <div class="item">
          <div class="num">{{ friendListNum.total }}</div>
          <div class="text">
            当前<br />
            邀请人数
          </div>
        </div>
        <div class="item">
          <div class="num">{{ friendListNum.notComplete }}</div>
          <div class="text">
            尚未完成助力<br />
            任务人数
          </div>
        </div>
        <div class="item">
          <div class="num">{{ friendListNum.complete }}</div>
          <div class="text">
            完成<br />
            助力人数
          </div>
        </div>
      </div>
      <div class="title">
        <div>昵称</div>
        <div>邀请时间</div>
        <div>状态</div>
      </div>
      <div class="scroll-box">
        <div class="item" v-for="item in friendList">
          <div class="nick-name">{{ item.nickName }}</div>
          <div class="time">{{ dayjs(item.createTime).format('MM.DD') }}</div>
          <div class="status">{{ friendStatusMap[item.status] }}</div>
        </div>
        <div class="no-data" v-if="friendList.length === 0">暂无数据</div>
      </div>
    </div>
  </div>

  <div class="to-jd-app" v-if="toJdAppPop" @click="toJdAppPop = false">
    <img src="//img10.360buyimg.com/imgzone/jfs/t1/344022/2/8255/15195/68d8f544Fc0a2ec85/43a2cdaac556e880.png" alt="" class="tip-img" />
  </div>
  <ShareImage ref="shareImageRef" />
  <VanPopup v-model:show="thresholdPop" teleport="body">
    <Threshold @close="thresholdPop = false" :threshold-type="thresholdType" />
  </VanPopup>
  <VanPopup v-model:show="rulePop" teleport="body">
    <Rule @close="rulePop = false" />
  </VanPopup>
  <VanPopup v-model:show="myPrizePop" teleport="body">
    <MyPrize v-if="myPrizePop" @close="myPrizePop = false" @saveAddress="saveAddress" />
  </VanPopup>
  <VanPopup v-model:show="confirmAssistPop" teleport="body">
    <ConfirmAssist @close="confirmAssistPop = false" @success="assistSuccess" :assist-nick="assistNick" />
  </VanPopup>
  <VanPopup v-model:show="awardPop" teleport="body">
    <Award @close="awardPop = false" :prize-info="prizeInfo" @saveAddress="saveAddress" />
  </VanPopup>
  <VanPopup v-model:show="saveAddressPop" teleport="body">
    <SaveAddress v-if="saveAddressPop" @close="saveAddressPop = false" :userPrizeId="prizeInfo.userPrizeId" :addressId="prizeInfo.addressId" />
  </VanPopup>
</template>

<script lang="ts" setup>
import { DecoData } from '@/types/DecoData';
import furnishStyles, { furnish } from './ts/furnishStyles';
import { computed, inject, ref } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Rule from './components/Rule.vue';
import MyPrize from './components/MyPrize.vue';
import Threshold from './components/Threshold.vue';
import Award from './components/Award.vue';
import ConfirmAssist from './components/ConfirmAssist.vue';
import SaveAddress from './components/SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { callShare } from '@/utils/platforms/share';
import dayjs from 'dayjs';
import ShareImage from './components/ShareImage.vue';

const toDaojiaSku = (skuId: string) => {
  window.location.href = `https://item.jd.com/${skuId}.html`;
};

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;

const rulePop = ref(false);
const myPrizePop = ref(false);
const thresholdPop = ref(false);
const saveAddressPop = ref(false);
const confirmAssistPop = ref(false);
const awardPop = ref(false);
const toJdAppPop = ref(false);

const canJoin = ref(false);
const taskType = ref(0); // 任务类型0-领券1-活动期间内下单并完成
const prizeType = ref(0); // 奖励对象0邀请人1被邀请人2邀请人&被邀请人
const orderLimit = ref(0); // 订单限制0无门槛1店铺新客（365天内无订单）2店铺老客（365天内有订单）
const canGetPrize = ref(false);
// 是否是受邀人
const isInvited = ref(false);

const prizeList = ref<any[]>([]);
const couponList = ref<any[]>([]);
const friendList = ref<any[]>([]);

const friendListNum = computed(() => {
  return {
    total: friendList.value.length,
    notComplete: friendList.value.filter((item) => item.status !== 1).length,
    complete: friendList.value.filter((item) => item.status === 1).length,
  };
});

const thresholdType = ref(0);
const checkThreshold = () => {
  if (baseInfo.status === 1) {
    showToast('活动未开始');
    return true;
  }
  if (baseInfo.status === 3) {
    showToast('活动已结束');
    return true;
  }
  if (baseInfo.thresholdResponseList?.find((item) => item.thresholdCode === 5)) {
    thresholdType.value = 2;
    thresholdPop.value = true;
    return true;
  }
  if (!canJoin.value) {
    if (orderLimit.value === 1) {
      thresholdType.value = 0;
    } else if (orderLimit.value === 2) {
      thresholdType.value = 1;
    }
    thresholdPop.value = true;
    return true;
  }
  return false;
};

const canAssist = ref(false);
const assistNick = ref('');
const hasAssistNick = ref(false);
const getActData = async () => {
  try {
    const { data } = await httpRequest.post('/99215/init', {
      shareId: pathParams?.isInvite ? pathParams?.shareId || '' : '',
    });
    taskType.value = data.taskType;
    prizeType.value = data.prizeType;
    isInvited.value = data.peopleStatus === 1;
    canAssist.value = data.canAssist;
    assistNick.value = data.assistNick;
    hasAssistNick.value = data.hasAssistNick;
    canJoin.value = data.canJoin;
    orderLimit.value = data.orderLimit;
    prizeList.value = data.prizeResponses || [];
    couponList.value = data.couponPrizeResponses || [];
    canGetPrize.value = data.prizeResponses.findIndex((item: any) => item.status === 1) !== -1;
  } catch (error) {}
};

const skuList = ref<any[]>([]);
const getSkus = async () => {
  try {
    const { data } = await httpRequest.post('/99215/getSkus', {
      pageNum: 1,
      pageSize: 40,
    });
    skuList.value = data.records;
  } catch (error) {}
};

const getInviteList = async () => {
  try {
    const { data } = await httpRequest.post('/99215/inviteList', {});
    friendList.value = data.records;
  } catch (error) {}
};
const friendStatusMap = computed(() => {
  if (taskType.value === 0) {
    return {
      0: '未领券',
      1: '已领券',
      2: '付款',
      3: '已取消',
    };
  }
  return {
    0: '未下单',
    1: '已完成',
    2: '已付款',
    3: '已取消',
  };
});

const assistSuccess = () => {
  confirmAssistPop.value = false;
  getActData();
};

const prizeInfo = ref<any>({});

const getCoupon = async (item: any) => {
  if (checkThreshold()) {
    return;
  }
  if (item.status === 3) {
    showToast('已领取');
    return;
  }
  if (item.status === 2 && !isInvited.value) {
    showToast('仅被邀请人可领取优惠券');
    return;
  }
  if (item.status !== 1) return;
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/99215/sendPrize', {
      prizeId: item.prizeId,
    });
    if (Array.isArray(data)) {
      prizeInfo.value = data[0];
    } else {
      prizeInfo.value = data;
    }
    prizeInfo.value.fromType = 'coupon';
    awardPop.value = true;
    closeToast();
    getActData();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

const getPrize = async () => {
  if (checkThreshold()) {
    return;
  }
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const item = prizeList.value.find((item) => item.status === 1);
    if (!item) {
      showToast('暂无可领取的奖励');
      return;
    }
    const { data } = await httpRequest.post('/99215/sendPrize', {
      prizeId: item.prizeId,
    });
    if (Array.isArray(data)) {
      prizeInfo.value = data[0];
    } else {
      prizeInfo.value = data;
    }
    prizeInfo.value.fromType = 'prize';
    awardPop.value = true;
    closeToast();
    getActData();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

const saveAddress = async (params: { userPrizeId: string; addressId: string }) => {
  console.log('saveAddress', params);
  awardPop.value = false;
  myPrizePop.value = false;
  prizeInfo.value = params;
  saveAddressPop.value = true;
};

// 邀请好友
const inviteFriend = async () => {
  if (checkThreshold()) {
    return;
  }
  if (window.jmfe.isApp('wx')) {
    toJdAppPop.value = true;
    return;
  }
  if (window.jmfe.isApp('jd')) {
    const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
    callShare({
      title: shareConfig.shareTitle,
      content: shareConfig.shareContent,
      imageUrl: shareConfig.shareImage,
      shareUrl: `${process.env.VUE_APP_HOST}99215/2001/?shopId=${baseInfo?.shopId}&activityMainId=${baseInfo?.activityMainId}&shareId=${shareConfig?.shareId}&isInvite=true`,
      afterShare: () => {},
    });
  }
};

// 分享海报邀友体验
const shareImageRef = ref();
const inviteFriendUseImage = async () => {
  if (checkThreshold()) {
    return;
  }
  await shareImageRef.value?.createShareCard();
};

const shareSku = (skuId: string) => {
  if (checkThreshold()) {
    return;
  }
  const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
    shareUrl: `${process.env.VUE_APP_HOST}99215/2001/?shopId=${baseInfo?.shopId}&activityMainId=${baseInfo?.activityMainId}&skuId=${skuId}&shareId=${shareConfig?.shareId}`,
    afterShare: () => {},
  });
};

const refreshOrder = async () => {
  try {
    await httpRequest.post('/99215/refreshOrder');
  } catch (error) {}
};
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  if (pathParams?.skuId) {
    toDaojiaSku(pathParams.skuId);
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await getActData();
    getInviteList();
    refreshOrder();
    if (taskType.value === 1) {
      await getSkus();
    }
    closeToast();
    if (!checkThreshold() && canAssist.value && pathParams?.isInvite) {
      confirmAssistPop.value = true;
    }
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
};

init();
</script>
<style lang="scss">
@font-face {
  font-family: 'JDLangZhengTi';
  src: url('https://lzcdn.dianpusoft.cn/fonts/JDLangZhengTi/JDLANGZHENGTI-B--GBK1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/JDLangZhengTi/JDLANGZHENGTI-B--GBK1-0.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'SourceHanSansCN';
  src: url('https://lzcdn.dianpusoft.cn/fonts/SourceHanSansCN/SourceHanSansCN-Regular.otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'SourceHanSansCN';
}
/* 隐藏所有滚动条 */
::-webkit-scrollbar {
  display: none;
}
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}
.kv {
  position: relative;
  margin-bottom: 0.3rem;
  .kv-img {
    width: 100%;
  }
  .btn-list {
    position: absolute;
    top: 2.18rem;
    right: 0;
    img {
      width: 1.16rem;
      margin-bottom: 0.2rem;
    }
  }
  .main-box {
    position: absolute;
    bottom: 0.6rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    .btn-common {
      background-size: 100%;
      background-repeat: no-repeat;
      font-size: 0.42rem;
      text-align: center;
      font-family: 'JDLangZhengTi';
      text-shadow: 0.024rem 0.032rem 0.1rem rgba(164, 138, 44, 0.67);
    }
    .big-btn {
      width: 4rem;
      height: 0.9rem;
      line-height: 0.9rem;
    }
    .small-btn {
      width: 2.84rem;
      height: 0.9rem;
      line-height: 0.9rem;
      &:first-child {
        margin-right: 0.37rem;
      }
    }
  }
}
.prize-box {
  width: 7rem;
  height: 5.9rem;
  background-size: 100%;
  background-repeat: no-repeat;
  margin: 0 auto 0.3rem;
  padding-top: 1.34rem;
  .tip-text {
    text-align: center;
    font-size: 0.28rem;
    line-height: 0.28rem;
    color: #333;
    margin-bottom: 0.2rem;
  }
  .scroll-box {
    width: 100%;
    overflow-x: auto;
    .content {
      width: max-content;
      min-width: 100%;
      display: flex;
      justify-content: space-evenly;
      .item {
        width: 2.5rem;
        margin: 0 0.1rem;
        .show-img {
          width: 2.5rem;
          height: 2.5rem;
          background-size: 100%;
          background-repeat: no-repeat;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            width: 2.12rem;
            height: 2.12rem;
            border-radius: 0.1rem;
          }
        }
        .name-box {
          width: 2.12rem;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 0.46rem;
          margin: 0.1rem auto;
          .name {
            font-size: 0.2rem;
            line-height: 0.23rem;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
        .btn {
          font-size: 0.276rem;
          line-height: 0.47rem;
          text-align: center;
          width: 1.69rem;
          height: 0.47rem;
          border-radius: 0.235rem;
          background-size: 100%;
          background-repeat: no-repeat;
          margin: 0 auto;
          font-family: 'JDLangZhengTi';
        }
        .bnts {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 0.18rem;
          .btn-buy,
          .btn-share {
            margin-bottom: 0.08rem;
            font-family: 'JDLangZhengTi';
            font-size: 0.276rem;
            line-height: 0.47rem;
            width: 1rem;
            height: 0.47rem;
            color: #fff;
            border-radius: 0.235rem;
            text-align: center;
          }
          .btn-buy {
            background-color: #e10301;
          }
          .btn-share {
            background-color: #10c168;
          }
        }
      }
    }
  }
}
.rule-img {
  width: 7rem;
  margin: 0 auto 0.3rem;
  img {
    width: 100%;
  }
}
.share-box {
  width: 7rem;
  height: 3.07rem;
  margin: 0 auto 0.3rem;
  background-size: 100%;
  background-repeat: no-repeat;
  padding-top: 1.84rem;
  .tip-text {
    text-align: center;
    font-size: 0.28rem;
    line-height: 0.32rem;
    color: #333;
    margin-bottom: 0.2rem;
    height: 0.64rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .big-btn {
    width: 3.1rem;
    height: 0.7rem;
    line-height: 0.7rem;
    margin: 0 auto;
    background-size: 100%;
    background-repeat: no-repeat;
    font-size: 0.36rem;
    text-align: center;
    font-family: 'JDLangZhengTi';
    text-shadow: 0.024rem 0.032rem 0.1rem rgba(164, 138, 44, 0.67);
  }
}
.inviter-box {
  width: 7rem;
  height: 8.9rem;
  background-size: 100%;
  background-repeat: no-repeat;
  margin: 0 auto;
  padding-top: 0.98rem;
  .big-btn {
    width: 3.1rem;
    height: 0.7rem;
    line-height: 0.7rem;
    margin: 0 auto;
    background-size: 100%;
    background-repeat: no-repeat;
    font-size: 0.36rem;
    text-align: center;
    font-family: 'JDLangZhengTi';
    text-shadow: 0.024rem 0.032rem 0.1rem rgba(164, 138, 44, 0.67);
    position: relative;
    .icon {
      position: absolute;
      top: -0.1rem;
      right: -1rem;
      width: 1.24rem;
    }
  }
  .time-tip {
    font-size: 0.18rem;
    line-height: 0.18rem;
    color: #333;
    text-align: center;
    margin: 0.05rem 0;
  }
  .num-box {
    display: flex;
    justify-content: space-between;
    padding: 0 0.2rem;
    // margin-top: 0.24rem;
    .item {
      width: 2.15rem;
      height: 1.5rem;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      .num {
        font-size: 0.56rem;
        line-height: 0.56rem;
        color: #24a570;
      }
      .text {
        font-size: 0.24rem;
        line-height: 0.26rem;
      }
    }
  }
  .title {
    height: 0.7rem;
    font-size: 0.28rem;
    line-height: 0.7rem;
    color: #333;
    text-align: center;
    display: flex;
    align-items: center;
    div {
      width: 33.33%;
    }
  }
  .scroll-box {
    width: 100%;
    height: 4.74rem;
    overflow-y: auto;
    padding: 0 0.1rem;
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.15rem 0;
      div {
        width: 33.33%;
        text-align: center;
        color: #333;
        word-break: break-all;
      }
    }
    .no-data {
      text-align: center;
      line-height: 4.74rem;
      font-size: 0.24rem;
      color: #333;
    }
  }
}
.to-jd-app {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 2000;
  .tip-img {
    position: absolute;
    top: 0.2rem;
    right: 0.2rem;
    width: 4rem;
  }
}
.gary {
  filter: grayscale(1);
}
</style>
