import { computed, reactive } from 'vue';

export const furnish = reactive({
  actBg: '',
  pageBg: '',
  actBgColor: '',
  followShopBtn: '',
  makeCommentBtn: '',
  drawBtn: '',
  rules: '',
  myPrizeBtn: '',
  joinMemberBg: '',
  notWinningBg: '',
  myPrizeBg: '',
  addressBg: '',
  logisticsBg: '',

});
const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));
const myPrizeBg = computed(() => ({
  backgroundImage: furnish.myPrizeBg ? `url("${furnish.myPrizeBg}")` : '',
}));
const joinMemberBg = computed(() => ({
  backgroundImage: furnish.joinMemberBg ? `url("${furnish.joinMemberBg}")` : '',
}));
const notWinningBg = computed(() => ({
  backgroundImage: furnish.notWinningBg ? `url("${furnish.notWinningBg}")` : '',
}));
const addressBg = computed(() => ({
  backgroundImage: furnish.addressBg ? `url("${furnish.addressBg}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/174321/29/46488/240935/67088571F0c43c20f/66585757810a1ff0.png")',
}));
const logisticsBg = computed(() => ({
  backgroundImage: furnish.logisticsBg ? `url("${furnish.logisticsBg}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/157779/18/49452/237687/67089e10Fda1a0df5/e9c4ba228461fa2a.png")',
}));
export default {
  pageBg,
  myPrizeBg,
  joinMemberBg,
  notWinningBg,
  addressBg,
  logisticsBg,

};
