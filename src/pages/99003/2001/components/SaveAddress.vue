<template>
  <div class="rule-bk">
    <div class="addressBg" :style="furnishStyles.addressBg.value">
      <div class="content">
        <div class="form">
          <div class="cellItem">
            <div class="label">收<span></span>件<span></span>人</div>
            <input type="text" maxlength="20" v-model="form.realName" />
          </div>
          <div class="cellItem">
            <div class="label">收件电话</div>
            <input type="text" maxlength="11" @input="validateInput" v-model="form.mobile" />
          </div>
          <div class="cellItem">
            <div class="label">收件地址</div>
            <input type="text" v-model="addressCode" readonly @click="addressSelects = true" />
          </div>
          <div class="cellItem">
            <div class="label">地址详情</div>
            <input maxlength="500" type="text" v-model="form.address" />
          </div>
          <div class="cellItem">
            <div class="label">邮<span></span>编</div>
            <input maxlength="6" type="text" @input="validatePostCode" v-model="form.postalCode" />
          </div>
        </div>
        <div class="warn">活动期间若未填写地址，则视为放弃中奖资格</div>
        <div class="submit" @click="checkForm"></div>
      </div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { closeToast, showLoadingToast, showToast } from 'vant';
import { computed, reactive, ref } from 'vue';
import { areaList } from '@vant/area-data';
import { httpRequest } from '@/utils/service';
import furnishStyles from '../ts/furnishStyles';

const props = defineProps({
  addressId: {
    type: String,
    required: true,
  },
  userPrizeId: {
    type: String,
    required: true,
  },
});
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const addressSelects = ref(false);

const form = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
  postalCode: '',
});

const validateInput = (event) => {
  const { value } = event.target;
  form.mobile = value.replace(/[^0-9]/g, '');
};
const validatePostCode = (event) => {
  const { value } = event.target;
  form.postalCode = value.replace(/[^0-9]/g, '');
};
const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/99003/userAddressInfo', {
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
      postalCode: form.postalCode,
      userPrizeId: props.userPrizeId,
      addressId: props.addressId,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的电话');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else if (!form.postalCode) {
    showToast('请输入邮编');
  } else if (form.postalCode.length !== 6) {
    showToast('请输入正确的邮编');
  } else {
    submit();
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 100vw;
  margin: 0;
  padding: 0;
  .addressBg {
    width: 6.85rem;
    background-repeat: no-repeat;
    height: 9rem;
    background-size: contain;
    box-sizing: border-box;
    position: relative;
  }

  .content {
    padding: 1.8rem 0 0;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .form {
      margin: 0 auto;
      text-align: center;
      padding: 0 0 0.22rem 0;
      font-size: 0.24rem;
      .cellItem {
        width: 6rem;
        margin: 0 auto 0.3rem;
        display: flex;
        align-items: center;
        justify-content: space-around;
        .label {
          font-weight: 700;
          font-size: 0.24rem;
          display: flex;
          width: 1rem;
          justify-content: space-between;
        }
        input {
          border: 1px solid #f5f5f5;
          border-radius: 0.1rem;
          outline: none;
          padding: 0;
          margin: 0;
          width: 4.2rem;
          height: 0.6rem;
          text-align: left;
          text-indent: 0.2rem;
          :hover {
            cursor: pointer;
          }
        }
      }
      .van-cell {
        border-radius: 0.08rem;
        margin-bottom: 0.1rem;
        background-color: rgba(255, 255, 255, 0);

        &::after {
          display: none;
        }
      }
    }
    .warn {
      color: #c0c2c3;
      text-align: center;
      font-size: 0.2rem;
      margin-top: -0.3rem;
    }
    .submit {
      margin: 1.4rem auto 0;
      width: 3rem;
      height: 0.6rem;
    }
  }
}
</style>
