<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg"  alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name"></div>
        <div>
          <img class="header-btn" :src="furnish.giftBtn" alt="" @click="showMyPrize = true" />
        </div>
      </div>
    </div>
    <div class="select-hover">
      <div class="wheel">
        <lucky-wheel
          ref="myLucky"
          width="78vw"
          height="78vw"
          :blocks="furnishStyles.params.value.blocks"
          :prizes="furnishStyles.params.value.prizes"
          :buttons="furnishStyles.params.value.buttons"
          @start="startCallback"
          @end="endCallback"
          :defaultConfig="furnishStyles.params.value.defaultConfig"
        />
      </div>
      <div class="draws-num" :style="furnishStyles.drawsNum.value">
        你还有<span :style="furnishStyles.drawNumSpan.value">{{ 0 }}次</span>抽奖机会
      </div>
      <div class="draw-btn" @click="startCallback">
        <img :src="furnish.drawBtn" alt="" />
      </div>
    </div>
    <div>
      <img class="rule-title" :src="furnish.ruleTitle" alt="" />
      <div class="rule-content" :style="furnishStyles.ruleBg.value"><div v-html="ruleTest"></div></div>
    </div>
  </div>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="center">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const isLoadingFinish = ref(false);
const ruleTest = ref('');
const showMyPrize = ref(false);

// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

const myLucky = ref();
const startCallback = () => {
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  // 模拟调用接口异步抽奖
  setTimeout(() => {
    // 假设后端返回的中奖索引是0
    const index = Math.floor(Math.random() * 8);
    const _award = prizeInfo[index];
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    // 调用stop停止旋转并传递中奖索引
    myLucky.value.stop(index);
  }, 2000);
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
  } else if (type === 'activity') {
    ruleTest.value = data.rules;
    if (data.prizeList.length) {
      prizeInfo.splice(0);
      prizeInfo.push(...data.prizeList);
    }
    console.log('c', data);
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    prizeInfo.splice(0);
    prizeInfo.push(...activityData.prizeList);
    ruleTest.value = activityData.rules;
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});

</script>

<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.2rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding-top: 1.5rem;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.71rem;
  }
}
.danmaku {
  width: 6.2rem;
  height: 1rem;
  margin: 0 auto;
}

.wheel {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .wheel-img {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    object-fit: contain;
  }
}

.draws-num {
  width: 3.43rem;
  height: 0.54rem;
  text-align: center;
  font-size: 0.3rem;
  line-height: 0.54rem;
  margin: 0.2rem auto 0;
  border-radius: 0.266rem;
}

.draw-btn {
  width: 3.15rem;
  margin: 0.11rem auto 0;

  img {
    width: 100%;
  }
}

.rule-title {
  height: 0.42rem;
  margin: 0.3rem auto 0;
}

.rule-content {
  width: 6.53rem;
  height: 3.57rem;
  margin: 0.14rem auto 0;
  padding: 0.2rem 0;
  div {
    padding: 0 0.2rem;
    height: 100%;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    word-break: break-word;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
