import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import index from './App.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin, { EventTrackParams } from '@/plugins/EventTracking';
import '@/style';
import './style';
import { httpRequest } from '@/utils/service';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  disableShare: true,
};

const getDecoData = async () => {
  try {
    // const { data } = await httpRequest.post('common/getActivityConfig');
    // app.provide('decoData', JSON.parse(data));

    const decoData = {
      actBg: '//img10.360buyimg.com/imgzone/jfs/t1/90911/17/45531/101343/66b46273F3e2dd13c/0390b04e07f2cf83.png',
      pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/26037/16/21910/569940/66b46074F56640e70/0b5ff04b88ef7794.png',
      actBgColor: '#fff',
      giftBtn: '//img10.360buyimg.com/imgzone/jfs/t1/6612/22/31542/5082/66b46075Fd2b3fdfe/e214b6d294abb32d.png',
      wheelType: '2',
      wheelBg: '//img10.360buyimg.com/imgzone/jfs/t1/234332/1/5398/62748/65697c18F023c731f/0cbabfc90d60f38b.png',
      wheelPanel: '//img10.360buyimg.com/imgzone/jfs/t1/81278/15/27075/163380/66b57f52F60a26b4f/7deac2239ca37015.png',
      wheelBtn: '//img10.360buyimg.com/imgzone/jfs/t1/56715/40/25938/10603/66b46075F535edc57/578a77bfa862a9ab.png',
      drawBtn: '//img10.360buyimg.com/imgzone/jfs/t1/17611/23/22573/7476/66b46075F6ce2844a/05ee30438497fef1.png',
      wheelTextColor: '#000000',
      drawNumBgColor: '#2d801c',
      drawsNum: '#fff',
      drawNumSpan: '#fbf81a',
      ruleTitle: '//img10.360buyimg.com/imgzone/jfs/t1/3170/17/24914/2077/66b46075F2bc2f5f3/848db4668f1f0e79.png',
      ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/231718/4/24990/152/66b46075Fd0ed3d3e/b55f23a40670729b.png',
    };
    app.provide('decoData', decoData);
  } catch (error: any) {
    console.error(error);
  }
};

init(config).then(async ({ baseInfo, pathParams }) => {
  // 设置页面title
  document.title = baseInfo?.activityName || '下单抽奖';
  app.provide('baseInfo', baseInfo);
  await getDecoData();
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
