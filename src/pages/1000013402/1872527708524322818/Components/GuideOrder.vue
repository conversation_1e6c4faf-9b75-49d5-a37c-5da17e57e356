<template>
  <div class="main">
    <div class="order-time-title">
      <svg class="step-svg" viewBox="0 0 300 50">
        <path id="arcPath" fill="transparent" d="M10,45 Q150,15 290,45" />
        <text fill="#f21332">
          <textPath href="#arcPath" startOffset="50%" text-anchor="middle">下单时间：{{ dayjs(activityInfo.orderStartTime).format('MM月DD日') }}-{{ dayjs(activityInfo.orderEndTime).format('MM月DD日') }}</textPath>
        </text>
      </svg>
    </div>
    <div class="free-user">
      <div class="swiper-container swiper-freeUser">
        <div class="swiper-wrapper free-wrapper">
          <div class="swiper-slide" v-for="(slide, index) in freeUserList" :key="index">{{ slide }}抽中免单红包</div>
        </div>
      </div>
    </div>
    <!-- <div class="order-time-tip">
        在2025 年 1 月3 日0:00:00 至2025 年 1月 22日23:59:59 期间内，同一笔订单内包含伊利集团（自营）<br />
        和非伊利品牌的其他品牌京东产品（自营），下单并完成收货，可参加免单抽奖活动，<br />
        单笔免单金额上限 5000 元。活动详情可在页面右上角查看活动规则。
      </div> -->
    <div class="to-draw" @click="scrollToAnchor('draw')"></div>
    <div class="sku-co">
      <div id="buyGoods"></div>
      <div class="swiper-container swiper-skuList">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item, index) in showSkuList" :key="index">
            <div class="sku-item" v-for="slide in item" :key="slide.skuId">
              <img @click="gotoSkuPage(slide.skuId)" :src="slide.skuMainPicture" alt="" class="sku-img" />
              <div class="price">¥ {{ slide.jdPrice }}</div>
              <div class="sku-name">{{ slide.skuName }}</div>
              <div class="sku-btn-list">
                <div @click="gotoSkuPage(slide.skuId)">立即抢购 ></div>
                <img src="../assets/cart.png" alt="" @click="addToCart(slide.skuId)" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <img src="../assets/pre.png" alt="" class="pre" @click="skuPre" />
      <img src="../assets/next.png" alt="" class="next" @click="skuNext" />
      <div class="mor-btn">
        <!-- <div @click="addAllSkuToCart"></div> -->
        <div @click="viewMoreYiliSKu"></div>
      </div>
    </div>
    <div class="shop-co">
      <div class="swiper-container swiper-shopList">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(slide, index) in shopList" :key="index">
            <div class="shop-slide-for" v-for="(item, index) in slide" :key="index">
              <img :src="item.shopLogo" alt="" class="shop-img" />
              <div class="to-shop" @click="toShop(item.shopId)"></div>
              <div class="to-sku" @click="gotoSkuPage(item.skuId)"></div>
              <div class="to-cart" @click="addToCart(item.skuId)"></div>
            </div>
          </div>
        </div>
      </div>
      <img src="../assets/pre.png" alt="" class="pre" @click="shopPre" />
      <img src="../assets/next.png" alt="" class="next" @click="shopNext" />
    </div>
    <div class="more-goods" @click="viewMoreGoods"></div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import { inject, onMounted, ref, nextTick } from 'vue';
import { activityInfo, scrollToAnchor } from '../hooks';
import dayjs from 'dayjs';
import { httpRequest } from '@/utils/service';
import { gotoSkuPage, addSkuToCart, gotoShopPage } from '@/utils/platforms/jump';
import { showToast } from 'vant';

Swiper.use([Autoplay]);

const freeUserList = ref<any[]>([]);
const skuList = ref<any[]>([]);
const showSkuList = ref<any[]>([]);
const shopList = ref<any[]>([]);

const addToCart = (skuId: number) => {
  if (!window.jmfe.isApp('jd')) {
    showToast('请在京东APP中进行加购');
    return;
  }
  addSkuToCart(skuId);
};

const addAllSkuToCart = () => {
  const skus = skuList.value.map((it) => it.skuId);
  addSkuToCart(skus);
};

let freeUserSwiper: Swiper;
let shopSwiper: Swiper;
let skuSwiper: Swiper;
const skuPre = () => {
  skuSwiper.slidePrev();
};
const skuNext = () => {
  skuSwiper.slideNext();
};
const shopPre = () => {
  shopSwiper.slidePrev();
};
const shopNext = () => {
  shopSwiper.slideNext();
};

const viewMoreYiliSKu = () => {
  scrollToAnchor('yiliShops');
};

const viewMoreGoods = () => {
  window.jmfe.toUrl({
    url: 'https://m.jd.com',
    openAppParams: {
      category: 'jump',
      des: 'HomePage',
    },
  });
};

const toShop = (shopId: number) => {
  gotoShopPage(shopId);
};

const getFreeUserList = async () => {
  try {
    const { data } = await httpRequest.post('/10112/getFreeUserList');
    freeUserList.value = data;
    nextTick(() => {
      if (freeUserSwiper) {
        freeUserSwiper.destroy();
      }
      freeUserSwiper = new Swiper('.swiper-freeUser', {
        slidesPerView: 1,
        // 竖向滚动
        loop: true,
        autoplay: {
          delay: 0,
          disableOnInteraction: false,
        },
        speed: 4000,
        // 禁止触摸滚动
        allowTouchMove: false,
        centerInsufficientSlides: true,
        observer: true,
        observeParents: true,
        observeSlideChildren: true,
        spaceBetween: 10,
      });
    });
  } catch (error: any) {
    console.error(error);
  }
};
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/10112/showSku');
    skuList.value = data;
    const _skuList = [];
    for (let i = 0; i < data.length; i += 3) {
      _skuList.push(data.slice(i, i + 3));
    }
    showSkuList.value = _skuList;
    nextTick(() => {
      skuSwiper = new Swiper('.swiper-skuList', {
        slidesPerView: 3,
        centerInsufficientSlides: true,
        observer: true,
        observeParents: true,
        observeSlideChildren: true,
        spaceBetween: 10,
      });
    });
  } catch (error: any) {
    console.error(error);
  }
};
const getShopList = async () => {
  try {
    const { data } = await httpRequest.post('/10112/hotShop');
    // data是数组，把data拆成二维数组，每个数组里有3个元素

    const list = [];
    for (let i = 0; i < data.length; i += 3) {
      list.push(data.slice(i, i + 3));
    }
    shopList.value = list;
    nextTick(() => {
      shopSwiper = new Swiper('.swiper-shopList', {
        slidesPerView: 2,
        centerInsufficientSlides: true,
        observer: true,
        observeParents: true,
        observeSlideChildren: true,
      });
    });
  } catch (error: any) {
    console.error(error);
  }
};
onMounted(() => {
  getFreeUserList();
  getSkuList();
  getShopList();
});
defineExpose({
  getFreeUserList,
});
</script>

<style scoped lang="scss">
.main {
  background: url('../assets/mainBk.png') no-repeat;
  background-size: 100%;
  width: 100%;
  height: 31rem;
  padding-top: 1.57rem;
  margin-bottom: 0.25rem;
  .order-time-title {
    width: 3.35rem;
    margin: 0 auto 0.3rem;
    .step-svg {
      font-size: 19px;
      font-weight: bold;
      vertical-align: middle;
      font-family: 'AlibabaPuHuiTi-Bold';
    }
  }
  .free-user {
    margin-bottom: 3.6rem;
    .swiper-freeUser {
      height: 0.43rem;
      width: 5rem;
      margin: 0 auto;
      overflow: hidden;
      .swiper-slide {
        text-align: center;
        color: #fff;
        font-size: 0.36rem;
        line-height: 0.43rem;
        font-family: 'AlibabaPuHuiTi-Bold';
      }
      .free-wrapper {
        transition-timing-function: linear;
      }
    }
  }
  .to-draw {
    width: 2.573rem;
    height: 0.693rem;
    margin: 0 auto 1.6rem;
  }
  .order-time-tip {
    text-align: center;
    font-size: 0.155rem;
    color: #fff;
    line-height: 0.18rem;
    font-family: 'AlibabaPuHuiTi-Bold';
    height: 0.53rem;
    margin-bottom: 1.24rem;
  }
  .sku-co {
    position: relative;
    margin-bottom: 1.36rem;
    #buyGoods {
      position: absolute;
      top: -1.35rem;
    }
    .swiper-skuList {
      width: 7rem;
      height: 10.75rem;
      margin: 0 auto 0.15rem;
      overflow: hidden;
      .swiper-slide {
        .sku-item {
          background-color: #fff;
          border-radius: 0.167rem;
          height: 3.5rem;
          padding: 0.06rem;
          margin-bottom: 0.1rem;
        }
      }
    }
    .sku-img {
      max-width: 100%;
      width: 2.14rem;
      height: 2.15rem;
      margin: 0 auto 0.25rem;
      border-radius: 0.167rem;
    }
    .price {
      font-size: 0.213rem;
      line-height: 0.213rem;
      color: #f21332;
      font-family: 'AlibabaPuHuiTi-Bold';
      margin-bottom: 0.1rem;
    }
    .sku-name {
      font-size: 0.168rem;
      line-height: 0.168rem;
      color: #696969;
      font-family: 'AlibabaPuHuiTi-Regular';
      margin-bottom: 0.1rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .sku-btn-list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      div {
        font-size: 0.168rem;
        line-height: 0.327rem;
        color: #fff;
        background-color: #f21332;
        width: 1.147rem;
        height: 0.327rem;
        background-color: #f21332;
        border-radius: 0.163rem;
        text-align: center;
      }
      img {
        width: 0.29rem;
      }
    }
    .pre {
      position: absolute;
      top: 4.3rem;
      left: 0;
      width: 0.367rem;
      z-index: 2;
    }
    .next {
      position: absolute;
      top: 4.3rem;
      right: 0;
      width: 0.367rem;
      z-index: 2;
    }
    .mor-btn {
      display: flex;
      justify-content: center;
      padding: 0 1.25rem;
      div {
        width: 2.4rem;
        height: 0.6rem;
      }
    }
  }
  .shop-co {
    position: relative;
    margin-bottom: 0.5rem;
    .swiper-shopList {
      width: 7.15rem;
      height: 7.92rem;
      margin: 0 auto 0.15rem;
      overflow: hidden;
      .shop-slide-for {
        position: relative;
        width: 3.5rem;
        margin: 0 auto 0.1rem;
        .shop-img {
          width: 100%;
        }
        .to-shop {
          position: absolute;
          top: 1.86rem;
          left: 0.28rem;
          width: 1.207rem;
          height: 0.253rem;
        }
        .to-sku {
          position: absolute;
          top: 2.06rem;
          left: 1.81rem;
          width: 0.9rem;
          height: 0.25rem;
        }
        .to-cart {
          position: absolute;
          top: 2.09rem;
          left: 3.05rem;
          width: 0.24rem;
          height: 0.187rem;
        }
      }
    }

    .pre {
      position: absolute;
      top: 3.17rem;
      left: 0;
      width: 0.367rem;
      z-index: 2;
    }
    .next {
      position: absolute;
      top: 3.17rem;
      right: 0;
      width: 0.367rem;
      z-index: 2;
    }
  }
  .more-goods {
    width: 4.253rem;
    height: 0.83rem;
    margin: 0 auto;
  }
}
</style>
