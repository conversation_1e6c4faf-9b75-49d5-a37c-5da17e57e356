import { ref } from 'vue';

export const activityInfo = ref<any>({});

export const scrollToAnchor = (id: string) => {
  const anchorElement = document.getElementById(id);
  if (anchorElement) {
    anchorElement.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'center' });
  }
};

export const toSmallGold = () => {
  // window.location.href = 'https://show.jd.com/m/dVAaqEjV45E3kRyP/?pageKey=dVAaqEjV45E3kRyP&cfxch=a02994';
  // 2月5日上班来了辛苦将小金库链接更换为：https://lc.jr.jd.com/ck/xjkHold/index/?channel=a02994&jrtransparentbar=true&jumpMarketing=1
  window.location.href = 'https://lc.jr.jd.com/ck/xjkHold/index/?channel=a02994&jrtransparentbar=true&jumpMarketing=1';
};

export const prizeListToImg = ref<any[]>([]);
