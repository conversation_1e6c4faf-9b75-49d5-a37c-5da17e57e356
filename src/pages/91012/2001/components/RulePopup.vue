<template>
  <div class="box">
    <div class="dialog">
      <div class="cloaseDiv" @click="closeClick()"></div>
      <div class="dialog_rule" v-html="rule"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: "",
  },
});

const emits = defineEmits(["close"]);
const closeClick = () => {
  emits("close");
};
</script>

<style scoped lang="scss">
.box {
  .dialog {
    width: 6.28rem;
    margin: 0 auto;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/225482/16/14034/140304/675fc05bFef9d750e/a2714cd05df32f49.png);
    background-repeat: no-repeat;
    height: 6.06rem;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 1.5rem 0.3rem 0;
    font-size: 0.3rem;
    font-weight: 400;
    .cloaseDiv{
      position: absolute;
      right: 0.36rem;
      top: 0.1rem;
      // background-color: red;
      width: 0.6rem;
      height: 0.6rem;
      border-radius: 50%;
    }
    .dialog_rule {
      max-height: 4.25rem;
      margin: 0 auto;
      padding: 0 0.2rem;
      overflow-y: scroll;
      white-space: pre-wrap;
    }
  }
}
</style>
