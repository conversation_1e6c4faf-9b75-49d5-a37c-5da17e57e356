import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/231862/17/22808/239709/6694b71bF5eeb1b1f/949ce471ed0991d6.png',
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/103199/33/31168/346896/6694b71cFa4049c17/41275392d8fe8308.png',
  actBgColor: '#068b2f',
  shopNameColor: '#fff',
  btnColor: '#068b2f',
  btnBgColor: '#fd8936',
  btnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/69102/18/19891/3425/65dd401eF25112cfc/34caf2d752cfd49c.png',
  btnBorderColor: '',
  wheelBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/18505/20/20955/16079/6694b71bFd33713f0/05445a3a35955c62.png',
  wheelPanel: '//img10.360buyimg.com/imgzone/jfs/t1/26637/9/20292/45152/6694b71dF9dcfefb2/450bee10e0bd12c9.png',
  wheelBg: '//img10.360buyimg.com/imgzone/jfs/t1/26637/9/20292/45152/6694b71dF9dcfefb2/450bee10e0bd12c9.png',
  drawBtn: '',
  wheelTextColor: '#333333',
  drawsNum: '#fef2e4',
  drawsNumBg: '//img10.360buyimg.com/imgzone/jfs/t1/246540/8/12299/2318/667935d3Fd11462d0/675938cfe58f3724.png',
  winnersBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/245564/40/3523/57672/65dd4cd6F665d6238/dc5b7d477f6dea1e.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/174354/30/42571/38775/668d006dF0004405d/6f96fc87b1d2b68f.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/18718/19/20310/39651/668d0048F9fce5ee6/9407ea6a7fe40cfb.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/200230/25/44360/6924/668d005fFff075ce6/825d8c6b7d860e77.png',
  wheelType: '2',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '消费满额抽奖(订单金额付款完成)';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
