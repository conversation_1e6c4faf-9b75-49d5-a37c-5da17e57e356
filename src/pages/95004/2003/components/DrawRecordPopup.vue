<template>
  <div class="rule-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/194291/2/43801/8072/660ba05eF6ff175c9/2e7bf58ba4757935.png" alt="" class="text" />
      <div class="close" @click="close"></div>
    </div>
    <div class="h-[40vh] px-2 pb-2 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="text-white text-center  mt-3">
        <div>下单状态为已完成时，才可以领取对应奖励。</div>
        <div>若取消中奖订单，则同时视为自动放弃该奖品。</div>
      </div>
      <div v-if="drawList.length">
        <div class="grid grid-cols-3 mt-3 bg-white p-2 rounded" v-for="(order, index) in drawList" :key="index">
          <div class="flex col-span-2">
            <div>抽奖时间：</div>
            <div>{{ order.createTime ? dayjs(order.createTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}</div>
          </div>
          <div class="flex">
            <div>状态：</div>
            <div >{{ order.missReason }}</div>
          </div>
          <template v-if="['中奖','订单已取消'].includes(order.missReason)">
            <div class="flex col-span-2">
              <div>订单编号：</div>
              <div>{{ order.orderId }}</div>
            </div>

            <div class="flex  items-center">
              <div>奖品图片：</div>
              <img alt="" style="width:.5rem" :src="order.prizeImg">
            </div>
            <div class="flex col-span-2">
              <div>奖品名称：</div>
              <div>{{ order.prizeName }}</div>
            </div>
            <div class="flex ">
              <div>奖品类型：</div>
              <div>{{ order.prizeType }}</div>
            </div>
          </template>
        </div>
      </div>
      <div v-else class="text-white text-sm h-[80%] flex justify-center items-center">暂无抽奖记录哦～</div>
    </div>
  </div>
</template>

<script lang="ts" setup>

import { ref } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';

interface Draw {
  orderId: string;
  missReason: string;
  prizeImg: string;
  prizeName: string;
  createTime: number;
  prizeType: string;
}

const drawList = ref<Draw[]>([]);
// const props = defineProps(['orderRestrainStatus']);
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const getRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/95004/drawRecord');
    drawList.value = data;
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};

!isPreview && getRecord();
</script>
<style lang="scss" scoped>

.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/222600/40/35224/219814/65016dfeF9234602d/d99de4f864849a24.png) no-repeat;
  background-size: 100%;
  width: 100vw;
  .title {
    position: relative;
    height: 0.86rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem;
    .text {
      height: 0.72rem;
    }
  }
  .close {
    width: 0.55rem;
    height: 0.55rem;
  }
}
</style>
