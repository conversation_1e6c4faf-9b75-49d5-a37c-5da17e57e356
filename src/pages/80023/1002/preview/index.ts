import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const a = {
  pageBg: '',
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/224883/18/11242/176813/6593865dFb079964c/0ec84eea4017bbd5.jpg',
  actBgColor: '#ee1e71',
  shopNameColor: '#ffffff',
  btnColor: '#ffffff',
  btnBg: '#ffffff',
  btnBorderColor: '#ffc92e',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/245131/13/1725/5743/6593854eFc7ce494e/b34ea731c403e38c.png',
  cutDownBg: '//img10.360buyimg.com/imgzone/jfs/t1/229215/21/10523/8274/6593854eF665e19fa/98142202e2341d28.png',
  cutDownColor: '#ffffff',
  cutDownNumBg: '#ffdca5',
  cutDownNumColor: '#cd3a2c',
  calendarBg: '//img10.360buyimg.com/imgzone/jfs/t1/101624/14/43475/18964/659383a8F8a9519b7/d8c21a1d5a6a537e.png',
  signInBeforeIcon: '//img10.360buyimg.com/imgzone/jfs/t1/248344/1/1611/15265/659383a7F81d65cdd/94d79967323f5a59.png',
  signInAfterIcon: '//img10.360buyimg.com/imgzone/jfs/t1/244521/1/1815/15275/659383a7Ff2b7477e/a3c3182abf49ceaf.png',
  signInBeforeBt: '//img10.360buyimg.com/imgzone/jfs/t1/4364/4/20662/8062/659383a8F0b3a853f/fb749c1c6ce89bba.png',
  signInAfterBt: '//img10.360buyimg.com/imgzone/jfs/t1/248990/31/1611/5924/659383a7F817ef770/ad7a1b55562910b8.png',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/237663/1/10296/39895/659383a9F58450708/d355fbf462dd37af.png',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/232020/18/11261/5484/65938666Fbdf93e5a/f077634481939643.jpg',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/232020/18/11261/5484/65938666Fbdf93e5a/f077634481939643.jpg',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/232020/18/11261/5484/65938666Fbdf93e5a/f077634481939643.jpg',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '日历签到';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
