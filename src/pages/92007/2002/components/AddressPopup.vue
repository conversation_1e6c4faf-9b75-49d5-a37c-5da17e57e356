<template>
  <div class="address-bk">
    <div class="form">
      <div>
        <div class="form">
          <VanField v-model.trim="form.realName" :label="'收货人'" maxlength="20" placeholder="请输入收货人姓名" :readonly="isPreview"></VanField>
          <VanField v-model.trim="form.mobile" label="手机号" maxlength="11" type="number" placeholder="请输入收货人手机号" :readonly="isPreview"></VanField>
          <VanField v-model="addressCode" label="所在地区" readonly @click="showAddressSelects" placeholder="请选择省市区"></VanField>
          <VanField v-model.trim="form.address" label="详细地址" maxlength="100" placeholder="请输入详细地址" :readonly="isPreview"></VanField>
        </div>
        <div class="tips">请注意：地址填写简略、手机号填写错误，皆可 能引发故障，导致您无法收到商品</div>
        <img v-if="!isPreview" src="../assets/saveBtn.png" alt="" class="submit" @click="checkForm" />
      </div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import { areaList } from '@vant/area-data';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const props = defineProps(['isPreview', 'echoInfo']);
const emits = defineEmits(['close']);

const form = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
  postalCode: '',
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});

const addressSelects = ref(false);

const showAddressSelects = () => {
  if (!props.isPreview) addressSelects.value = true;
};

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/92007/userAddressInfo', {
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
      postalCode: form.postalCode,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  const reg = /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (reg.test(form.realName)) {
    showToast('姓名不能包含表情');
  } else if (!form.mobile) {
    showToast('请输入手机号');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的手机号');
  } else if (!form.province) {
    showToast('请选择所在地区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else if (reg.test(form.address)) {
    showToast('详细地址不能包含表情');
  } else {
    submit();
  }
};

onMounted(() => {
  if (props.isPreview) {
    console.log('props.echoInfo', props.echoInfo);

    form.realName = props.echoInfo.realName;
    form.mobile = props.echoInfo.mobile;
    form.province = props.echoInfo.province;
    form.city = props.echoInfo.city;
    form.county = props.echoInfo.county;
    form.address = props.echoInfo.address;
    form.postalCode = props.echoInfo.postalCode;
  }
});
</script>

<style scoped lang="scss">
.tips {
  width: 4.6rem;
  font-size: 0.2rem;
  letter-spacing: 0.01rem;
  background: linear-gradient(to right, #7b4e2b, #b27a4b, #7b4e2b);
  -webkit-background-clip: text;
  color: transparent; /* 隐藏文字本身的颜色 */
  margin: 0.8rem auto 0;
}
.submit {
  width: 1.736rem;
  height: 0.6rem;
  margin: 0.4rem auto 0;
}
</style>
<style lang="scss">
.address-bk {
  background: url('../assets/addressBk.png') no-repeat;
  background-size: 100%;
  width: 6.5rem;
  height: 8rem;
  padding-top: 1.4rem;
}
.form {
  width: 5.68rem;
  margin: 0 auto;
  .van-cell {
    color: #262626;
    padding: 0;
    background-color: transparent;
    align-items: center;
    font-size: 0.28rem;
    .van-cell__title {
      width: 1.25rem;
      background: linear-gradient(to right, #7b4e2b, #b27a4b, #7b4e2b);
      -webkit-background-clip: text;
      color: transparent; /* 隐藏文字本身的颜色 */
    }
    .van-field__value {
      margin: 0.1rem 0;
      background-image: url('../assets/whiteBox.png');
      background-repeat: no-repeat;
      background-size: 100%;
      input {
        text-align: center;
        padding-bottom: 0.24rem;
      }
    }

    input {
      height: 0.74rem;
      padding: 0 0.2rem;
    }

    &::after {
      display: none;
    }
  }
}
</style>
<style>
@font-face {
  font-family: 'FZZZHJT';
  font-style: normal;
  font-weight: normal;
  src: url(data:../assets/FZZZHJT.TTF) format('TTF');
  font-display: swap;
}
* {
  font-family: 'FZZZHJT';
}
</style>
