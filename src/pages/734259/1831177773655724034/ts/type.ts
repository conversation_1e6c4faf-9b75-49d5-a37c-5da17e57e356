// sku 接口类型
export interface Skus {
  name?: string
}

// res 接口类型
export interface Res {
  data?: any,
  code?: number,
  message?: string
}

// api 接口类型
export interface ApisOpts {
  petSearch: string,
  petVote: string,
  activityContent: string,

  [propname: string]: any,
}

export interface IList {
  id: string,
  image: string,
  name: string,
  orderVal: number,
  voteNum: number,
}

export interface IPageVo {
  list: IList[],
  rule: string
}

export interface IRankVo {
  rankList: IList[],
  rule: string
}

export interface IActivityData {
  drawCount: number,
  flag: string,
  pageVO: IPageVo,
  rankVO: IRankVo,
  // pageVO: IPageVo[],
}
