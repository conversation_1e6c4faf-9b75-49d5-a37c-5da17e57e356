<template>
  <Popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false" @ClickOverlay="handleClose">
    <div class="box"><Icon name="https://img10.360buyimg.com/imgzone/jfs/t1/276920/21/25851/1789/68072d52F82ff7222/cd73c6e6aa91b971.png" size="26" color="#fff" class="close-icon" @click="handleClose" /></div>
  </Popup>
</template>
<script setup lang="ts">
import { computed, defineEmits, defineProps } from 'vue';
import { Popup, Icon } from 'vant';

const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog']);
const handleClose = () => {
  emits('closeDialog');
};
</script>
<style scoped lang="scss">
.box {
  background: url(../assets/dialog-bg.png) no-repeat;
  background-size: contain;
  width: 6.77rem;
  height: 8.75rem;
  padding: 0.13rem 0.4rem;
  box-sizing: border-box;
  position: relative;
  .close-icon {
    position: absolute;
    width: 1rem;
    height: 1rem;
    right: 0;
    top: 0;
  }
}
</style>
