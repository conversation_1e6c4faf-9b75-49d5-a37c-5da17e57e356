/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2024/4/8 14:36
 * Description:
 */
import DialogPrize from './component/DialogPrize.vue';
import DialogProduct from './component/DialogProduct.vue';
import DialogInfo from './component/DialogInfo.vue';
import { showToast } from 'vant';

export const PRIZE_TYPE = {
  0: 'noWin',
  1: 'winCoupon',
  2: 'winJdBean',
  3: 'winProduct',
};
export const dialogComponent = {
  prize: DialogPrize,
  winProduct: DialogProduct,
  info: DialogInfo,
};
export const checkUserInfo = (userInfo: any): boolean => {
  if (!userInfo.realName) {
    showToast('请输入姓名');
    return false;
  }
  if (!userInfo.mobile) {
    showToast('请输入电话');
    return false;
  }
  if (!userInfo.province || !userInfo.city || !userInfo.county) {
    showToast('请输入地区');
    return false;
  }
  if (!userInfo.address) {
    showToast('请输入详细地址');
    return false;
  }
  if (!userInfo.addressCode) {
    showToast('请输入邮政编码');
    return false;
  }
  const regex = /^1[3-9]\d{9}$/;
  if (!regex.test(userInfo.mobile)) {
    showToast('手机号格式不正确');
    return false;
  }
  if (userInfo.addressCode.length < 6) {
    showToast('邮政编码格式不正确，应为6位数字');
    return false;
  }
  return true;
};

export const getRoomUrl = (liveId: string): string => `https://lives.jd.com/#/${liveId}?origin=3&appid=jdzb&id=${liveId}`;
