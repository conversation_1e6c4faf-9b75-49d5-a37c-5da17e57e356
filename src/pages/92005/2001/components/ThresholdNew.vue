<template>
  <div class="bg">
    <div class="title">非常抱歉</div>
    <div class="tip1">您未满足活动条件,感谢您对古驰的喜爱与支持</div>
    <div class="tip2">您不满足活动条件的原因可能如下所示：</div>
    <div class="content">
      {{ text }}
    </div>
    <div class="btn" @click="gotoShopPage(baseInfo.shopId)">返回店铺</div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script setup lang="ts">
import { defineProps, inject, ref } from 'vue';
import dayjs from 'dayjs';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const props = defineProps(['data']);
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const text = ref(`1.您的生日不在本活动当月
2.生日月前${props.data?.beforeDay}天内，您未在古驰(GUCCI)美妆京东自营旗舰店产生过任何${props.data?.beforeOrderNum}笔正装商品订单（需在生日月前${props.data?.beforeDay}天内买过${props.data?.beforeOrderNum}笔正装商品订单，方可参与生日礼活动)。
3.未查询到您本月在古驰(GUCCI)美妆京东自营旗舰店产生过任何${props.data?.afterOrderNum}笔正装商品订单`);
</script>

<style scoped lang="scss">
.bg {
  position: relative;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/281597/13/18163/2423/67f7af8eF1a1b7ee6/03411ae8226fa226.png) no-repeat;
  background-size: 100%;
  width: 5.5rem;
  height: 6.5rem;
  .title {
    font-size: 0.3rem;
    line-height: 0.3rem;
    padding-top: 0.73rem;
    padding-bottom: 0.4rem;
    text-align: center;
  }
  .tip1 {
    display: block;
    width: fit-content;
    margin: 0 auto 0.7rem;
    border-bottom: 0.01rem solid #000;
    font-size: 0.18rem;
  }
  .tip2 {
    padding: 0 0.32rem;
    font-size: 0.18rem;
    font-weight: bold;
    margin-bottom: 0.35rem;
  }
  .content {
    font-size: 0.16rem;
    white-space: pre-wrap;
    padding: 0 0.32rem;
    line-height: 0.32rem;
  }
  .btn {
    position: absolute;
    bottom: 0.75rem;
    left: 50%;
    transform: translateX(-50%);
    width: 1.5rem;
    height: 0.4rem;
    background-color: #000;
    color: #fff;
    text-align: center;
    line-height: 0.4rem;
    font-size: 0.18rem;
  }
}
.close {
  width: 0.3rem;
  height: 0.3rem;
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
}
</style>
