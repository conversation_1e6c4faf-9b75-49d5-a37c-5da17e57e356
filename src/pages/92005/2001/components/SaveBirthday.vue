<template>
  <div class="pop-bg">
    <img src="//img10.360buyimg.com/imgzone/jfs/t1/279270/39/25732/543/6808836dFff95d695/b669a3166f2e7e91.png" class="close" alt="" @click="close" />
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/272407/19/26592/518/6808836dF4fd6e6b9/3b1a3777d0e5c973.png" alt="" />
      填写生日信息
    </div>
    <div class="form-item">
      <div class="label">生日：</div>
      <div class="value" @click="saveBirthdayPopup = true">{{ birthday ? birthday : '请选择您的生日>' }}</div>
    </div>
    <div class="btn" @click="submit">确认提交</div>
  </div>
  <VanPopup teleport="body" v-model:show="saveBirthdayPopup" position="bottom">
    <VanDatePicker v-model="currentDate" :minDate="minDate" :maxDate="maxDate" @cancel="saveBirthdayPopup = false" @confirm="confirm" />
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
const saveBirthdayPopup = ref(false);
const minDate = ref(new Date('1900-01-01'));
const maxDate = ref(new Date());
const birthday = ref('');
const currentDate = ref([dayjs().year().toString(), (dayjs().month() + 1).toString(), dayjs().date().toString()]);

const confirm = (value: any) => {
  const { selectedValues } = value;
  birthday.value = `${selectedValues[0]}-${selectedValues[1]}-${selectedValues[2]}`;
  saveBirthdayPopup.value = false;
};

const submit = async () => {
  if (!birthday.value) {
    showToast('请选择生日');
    return;
  }
  try {
    showLoadingToast({
      message: '正在提交',
      duration: 0,
      forbidClick: true,
    });
    const data = await httpRequest.post('/92005/birthday', {
      birthday: birthday.value,
    });
    closeToast();
    showToast('提交成功');
    close();
  } catch (error: any) {
    console.error(error);
    closeToast();
    showToast(error.message);
  }
};
</script>

<style scoped lang="scss">
.pop-bg {
  width: 5.5rem;
  height: 4.05rem;
  position: relative;
  background-color: #e6e4e9;
  border-radius: 0.24rem;
  padding-top: 0.96rem;
  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.21rem;
  }
  .title {
    text-align: center;
    font-size: 0.3rem;
    line-height: 0.3rem;
    letter-spacing: 0.05rem;
    img {
      display: inline;
      width: 0.26rem;
    }
  }
  .form-item {
    width: 3.1rem;
    height: 0.48rem;
    border: 0.01rem solid #000;
    margin: 0.28rem auto 0.38rem;
    padding-left: 0.15rem;
    padding-right: 0.13rem;
    font-size: 0.2rem;
    line-height: 0.48rem;
    display: flex;
    align-items: center;
    .label {
    }
    .value {
      flex: 1;
      text-align: right;
    }
  }
  .btn {
    width: 1.5rem;
    height: 0.4rem;
    background-color: #000;
    color: #fff;
    text-align: center;
    line-height: 0.4rem;
    font-size: 0.18rem;
    margin: 0 auto;
  }
}
</style>
