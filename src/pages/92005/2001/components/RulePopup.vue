<template>
  <div class="rule-bk">
    <div class="title">活动规则</div>
    <div class="content">
      <div v-html="rule"></div>
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.close {
  width: 0.3rem;
  height: 0.3rem;
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
}
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/281597/13/18163/2423/67f7af8eF1a1b7ee6/03411ae8226fa226.png) no-repeat;
  background-size: 100%;
  width: 5.5rem;
  height: 6.5rem;

  .title {
    font-size: 0.3rem;
    line-height: 0.3rem;
    padding-top: 0.35rem;
    padding-bottom: 0.3rem;
    text-align: center;
  }

  .content {
    width: 5.5rem;
    height: 5.55rem;
    margin: 0 auto;
    padding: 0 0.25rem;
    font-size: 0.16rem;
    white-space: pre-wrap;
    overflow-y: scroll;
    word-wrap: break-word;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
