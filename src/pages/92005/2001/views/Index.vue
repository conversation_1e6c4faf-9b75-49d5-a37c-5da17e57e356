<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <img :src="furnish.actBg" alt="" class="page-bk" />
    <div class="content">
      <div class="join-btn" v-if="!isReceive" @click="getPrize">立即领取</div>
      <div class="join-btn" v-else @click="toSku">已领取</div>
    </div>
  </div>
  <Threshold v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
  <!--  新增门槛-->
  <VanPopup teleport="body" v-model:show="showAddLimit">
    <ThresholdNew @close="showAddLimit = false" :data="actData" />
  </VanPopup>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @toSku="toSku"></AwardPopup>
  </VanPopup>
  <!-- 填写生日 -->
  <VanPopup teleport="body" v-model:show="saveBirthdayPopup">
    <SaveBirthday v-if="saveBirthdayPopup" @close="saveBirthdayPopup = false"></SaveBirthday>
  </VanPopup>
  <!-- 非生日月 -->
  <VanPopup teleport="body" v-model:show="notBirthdayPop">
    <NotBirthday @close="notBirthdayPop = false"></NotBirthday>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, inject } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveBirthday from '../components/SaveBirthday.vue';
import ThresholdNew from '../components/ThresholdNew.vue';
import NotBirthday from '../components/NotBirthday.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import 'swiper/swiper.min.css';
import Threshold from '../components/Threshold.vue';
import { gotoSkuPage } from '@/utils/platforms/jump';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const isReceive = ref(false);
const actData = ref<any>({});
const skuId = ref('');

const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
const saveBirthdayPopup = ref(false);
const showMyPrize = ref(false);
const notBirthdayPop = ref(false);
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
  prizeImg: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const userPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string, userPrizeId1: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  userPrizeId.value = userPrizeId1;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

const showAddLimit = ref(false);
const showLimit = ref(false);
const checkLimitDialog = async () => {
  if (baseInfo.status === 1) {
    showToast('活动未开始！');
    return;
  }
  if (baseInfo.status === 3) {
    showToast('活动已结束！');
    return;
  }
  if (baseInfo.thresholdResponseList.findIndex((item) => item.thresholdCode === 4 || item.thresholdCode === 5 || item.thresholdCode === 8) !== -1) {
    showLimit.value = true;
  }
};
const getActivityData = async () => {
  try {
    const { data } = await httpRequest.post('/92005/activity');
    isReceive.value = data.isReceive === 2;
    actData.value = data;
    [skuId.value] = data.skuList;
  } catch (error) {
    console.error(error);
  }
};
const getPrize = async () => {
  if (baseInfo.thresholdResponseList.length) {
    checkLimitDialog();
    return;
  }
  try {
    showLoadingToast({
      message: '',
      duration: 0,
      forbidClick: true,
    });
    const data = await httpRequest.post('/92005/sendUser');
    showAward.value = true;
    isReceive.value = true;
    closeToast();
  } catch (error: any) {
    closeToast();
    if (error.message === '生日月前订单笔数不足' || error.message === '生日月内订单笔数不足') {
      showAddLimit.value = true;
    } else if (error.message === '未填写生日信息') {
      saveBirthdayPopup.value = true;
    } else if (error.message === '当前月不是生日月') {
      notBirthdayPop.value = true;
    } else {
      showToast(error.message);
    }
    console.error(error);
  }
};
const toSku = () => {
  gotoSkuPage(skuId.value);
};
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      duration: 0,
      forbidClick: true,
    });
    await getActivityData();
    closeToast();
    checkLimitDialog();
  } catch (error) {
    closeToast();
  }
};
init();
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style lang="scss">
@font-face {
  font-family: 'SourceHanSansCN';
  src: url('https://lzcdn.dianpusoft.cn/fonts/SourceHanSansCN/SourceHanSansCN-Regular.otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'GucciSans';
  src: url('https://lzcdn.dianpusoft.cn/fonts/GucciSans/GucciSans-Bold.otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'GucciSans', 'SourceHanSansCN';
}
</style>
<style scoped lang="scss">
.bg {
  position: relative;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-color: #f2f2f2;
  padding-bottom: 0.5rem;
  .page-bk {
    width: 100%;
  }
  .content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    .join-btn {
      width: 1.8rem;
      height: 0.52rem;
      border: 0.01rem solid #000;
      text-align: center;
      line-height: 0.52rem;
      font-size: 0.24rem;
      position: absolute;
      top: 8.5rem;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style>
