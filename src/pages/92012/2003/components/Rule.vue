<template>
  <div class="rule-bk">
    <div class="content" v-html="rule"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 6.48rem;
  height: 8.2rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/273194/26/22234/110109/67ffaf6fF630aee10/87aa5d9dddbf1b6b.png);
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
  .close {
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/281515/2/20664/1823/67fe160aF8aff0f7c/58b1e21a2ee3ea1d.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: .8rem;
    height: .8rem;
    border-radius: 50%;
  }

  .content {
    width: 6rem;
    height: 5rem;
    white-space: pre-wrap;
    overflow-y: scroll;
    word-wrap: break-word;
    position: absolute;
    padding: 0 .3rem;
    left: 50%;
    transform: translateX(-50%);
    top: 1.3rem;
    font-size: .24rem;
    //color: #d3a24e;
  }
}
</style>
