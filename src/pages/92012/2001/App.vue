<template>
  <div v-if="isLoadingFinish">
    <div class="header-kv select-hover">
      <img
        :src="
          furnish.actBg ??
          'https://img10.360buyimg.com/imgzone/jfs/t1/139876/9/21637/89337/619f33f3E28ef638d/7aa9c2fad2d8275b.png'
        "
        alt=""
        class="kv-img"
      />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>
        <div>
          <div
            class="header-btn header-btn1"
            :style="furnishStyles.ruleBtn.value"
            @click="rulePopup = true"
          >
            <div></div>
          </div>
          <div
            class="header-btn header-btn2"
            :style="furnishStyles.myPrizeBtn.value"
            @click="myPrizePopup = true"
          >
            <div></div>
          </div>
          <div
            class="header-btn header-btn3"
            :style="furnishStyles.orderBtn.value"
            @click="showOrderRecord = true"
          >
            <div></div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg" :style="furnishStyles.pageBg.value">
      <!--      奖品标题-->
      <img
        src="//img10.360buyimg.com/imgzone/jfs/t1/287203/8/10009/11181/683fa8a0F28725217/7716ae4139bd118f.png"
        class="prizeTitle"
      />
      <!--      机制奖品列表-->
      <div class="prize-list">
        <div
          class="prize-item"
          :style="furnishStyles.thresholdBg.value"
          v-for="(item, index) in prizeList"
          :key="index"
        >
          <div class="prize-threshold-text">
            <span style="font-weight: bold"
              >{{ item.seriesName }}{{prizeList.length > 1 ? '(' + prizeList.length + '档机制不可共享)' : ''}}:</span
            >首次购买试用装<span style="color: red; font-weight: bold"
              >{{ item.sampleSkuName }}任意一罐</span
              >，在{{ activityData.moreBuyTimeLimitNum }}天内复购同系列<span
              style="color: red; font-weight: bold"
              >正装{{ item.formalNum }}罐</span
              >，可获得首购试用装{{ item.sampleSkuName }}一罐的等额新客尝鲜价京豆
          </div>
          <img
            :src="furnish.receivePrizeBg"
            alt=""
            class="receive-btn"
            v-if="item.status === 0"
            @click="handlereceive(item.type)"
          />
          <img
            :src="furnish.receivePrizeBgGray"
            alt=""
            class="receive-btn"
            v-else
            @click="handleCannotClick(item.status)"
          />
        </div>
      </div>
      <!--      系列及sku列表-->
      <div class="series-box">
        <!--       系列tab-->
        <div class="series-tab-box">
          <div
            class="series-tab"
            :class="actTabIndex === index ? 'series-tab-active' : ''"
            :style="
              actTabIndex === index
                ? furnishStyles.seriesTabAct.value
                : furnishStyles.seriesTabNotAct.value
            "
            v-for="(item, index) in seriesList"
            :key="index"
            @click="toSign(item, index)"
          >
            {{ item.seriesName }}
          </div>
        </div>
        <!--        试用装标题-->
        <img
          src="//img10.360buyimg.com/imgzone/jfs/t1/283096/38/16835/17146/67f4e65cF16b605f9/f58b87260f9422d2.png"
          class="sampleTitle"
        />
        <!--        试用装sku-->
        <div class="series-box-sample-list" :style="furnishStyles.sampleBg.value">
          <div class="series-box-sample-box">
            <div
              class="series-box-sample-item"
              :style="furnishStyles.sampleGoodsBg.value"
              v-for="(item, index) in seriesSampleList"
              :key="index"
              v-click-track="{ code: 'llsp', value: item.skuId }"
              @click="handleGoSku(item.skuId)"
            >
              <div class="sample-name">{{ item.skuName }}</div>
              <img
                :src="item.skuMainPicture"
                alt=""
                @error="onError(item)"
                v-if="!item.hasError"
                class="sample-img"
              />
            </div>
          </div>
        </div>
        <!--        正装标题-->
        <img
          src="//img10.360buyimg.com/imgzone/jfs/t1/282376/19/16515/17644/67f4e65cFe1824222/6a8f6430ec8c4de3.png"
          class="sampleTitle"
        />
        <!--        正装装sku-->
        <div class="series-box-formal-list" :style="furnishStyles.formalBg.value">
          <div class="series-box-formal-box">
            <div
              class="series-box-formal-item"
              :style="furnishStyles.formalGoodsBg.value"
              v-for="(item, index) in seriesFormalList"
              :key="index"
              v-click-track="{ code: 'llsp', value: item.skuId }"
              @click="handleGoSku(item.skuId)"
            >
              <div class="formal-name">{{ item.skuName }}</div>
              <img :src="item.skuMainPicture" alt="" class="formal-img" />
            </div>
          </div>
        </div>
      </div>
      <!--      返回顶部-->
      <div
        class="back-top"
        :style="furnishStyles.returnTopBtn.value"
        @click="backTop"
      ></div>
    </div>
    <VanPopup teleport="body" v-model:show="rulePopup">
      <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="myPrizePopup">
      <MyPrize v-if="myPrizePopup" @close="myPrizePopup = false"></MyPrize>
    </VanPopup>
    <!--我的订单弹窗-->
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="center">
      <OrderRecordPopup
        v-if="showOrderRecord"
        @close="showOrderRecord = false"
      ></OrderRecordPopup>
    </VanPopup>
    <!-- 非会员拦截 -->
    <VanPopup teleport="body" v-model:show="showOpenCard">
      <OpenCard :showPopup="showOpenCard" @closeDialog="showOpenCard = false" />
    </VanPopup>
    <!--     兑换成功弹窗 -->
    <VanPopup teleport="body" v-model:show="successPopup" :close-on-click-overlay="false">
      <div class="success-exange">
        <div class="content">
          <p class="prize-name">
            奖品将在首购订单和复购订单完成{{ activityData.jdTimeLimitNum }}天后<br />
            为您自动发放
          </p>
        </div>
        <div class="btn" @click="gotoShopPage(baseInfo.shopId)"></div>
        <div class="close" @click="handleSuccessPopupClose"></div>
      </div>
    </VanPopup>
    <!-- 活动门槛 -->
    <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
    <!-- 已经参与过0元试喝，不能再次参与活动 -->
    <VanPopup teleport="body" v-model:show="showNoJoinAc">
      <NoJoinAc @close="showNoJoinAc = false" />
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, reactive, ref } from "vue";
import furnishStyles, { furnish } from "./ts/furnishStyles";
import Threshold2 from "@/components/Threshold2/index.vue";
import { gotoSkuPage, gotoShopPage } from "@/utils/platforms/jump";
import MyPrize from "./components/MyPrize.vue";
import Rule from "./components/Rule.vue";
import Award from "./components/AwardPopup.vue";
import OrderRecordPopup from "./components/OrderRecordPopup.vue";
import OpenCard from "./components/OpenCard.vue";
import { DecoData } from "@/types/DecoData";
import { BaseInfo } from "@/types/BaseInfo";
import { closeToast, showLoadingToast, showToast } from "vant";
import { httpRequest } from "@/utils/service";
import useThreshold from "@/hooks/useThreshold";
import { deepCopy } from "@/utils/platforms/client";
import NoJoinAc from "./components/NoJoinAc.vue";

const decoData = inject("decoData") as DecoData;
const baseInfo = inject("baseInfo") as BaseInfo;
const shopName = ref(baseInfo.shopName);

const showNoJoinAc = ref(false);
// 领奖成功
const successPopup = ref(false);
// 订单弹窗是否显示
const showOrderRecord = ref(false);
// 展示门槛显示弹框
const showLimit = ref(false);
// 活动规则显示
const rulePopup = ref(false);
// 规则数据
const ruleText = ref("");
// 我拍奖品显示
const myPrizePopup = ref(false);
// 领奖成功弹窗展示
const awardPopup = ref(false);
// 入会拦截弹窗展示
const showOpenCard = ref(false);
const isLoadingFinish = ref(false);
// 活动主数据
const activityData = ref<any>({});
// 机制及奖品数据
const prizeList = ref<any[]>([]);
// 系列及sku列表
const seriesList = ref<any[]>([]);
// 系列点击index
const actTabIndex = ref(0);
// 试用装列表
const seriesSampleList = ref<any[]>([]);
// 正装列表
const seriesFormalList = ref<any[]>([]);
// 暂存状态字段
const thresholdType = ref(0);
// 活动主接口  1 不满足未购条件 2 不满足复购条件 3 不满足罐数 4 不满足首购条件
const thresholdInfo: any = {
  1: "抱歉您当前不满足未购条件",
  2: "抱歉您不满足复购条件,请下单后参与活动",
  3: "抱歉您当前订单未满足门槛罐数",
  4: "抱歉您当前不满足首购条件",
  5: "您已领取过",
  6: "黑名单",
};
// 无法领奖
const canNotText: any = {
  "-1": "您不满足领取条件",
  1: "您已领取过",
  2: "您已取消报名",
  3: "奖品已发放",
  4: "奖品剩余份数不足",
  6: "不满足当前机制领取条件",
};

const handleCannotClick = (status: any) => {
  if (status === -1) {
    if (thresholdType.value === 6) {
      showNoJoinAc.value = true;
    } else {
      showToast({
        message: thresholdInfo[thresholdType.value],
        duration: 2000,
      });
    }
  } else if (status === 5) {
    showOpenCard.value = true;
  } else {
    showToast({
      message: canNotText[status],
      duration: 2000,
    });
  }
};
// 点击系列按钮暂时点击效果
const toSign = (item: any, index: number) => {
  actTabIndex.value = index;
  seriesSampleList.value = deepCopy(seriesList.value[index].sampleList);
  seriesFormalList.value = deepCopy(seriesList.value[index].formalList);
};
const onError = (item:any) => {
  // console.log("onError========", item);
  item.hasError = true;
};
// 返回顶部
const backTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
};
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get("/common/getRule");
      ruleText.value = data;
    }
    // rulePopup.value = true;
  } catch (error) {
    console.error();
  }
};

const getActInfo = async () => {
  try {
    const { data } = await httpRequest.post("/92012/activity");
    prizeList.value = data.prizeList;
    activityData.value = data;
    thresholdType.value = data.thresholdType;
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
      showOpenCard.value = true;
      console.log("非会员");
      closeToast();
      return;
    }
    // 添加对 baseInfo.thresholdResponseList 的存在性判断
    if (baseInfo.thresholdResponseList && baseInfo.thresholdResponseList.length > 0) {
      showLimit.value = useThreshold({
        thresholdList: baseInfo.thresholdResponseList,
      });
    }
    // 添加对 data.thresholdType 的存在性判断
    if (
      baseInfo.thresholdResponseList[0]?.thresholdCode !== 4 &&
      baseInfo.status === 2 &&
      data.thresholdType &&
      data.thresholdType !== 0
    ) {
      if (data.thresholdType === 6) {
        showNoJoinAc.value = true;
        closeToast();
      } else {
        showToast({
          message: thresholdInfo[data.thresholdType],
          duration: 2000,
        });
      }
    } else {
      closeToast();
    }
  } catch (error: any) {
    // console.error(error);
    showToast({
      message: error.message,
      duration: 2000,
    });
  }
};
// 领奖
const handlereceive = async (type: any) => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post("/92012/cashPrize", { type });
    successPopup.value = true;
    closeToast();
  } catch (error: any) {
    // console.error(error);
    showToast({
      message: error.message,
      duration: 2000,
    });
    getActInfo();
  }
};
const handleSuccessPopupClose = () => {
  successPopup.value = false;
  getActInfo();
};
// 获取系列商品数据
const getSeriesGoods = async () => {
  try {
    const { data } = await httpRequest.post("/92012/getSeriesTypeSkuList");
    seriesList.value = data;
    seriesSampleList.value = deepCopy(seriesList.value[actTabIndex.value].sampleList);
    seriesFormalList.value = deepCopy(seriesList.value[actTabIndex.value].formalList);
  } catch (error) {
    console.error();
  }
};
// 去购买
const handleGoSku = (skuId: any) => {
  gotoSkuPage(skuId);
};
const init = async () => {
  // console.log(decoData);
  if (decoData) {
    (Object.keys(decoData) as Array<keyof DecoData>).forEach((item) => {
      const value = decoData[item];
      if (value !== undefined && item in furnish) {
        furnish[item as keyof typeof furnish] = decoData[item];
      }
    });
  }
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });

    await Promise.all([getActInfo(), getSeriesGoods(), showRulePopup()]);
    isLoadingFinish.value = true;
  } catch (error) {
    console.error("初始化失败:", error);
    closeToast();
  }
};
init();
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  height: 0;
}
::-webkit-scrollbar {
  display: none !important;
  width: 0;
  height: 0;
}
&::-webkit-scrollbar {
  display: none !important;
}
</style>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  //background-repeat: no-repeat;
  overflow: hidden;
  //padding-bottom: 0.3rem;
  .prizeTitle {
    width: 4.58rem;
    height: 0.45rem;
    margin: 0.25rem auto;
  }
  .prize-item {
    width: 7.1rem;
    height: 2.46rem;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin: 0.2rem auto;
    position: relative;
    overflow: hidden;
    .prize-threshold-text {
      font-size: 0.23rem;
      width: 6.5rem;
      height: 1.1rem;
      margin: 0 auto;
      text-align: justify;
      color: #754623;
      margin-top: 0.3rem;
      overflow-y: scroll;
    }
    .receive-btn {
      width: 2.21rem;
      height: 0.48rem;
      position: absolute;
      bottom: 0.4rem;
      left: 50%;
      transform: translate(-50%);
    }
    .receive-btn-cannot {
      filter: grayscale(1);
    }
  }
  .series-box {
    width: 7.5rem;
    .series-tab-box {
      display: flex;
      overflow-x: scroll;
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari 和 Opera */
      }
      scrollbar-width: none; /* Firefox */
      .series-tab {
        min-width: 1.68rem;
        height: 0.46rem;
        background-size: 100% 100%;
        margin: 0.2rem 0.2rem 0 0.2rem;
        background-repeat: no-repeat;
        font-size: 0.26rem;
        text-align: center;
        line-height: 0.46rem;
        font-weight: 600;
        color: #da2e24;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .series-tab-active {
        color: #fdf1f1;
      }
    }

    .sampleTitle {
      width: 6.11rem;
      height: 0.52rem;
      margin: 0.25rem auto;
    }
    .series-box-sample-list {
      width: 7.1rem;
      height: 7rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.2rem auto;
      position: relative;
      .series-box-sample-box {
        width: 7.11rem;
        height: 6.5rem;
        margin: 0.2rem auto;
        display: flex;
        flex-wrap: wrap;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        top: 0;
        overflow-y: scroll;
        .series-box-sample-item {
          width: 3.5rem;
          height: 3.35rem;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          position: relative;
          .sample-name {
            width: 2.5rem;
            margin: 0 auto;
            line-height: 0.8rem;
            text-align: center;
            font-size: 0.28rem;
            font-weight: 600;
            color: #744624;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .sample-img {
            width: 1.1rem;
            height: 1.7rem;
            position: absolute;
            left: 0.25rem;
            top: 0.7rem;
            object-fit:contain;
          }
        }
      }
    }
    .series-box-formal-list {
      width: 7.1rem;
      height: 3.7rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.2rem auto;
      position: relative;
      .series-box-formal-box {
        width: 7.1rem;
        height: 3.2rem;
        margin: 0.2rem auto;
        display: flex;
        flex-wrap: wrap;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        top: 0;
        overflow-y: scroll;
        .series-box-formal-item {
          width: 3.5rem;
          height: 3.35rem;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          position: relative;
          .formal-name {
            width: 2.5rem;
            margin: 0 auto;
            line-height: 0.8rem;
            text-align: center;
            font-size: 0.28rem;
            font-weight: 600;
            color: #744624;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .formal-img {
            height: 1.7rem;
            position: absolute;
            left: 50%;
            transform: translate(-50%);
            top: 0.7rem;
            object-fit:contain;
          }
        }
      }
    }
  }
  .back-top {
    width: 3.64rem;
    height: 0.92rem;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0.2rem auto;
  }
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 3.47rem;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 1.18rem;
    height: 0.44rem;
    cursor: pointer;
    // position: absolute;
    // right: 0;
    font-weight: 700;
    margin-bottom: 0.13rem;
  }
}
.success-exange {
  width: 6.25rem;
  height: 7.58rem;
  padding-top: 1.6rem;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/279214/40/17704/331513/67f5f0e7F4b38d19a/f977e50272cf0d39.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  .content {
    .prize-name {
      font-size: 0.28rem;
      font-weight: bold;
      margin: 1.3rem 0 0;
      text-align: center;
      color: #a16f09;
    }
  }
  .btn {
    position: absolute;
    width: 2.7rem;
    height: 0.6rem;
    top: 4.8rem;
    left: 50%;
    transform: translate(-50%);
  }
  .close {
    position: absolute;
    width: 0.8rem;
    height: 0.8rem;
    bottom: 0rem;
    left: 50%;
    transform: translate(-50%);
  }
}
</style>
