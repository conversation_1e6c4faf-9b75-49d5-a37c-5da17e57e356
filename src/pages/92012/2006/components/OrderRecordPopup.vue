<template>
  <div class="order-bk">
    <div class="tab-box">
      <div
        class="first-buy"
        :class="{ 'first-buy-act': activeTab === 'firstOrder' }"
        @click="handleTabClick('firstOrder')"
      ></div>
      <div
        class="second-buy"
        :class="{ 'second-buy-act': activeTab === 'moreOrder' }"
        @click="handleTabClick('moreOrder')"
      ></div>
    </div>
    <div class="content">
      <div v-if="!orderList[activeTab].length" class="no-data">暂无订单记录哦~</div>
      <div v-else>
        <!--        首购订单-->
        <div v-if="activeTab === 'firstOrder'">
          <div class="first-order-title">
            <span>订单id</span>
            <span>完成时间</span>
            <span>skuId</span>
          </div>
          <div
            v-for="(item, index) in orderList[activeTab]"
            :key="index"
            class="order-list"
          >
            <div class="order-list-line1">
              <!--订单id-->
              <span> {{ item.orderId }}</span>
              <!--订单完成时间-->
              <span> {{ item.orderEndTime ? dayjs(item.orderEndTime).format("YYYY-MM-DD") : '--' }}</span>
              <!--skuid数组-->
              <span>
                <div v-for="(item, index) in item.skuId" :key="index">{{ item }}</div>
              </span>
            </div>
          </div>
        </div>
        <!--复购订单-->
        <div v-else>
          <div class="more-order-title">
            <span>订单id</span>
            <span>复购罐数</span>
            <span>skuId</span>
          </div>
          <div
            v-for="(item, index) in orderList[activeTab]"
            :key="index"
            class="order-list order-listFu"
          >
            <div
              v-for="(seriesType, index) in item.oederTypeNum"
              :key="index"
              class="more-order-box"
            >
              <div class="series-name-context">
                <span>机制名：{{ seriesType.type }}</span>
                <span>此机制对应罐数：{{ seriesType.stepMoreNum }}</span>
              </div>
              <div class="order-list-line2">
                <!--订单id-->
                <span> {{ item.orderId }}</span>
                <!--复购罐数-->
                <span> {{ seriesType.moreNum }}</span>
                <!--订单id-->
                <span>
                  <div v-for="(skuIdItem, index) in item.skuId" :key="index">
                    {{ skuIdItem }}
                  </div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { inject, ref } from "vue";
import { closeToast, showLoadingToast } from "vant";
import { httpRequest } from "@/utils/service";
import dayjs from "dayjs";

interface orderInfo {
  activityId: number;
  createTime: string;
  encryptPin: string;
  id: number;
  orderEndTime: string;
  orderId: string;
  orderPrice: string;
  orderStartTime: string;
  orderStatus: string;
  orderType: number;
  shopId: number;
  updateTime: string;
}

interface OrderList {
  firstOrder: orderInfo[];
  moreOrder: orderInfo[];
}

const orderList = ref<any>({
  firstOrder: [],
  moreOrder: [],
});

const emits = defineEmits(["close"]);
const close = () => {
  emits("close");
};
const getMyOrder = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/92012/getUserOrders');
    orderList.value.firstOrder = data.firstOrder || [];
    orderList.value.moreOrder = data.moreOrder || [];
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
const isPreview = inject("isPreview") as boolean;
if (!isPreview) {
  getMyOrder();
}
getMyOrder();
const activeTab = ref<"firstOrder" | "moreOrder">("firstOrder");

const handleTabClick = (tab: "firstOrder" | "moreOrder") => {
  activeTab.value = tab;
};
</script>

<style scoped lang="scss">
.order-bk {
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/274245/16/21914/55413/6801edf5Fd2c6fefd/a79386360b511f61.png");
  background-size: 100% 100%;
  width: 7.12rem;
  height: 8.29rem;
  background-repeat: no-repeat;
  position: relative;
  .tab-box {
    width: 5.6rem;
    display: flex;
    justify-content: space-around;
    position: absolute;
    top: 1.7rem;
    left: 51%;
    transform: translate(-50%);
    .first-buy {
      width: 2.27rem;
      height: 0.58rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/280742/20/20910/3162/6801edf5Fe6c55408/525ac44f46728869.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .first-buy-act {
      width: 2.27rem;
      height: 0.58rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/270769/25/24031/2607/6801edf5F5fb69cf2/4e1476fba6b733c3.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .second-buy {
      width: 2.27rem;
      height: 0.58rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/279364/32/22256/3446/6801edf6F66b9ec40/6fd6771667f047d8.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .second-buy-act {
      width: 2.27rem;
      height: 0.58rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/270560/12/23606/2878/6801edf6Fb3305d42/5eeeb6fb3c28e6d1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: 0.8rem;
    height: 0.8rem;
    border-radius: 50%;
  }
  .no-data {
    text-align: center;
    line-height: 3.5rem;
    font-size: 0.24rem;
    color: #836953;
  }
  .content {
    position: absolute;
    left: 50%;
    top: 2.5rem;
    transform: translate(-50%);
    width: 6rem;
    height: 4.7rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #836953;
    white-space: pre-wrap;
    .first-order-title,
    .more-order-title {
      display: flex;
      justify-content: space-between;
      color: #836953;
      font-size: 0.26rem;
      span {
        flex: 1;
        text-align: center;
      }
    }
    .order-listFu {
      border: 0.01rem solid #d3a24e;
      border-radius: 0.16rem;
      margin-top: 0.1rem;
    }
    .order-list {
      //margin-left: calc(50% - 5rem / 2);
      padding: 0.1rem;
      width: 6rem;
      //height: 1.14rem;
      margin-bottom: 0.14rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .order-list-line1 {
        margin-bottom: 0.2rem;
        display: flex;
        justify-content: space-between;
        color: #836953;
        font-size: 0.19rem;
        //border: 1px dotted #000;
        span {
          flex: 1;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
      }
      .more-order-box {
        //border: 1px dashed #000;
        //border-radius: 0.1rem;
        margin-bottom: 0.1rem;
        .series-name-context {
          display: flex;
          // text-align: center;
          // justify-content: space-around;
          color: #836953;
          font-size: 0.23rem;
          border-bottom: 1px dashed #999;
          padding-bottom: 0.1rem;
          span:first-child {
            width: 2rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      .order-list-line2 {
        // margin-bottom: 0.2rem;
        margin-top: 0.1rem;
        display: flex;
        justify-content: space-between;
        color: #836953;
        font-size: 0.19rem;

        span {
          flex: 1;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
</style>
