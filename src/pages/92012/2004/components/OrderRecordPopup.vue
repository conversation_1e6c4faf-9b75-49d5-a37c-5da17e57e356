<template>
  <div class="order-bk">
    <div class="tab-box">
      <div
        class="first-buy"
        :class="{'first-buy-act': activeTab === 'firstOrder'}"
        @click="handleTabClick('firstOrder')"
      ></div>
      <div
        class="second-buy"
        :class="{'second-buy-act': activeTab === 'moreOrder'}"
        @click="handleTabClick('moreOrder')"
      ></div>
    </div>
    <div class="content">
      <div v-if="!orderList[activeTab].length" class="no-data">暂无订单记录哦~</div>
      <div v-else>
<!--        首购订单-->
        <div v-if="activeTab==='firstOrder'">
          <div class="first-order-title">
            <span>订单id</span>
            <span>完成时间</span>
            <span>skuId</span>
          </div>
          <div v-for="(item, index) in orderList[activeTab]" :key="index" class="order-list">
            <div class="order-list-line1">
              <!--订单id-->
              <span> {{ item.orderId }}</span>
              <!--订单完成时间-->
              <span> {{ item.orderEndTime ? dayjs(item.orderEndTime).format('YYYY-MM-DD') : '--' }}</span>
              <!--skuid数组-->
              <span>
                <div v-for="(item, index) in item.skuId" :key="index">{{item}}</div>
              </span>
            </div>
          </div>
        </div>
        <!--复购订单-->
        <div v-else>
          <div class="more-order-title">
            <span>订单id</span>
            <span>复购罐数</span>
            <span>skuId</span>
          </div>
          <div v-for="(item, index) in orderList[activeTab]" :key="index" class="order-list order-listFu">
            <div  v-for="(seriesType, index) in item.oederTypeNum" :key="index" class="more-order-box">
              <div class="series-name-context">
                <span>机制名：{{seriesType.type}}</span>
                <span>此机制对应罐数：{{seriesType.stepMoreNum}}</span>
              </div>
              <div class="order-list-line2">
                <!--订单id-->
                <span> {{ item.orderId }}</span>
                <!--复购罐数-->
                <span> {{ seriesType.moreNum }}</span>
                <!--订单id-->
                <span>
                <div v-for="(skuIdItem, index) in item.skuId" :key="index">{{skuIdItem}}</div>
              </span>
              </div>
            </div>
          </div>
      </div>
        </div>
    </div>
     <div class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { inject, ref } from 'vue';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';

interface orderInfo {
  activityId: number;
  createTime: string;
  encryptPin: string;
  id: number;
  orderEndTime: string;
  orderId: string;
  orderPrice: string;
  orderStartTime: string;
  orderStatus: string;
  orderType: number;
  shopId: number;
  updateTime: string;
}

interface OrderList {
  firstOrder: orderInfo[];
  moreOrder: orderInfo[];
}

const orderList = ref<any>({
  firstOrder: [],
  moreOrder: [],
});

const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
const getMyOrder = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/92012/getUserOrders');
    orderList.value.firstOrder = data.firstOrder || [];
    orderList.value.moreOrder = data.moreOrder || [];
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
const isPreview = inject('isPreview') as boolean;
if (!isPreview) {
  getMyOrder();
}
const activeTab = ref<'firstOrder' | 'moreOrder'>('firstOrder');

const handleTabClick = (tab: 'firstOrder' | 'moreOrder') => {
  activeTab.value = tab;
};

</script>

<style scoped lang="scss">
.order-bk {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/277474/34/22692/109238/6800998dF8d8a7f76/d98ee2e9ff962a6d.png');
  background-size: 100%;
  width: 7.02rem;
  height: 9.8rem;
  background-repeat: no-repeat;
  position: relative;
  .tab-box{
    width: 5.3rem;
    display: flex;
    justify-content: space-around;
    position: absolute;
    top: 2.5rem;
    left: 1.1rem;
    .first-buy {
      width: 1.24rem;
      height: 0.44rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/281462/29/21006/4786/6800998fF2f4f70f7/121dc76b69888cd5.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .first-buy-act {
      width: 1.24rem;
      height: 0.44rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/279417/14/21832/3718/6800998dFe3b54f25/306616d4d800259a.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .second-buy {
      width: 1.24rem;
      height: 0.44rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/271192/19/23108/4931/68009990Ffc54c296/9eba72235d0ab6d7.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .second-buy-act {
      width: 1.24rem;
      height: 0.44rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/274627/5/22472/3933/680099e1F10243e03/b91ebec8ffb8a1b3.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .close {
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/281515/2/20664/1823/67fe160aF8aff0f7c/58b1e21a2ee3ea1d.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: .8rem;
    height: .8rem;
    border-radius: 50%;
  }
  .no-data {
    text-align: center;
    line-height: 3.5rem;
    font-size: 0.24rem;
    color: #a58542;
  }
  .content {
    position: absolute;
    left: 1.1rem;
    top: 3rem;
    width: 5.3rem;
    height: 5.4rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #000;
    white-space: pre-wrap;
    .first-order-title,
    .more-order-title{
      display: flex;
      justify-content: space-between;
      color: #a58542;
      font-size: 0.26rem;
      span {
        flex: 1;
        text-align: center;
      }
    }
    .order-listFu{
      border: 0.01rem solid #d3a24e;
      border-radius: 0.16rem;
      margin-top:0.1rem;
    }
    .order-list {
      margin-left: calc(50% - 5rem / 2);
      padding: 0.1rem;
      width: 5rem;
      //height: 1.14rem;
      margin-bottom: 0.14rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .order-list-line1 {
        margin-bottom: 0.2rem;
        display: flex;
        justify-content: space-between;
        color: #a58542;
        font-size: 0.19rem;
        //border: 1px dotted #000;
        span {
          flex: 1;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
      }
      .more-order-box{
        //border: 1px dashed #000;
        //border-radius: 0.1rem;
        margin-bottom: 0.1rem;
        .series-name-context{
          display: flex;
          // text-align: center;
          // justify-content: space-around;
          color: #a58542;
          font-size: 0.23rem;
          border-bottom: 1px dashed #999;
          padding-bottom: 0.1rem;
          span:first-child {
            width: 2rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      .order-list-line2 {
        // margin-bottom: 0.2rem;
        margin-top:0.1rem;
        display: flex;
        justify-content: space-between;
        color: #a58542;
        font-size: 0.19rem;

        span {
          flex: 1;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
</style>
