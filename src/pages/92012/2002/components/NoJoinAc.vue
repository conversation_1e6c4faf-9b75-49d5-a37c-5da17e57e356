<template>
  <div class="no-join-ac-all">
    <div class="knowDiv" @click="closeDialog()"></div>
  </div>
</template>
<script lang="ts" setup>
const emits = defineEmits(["close"]);
const closeDialog = () => {
  emits("close");
};
</script>
<style lang="scss" scoped>
.no-join-ac-all {
  width: 5.46rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/311888/15/10756/27947/6853d1fdF55657f77/4ea2d1a1dcde07c1.png)
    no-repeat;
  height: 7.49rem;
  background-size: contain;
  box-sizing: border-box;
  text-align: center;
  position: relative;
  .knowDiv {
    position: absolute;
    bottom: 2.1rem;
    width: 2.5rem;
    height: 0.6rem;
    // background-color: red;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
