<template>
    <div class="box">
      <div class='dialog'>
        <div class="join-text">
          <span>您还不是店铺会员</span><br>
          <span>本活动仅适用于{{baseInfo.shopName}}会员</span><br>
          <span>请先加入会员解锁</span>
        </div>
        <div class="confirm-btn" @click="openCard"/>
        <div class="cancel-btn" @click="closeDialog"/>
      </div>
    </div>
</template>

<script setup lang='ts'>
import { computed, defineEmits, inject } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});
const isShowPopup = computed(() => props.showPopup);

const openCard = async () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

const emits = defineEmits(['closeDialog']);

const closeDialog = () => {
  emits('closeDialog');
};
</script>
<style lang='scss' scoped>
.box {
  .dialog {
    width: 6.08rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/282143/38/22745/37901/68020f75F811fd5f2/98f3b8e7204660b2.png) no-repeat;
    height: 9.32rem;
    background-size: contain;
    box-sizing: border-box;
    text-align: center;
    position: relative;
    .join-text{
      width: 100%;
      font-size: 0.26rem;
      position: absolute;
      height: 2rem;
      line-height: 0.5rem;
      top: 2.5rem;
      text-align: center;
      color: #a16f09;
      font-weight: 600;
    }
    .confirm-btn {
      font-size: 0.26rem;
      position: absolute;
      width: 3rem;
      height: 0.67rem;
      top: 4.8rem;
      left: 1.6rem;
    }
    .cancel-btn{
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/281515/2/20664/1823/67fe160aF8aff0f7c/58b1e21a2ee3ea1d.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translate(-50%);
      width: .8rem;
      height: .8rem;
      border-radius: 50%;
    }
  }
}

</style>
