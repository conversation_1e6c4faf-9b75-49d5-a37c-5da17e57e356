<template>
  <div class="rule-bk">
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="time">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
        <div class="name">{{ item.prizeNum }}京豆</div>
        <div class="name">{{ item.status }}</div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
    <div class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  winStatus: number;
}

const prizes = ref([]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/92012/getUserPrizes');
    res.data.forEach((item: Prize) => {
      item.createTime = dayjs(item.createTime).format('YYYY-MM-DD');
    });
    closeToast();
    prizes.value = res.data;
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/281391/11/20663/20614/67fe19a4F9b5a72cf/47f5ccea887faa3b.png');
  background-size: 100%;
  width: 5.5rem;
  height: 8.5rem;
  background-repeat: no-repeat;
  position: relative;

  .close {
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/281515/2/20664/1823/67fe160aF8aff0f7c/58b1e21a2ee3ea1d.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: .8rem;
    height: .8rem;
    border-radius: 50%;
  }

  .content {
    width: 5.5rem;
    height: 4rem;
    overflow-y: scroll;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 1.8rem;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    .prize {
      padding: 0.2rem 0;
      color: #000;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .time,
      .name {
        flex: 1;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .no-data {
      text-align: center;
      line-height: 4rem;
      font-size: 0.24rem;
      color: #000;
    }
  }
}
</style>
