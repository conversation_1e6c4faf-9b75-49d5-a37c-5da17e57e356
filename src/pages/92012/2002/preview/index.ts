import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData2 = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/276952/7/20437/20341/67fca8deF0f7ed1ed/4fcd6409200996b7.png', // 页面背景图
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/276635/32/20738/206384/67fca977Fb170f884/463d00822cee0689.png', // 主KV
  shopNameColor: '#72421f', // 店铺名称颜色
  orderBtn: '//img10.360buyimg.com/imgzone/jfs/t1/274462/26/20599/3585/67fca71cF2ccfc878/74e0176ed788d404.png', // 订单按钮
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/275706/12/19558/4582/67fca71dF97cd1c0b/ee2966076f5a8334.png', // 活动规则按钮
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/271189/15/21038/4598/67fca71dFcc89cab5/0ac9d9effaa23ee6.png', // 我的奖品按钮
  thresholdBg: '//img10.360buyimg.com/imgzone/jfs/t1/273089/6/21310/32058/67fe016dFb654b2c7/22aa32ea5c722f13.png', // 机制背景图
  receivePrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/272953/24/20667/4227/67fdeff9F752a9b03/63672b1f8c54e177.png', // 领取奖品按钮
  seriesTabAct: '//img10.360buyimg.com/imgzone/jfs/t1/272232/4/19290/2918/67fca707F2a4fed3c/6c4f5fe3f2a708d5.png', // 系列tab选中
  seriesTabNotAct: '//img10.360buyimg.com/imgzone/jfs/t1/279602/17/20082/3018/67fca707F16522dc9/339c69766232c8df.png', // 系列tab未选中
  sampleBg: '//img10.360buyimg.com/imgzone/jfs/t1/281204/2/19698/28610/67fdeff8F33419919/7bba2cc0acf7e8b5.png', // 试用装背景框
  sampleGoodsBg: '//img10.360buyimg.com/imgzone/jfs/t1/275400/29/20023/20537/67fdeff9F615c862d/1e7fc96599dc0def.png', // 试用装商品背景框
  formalBg: '//img10.360buyimg.com/imgzone/jfs/t1/283012/32/19755/29052/67fdeff8F04447eb8/11660750d4040e08.png', // 正装背景框
  formalGoodsBg: '//img10.360buyimg.com/imgzone/jfs/t1/273048/38/21285/7600/67fdeff7F43d355b9/45a6edb9528575ee.png', // 正装商品背景框
  productExample: '//img10.360buyimg.com/imgzone/jfs/t1/280332/33/19396/39446/67fdeff9Fa9db5005/a97ac52df777fcd5.png', // 商品示例
  returnTopBtn: '//img10.360buyimg.com/imgzone/jfs/t1/281927/19/20513/6930/67fdeffbFe97e6bc4/0e62ced284886869.png', // 返回顶部按钮
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/277620/1/20959/64065/67fdcb7bFc3eb1274/c865b067df2393de.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/228100/7/38039/13055/67fdcb7bF095f7c37/83bcbb44d3cddb55.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/278116/36/21130/88070/67fdcb7aFcf57fe7a/210b7d3fb8352132.png',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '零元试喝';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
