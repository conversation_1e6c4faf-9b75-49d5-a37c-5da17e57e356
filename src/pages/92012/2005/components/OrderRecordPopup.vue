<template>
  <div class="order-bk">
    <div class="tab-box">
      <div
        class="first-buy"
        :class="{ 'first-buy-act': activeTab === 'firstOrder' }"
        @click="handleTabClick('firstOrder')"
      ></div>
      <div
        class="second-buy"
        :class="{ 'second-buy-act': activeTab === 'moreOrder' }"
        @click="handleTabClick('moreOrder')"
      ></div>
    </div>
    <div class="content">
      <div v-if="!orderList[activeTab].length" class="no-data">暂无订单记录哦~</div>
      <div v-else>
        <!--        首购订单-->
        <div v-if="activeTab === 'firstOrder'">
          <div class="first-order-title">
            <span>订单id</span>
            <span>完成时间</span>
            <span>skuId</span>
          </div>
          <div
            v-for="(item, index) in orderList[activeTab]"
            :key="index"
            class="order-list"
          >
            <div class="order-list-line1">
              <!--订单id-->
              <span> {{ item.orderId }}</span>
              <!--订单完成时间-->
              <span> {{ item.orderEndTime ? dayjs(item.orderEndTime).format("YYYY-MM-DD") : '--' }}</span>
              <!--skuid数组-->
              <span>
                <div v-for="(item, index) in item.skuId" :key="index">{{ item }}</div>
              </span>
            </div>
          </div>
        </div>
        <!--复购订单-->
        <div v-else>
          <div class="more-order-title">
            <span>订单id</span>
            <span>复购罐数</span>
            <span>skuId</span>
          </div>
          <div
            v-for="(item, index) in orderList[activeTab]"
            :key="index"
            class="order-list order-listFu"
          >
            <div
              v-for="(seriesType, index) in item.oederTypeNum"
              :key="index"
              class="more-order-box"
            >
              <div class="series-name-context">
                <span>机制名：{{ seriesType.type }}</span>
                <span>此机制对应罐数：{{ seriesType.stepMoreNum }}</span>
              </div>
              <div class="order-list-line2">
                <!--订单id-->
                <span> {{ item.orderId }}</span>
                <!--复购罐数-->
                <span> {{ seriesType.moreNum }}</span>
                <!--订单id-->
                <span>
                  <div v-for="(skuIdItem, index) in item.skuId" :key="index">
                    {{ skuIdItem }}
                  </div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { inject, ref } from "vue";
import { closeToast, showLoadingToast } from "vant";
import { httpRequest } from "@/utils/service";
import dayjs from "dayjs";

interface orderInfo {
  activityId: number;
  createTime: string;
  encryptPin: string;
  id: number;
  orderEndTime: string;
  orderId: string;
  orderPrice: string;
  orderStartTime: string;
  orderStatus: string;
  orderType: number;
  shopId: number;
  updateTime: string;
}

interface OrderList {
  firstOrder: orderInfo[];
  moreOrder: orderInfo[];
}

const orderList = ref<any>({
  firstOrder: [],
  moreOrder: [],
});

const emits = defineEmits(["close"]);
const close = () => {
  emits("close");
};
const getMyOrder = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/92012/getUserOrders');
    orderList.value.firstOrder = data.firstOrder || [];
    orderList.value.moreOrder = data.moreOrder || [];
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
const isPreview = inject("isPreview") as boolean;
if (!isPreview) {
  getMyOrder();
}
const activeTab = ref<"firstOrder" | "moreOrder">("firstOrder");

const handleTabClick = (tab: "firstOrder" | "moreOrder") => {
  activeTab.value = tab;
};
</script>

<style scoped lang="scss">
.order-bk {
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/278576/29/22353/20394/6800cf2eF419b2a91/8ba668e12d962321.png");
  background-size: 100% 100%;
  width: 5.66rem;
  height: 7.57rem;
  background-repeat: no-repeat;
  position: relative;
  .tab-box {
    width: 5.6rem;
    display: flex;
    justify-content: space-around;
    position: absolute;
    top: 1.3rem;
    left: 51%;
    transform: translate(-50%);
    .first-buy {
      width: 1.17rem;
      height: 0.5rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/274749/19/22827/2409/6800cf2fF3ae4cfa6/17e6c935ab024d27.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .first-buy-act {
      width: 1.17rem;
      height: 0.5rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/284296/40/21944/1871/6800cf2eF27d5ff97/0194228c28829db9.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .second-buy {
      width: 1.17rem;
      height: 0.5rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/270954/14/22835/2517/6800cf32F5e74542c/4828b4712d2eeb86.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .second-buy-act {
      width: 1.17rem;
      height: 0.5rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/282896/19/20742/2309/6800cf32Ffda88b76/f3a04913c7a03624.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: 0.8rem;
    height: 0.8rem;
    border-radius: 50%;
  }
  .no-data {
    text-align: center;
    line-height: 3.5rem;
    font-size: 0.24rem;
    color: #a16f09;
  }
  .content {
    position: absolute;
    left: 50%;
    top: 2rem;
    transform: translate(-49%);
    width: 5rem;
    height: 3.7rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #000;
    white-space: pre-wrap;
    .first-order-title,
    .more-order-title {
      display: flex;
      justify-content: space-between;
      color: #a16f09;
      font-size: 0.26rem;
      span {
        flex: 1;
        text-align: center;
      }
    }
    .order-listFu{
       border: 0.01rem solid #d3a24e;
      border-radius: 0.16rem;
      margin-top:0.1rem;
    }
    .order-list {
      margin-left: calc(50% - 5rem / 2);
      padding: 0.1rem;
      width: 5rem;
      //height: 1.14rem;
      // margin-bottom: 0.14rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .order-list-line1 {
        // margin-bottom: 0.2rem;
        display: flex;
        justify-content: space-between;
        color: #a16f09;
        font-size: 0.19rem;
        //border: 1px dotted #000;
        span {
          flex: 1;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
      }
      .more-order-box {
        //border: 1px dashed #000;
        //border-radius: 0.1rem;
        margin-bottom: 0.1rem;
        .series-name-context {
          display: flex;
          // text-align: center;
          // justify-content: space-around;
          color: #a16f09;
          font-size: 0.23rem;
          border-bottom: 1px dashed #999;
          padding-bottom: 0.1rem;
          span:first-child {
            width: 2rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      .order-list-line2 {
        // margin-bottom: 0.2rem;
        margin-top: 0.1rem;
        display: flex;
        justify-content: space-between;
        color: #a16f09;
        font-size: 0.19rem;

        span {
          flex: 1;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
</style>
