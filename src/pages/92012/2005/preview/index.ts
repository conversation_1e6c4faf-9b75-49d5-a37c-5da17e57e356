import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData2 = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/276806/5/22345/13517/6800b359F8282633d/29adefd34e9c20fa.png',
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/276449/18/22803/111413/6800b357Ffcbd61a7/87ce04dee03c8cd1.png',
  shopNameColor: '#72421f',
  orderBtn: '//img10.360buyimg.com/imgzone/jfs/t1/281780/35/22156/2429/6800b34eF84e22263/2b458434f3fc9938.png',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/282926/13/21201/2503/6800b352F29375372/bcb892c8514cab02.png',
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/280172/29/21911/2543/6800b34eFaa836415/3ecf8f501697c3f8.png',
  thresholdBg: '//img10.360buyimg.com/imgzone/jfs/t1/277804/32/22748/31998/6800b351F99ac8333/9e35f0c072f5232f.png',
  receivePrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/278209/28/22895/6929/6800b350Ff49dcb0b/0ecf83aeea051bdb.png',
  seriesTabAct: '//img10.360buyimg.com/imgzone/jfs/t1/281597/23/22175/2741/6800b34eF647a2c1f/03411ae8226fa226.png',
  seriesTabNotAct: '//img10.360buyimg.com/imgzone/jfs/t1/285064/35/20918/2408/6800b34eF7e65f851/808ce8d2602b884f.png',
  sampleBg: '//img10.360buyimg.com/imgzone/jfs/t1/278053/27/22886/16467/6800c77dFe18da1bd/6ad9caa4999ec4fe.png',
  sampleGoodsBg: '//img10.360buyimg.com/imgzone/jfs/t1/273685/32/21196/26816/6800b34fF006d595d/c864ed7831fb2481.png',
  formalBg: '//img10.360buyimg.com/imgzone/jfs/t1/272607/25/22725/16006/6800c77cF88712483/fc88da712a6c7b85.png',
  formalGoodsBg: '//img10.360buyimg.com/imgzone/jfs/t1/281582/10/21887/9849/6800b34dF647c9a82/2d0420d59abedeca.png',
  productExample: '//img10.360buyimg.com/imgzone/jfs/t1/281869/2/21851/44645/68007672Fc8868711/255f278290de3cba.png',
  returnTopBtn: '//img10.360buyimg.com/imgzone/jfs/t1/273492/10/21172/6415/6800b353Fab4aa212/3c0dad72b6f000bd.png',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/273023/29/22693/22460/6800b9a3F3d6c0de3/b64bfaf34bbd73c4.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/281953/38/21170/6771/6800b9a2F8b776f55/04aeaeb121f0a873.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/274273/40/22764/40512/6800b9a2F2ec1f795/d2918ea27a96a2fd.png',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '零元试喝';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
