import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/148497/13/41567/85153/66babccaFc2496d76/300c897d381eca70.png',
  ruleBtnImg: '//img10.360buyimg.com/imgzone/jfs/t1/234412/26/24232/4986/66babc8aFf0aa7a99/891314b2f30728c1.png',
  promotionImg: '//img10.360buyimg.com/imgzone/jfs/t1/58504/16/26329/92792/66bacbf0F527f9fa8/76a31d2ef8d00456.png',
  promotionLink: '',
  bottomBtn: '//img10.360buyimg.com/imgzone/jfs/t1/243893/27/15862/7908/66babc49F84e2e9fc/e6c41d62d26ac23c.png',
  bottomBtnLink: '',
  receivedSussessPopup: '//img10.360buyimg.com/imgzone/jfs/t1/47589/32/26560/170619/66babc22Fb132be26/f1c12809e83e246d.png',
  receivedSussessPopupLink: '',
  receivedPopup: '//img10.360buyimg.com/imgzone/jfs/t1/30378/19/22405/160552/66babc1bFd72c77cd/d5ca29bbe5c2dcb8.png',
  receivedPopupLink: '',
  receivedOtherPopup: '//img10.360buyimg.com/imgzone/jfs/t1/29257/22/21907/199368/66babc18F91e2827a/0d242edba61973a6.png',
  receivedOtherPopupLink: '',
  gradePopup: '//img10.360buyimg.com/imgzone/jfs/t1/12029/19/22426/158410/66babc2fFc99622b7/7846b76b6d00e80e.png',
  gradePopupLink: '',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/82291/9/26913/156658/66babc24F997ce741/9797a127a427c65c.png',
  shelfProduct01: '//img10.360buyimg.com/imgzone/jfs/t1/110556/26/51055/26368/66babcf7Fa94e2d29/77e6b79677dbeabb.png',
  shelfProduct01Link: '',
  shelfProduct02: '//img10.360buyimg.com/imgzone/jfs/t1/247930/23/16877/26091/66babcf2F227fa52c/bf2ca35c611ed7f0.png',
  shelfProduct02Link: '',
  shelfProduct03: '//img10.360buyimg.com/imgzone/jfs/t1/35694/39/22490/26912/66babce9F97fc71f1/87f108c80b4b51ce.png',
  shelfProduct03Link: '',
  shelfProduct04: '//img10.360buyimg.com/imgzone/jfs/t1/18154/36/22936/28229/66babce3Fcd7867d7/393bd4ee1cd0c287.png',
  shelfProduct04Link: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '资生堂会员进店送积分';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
