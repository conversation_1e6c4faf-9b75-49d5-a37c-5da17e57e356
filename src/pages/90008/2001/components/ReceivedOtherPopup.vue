<template>
  <div class="rule-bk" :style="furnishStyles.receivedOtherPopup.value">
    <div class="close" @click="close"></div>
    <!-- 去参与更多活动 -->
    <div class="go-more" @click="handleGoMore"></div>
  </div>
</template>

<script lang="ts" setup>
import furnishStyles, { furnish } from '../ts/furnishStyles';

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const handleGoMore = () => {
  window.jmfe.toAny(furnish.receivedOtherPopupLink);
  console.log('handleGoMore');
};
</script>

<style scoped lang="scss">
.rule-bk {
  // background: url(//img10.360buyimg.com/imgzone/jfs/t1/236810/34/9907/10566/65969c00F90424d43/759a43f2c99a47cb.png) no-repeat;
  background-size: 100% 100%;
  width: 5.16rem;
  height: 5.72rem;
  border-radius: 0.4rem;
  position: relative;
  .close {
    width: 0.45rem;
    height: 0.45rem;
    margin: 0 auto;
    // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/226770/28/11313/1145/65969c51Fb9dda6aa/31b3866cf538306e.png');
    background-repeat: no-repeat;
    background-size: 100%;
    position: absolute;
    top: 0.4rem;
    right: 0.3rem;
  }
  .go-more {
    width: 2.55rem;
    height: 0.6rem;
    // background: green;
    position: absolute;
    bottom: 1.15rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
