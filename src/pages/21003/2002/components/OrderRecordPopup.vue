<template>
  <div class="rule-bk">
    <div class="title">我的订单</div>
    <div class="content">
      <div v-if="!orderList.length" class="no-data">暂无订单记录哦～</div>
      <div class="list-box" v-else>
        <div class="order-list" v-for="(item, index) in orderList" :key="index">
          <div class="order-info">
            <div class="order-info-title">订单信息</div>
            <div class="order-info-box">
              <!--                <div class="order-info-img">-->
              <!--                  <img :src="item.skuImage" alt="">-->
              <!--                </div>-->
              <div class="order-info-list">
                <div>下单时间：{{dayjs(item.order.orderStartTime).format('YYYY-MM-DD HH:mm:ss')}}</div>
                <div>订单金额：{{item.order.orderPrice}}元</div>
                <div>订单状态：{{item.order.orderStatus}}</div>
                <div>订单编号：{{item.order.orderId}}</div>
              </div>
            </div>
          </div>
          <div class="gift-info-title">随单领取赠品信息：</div>
          <div class="gift-info">
            <div class="gift-list" v-for="(item2, index2) in item.giftList" :key="index2">
              <div>
                <img :src="item2.giftImage" alt="">
              </div>
              <p>{{item2.giftName}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';

interface Order {
    giftSkuId: number;
    orderId: string;
    orderPrice: string;
    orderStartTime: string;
    orderStatus: string;
    paymentTime: string;
    skuId: number;
}

interface Gift {
  giftImage: string;
  giftName: string;
}

interface list {
  giftList: Gift;
  order: Order;
}

const orderList = ref<list[]>([]);

// const props = defineProps(['orderRestrainStatus']);
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const getRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/21003/receiveRecord');
    orderList.value = data;
    closeToast();
  } catch (error) {
    closeToast();
  }
};

!isPreview && getRecord();
</script>
<style lang="scss" scoped>
.close {
  width: 0.45rem;
  height: 0.45rem;
  margin: 0 auto;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/226770/28/11313/1145/65969c51Fb9dda6aa/31b3866cf538306e.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/236810/34/9907/10566/65969c00F90424d43/759a43f2c99a47cb.png) no-repeat;
  background-size: 100% 100%;
  width: 6.52rem;
  height: 9.42rem;
  border-radius: 0.4rem;

  .title{
    font-size: 0.46rem;
    line-height: 0.61rem;
    padding-top: 0.4rem;
    text-align: center;
    margin: 0 auto;
    width: 2rem;
    background: linear-gradient(to right, #513c28, #8d6a35, #513c28);
    -webkit-background-clip: text;
    color: transparent; /* 隐藏文字本身的颜色 */
  }

  .content {
    //height: 8rem;
    margin: 0 auto;
    padding: 0.2rem 0.35rem 0.3rem 0.4rem;
    font-size: 0.24rem;
    color: #262626;
    white-space: pre-wrap;
    //div {
    //  height: 100%;
    //  overflow-y: scroll;
    //}
    .list-box{
      height: 7.5rem;
      overflow-y: scroll;
    }
    .no-data{
      margin: 3rem auto 0 auto;
      text-align: center;
      background: linear-gradient(to right, #513c28, #8d6a35, #513c28);
      -webkit-background-clip: text;
      color: transparent; /* 隐藏文字本身的颜色 */
    }
  }
}
.order-list{
  margin:0 auto 0.5rem auto;
  padding: 0.2rem 0.2rem 0 0.2rem;
  .order-info{
    border-bottom: 0.01rem solid #513c28;
    //height: 2.3rem;
  }
  .order-info-title{
    font-size: 0.3rem;
    width: 1.6rem;
    line-height: 0.61rem;
    background: linear-gradient(to right, #513c28, #8d6a35, #513c28);
    -webkit-background-clip: text;
    color: transparent; /* 隐藏文字本身的颜色 */
    font-weight: 500;
  }
  .gift-info-title{
    font-size: 0.3rem;
    width: 3.6rem;
    line-height: 0.61rem;
    background: linear-gradient(to right, #513c28, #8d6a35, #513c28);
    -webkit-background-clip: text;
    color: transparent;
  }
  .order-info-box{
    display: flex;
    justify-content: space-between;
    .order-info-img{
      width: 1.46rem;
      height: 1.46rem;
      background-color: #ffffff;
      border-radius: 0.05rem;
      img{
        margin: 0 auto;
        height: 1.46rem;
      }
    }
    .order-info-list{
      font-size: 0.22rem;
      color: #262626;
      padding: 0 0 0.3rem 0;
      div{
        text-align: left;
        margin: 0.1rem 0;
        background: linear-gradient(to right, #513c28, #8d6a35, #513c28);
        -webkit-background-clip: text;
        color: transparent;
      }
      :first-child{
        margin-top: 0;
      }
    }
  }
  .gift-info{
    //height: 2rem;
    .gift-list{
      display: flex;
      //justify-content: space-between;
      margin-bottom: 0.1rem;
      div {
        //width: 1.46rem;
        //height: 1.46rem;
        background-color: #ffffff;
        border-radius: 0.05rem;
        flex:1;
        img {
          margin: 0 auto;
          max-height: 1.46rem;
        }
      }
      p {
        text-align: left;
        display: flex;
        align-items: center;
        background: linear-gradient(to right, #513c28, #8d6a35, #513c28);
        -webkit-background-clip: text;
        color: transparent;
        padding-left: 0.3rem;
        flex:3;
      }
    }
  }
}
</style>
