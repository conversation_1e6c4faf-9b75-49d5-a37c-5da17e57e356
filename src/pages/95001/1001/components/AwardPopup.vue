<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <img :src="prize.prizeImg" alt="" class="prize-img" />
    <div class="content">
      <p class="p1">报名成功</p>
      <p class="prize-name">{{ prize.prizeName }}</p>
      <div>
                <div class="p3" v-if="prize.prizeType === 3">您已经完成活动报名，在完成订单付款，并最终确认收货后发货。请在报名后1小时之内及时填写邮寄地址，以免礼品发放失败！未完成付款或未确认收货的将无法获得礼品</div>
        <div class="p3" v-else-if="prize.prizeType === 12">您已经完成活动报名，在完成订单付款，并最终确认收货后发放。请在报名后1小时之内及时填写信息，以免礼品发放失败！未完成付款或未确认收货的将无法获得礼品</div>
        <div class="p3" v-else-if="prize.prizeType === 7">您已经完成活动报名，在完成订单付款，并最终确认收货后发放（前往我的奖品进行兑换）。未完成付款或未确认收货的将无法获得礼品</div>
        <div class="p3" v-else>您已经完成活动报名，虚拟礼品将在您完成订单付款，并最终确认收货后20至30分钟内自动发放，未完成付款或未确认收货的将无法获得礼品</div>
      </div>
      <div class="btn-list">
        <div class="btn btn-left" @click="shareAct">立即分享</div>
        <div class="btn btn-right" v-if="prize.prizeType === 3" @click="saveAddress">填写地址</div>
        <div class="btn btn-right" v-else-if="prize.prizeType === 12" @click="savePhone">填写信息</div>
        <div class="btn btn-right" v-else @click="close">我知道了</div>
      </div>
    </div>
  </div>
  <div class="thanks-join" v-else>
    <div class="close" @click="close"></div>
    <div class="btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { gotoShopPage } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  prizeImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
  prizeContent: string;
  userReceiveRecordId: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const saveAddress = () => {
  emits('saveAddress', props.prize.userReceiveRecordId, props.prize.userPrizeId);
};

const showCardNum = () => {
  emits('showCardNum', { ...props.prize.result, prizeImg: props.prize.prizeImg, prizeName: props.prize.prizeName });
};

const savePhone = () => {
  const prizeContent = JSON.parse(props.prize.prizeContent);
  emits('savePhone', props.prize.userReceiveRecordId, prizeContent.result.planDesc);
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bk {
  height: 6.9rem;
  width: 6rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/102050/20/27655/237782/6268e9b4E40cf6e02/1ed0ccada5088734.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 1rem;
  .prize-img {
    height: 2rem;
    width: 2rem;
    margin: 0 auto;
  }
  .content {
    width: 5.6rem;
    height: 3.5rem;
    background-color: white;
    border-radius: 0.2rem;
    margin: 0.2rem auto 0;
    padding: 0.3rem;
    .p1 {
      display: block;
      color: #262626;
      font-size: 0.24rem;
      font-weight: 500;
    }
    .prize-name {
      font-size: 0.36rem;
      font-weight: bold;
      margin: 0.2rem 0 0;
      text-align: center;
      color: #ff3333;
    }
    .p3 {
      font-size: 0.18rem;
      color: #b8b8b8;
      text-align: center;
      height: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        color: #f2270c;
      }
    }
    .btn-list {
      display: flex;
      justify-content: space-between;
      .btn {
        width: 2.4rem;
        height: 0.9rem;
        line-height: 0.9rem;
        text-align: center;
        color: white;
        font-size: 0.3rem;
        border-radius: 0.1rem;
      }
      .btn-left {
        background: linear-gradient(to right, #f2270c, #ff6320);
      }
      .btn-right {
        background: #ff9900;
      }
    }
  }
}
.thanks-join {
  width: 6rem;
  height: 6.3rem;
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/192914/3/22012/24033/623985b9E8508c48b/019e54628504b2dc.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 4.5rem;
  .close {
    height: 0.24rem;
    width: 0.24rem;
    position: absolute;
    right: 0.34rem;
    top: 0.34rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/143521/26/18654/387/5fd9e706E8f1594e3/ae5cc06440559585.png) no-repeat;
    background-size: 100% 100%;
  }
  .btn {
    display: block;
    margin: 0 auto;
    width: 4rem;
    height: 0.76rem;
    line-height: 0.76rem;
    color: #fff;
    font-size: 0.3rem;
    border-radius: 0.38rem;
    text-align: center;
    background-color: rgb(201, 0, 26);
  }
}
</style>
