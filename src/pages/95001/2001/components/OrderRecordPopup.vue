<template>
  <div class="rule-bk">
    <div class="title">
      <div class="title-rotate">我的订单</div>
      <img data-v-705393a4="" alt="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="h-[40vh]pb-2 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div v-if="orderList.length" class="orderListDivAll">
        <div class="orderItemList" v-for="(order, index) in orderList" :key="index">

          <div class="orderBox">
            <div class="orderTopBox">
              <span class="orderNum">订单编号</span>
              <span class="orderTime">下单时间:{{ order.orderStartTime ? dayjs(order.orderStartTime).format('YYYY-MM-DD HH:mm:ss') : '&#45;&#45;' }}</span>
            </div>
            <div class="orderBottomBox">
              <span class="orderIdNum">{{order.orderId }}</span>
              <span class="orderPrice">￥{{order.orderPrice }}</span>
              <span class="orderStatus">{{order.orderStatus }}</span>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="noDataDiv  text-sm h-[80%] flex justify-center items-center">暂无订单记录哦～</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';

interface Order {
  orderId: string;
  orderStatus: string;
  orderPrice: string;
  orderEndTime: number;
  orderStartTime: string;
}

const orderList = ref<Order[]>([]);
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
const getRecord = async () => {
  // orderList.value = [
  //   {
  //     orderId: '123456789',
  //     orderStatus: '已完成',
  //     orderPrice: '100.00',
  //     orderEndTime: 1630000000000,
  //     orderStartTime: '2021-09-01 12:00:00',
  //   },
  //   {
  //     orderId: '987654321',
  //     orderStatus: '已完成',
  //     orderPrice: '200.00',
  //     orderEndTime: 1630000000000,
  //     orderStartTime: '2021-09-01 12:00:00',
  //   },
  // ];
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/95001/userOrder');
    orderList.value = data;
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};

!isPreview && getRecord();
</script>

<style lang="scss" scoped>
.rule-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;
  height: 7rem;
  .text-white{
    padding-left: 0.33rem;
  }
  .noDataDiv{
    padding-top: 2rem;
  }
  .orderListDivAll {
    max-height: 4.5rem;
    overflow-y: scroll;

    .orderItemList {
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/105172/35/44995/1314/6503b473F030c0abf/73e798967da06461.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 6.90rem;
      height: 1.06rem;
      margin-left: calc(50% - 6.90rem / 2);
      margin-bottom: 0.14rem;
    }

    .orderBox {

      font-size: 0.2rem;
      color: #aaa;
      padding: 0 0.3rem;
      margin: 0 auto;

      .orderTopBox {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        height: 0.42rem;

        .orderNum {
          color: #999999;
          font-size: 0.19rem;
        }

        .orderTime {
          color: #999999;
          font-size: 0.19rem;
        }
      }

      .orderBottomBox {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        font-size: 0.3rem;
        color: #303030;
        height: 0.62rem;

        .orderIdNum {
          color: #262626;
          font-size: 0.23rem;
        }

        .orderPrice {
          color: #262626;
          font-size: 0.23rem;
        }

        .orderStatus {
          color: #262626;
          font-size: 0.23rem;
          text-align: right;
          width: 2rem;
        }
      }
    }

  }

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/228047/15/22924/16362/668ce2acF3811da1a/a562c9321ce48073.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.9rem;
    line-height: 2rem;
    font-size: 0.6rem;
    color: #7f5a35;
    font-weight: bold;
    .title-rotate {
      transform: rotate(-5deg);
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }
}
</style>
