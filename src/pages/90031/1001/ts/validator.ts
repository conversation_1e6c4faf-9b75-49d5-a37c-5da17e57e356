// 这是一个chatGPT生成的校验方法
import { showToast } from 'vant';

/**
 * 判断字符串是否是手机号码
 * @param str 待检查的字符串
 * @returns 字符串是手机号码返回 true，否则返回 false
 */
export const isPhoneNumber = (phoneNumber: string): boolean => /^(13[0-9]|14[5-9]|15[0-35-9]|166|17[01235678]|18[0-9]|19[0-35-9])\d{8}$/.test(phoneNumber);

/**
 * 判断字符串是否是邮箱
 * @param str 待检查的字符串
 * @returns 字符串是邮箱返回 true，否则返回 false
 */
export const isEmail = (email: string): boolean => /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(email);

/**
 * 判断字符串是否是邮政编码
 * @param str 待检查的字符串
 * @returns 字符串是邮箱返回 true，否则返回 false
 */
export const isPostCode = (postCode: string): boolean => /^[0-9]{6}$/.test(postCode);

/**
 * 判断字符串是否包含 Emoji 表情符号
 * @param text 待检查的字符串
 * @returns 字符串中包含 Emoji 不包含表情符号返回 true，否则返回 false
 */
export const containsEmoji = (text: string): boolean => {
  const reg = /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g;
  return !reg.test(text);
};

/**
 * 判断字符串是否包含 特殊字符
 * @param str 待检查的字符串
 * @returns 字符串中包含 不包含特殊字符 返回 true，否则返回 false
 */
export const containsSpecialChars = (str: string): boolean => {
  const regex = /[*\-=~!@#$%^&(){}[\]<>/?·！@#￥…&（）——【】‘；：”“’。，、？]/;
  return !regex.test(str);
};

interface Rule {
  required?: boolean;
  message?: string;
  validator?: RegExp | ((value: any) => boolean);
}

type ValidateRules = Record<string, Rule[]>;
/**
 * 校验表单数据是否符合规则
 * @param rules 规则
 * @param data 待校验的表单数据
 * @returns 是否通过校验   true 通过
 */
export const validateDataWithRules = (rules: ValidateRules, data: { [key: string]: any }): boolean => {
  let valid = true;
  Object.entries(rules)
    .some(([key, ruleSet]) => ruleSet.some(({
      required = false,
      validator,
      message = `${key}校验失败`,
    }) => {
      const value = data[key];
      if (required && !value) {
        showToast(message);
        valid = false;
        return true;
      }
      if (validator && !((typeof validator === 'function' && validator(value))
        || (validator instanceof RegExp && validator.test(value)))) {
        showToast(message);
        valid = false;
        return true;
      }
      return false;
    }));

  return valid;
};
/**
 * 平滑滚动到指定位置
 * @param {number} distance: 滚动的位置
 * @param {number} time： 滚动时间
 */
export const scrollToPosition = (distance: number, time: number) => {
  // body兼容性检查
  let doc = document.documentElement;
  document.body.scrollTop += 1;
  if (document.body.scrollTop !== 0) {
    doc = document.body;
  }
  // st为函数执行时滚动条到文档顶部的距离
  const st = doc.scrollTop;

  // 需要滑动的距离
  const need = st - distance;
  // 循环时间,值越小越耗性能
  const loopTime = 20;
  // 循环次数
  let loopCount = time / loopTime;
  // 每次步进的距离
  const step = need / loopCount;
  const scrollTimer = setInterval(() => {
    if (loopCount > 0) {
      // 此项设置是为了精准定位
      loopCount !== 1 ? (doc.scrollTop -= step) : (doc.scrollTop = distance);
      loopCount -= 1;
    } else {
      clearInterval(scrollTimer);
    }
  }, loopTime);
};

export default {
  containsSpecialChars,
  containsEmoji,
  isPhoneNumber,
  validateDataWithRules,
};
