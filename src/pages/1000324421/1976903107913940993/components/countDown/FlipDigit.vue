<!--
翻牌数字组件
单个数字的翻牌显示组件，可复用于小时、分钟、秒的任意位
-->

<template>
  <ul class="flip" :class="{ play: isPlaying }">
    <li
      v-for="digit in digitItems"
      :key="digit"
      :class="{
        active: currentValue === digit,
        before: beforeValue === digit
      }"
    >
      <a href="javascript:void(0)">
        <div class="up">
          <div class="shadow"></div>
          <div class="inn">{{ digit }}</div>
        </div>
        <div class="down">
          <div class="shadow"></div>
          <div class="inn">{{ digit }}</div>
        </div>
      </a>
    </li>
  </ul>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  value: number    // 当前显示的数字
  max: number      // 最大值，如 9 或 5
}

const props = defineProps<Props>()

// 组件内部状态
const currentValue = ref(-1)
const beforeValue = ref(-1)
const isPlaying = ref(false)

// 生成所有可能的数字数组
const digitItems = computed(() => {
  return Array.from({ length: props.max + 1 }, (_, i) => i)
})

// 监听 value 变化，触发翻牌动画
watch(() => props.value, (newValue, oldValue) => {
  if (newValue === oldValue || currentValue.value === newValue) return

  // 开始动画状态
  isPlaying.value = true

  // 使用单层 requestAnimationFrame 确保在下一帧更新
  requestAnimationFrame(() => {
    // 更新前后值
    beforeValue.value = currentValue.value
    currentValue.value = newValue
  })
}, { immediate: true })
</script>
<style lang="scss">
@font-face {
  font-family: 'HisenseAlfabet',sans-serif;
  src: url('../../assets/HisenseAlfabet-Regular.otf') format('otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
</style>
<style scoped lang="scss">
// ============ 变量定义 ============
// 宽度
$flip-width: 1rem;
// 高度
$flip-height: 0.7rem;
// 字体大小
$flip-font-size: 0.46rem;
// 边框圆角
$flip-border-radius: 0.1rem;
// 背景颜色
$flip-bg-color: #fff;
// 文字颜色
$flip-text-color: #000;
// 阴影
$flip-shadow: 0 2px 5px rgba(0, 0, 0, 0.7);
// 分割线高度
$flip-divider-height: 3px;
// 动画时长
$animation-duration: 0.5s;
// 动画延迟
$animation-delay: 0.5s;

// ============ 动画定义 ============
// 上翻动画
@keyframes flipUp {
  0% { transform: rotateX(0deg); }
  100% { transform: rotateX(-90deg); }
}

// 下翻动画
@keyframes flipDown {
  0% { transform: rotateX(90deg); }
  100% { transform: rotateX(0deg); }
}

// 索引变化动画
@keyframes zIndexChange {
  0%, 5% { z-index: 2; }
  5.01%, 100% { z-index: 4; }
}

// 淡入动画
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// 淡出动画
@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

// ============ 主样式 ============
// 翻牌容器
ul.flip {
  // Reset
  margin: 0;
  padding: 0;
  list-style: none;

  // 翻牌容器
  position: relative;
  width: $flip-width;
  height: $flip-height;
  font-size: $flip-font-size;
  font-weight: bold;
  font-family: 'HisenseAlfabet',sans-serif;
  line-height: $flip-height;
  border-radius: $flip-border-radius;
  box-shadow: $flip-shadow;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;

  // 中间分隔线（固定在容器中间，不受任何动画影响）
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    z-index: 100;
    width: 100%;
    height: $flip-divider-height;
    background-color: rgba(0, 0, 0, 0.4);
    pointer-events: none;
  }

  // 翻牌项
  li {
    margin: 0;
    padding: 0;
    list-style: none;
    z-index: 1;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;

    &:first-child {
      z-index: 2;
    }

    // 链接
    a {
      display: block;
      height: 100%;
      perspective: 200px;
      text-decoration: none;
      color: inherit;

      // 上下半部分容器
      div {
        z-index: 1;
        position: absolute;
        left: 0;
        width: 100%;
        height: 50%;
        overflow: hidden;
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;

        // 阴影层
        .shadow {
          position: absolute;
          width: 100%;
          height: 100%;
          z-index: 2;
        }

        // 数字内容
        .inn {
          position: absolute;
          left: 0;
          z-index: 1;
          width: 100%;
          height: 200%;
          color: $flip-text-color;
          text-shadow: 0 1px 2px #000;
          text-align: center;
          background-color: $flip-bg-color;
        }

        // 上半部分
        &.up {
          transform-origin: 50% 100%;
          top: 0;
          will-change: transform;
          border-radius: $flip-border-radius $flip-border-radius 0 0;

          .inn {
            top: 0;
            border-radius: $flip-border-radius $flip-border-radius 0 0;
          }
        }

        // 下半部分
        &.down {
          transform-origin: 50% 0;
          bottom: 0;
          will-change: transform;
          border-radius: 0 0 $flip-border-radius $flip-border-radius;

          .inn {
            bottom: 0;
            border-radius: 0 0 $flip-border-radius $flip-border-radius;
          }
        }
      }
    }
  }

  // ============ 动画状态 ============
  &.play {
    li {
      // 前一个状态（即将翻走的）
      &.before {
        z-index: 3;

        .up {
          z-index: 2;
          animation: flipUp $animation-duration linear both;

          .shadow {
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 1) 100%);
            animation: fadeIn $animation-duration linear both;
          }
        }

        .down .shadow {
          background: linear-gradient(to bottom, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0.1) 100%);
          animation: fadeIn $animation-duration linear both;
        }
      }

      // 当前激活状态（即将翻入的）
      &.active {
        animation: zIndexChange $animation-duration $animation-delay linear both;
        z-index: 2;

        .down {
          z-index: 2;
          animation: flipDown $animation-duration $animation-delay linear both;
        }

        .up .shadow {
          background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 1) 100%);
          animation: fadeOut $animation-duration 0.3s linear both;
        }

        .down .shadow {
          background: linear-gradient(to bottom, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0.1) 100%);
          animation: fadeOut $animation-duration 0.3s linear both;
        }
      }
    }
  }
}
</style>
