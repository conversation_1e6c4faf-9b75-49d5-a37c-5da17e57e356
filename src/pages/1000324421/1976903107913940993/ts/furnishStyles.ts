import { computed, reactive } from 'vue';

export const furnish = reactive({
  kv:"",
  pageBg:"",
  pageBgColor: "",
  countDownBg: "",
  ladderListBg: "",
  ladderItemBg:"",
  inviteBoxBg:"",
  skuTitleImg:"",
  cmdImg: '',
  h5Img: '',
  mpImg: '',
});

const pageBg = computed(() => ({
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
  backgroundColor: furnish.pageBgColor ?? '',
}));
const countDownBg = computed(() => ({
  backgroundImage: furnish.countDownBg ? `url("${furnish.countDownBg}")` : '',
}));
const ladderListBg = computed(() => ({
  backgroundImage: furnish.ladderListBg ? `url("${furnish.ladderListBg}")` : '',
}));
const ladderItemBg = computed(() => ({
  backgroundImage: furnish.ladderItemBg ? `url("${furnish.ladderItemBg}")` : '',
}));
const inviteBoxBg = computed(() => ({
  backgroundImage: furnish.inviteBoxBg ? `url("${furnish.inviteBoxBg}")` : '',
}));

export default {
  pageBg,
  countDownBg,
  ladderListBg,
  ladderItemBg,
  inviteBoxBg,
};
