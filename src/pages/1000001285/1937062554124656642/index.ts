/**
 * @Description:caoshijie
 * @Date: 2025-06-24 17:24:30
 * @Description: 汤臣倍健 新客礼
 * @OtherPath: src\pages\1000001285\1937062554124656642\index.ts
 */
import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init, checkStatus, clipboardText } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';

initRem(750);

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  disableShare: true,
  disableThreshold: true,
  showUnStartPage: true,
  showFinishedPage: true,
};

init(config).then(async ({ baseInfo, pathParams, userInfo, decoData }) => {
  checkStatus(baseInfo, true, true);
  // 设置页面title
  document.title = baseInfo.activityName;
  app.provide('baseInfo', baseInfo);
  app.provide('userBaseInfo', userInfo);
  app.provide('pathParams', pathParams);
  app.provide('decoData', decoData);
  app.use(clipboardText);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
