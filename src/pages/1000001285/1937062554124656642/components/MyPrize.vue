<template>
  <popup v-model:show="isShowPopup" :close-on-click-overlay="false" teleport="body" @open="openDialog" @click-overlay="closeDialog">
    <div class="form">
      <div class="title">
        <div class="title-text">领取时间</div>
        <div class="title-text">奖品名称</div>
        <div class="title-text">状态</div>
      </div>
      <template v-if="prizeList.length">
        <div class="title list" v-for="item in prizeList">
          <div class="list-text">{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          <div class="list-text">{{ item.prizeName }}</div>
          <div class="list-text" :class="{ 'save-address': !item.isAddress && item.type === 1 }" @click="() => toAddress(item)">{{ item.isAddress || item.type !== 1 ? '已填写' : '填写地址' }}</div>
        </div>
      </template>
      <div v-else class="no-data">暂无数据</div>
    </div>
  </popup>
</template>

<script lang="ts" setup>
import { showToast, closeToast, Popup } from 'vant';
import { reactive, ref, computed, PropType } from 'vue';
import { getMyPrize } from '../script/ajax';
import dayjs from 'dayjs';

const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog', 'editAddress']);
const closeDialog = () => {
  emits('closeDialog');
};
interface PrizeItem {
  createTime: string;
  prizeName: string;
  isAddress: boolean;
  type: number;
  addressId: string;
}
const prizeList = ref<PrizeItem[]>([]);
const openDialog = async () => {
  prizeList.value = await getMyPrize();
};
const toAddress = (item: PrizeItem) => {
  if (!item.isAddress && item.type === 1) {
    emits('editAddress', item.addressId);
  }
};
</script>

<style scoped lang="scss">
.form {
  background-color: #fff;
  color: #000;
  width: 6rem;
  height: 3rem;
  .title {
    background-color: #f00;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    padding: 0.1rem 0;
    box-sizing: border-box;
    div {
      flex: 1;
    }
    margin-bottom: 0.1rem;
  }
  .list {
    background-color: #fff;
    color: #000;
  }
}
.no-data {
  text-align: center;
  margin-top: 1rem;
}
.save-address {
  text-decoration: underline;
  color: dodgerblue;
}
</style>
