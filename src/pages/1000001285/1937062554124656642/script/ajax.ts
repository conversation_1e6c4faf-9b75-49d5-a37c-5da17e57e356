import { showLoadingToast, showToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';

// 活动主接口
export const getActInfo = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/byhealth/1937062554124656642/init', {});
    closeToast();
    return data;
  } catch (error) {
    closeToast();
    console.log('🚀 ~ getActInfo ~ error:', error);
    showToast('服务繁忙，请稍后再试~');
  }
  return '';
};

// 领取奖品
export const receivePrize = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/byhealth/1937062554124656642/receivePrize', {});
    // const { data } = {
    //   data: {
    //     activityPrizeId: '1937066359486652418',
    //     prizeImg: null,
    //     prizeName: '柠檬酸钙DK50片',
    //     prizeType: 3,
    //     result: {
    //       cardDesc: null,
    //       cardNumber: null,
    //       cardPassword: null,
    //       fuluEquityResp: null,
    //       grantId: '1938171949495717889',
    //       id: null,
    //       jdOrderId: null,
    //       msg: null,
    //       result: 'o250626174616181803',
    //       success: true,
    //     },
    //     sortId: 1,
    //     status: 1,
    //     userPrizeId: '1938171949495717889',
    //   },
    // };
    closeToast();
    return data;
  } catch (error: any) {
    closeToast();
    console.log('🚀 ~ getActInfo ~ error:', error);
    showToast(error.message || '服务繁忙，请稍后再试~');
  }
  return '';
};
// 保存地址
export const editAddress = async (form: any) => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    await httpRequest.post('/byhealth/1937062554124656642/editAddress', {
      ...form,
    });
    closeToast();
    return true;
  } catch (error) {
    closeToast();
    console.log('🚀 ~ getActInfo ~ error:', error);
    showToast('服务繁忙，请稍后再试~');
    return false;
  }
  return false;
};
// 我的奖品
export const getMyPrize = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/byhealth/1937062554124656642/myPrize', {});
    closeToast();
    return data;
  } catch (error) {
    closeToast();
    console.log('🚀 ~ getActInfo ~ error:', error);
    showToast('服务繁忙，请稍后再试~');
    return [];
  }
  return [];
};
