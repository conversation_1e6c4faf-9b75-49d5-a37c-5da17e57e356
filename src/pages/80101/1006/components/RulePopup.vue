<template>
  <div class="rule-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/242474/30/546/14399/65854d1bF5a3cbd87/94072ce882c32bd9.png" alt="" class="text" />
      <div class="close" @click="close"></div>
    </div>
    <div class="content">
      <div v-html="rule"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/243262/34/343/14918/65854f02Fba2bb3b9/95452d0aa6a9b975.png) no-repeat;
  background-size: 100%;
  width: 100vw;

  .title {
    position: relative;
    height: 0.97rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem;
    .text {
      height: 0.77rem;
    }
  }

  .close {
    width: 0.55rem;
    height: 0.55rem;
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/108458/3/36291/1280/65854d1bFa872a3fe/b1c5a7fea47344d6.png");
    background-repeat: no-repeat;
    background-size: 100%;
  }

  .content {
    height: 8.35rem;
    width: 7rem;
    margin: 0.2rem auto 0  auto;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/232560/34/9110/4825/65854f01Fdebcf9a2/a37203c75a9d6936.png) no-repeat;
    background-size: 100%;
    padding: 0.3rem 0.3rem 0.3rem 0.3rem;
    font-size: 0.24rem;
    color: #262626;
    white-space: pre-wrap;
    word-break: break-all;
    div {
      height: 100%;
      overflow-y: scroll;
    }
  }
}
</style>
