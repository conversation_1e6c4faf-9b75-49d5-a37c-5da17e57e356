<template>
  <div class="rule-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>进店逛逛</div>
      <div class="rightLineDiv"></div>
      <img
        alt=""
        data-v-705393a4=""
        src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png"
        class="close"
        @click="emits('close')" />
    </div>
    <div class="content">
      <div v-for="item in shopInfo" class="btn" :key="item.shopId" @click="gotoShopPage(item.shopId)">{{ item.shopName }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { gotoShopPage } from '@/utils/platforms/jump';
import { ref, defineEmits, defineProps, inject, watch } from 'vue';

const emits = defineEmits(['close']);

const shopInfo = ref<any>([
  {
    shopName: '波司登京东自营旗舰店  ',
    shopId: 1000384206,
  },
  {
    shopName: '波司登官方旗舰店',
    shopId: 44892,
  },
  {
    shopName: '波司登户外官方旗舰店',
    shopId: 12622397,
  },
  {
    shopName: '波司登奥特莱斯旗舰店',
    shopId: 12558108,
  },
  {
    shopName: '波司登服饰官方旗舰店 ',
    shopId: 82047,
  },
]);
</script>

<style scoped lang="scss">
.rule-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .content {
    text-align: center;
    // padding-top: 3rem;
    font-size: 0.3rem;
    height: 35vh;
    overflow-y: scroll;
    .btn {
      width: 3.5rem;
      height: 0.7rem;
      line-height: 0.7rem;
      text-align: center;
      color: white;
      font-size: 0.3rem;
      border-radius: 0.1rem;
      background-color: #ff9900;
      margin: 0.3rem auto;
    }
  }
}
</style>
