<template>
  <CommonDrawer title="我的订单" @close="emits('close')">
    <div class="h-[40vh] px-2 pb-2 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="text-gray-400 text-center">下单状态为已完成时，才可以领取对应奖励。</div>
      <div v-if="orderList.length">
        <div class="grid grid-cols-3 mt-3 bg-white p-2 rounded" v-for="(order, index) in orderList" :key="index">
          <div class="flex col-span-2">
            <div>订单编号：</div>
            <div>{{ order.orderId }}</div>
          </div>
          <div class="flex">
            <div>订单状态：</div>
            <div :class="order.orderStatus === '完成' ? 'text-yellow-500' : 'text-green-500'">{{ order.orderStatus }}</div>
          </div>
          <div class="flex col-span-2">
            <div>下单时间：</div>
            <div>{{ order.orderStartTime ? dayjs(order.orderStartTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}</div>
          </div>
          <div class="flex">
            <div>订单金额：</div>
            <div>¥{{ order.orderPrice }}</div>
          </div>
        </div>
      </div>
      <div v-else class="text-gray-400 text-sm h-[80%] flex justify-center items-center">暂无订单记录哦～</div>
    </div>
  </CommonDrawer>
</template>

<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';

import { ref } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';

interface Order {
  orderId: string;
  orderStatus: string;
  orderPrice: string;
  orderEndTime: number;
  orderStartTime: string;
}

const orderList = ref<Order[]>([]);
const props = defineProps(['orderRestrainStatus']);
const emits = defineEmits(['close']);

const getRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/80101/getMyOrder');
    orderList.value = data;
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};

!isPreview && getRecord();
</script>
