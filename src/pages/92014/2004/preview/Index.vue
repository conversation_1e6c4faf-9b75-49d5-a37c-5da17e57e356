<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <img :src="furnish.actBg" alt="" class="bg-img" />
    <div>
      <!-- <img :src="furnish.ruleImg" alt="" class="rule-btn" @click="showRule = true" /> -->
      <img :src="furnish.joinBtn" alt="" class="get-prize" @click="toast" />
    </div>
  </div>
  <!--活动规则-->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!--我的订单-->
  <VanPopup teleport="body" v-model:show="showOrderRecord">
    <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
  </VanPopup>
  <!--领取攻略-->
  <VanPopup teleport="body" v-model:show="showStrategyPopup">
    <StrategyPopup @close="showStrategyPopup = false"></StrategyPopup>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showAddLimit">
    <ThresholdNew @close="showAddLimit = false"  />
  </VanPopup>
</template>
<script setup lang="ts">
import { ref, inject, onMounted } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import StrategyPopup from '../components/StrategyPopup.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';
import ThresholdNew from '../components/ThresholdNew.vue';
const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

// 新增的门槛弹窗
const showAddLimit = ref(false);
const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const shopName = ref('');
const total = ref(0);

const isLoadingFinish = ref(true);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
};
const prizeList = ref<Prize>(defaultStateList);

type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuListPreview = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);
const nextStateAmount = ref(0);

const showRule = ref(false);
const ruleTest = ref('');
const showAward = ref(false);
const showOrderRecord = ref(false);
const showStrategyPopup = ref(false);

// 活动规则相关
const showRulePopup = async () => {
  showRule.value = true;
};

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 页面截图
const isCreateImg = ref(false);

const createImg = async () => {
  showRule.value = false;
  showOrderRecord.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修时选择框
const showSelect = ref(false);
// 装修展示页面  1主页 2成功页 3领取攻略
const activeKey = ref('1');
const isSuccess = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuListPreview) {
    skuListPreview.value = data.skuListPreview;
  }
  if (data.orderSkuList) {
    orderSkuList.value = data.orderSkuList;
  }
  ruleTest.value = data.rules;
  shopName.value = data.shopName;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
  showSelect.value = false;
});
// 点击边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

registerHandler('activeKey', (data: any) => {
  activeKey.value = data;
  if (activeKey.value === '3') {
    showStrategyPopup.value = true;
  }
  if (activeKey.value === '2') {
    isSuccess.value = true;
    showStrategyPopup.value = false;
  }
  if (activeKey.value === '1') {
    isSuccess.value = false;
    showStrategyPopup.value = false;
  }
});

onMounted(() => {
  if (activityData) {
    prizeList.value = activityData.prizeList;
    ruleTest.value = activityData.rules;
    orderSkuList.value = activityData.orderSkuList;
    // endTime.value = dayjs(activityData.endTime).valueOf();
    nextStateAmount.value = prizeList.value[0].stepAmount || 0;
    shopName.value = activityData.shopName;
    skuListPreview.value = activityData.skuListPreview;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style lang="scss" scoped>
.bg {
  min-height: 100vh;
  position: relative;
  background-repeat: no-repeat;
  background-size: 100%;
  .bg-img {
    width: 100%;
  }
  .rule-btn {
    position: absolute;
    top: 1.04rem;
    right: 0;
    width: 1.05rem;
  }
  .get-prize {
    position: absolute;
    top: 9rem;
    left: 50%;
    transform: translateX(-50%);
    width: 1.87rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
