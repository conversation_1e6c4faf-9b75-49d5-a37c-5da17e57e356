<template>
  <VanPopup v-model:show="limitPopup" :closeOnClickOverlay="false" z-index="9999">
    <div class="bg" v-if="thresholdType === 4">
      <img :src="userInfo.avatar" alt="" class="avatar" />
      <div class="nickname">用户：{{ userInfo.nickname }}</div>
      <div class="title">加入店铺会员才能参与活动哦!</div>
      <div class="tip1">加入会员:HERMES爱马仕香水美妆京东自营官方旗舰店</div>
      <div class="btn" @click="eventClick(1)">立即开卡</div>
    </div>
    <div class="bg" v-else-if="thresholdType === 5">
      <img :src="userInfo.avatar" alt="" class="avatar" />
      <div class="nickname">用户：{{ userInfo.nickname }}</div>
      <div class="title">关注店铺才能参与活动哦!</div>
      <div class="tip1">关注店铺:HERMES爱马仕香水美妆京东自营官方旗舰店</div>
      <div class="btn" @click="eventClick(2)">关注店铺</div>
    </div>
    <div class="bg" v-else-if="thresholdType === 8">
      <img :src="userInfo.avatar" alt="" class="avatar" />
      <div class="nickname">用户：{{ userInfo.nickname }}</div>
      <div class="title" style="font-size: 0.3rem">关注店铺并加入会员才能参与活动哦!</div>
      <div class="tip1">关注店铺并加入会员:HERMES爱马仕香水美妆京东自营官方旗舰店</div>
      <div class="btn" @click="eventClick(4)">关注店铺并开卡</div>
    </div>
    <div class="close" @click="close"></div>
  </VanPopup>
</template>
<script setup lang="ts">
import { defineEmits, defineProps, inject, ref, watch, onMounted } from 'vue';
import { gotoShopPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { Handler } from '@/utils/handle';
import useThreshold from '@/hooks/useThreshold';
import openCard from '@/utils/openCard';
import { UserInfo } from '@/utils/products/types/UserInfo';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

const baseInfo = inject('baseInfo') as BaseInfo;
const userInfo = inject('userInfo') as UserInfo;
const handler = Handler.getInstance();

const props = defineProps(['data', 'show']);
const emits = defineEmits(['update:show']);

const data = baseInfo.thresholdResponseList;

const thresholdType = ref(4);
if (data.find((item) => item.thresholdCode === 4)) {
  thresholdType.value = 4;
} else if (data.find((item) => item.thresholdCode === 5)) {
  thresholdType.value = 5;
} else if (data.find((item) => item.thresholdCode === 8)) {
  thresholdType.value = 8;
}

const limitPopup = ref(props.show);
const close = () => {
  limitPopup.value = false;
  emits('update:show', false);
};

watch(
  () => props.show,
  (val) => {
    limitPopup.value = val;
  },
);

const fellowShop = async () => {
  lzReportClick('fellowShop');
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    await httpRequest.post('/common/followShop');
    showToast({
      message: '关注成功',
      forbidClick: true,
    });
    return true;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
    return false;
  }
};
const eventClick = async (type: number) => {
  switch (type) {
    case 1:
      // 去开卡
      lzReportClick('join');
      openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
      // openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
      // window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;`;
      break;
    case 2:
      // 关注店铺
      await fellowShop();
      setTimeout(() => {
        window.location.reload();
      }, 1500);
      break;
    case 3:
      // 进店铺
      await gotoShopPage(baseInfo.shopId);
      break;
    case 4:
      // 关注店铺并立即入会
      await fellowShop();
      lzReportClick('join');
      openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
      // openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
      // window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;`;
      break;
    default:
      console.log('~');
  }
};
// const openThreshold = () => {
//   limitPopup.value = useThreshold({
//     thresholdList: baseInfo.thresholdResponseList,
//   });
//   return baseInfo.thresholdResponseList.length;
// };

// onMounted(() => {
//   handler.on('onThresholdOpen', () => openThreshold());
//   handler.on('onThresholdIf', () => baseInfo.thresholdResponseList.length);
// });
</script>
<style lang="scss" scoped>
.bg {
  position: relative;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/304012/7/11017/8176/684feffaFae02153d/d73aeb9f379bf4c5.png) no-repeat;
  background-size: 100%;
  width: 5.5rem;
  height: 7.4rem;
  color: #444444;
  padding-top: 1.65rem;
  .avatar {
    width: 0.86rem;
    height: 0.86rem;
    border-radius: 50%;
    margin: 0 auto;
  }
  .nickname {
    font-size: 0.2rem;
    text-align: center;
    padding-top: 0.2rem;
  }
  .title {
    font-size: 0.34rem;
    line-height: 0.34rem;
    padding-top: 0.73rem;
    padding-bottom: 0.25rem;
    text-align: center;
  }
  .tip1 {
    text-align: center;
    font-size: 0.2rem;
    opacity: 0.5;
    margin-bottom: 0.2rem;
  }
  .btn {
    margin: 0 auto;
    width: 1.87rem;
    height: 0.5rem;
    background-color: #444444;
    color: #fff;
    text-align: center;
    line-height: 0.5rem;
    font-size: 0.18rem;
    border-radius: 0.08rem;
  }
}
.close {
  width: 0.7rem;
  height: 0.7rem;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0rem;
}
</style>
