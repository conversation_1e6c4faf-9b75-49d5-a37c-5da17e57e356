import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData = {
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/311339/15/10096/213275/684fedffFaffd6a30/f25411ce37ab6e12.png',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/236087/37/11174/14302/6596861eF8a7c5840/596ef1d14b83d307.png',
  actBgColor: '',
  joinBtn: '//img10.360buyimg.com/imgzone/jfs/t1/299815/25/15872/1778/684fedfdF3c874a39/02e2e90259c69165.png',
  successBtn: '//img10.360buyimg.com/imgzone/jfs/t1/300274/19/15468/1506/684fedfeF25e7918f/b81dce6d55c879f6.png',
  ruleImg: '//img10.360buyimg.com/imgzone/jfs/t1/279162/31/18263/1717/67f7af74F616737c9/aace214116988f93.png',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员令牌复购';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', _decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
