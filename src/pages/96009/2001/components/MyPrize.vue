<template>
  <div class="rule-bk">
    <div class="tabBox">
      <div class="tab-item" :class="{active: tabIndex === 1}" @click="changeTab(1)">小罐奖品</div>
      <div class="tab-item" :class="{active: tabIndex === 2}" @click="changeTab(2)">复购奖品</div>
    </div>
    <div class="content">
      <div v-for="(item, index) in showPrizesList.prizeInfo" :key="index" class="prize">
        <div class="time">{{ item.drawDate }}</div>
        <div class="name">{{ item.prizeName }}</div>
        <div class="status" >
          <div class="statusBtn">
            <div v-if="item.prizeType === '3'">
              <span v-if="(item.status === 0 || item.status === 1 || item.status === 2) && !item.address" @click="toSaveAddress(item.userPrizeId)">填写地址</span>
              <span v-else-if="item.status === 3">已发货</span>
              <span v-else>待发货</span>
            </div>
            <div v-else-if="item.prizeType === '7'" class="copy-btn" :copy-text="getCopyText(item.prizeContent)">复制链接</div>
            <div v-else>已发放</div>
          </div>
        </div>
      </div>
      <div v-if="!showPrizesList.prizeInfo?.length" class="no-data">暂无数据~</div>
    </div>
    <div class="close" @click="close"></div>
  </div>
  <VanPopup teleport="body" v-model:show="saveAddressPopup" :closeOnClickOverlay="false">
    <SaveAddress v-if="saveAddressPopup" @close="closeSaveAddress" :userPrizeId="userPrizeId"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, reactive, ref } from 'vue';
import {showLoadingToast, closeToast, showToast} from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import Clipboard from "clipboard";

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  address: string,
  city: string,
  county: string,
  mobile: string,
  prizeContent: string,
  prizeImg: string,
  prizeKey: string,
  prizeName: string,
  prizeType: number,
  province: string,
  realName: string,
  status: number,
  userPrizeId: number
}
const prizes = reactive([] as Prize[]);
const showPrizesList = reactive({
  prizeInfo:[],
  type: 1,
});

/**
 * 获取用户奖品列表
 */
const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/96009/getUserPrizes');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
    showPrizesList.prizeInfo = res.data[0].prizeInfo;
    showPrizesList.type = res.data[0].type;
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

const tabIndex = ref(1);
const changeTab = (index: number) => {
  tabIndex.value = index;
  const filteredPrizes = prizes.filter((item) => item.type === index);
  showPrizesList.prizeInfo = filteredPrizes.length > 0 ? filteredPrizes[0].prizeInfo : [];
  showPrizesList.type = index;
};

if (!isPreview) {
  getUserPrizes();
}

const saveAddressPopup = ref(false);
const userPrizeId = ref('');

/**
 * 保存地址信息
 * @param id
 */
const toSaveAddress = (id: string) => {
  userPrizeId.value = id;
  saveAddressPopup.value = true;
};

/**
 * 关闭收货地址
 * @param type
 */
const closeSaveAddress = () => {
  saveAddressPopup.value = false;
  emits('close');
};

/**
 * 复制礼品卡信息
 * @param content
 */
const getCopyText = (content:any) => {
  let text = '';
  const prizeContent = JSON.parse(content);
  if (prizeContent.cardNumber && prizeContent.cardPassword) {
    text = `${prizeContent.cardNumber}\n${prizeContent.cardPassword}`;
  } else if (prizeContent.cardNumber && !prizeContent.cardPassword) {
    text = prizeContent.cardNumber;
  }
  return text;
};

// 复制卡号
const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>

<style scoped lang="scss">
.rule-bk {
  background-size: 100% 100%;
  width: 5.73rem;
  height: 7.09rem;
  position: relative;
  padding-top: 1rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/298024/5/25412/73086/687f2e03F89b462c7/13a8e66deb706609.png') no-repeat;
  background-size: 100% 100%;
  .tabBox{
    width: 5.74rem;
    height: 0.6rem;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 0.24rem;
    margin-bottom: 0.5rem;
    .tab-item{
      width: 1.81rem;
      height: 0.42rem;
      background-color: #fdedc9;
      color: #925906;
      line-height: 0.42rem;
      border-radius: 0.2rem;
      text-align: center;
    }
    .active {
      width: 1.81rem;
      height: 0.42rem;
      background-color: #925906;
      color: #fff;
      border-radius: 0.2rem;
      text-align: center;
    }
  }
  .content {
    height: 4.2rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    font-weight: 500;
    white-space: pre-wrap;
    padding: 0 0.3rem;
    /* background: #fff; */

    .prize {
      padding: 0.2rem 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #454545;
      .time,
      .name,
      .status {
        width: 30%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: pre-wrap;
        overflow-y: scroll;
        word-wrap: break-word;
        color: #e7a13f;
        .statusBtn{
          width: 1.04rem;
          height: 0.31rem;
          color: #e7a13f;
          margin: 0 auto;
          font-size: 0.2rem;
        }
      }
    }

    .no-data {
      text-align: center;
      line-height: 3.95rem;
      font-size: 0.24rem;
      color: #dfbb8a;
    }
  }
  .close{
    width: 0.7rem;
    height: 0.7rem;
    position: relative;
    top: 0.1rem;
    left: 50%;
    transform: translate(-50%);
    /* background: #fff; */
  }
}
</style>
