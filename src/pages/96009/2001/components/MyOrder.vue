<template>
  <div class="rule-bk">
    <div class="tabBox">
      <div class="tab-item" :class="{active: tabIndex === 1}" @click="changeTab(1)">小罐订单</div>
      <div class="tab-item" :class="{active: tabIndex === 2}" @click="changeTab(2)">复购订单</div>
    </div>
    <div class="content">
      <div class="prizeTitle">
        <!--订单号-->
        <div class="idTitle">订单号</div>
        <!--完成时间-->
        <div class="idTitle">时间</div>
        <!--skuid-->
        <div class="idTitle">skuId</div>
        <!--状态-->
        <div class="idTitle">状态</div>
      </div>
      <div v-if="!showOrders.orderList?.length" class="no-data">暂无订单数据~</div>
      <div v-else class="listDiv">
        <div v-for="(item, index) in showOrders.orderList" :key="index" class="ordersItem">
          <!-- 订单号列 -->
          <div class="id">{{ item.orderId }}</div>
          <!-- 完成时间列 -->
          <div class="endTime">{{ item.orderStartTime }}</div>
          <!-- SKU ID列 - 垂直排列多个SKU -->
          <div class="skuIdColumn">
            <div v-for="(skuItem, skuIndex) in item.skuList" :key="skuIndex" class="skuId">
              {{ skuItem.skuId }}
            </div>
          </div>
          <!-- 状态列 - 垂直排列多个状态 -->
          <div class="statusColumn">
            <div v-for="(skuItem, skuIndex) in item.skuList" :key="skuIndex" class="status">
              {{ skuItem.status }}
            </div>
          </div>
        </div>
        <div class="rightBottomTips"/>
      </div>
    </div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive, ref } from 'vue';
import {showLoadingToast, closeToast} from 'vant';
import { httpRequest } from '@/utils/service';

const isPreview = inject('isPreview') as boolean;
const props = defineProps(['awardDays']);
const emits = defineEmits(['close']);

const tabIndex = ref(1);

const close = () => {
  emits('close');
};

const orders = reactive([] as any[]);
const showOrders = reactive({
  orderList: [],
  type: 1,
} as any);

const changeTab = (index: number) => {
  tabIndex.value = index;
  const filteredOrders = orders.filter((item) => item.type === index);
  showOrders.orderList = filteredOrders.length > 0 ? filteredOrders[0].orderList : [];
  showOrders.type = index;
};

/**
 * 获取用户订单列表
 */
const getUserOrders = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res:any = await httpRequest.post('/96009/getUserOrders');
    closeToast();
    orders.splice(0);
    orders.push(...res.data);
    showOrders.orderList = res.data[0].orderList;
    showOrders.type = res.data[0].type;
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserOrders();
}
</script>

<style scoped lang="scss">
.rule-bk {
  background-size: 100% 100%;
  width: 5.74rem;
  height: 7.12rem;
  position: relative;
  padding-top: 1.1rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/288044/36/16547/68589/6879fc22F39b18759/de89157aaa6ba4d9.png') no-repeat;

  .tabBox{
    width: 5.74rem;
    height: 0.6rem;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 0.24rem;
    .tab-item{
      width: 1.81rem;
      height: 0.42rem;
      background-color: #fdedc9;
      color: #925906;
      line-height: 0.42rem;
      border-radius: 0.2rem;
      text-align: center;
    }
    .active {
      width: 1.81rem;
      height: 0.42rem;
      background-color: #925906;
      color: #fff;
      border-radius: 0.2rem;
      text-align: center;
    }
  }
  background-size: 100% 100%;

  .content {
    font-size: 0.24rem;
    font-weight: 500;
    white-space: pre-wrap;
    padding: 0 0.3rem;

    .prizeTitle{
      padding: 0 0 0.1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      img{
        height: 0.36rem;
      }
      .idTitle{
        width: 1.36rem;
        height: 0.54rem;
        color: #925906;
        text-align: center;
        line-height: 0.54rem;
      }
    }
    .listDiv{
      height: 3.4rem;
      overflow-y: scroll;
      font-size: 0.15rem;
      margin: 0 auto;
      .ordersItem {
        padding: 0 0 0.1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        text-align: center;
        color: #e7a13f;

        // 订单号列
        .id {
          width: 25%;
          text-align: center;
          word-wrap: break-word;
          word-break: break-all;
          white-space: normal;
          line-height: 1.4;
        }

        // 完成时间列 - 特殊处理日期换行
        .endTime {
          width: 25%;
          text-align: center;
          word-wrap: break-word;
          white-space: pre-wrap;
          line-height: 1.4;
          word-break: normal;
        }

        // SKU ID列 - 支持垂直排列多个SKU
        .skuIdColumn {
          width: 25%;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .skuId {
            text-align: center;
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            line-height: 1.4;
            margin-bottom: 0.05rem;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        // 状态列 - 支持垂直排列多个状态
        .statusColumn {
          width: 25%;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .status {
            text-align: center;
            word-wrap: break-word;
            white-space: normal;
            line-height: 1.4;
            margin-bottom: 0.05rem;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
      .rightBottomTips {
        background: url('//img10.360buyimg.com/imgzone/jfs/t1/298483/2/24669/1557/687db341Fa8382486/549c123cdf8a20a4.png') no-repeat;
        background-size: 100% 100%;
        width: 2.17rem;
        height: 0.19rem;
        position: absolute;
        bottom: 1.2rem;
        right: 0.6rem;
      }
    }
    .no-data {
      text-align: center;
      line-height: 3.5rem;
      font-size: 0.24rem;
      color: #dfbb8a;
    }
  }
  .close{
    width: 0.7rem;
    height: 0.7rem;
    position: relative;
    top: 0.6rem;
    left: 50%;
    transform: translate(-50%);
    /* background: #fff; */
  }
}
</style>
