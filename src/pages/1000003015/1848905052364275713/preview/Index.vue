<template>
  <div class="container" v-if="loading" :style="{
    backgroundImage: `url(${decoData?.pageBg}})`,
    backgroundColor:decoData?.pageColor
  }">
    <img class="title" :src="decoData.titleBg" alt="" draggable="false">
    <div class="main">
      <div class="tabs">
        <div v-for="(tab, index) in decoData?.tabsList" :key="tab">
          <img class="tab"
               draggable="false"
               v-if="tab.tabTile || tab.tabActiveTile"
               :src="currentTab === index ? tab.tabActiveTile : tab.tabTile"
               @click="tabClick(index)"
               alt=""/>
        </div>
      </div>
      <div class="skuList">
        <div v-for="(sku, index) in decoData?.tabsList[currentTab]?.skuList"
             :key="sku"
             class="sku">
          <img class="sku-column2"
               draggable="false"
               :class="{'sku-column1': index === 0}"
               :src="sku.prizeImg"
               @click="updateCount(sku, 1,index)"
               alt=""/>
          <div v-if="sku.prizeImg" class="addBtnBox" :style="{backgroundColor: decoData.addBtnBg}">
            <img class="car" :src="decoData.addCar" alt="" draggable="false">
            <div class="addBtn" :style="{color: decoData.addBtnColor}">
              <div class="btn" @click="updateCount(sku, -1,index)"><van-icon name="minus" /></div>
              <div class="btn count">{{ skuCount[sku.skuId]?.num || 0 }}</div>
              <div class="btn" @click="updateCount(sku, 1,index)"><van-icon name="plus" /></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="tip">
        <div class="top">
          已选满{{ computedTotalPrice.totalPrice }}元
          <span>立减{{ computedTotalPrice.totalPrice >= decoData.limitTotal ? decoData.discount : 0 }}元</span>
        </div>
        <div class="down">{{ computedTotalPrice.totalPrice >= decoData.limitTotal ? `仅需￥${computedTotalPrice.totalPrice - decoData.discount}` : `距优惠还差￥${decoData.limitTotal - computedTotalPrice.totalPrice}` }}</div>
      </div>
      <img class="add-car"
           draggable="false"
           :src="decoData.joinCar"
           @click="addClickSkus"
           alt="">
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { showToast } from 'vant';

const decoData = ref<any>({});
const currentTab = ref(0);
const skuCount = ref<any>({});
const loading = ref(false);

const tabClick = (index: any) => {
  currentTab.value = index;
};

const computedTotalPrice = computed(() => ({
  totalPrice: Object.values(skuCount.value).reduce((total:any, item:any) => total + Number(item.unitPrice) * item.num, 0),
})) as any;

const updateCount = (item: any, delta: number, index:number) => {
  const id = item.skuId;
  const currentCount = skuCount.value[id]?.num || 0;
  if (currentCount + delta >= 0 && currentCount + delta <= 9) {
    skuCount.value[id] = {
      num: currentCount + delta,
      unitPrice: item.unitPrice,
      position: currentTab.value * 3 + index + 1,
    };
  } else if (currentCount + delta > 9) {
    showToast('该商品加购已达到最大数量');
  }
};

const addClickSkus = () => {
  const addSkus = Object.keys(skuCount.value)
    .filter((key) => skuCount.value[key].num > 0)
    .map((key) => ({
      skuId: key,
      count: skuCount.value[key].num,
      position: skuCount.value[key].position,
    }));

  if (addSkus.length === 0) {
    showToast('请先选择加购商品');
    return;
  }
  showToast('预览页面');
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  decoData.value = data;
  loading.value = true;
  if (type === 'activity') {
    tabClick(data.currentTab);
  } else {
    tabClick(0);
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
.container {
  width: 7.5rem;
  height: 100vh;
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
  padding: 0 0.12rem;
  user-select: none;
  .title {
    width: 7.26rem;
    height: 0.68rem;
    margin: 0 auto;
  }
  .main {
    overflow: hidden;
    .tabs {
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin: 0.2rem auto;
      .tab {
        width: 1.68rem;
        height: 0.43rem;
      }
    }
    .skuList {
      margin: 0 auto;
      width: 7.26rem;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      .sku {
        position: relative;
        .sku-column2 {
          width: 3.61rem;
          display: block;
        }
        .sku-column1 {
          width: 7.26rem;
          margin-bottom: 0.16rem;
        }
        .addBtnBox {
          position: absolute;
          top: 0.08rem;
          left: 0.08rem;
          display: flex;
          justify-content: space-around;
          align-items: center;
          width: 1.2rem;
          height: 0.38rem;
          background-color: #34785d;
          border-top-left-radius: 0.16rem;
          border-bottom-right-radius: 0.16rem;
          .car {
            width: 0.32rem;
            height: 0.22rem;
            @keyframes scale {
              0% {
                transform: scale(1);
              }
              50% {
                transform: scale(1.2);
              }
              100% {
                transform: scale(1);
              }
            }
            animation: scale 1s infinite;
          }
          .addBtn {
            width: 0.54rem;
            height: 0.22rem;
            display: flex;
            justify-content: space-around;
            align-items: center;
            cursor: pointer;
            .btn {
              width: 0.22rem;
              font-size: 0.2rem;
              display: flex;
              justify-content: center;
              align-items: center;
              vertical-align: middle;
            }
            .count {
              width: 0.22rem;
              font-size: 0.26rem;
            }
          }
        }
      }
    }
  }
  .footer {
    margin-top: 0.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .tip {
      width: 3.6rem;
      flex: 1;
      text-align: center;
      font-weight: bold;
      .top {
        font-size: 0.28rem;
        span {
          color: #ae2825;
        }
      }
      .down {
        margin-top: 0.06rem;
        font-size: 0.34rem;
        color: #ae2825;
      }
    }
    .add-car {
      width: 3rem;
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
