<template>
  <!-- 用户身份转变弹窗 -->
  <div class='box'>
    <div class='handle-group'>
      <div style='width: 2.3rem;height: 100%' @click='getShare()'></div>
      <div style='width: 2.3rem;height: 100%' @click='closeDialog()'></div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { inject, PropType } from 'vue';
import { closeDialog } from '../ts/dialog';
import { gotoShopPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';
import { getOpenCardStatus } from '../ts/opencard';
import type { IActivityData } from '../ts/type';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({ activityData: Object as PropType<IActivityData> });
const emits = defineEmits(['getShare']);
const getShare = () => {
  emits('getShare', 'invite');
};
</script>

<style lang='scss' scoped>
.box {
  padding-top: 2.5rem;
  background-image: url("../assets/dialog/change-user-status.png");

  .handle-group {
    width: 5.3rem;
    height: 1.3rem;
    position: absolute;
    bottom: 2.7rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
