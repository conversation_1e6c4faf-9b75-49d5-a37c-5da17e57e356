<template>
  <div class="task-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>获得更多抽奖机会</div>
      <div class="rightLineDiv"></div>
      <img alt="" data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="times">
      今天还有 <span>{{ times }}</span> 次抽奖机会
    </div>
    <div class="content">
      <div v-for="(item, index) in tasks" :key="item.id" class="task">
        <img :src="taskInfo[item.taskType].icon" alt="" class="icon" />
        <div class="info">
          <div class="name">{{ taskInfo[item.taskType].label }}</div>
          <div class="rule">{{ taskRule[item.taskType](item) }}</div>
          <div class="rule" v-if="item.taskType===21">剩余{{item.userScore?item.userScore:0}}积分</div>
          <div class="rule" v-if="item.taskType===27">每日可完成{{item.dailyLimit}}次</div>
          <div class="rule" v-if="item.taskType===27">已成功邀请{{item.inviteCount?item.inviteCount:0}}人，获得{{item.lotteryCountByInvite?item.lotteryCountByInvite:0}}次抽奖机会</div>
        </div>
        <div class="button" v-if="item.taskFinishCount < item.limit || item.taskType === 8" @click="doTask(index)">{{ taskInfo[item.taskType].button }}</div>
        <div class="button button-dis" v-else>{{ taskInfo[item.taskType].buttonDIs }}</div>
      </div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSku" position="bottom">
    <ShowSku v-if="showSku" :detail="tasks[taskDetailIndex]" @close="showSku = false" @refreshTask="refreshTask" @goSku="goSku"></ShowSku>
  </VanPopup>
  <!-- 确认兑换弹窗 -->
  <VanPopup teleport="body" v-model:show="showExchangeConfirm" :close-on-click-overlay="false">
    <ExchangeConfirm :exchangePoint="exchangePoint"  @close="confirmClose" @clickExchangeConfirm="clickExchangeConfirm" />
  </VanPopup>
</template>

<script lang="ts" setup>
import { PropType, inject, ref, onMounted, onUnmounted, nextTick } from 'vue';
import { showToast, showLoadingToast, closeToast } from 'vant';
import ShowSku from './ShowSku.vue';
import { callShare } from '@/utils/platforms/share';
import { httpRequest } from '@/utils/service';
import { Task } from 'pages/10021/1001/ts/type';
import constant from '@/utils/constant';
import dayjs from 'dayjs';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import ExchangeConfirm from './ExchangeConfirm.vue';

const baseInfo = inject('baseInfo') as BaseInfo;
const isPreview = (inject('isPreview') as boolean) ?? false;

const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) as string);

const pros = defineProps({
  times: {
    type: Number,
    default: 0,
  },
  tasks: {
    type: Array as PropType<Task[]>,
    default: () => [],
    required: true,
  },
  shareImg: {
    type: String,
    default: '',
  },
  shareTitle: {
    type: String,
    default: '',
  },
  shopId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close', 'refreshTask']);

const close = () => {
  emits('close');
};

const refreshTask = () => {
  emits('refreshTask');
};
const showExchangeConfirm = ref(false);
const exchangePoint = ref(0);
const showSku = ref(false);
const taskDetailIndex = ref(0);
// 任务时常
const taskSeconds = ref(0);
const confirmClose = () => {
  showExchangeConfirm.value = false;
};
// 上报任务完成
const reportTask = async (taskId: number, skuId = '') => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const apis = {
      2: '/10021/browseShop/shareShop',
      4: '/10021/browseLive/execute',
      9: '/10021/shareSku',
      10: '/10021/shareShop',
      12: '/10021/shareActivity',
      26: '/10021/browseSku/browseSku',
      21: '/10021/pointsExchange/execute',
    };
    const res = await httpRequest.post(apis[taskId], { skuId });
    if (res.code === 200) {
      if (taskId === 21) {
        showExchangeConfirm.value = false;
        showToast('兑换成功');
        refreshTask();
        await nextTick(() => {
          close();
        });
      } else {
        showToast('任务完成');
        refreshTask();
      }
    }
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

const checkTaskTimeLimit = async (taskType: any) => {
  try {
    const res = await httpRequest.post('/10021/checkTask', { taskType });
    return !(res.code === 200);
  } catch (e) {
    showToast(e);
    return true;
  }
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
const doTask = async (index: number) => {
  if (isPreview) return;
  const isTimeLimitExceeded = await checkTaskTimeLimit(pros.tasks[index].taskType);
  if (isTimeLimitExceeded) {
    return;
  }
  if ([5, 6, 7, 8, 9].includes(pros.tasks[index].taskType)) {
    taskDetailIndex.value = index;
    showSku.value = true;
  } else if (pros.tasks[index].taskType === 10) {
    callShare({
      title: shareConfig.shareTitle,
      shareUrl: `https://shop.m.jd.com/?shopId=${pros.shopId}`,
      afterShare: () => {
        reportTask(pros.tasks[index].taskType);
      },
    });
  } else if (pros.tasks[index].taskType === 12) {
    callShare({
      title: shareConfig.shareTitle,
      content: shareConfig.shareTitle,
      shareUrl: window.location.href,
      imageUrl: shareConfig.shareImage,
      afterShare: () => {
        reportTask(pros.tasks[index].taskType);
      },
    });
  } else if (pros.tasks[index].taskType === 2) {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 500,
    });
    // 浏览店铺
    const oldTime = dayjs().valueOf();
    localStorage.setItem('doTask', JSON.stringify({ taskType: pros.tasks[index].taskType, time: oldTime, taskSeconds: Number(pros.tasks[index].taskSeconds) * 1000 }));
    gotoShopPage(baseInfo.shopId);
  } else if (pros.tasks[index].taskType === 4) {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 500,
    });
    // 浏览链接
    const oldTime = dayjs().valueOf();
    localStorage.setItem('doTask', JSON.stringify({ taskType: pros.tasks[index].taskType, time: oldTime, taskSeconds: Number(pros.tasks[index].taskSeconds) * 1000 }));
    // window.location.href = pros.tasks[index].pageLink;
    window.jmfe.toAny(pros.tasks[index].pageLink);
    console.log('浏览链接');
  } else if (pros.tasks[index].taskType === 21) {
    // 积分兑换
    exchangePoint.value = pros.tasks[index].consumeScore;
    showExchangeConfirm.value = true;
    console.log('消耗多少积分');
  } else if (pros.tasks[index].taskType === 26) {
    taskDetailIndex.value = index;
    // 浏览指定商品
    showSku.value = true;
    console.log('浏览指定商品');
  } else if (pros.tasks[index].taskType === 27) {
    // 邀请好友
    console.log('邀请好友');
    shareAct();
  }
};
// 浏览指定商品
const goSku = (skuId:any) => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 1000,
  });
  const oldTime = dayjs().valueOf();
  localStorage.setItem('doTask', JSON.stringify({ taskType: pros.tasks[taskDetailIndex.value].taskType, skuId, time: oldTime, taskSeconds: Number(pros.tasks[taskDetailIndex.value].taskSeconds) * 1000 }));
  gotoSkuPage(skuId);
};
// 消耗积分
const clickExchangeConfirm = () => {
  showExchangeConfirm.value = false;
  reportTask(21);
};
// 检查任务状态
const initTask = async () => {
  const task = window.localStorage.getItem('doTask');
  if (!task) return;
  window.localStorage.removeItem('doTask');
  const newItem = JSON.parse(task);
  const newTime: number = dayjs().valueOf(); // 当前时间戳
  const oldTime: number = newItem.time; // 做任务的时间
  const { taskType, skuId, taskSeconds } = newItem;
  const num = ref(0); // 需要做任务满足时长
  if (taskType === 2 || taskType === 4) {
    // num.value = taskSeconds.value * 1000;
    const downTime = newTime - oldTime >= taskSeconds;
    if (downTime) {
      reportTask(taskType);
    } else {
      showToast('浏览时间不足，不能获得奖励哦~');
    }
  }
  if (taskType === 26) {
    // num.value = taskSeconds.value * 1000;
    const downTime = newTime - oldTime >= taskSeconds;
    if (downTime) {
      reportTask(taskType, skuId);
    } else {
      showToast('浏览时间不足，不能获得奖励哦~');
    }
  }
};
const handleVisiable = async (e: any) => {
  // 如果缓存中存在flag再执行判断visibilityState
  if (document.visibilityState !== 'visible') return;
  await initTask();
};
onMounted(() => {
  initTask();
  document.addEventListener('visibilitychange', handleVisiable);
});
onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisiable);
});
const taskInfo = {
  2: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/136611/18/26554/7346/625fb568E150817e6/886f863f7b0e5e57.png',
    label: '浏览店铺',
    button: '去浏览',
    buttonDIs: '已浏览',
  },
  4: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/136611/18/26554/7346/625fb568E150817e6/886f863f7b0e5e57.png',
    label: '浏览会场/直播',
    button: '去浏览',
    buttonDIs: '已浏览',
  },
  21: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/136611/18/26554/7346/625fb568E150817e6/886f863f7b0e5e57.png',
    label: '积分兑换',
    button: '去兑换',
    buttonDIs: '已兑换',
  },
  26: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/136611/18/26554/7346/625fb568E150817e6/886f863f7b0e5e57.png',
    label: '浏览指定商品',
    button: '去浏览',
    buttonDIs: '已浏览',
  },
  27: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/136611/18/26554/7346/625fb568E150817e6/886f863f7b0e5e57.png',
    label: '邀请好友',
    button: '去邀请',
    buttonDIs: '已邀请',
  },
  5: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/136611/18/26554/7346/625fb568E150817e6/886f863f7b0e5e57.png',
    label: '关注商品',
    button: '去关注',
    buttonDIs: '已关注',
  },
  6: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/136611/18/26554/7346/625fb568E150817e6/886f863f7b0e5e57.png',
    label: '预约商品',
    button: '去预约',
    buttonDIs: '已预约',
  },
  7: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/215894/39/17895/6930/625fb569E7b0ddbd2/71a5d597757641c7.png',
    label: '加购商品',
    button: '去加购',
    buttonDIs: '已加购',
  },
  8: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/215894/39/17895/6930/625fb569E7b0ddbd2/71a5d597757641c7.png',
    label: '购买商品',
    button: '去购买',
    buttonDIs: '已购买',
  },
  9: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/107474/38/27014/7339/625fb569Ec2dbbb5b/a79a2be8591b1c76.png',
    label: '分享商品',
    button: '去分享',
    buttonDIs: '已分享',
  },
  10: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/107474/38/27014/7339/625fb569Ec2dbbb5b/a79a2be8591b1c76.png',
    label: '分享店铺',
    button: '去分享',
    buttonDIs: '已分享',
  },
  12: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/107474/38/27014/7339/625fb569Ec2dbbb5b/a79a2be8591b1c76.png',
    label: '分享活动',
    button: '去分享',
    buttonDIs: '已分享',
  },
};

const taskRule = {
  5: (info: any) => {
    if (info.optWay === 1) {
      return `每关注${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`;
    }
    return `成功关注全部商品，可获得${info.lotteryCount}次抽奖机会`;
  },
  6: (info: any) => `每预约${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`,
  7: (info: any) => {
    if (info.optWay === 1) {
      return `每加购${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`;
    }
    return `成功加购全部商品，可获得${info.lotteryCount}次抽奖机会`;
  },
  8: (info: any) => `每成功下${info.perOperateCount}单，可获得${info.perLotteryCount}次抽奖机会`,
  9: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
  10: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
  12: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
  2: (info: any) => `每成功浏览店铺${info.taskSeconds}秒，可获得${info.perLotteryCount}次抽奖机会`,
  4: (info: any) => `每成功浏览链接${info.taskSeconds}秒，可获得${info.perLotteryCount}次抽奖机会`,
  21: (info: any) => `每消耗${info.consumeScore}积分，可获得${info.perLotteryCount}次抽奖机会`,
  26: (info: any) => `每成功浏览${info.perOperateCount}件指定商品${info.taskSeconds}秒，可获得${info.perLotteryCount}次抽奖机会`,
  27: (info: any) => `每成功邀请${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
};
</script>

<style scoped lang="scss">
.task-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .times {
    text-align: center;
    color: #262626;
    font-size: 0.24rem;
    margin-top: 0.3rem;

    span {
      color: rgb(242, 39, 12);
    }
  }

  .content {
    height: 8.5rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;

    .task {
      background-color: #fff;
      margin-bottom: 0.1rem;
      border-radius: 0.1rem;
      padding: 0.2rem 0.3rem;
      display: flex;
      align-items: center;

      .icon {
        width: 0.83rem;
      }

      .info {
        flex: 1;
        padding: 0 0.39rem;
      }

      .name {
        font-size: 0.3rem;
        color: rgb(255, 153, 0);
      }

      .rule {
        font-size: 0.24rem;
        color: #8c8c8c;
      }

      .button {
        width: 1.36rem;
        height: 0.46rem;
        background: linear-gradient(90deg, rgb(242, 39, 12) 0%, rgb(255, 100, 32) 100%);
        font-size: 0.2rem;
        color: rgb(255, 255, 255);
        border-radius: 0.23rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .button-dis {
        background: #ffe3e3;
      }
    }
  }
}
</style>
