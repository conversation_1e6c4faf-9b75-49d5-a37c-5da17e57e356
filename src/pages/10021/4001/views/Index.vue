<template>
  <div class='bg' :style='furnishStyles.pageBg.value'>
    <div class='header-kv select-hover'>
      <div class='header-content'>
        <div class='shop-name' :style='furnishStyles.shopNameColor.value'>
          <span v-if='furnish.disableShopName === 1'>{{ shopName }}</span>
        </div>
        <div>
          <div class='header-btn' :style='furnishStyles.headerBtn.value' @click='showRulePopup' v-click-track="'hdgz'">活动规则</div>
          <div class='header-btn' :style='furnishStyles.headerBtn.value' @click='showMyPrize = true' v-click-track="'wdjp'">我的奖品</div>
        </div>
      </div>
    </div>
    <div class='wheel-view'>
      <div class='wheel'>
        <img class='pointer-icon' src='//img10.360buyimg.com/imgzone/jfs/t1/328412/40/19158/4846/68c3bf51F96e2a37b/7cda1cf8eccaddb6.png' alt=''>
        <lz-lucky-wheel ref='myLucky' width='83vw' height='83vw' :blocks='furnishStyles.params.value.blocks' :prizes='furnishStyles.params.value.prizes' :buttons='furnishStyles.params.value.buttons' @start='startCallback' @end='endCallback' :defaultConfig='furnishStyles.params.value.defaultConfig' />
      </div>
      <div class='draws-num' :style='furnishStyles.drawsNum.value'>
        <span v-threshold-if>当前还有 {{ chanceNum }}次 抽奖机会</span>
      </div>
      <!--      <div class='draw-btn' v-if='isShowTask'>-->
      <div class='draw-btn'>
        <img :src="furnish.drawBtn ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/223338/24/2898/19533/61946f11E40826e26/a2e11c29a67b4717.png'" alt='' v-threshold-click='showTaskPopup' v-click-track="'hqgdcjjh'" />
      </div>

      <div class='draw-btn' style='margin-top: .2rem'>
        <img :src="furnish.guideActBtn ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/342392/5/2022/4017/68c3bf48F4b3fb999/1ef27d29349ffc1c.png'" alt='' @click='gotoUrl(furnish.guideActUrl)' v-click-track="'ydhd'" />
      </div>
    </div>
    <div v-if='furnish.showWinnersBg === 1' class='winners select-hover' :style='furnishStyles.winnersBg.value'>
      <div class='winners-content'>
        <div class='winner-list swiper-container' ref='swiperRef'>
          <div class='swiper-wrapper' v-if='activityGiftRecords?.length != 0'>
            <div class='winner swiper-slide' v-for='(item, index) in activityGiftRecords' :key='index'>
              <div>
                <img src='https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png' alt='' v-if='!item.avatar' />
                <img v-else :src='item.avatar' alt='' />
                <span style='margin-left: .2rem;color: #6b3624;font-size: .3rem'>{{ item.nickName }}</span>
              </div>
              <span>{{ item.prizeName }}</span>
            </div>
          </div>
          <div v-else>
            <p class='winner-null'>暂无相关获奖信息哦~</p>
          </div>
        </div>
      </div>
    </div>
    <div class='bottom-div'>我也是有底线的哦~</div>
  </div>
  <!-- 活动门槛 -->
  <!-- 规则弹窗 -->
  <VanPopup teleport='body' v-model:show='showRule'>
    <RulePopup :rule='ruleTest' @close='showRule = false'></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport='body' v-model:show='showMyPrize'>
    <MyPrize v-if='showMyPrize' @close='showMyPrize = false'></MyPrize>
  </VanPopup>
  <!-- 做任务弹窗  -->
  <VanPopup teleport='body' v-model:show='showSku'>
    <ShowSku v-if='showSku' :detail='tasks[0]' @close='showSku = false' @refreshTask='refreshTask'></ShowSku>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport='body' v-model:show='showAward'>
    <AwardPopup :prize='award' @close='showAward = false' @saveAddress='toSaveAddress'></AwardPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport='body' v-model:show='showSaveAddress'>
    <SaveAddress v-if='showSaveAddress' :addressId='addressId' :activityPrizeId='activityPrizeId' @close='showSaveAddress = false'></SaveAddress>
  </VanPopup>
</template>

<script setup lang='ts'>
import { ref, reactive, nextTick, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import ShowSku from '../components/ShowSku.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { Task, CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';

const pathParams = inject('pathParams') as any;

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const shopName = ref(baseInfo.shopName);
const isShowTask = ref(false);
const showRule = ref(false);
const ruleTest = ref('');

const gotoUrl = (url: string) => {
  if (url) {
    window.location.href = url;
  }
};
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

// 抽奖次数
const chanceNum = ref(0);

const showMyPrize = ref(false);

const tasks = reactive([] as Task[]);
const showSku = ref(false);

const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

const winnerDemo = [
  {
    'avatar': 'https://img10.360buyimg.com/imgzone/jfs/t1/329644/13/15162/42206/68cd0bacF7eff0cc0/c428700cf637a947.jpg',
    'nickName': 'Z**安',
    'prizeName': '爱肯拿口水巾',
  },
  {
    'avatar': 'http://img10.360buyimg.com/imgzone/jfs/t1/343791/15/5236/283633/68cd0c44F4c11332c/d04452b53982471b.jpg',
    'nickName': 'Y**a',
    'prizeName': '99-10入会会员券',
  },
  {
    'avatar': 'https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png',
    'nickName': 'j**a',
    'prizeName': '爱肯拿粮袋',
  },
  {
    'avatar': 'http://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png',
    'nickName': 'j**h',
    'prizeName': '宠物包',
  },
];

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

// 获取客抽奖次数
const getChanceNum = async () => {
  try {
    const { data } = await httpRequest.post('/10021/chanceNum');
    chanceNum.value = data;
  } catch (error) {
    console.error(error);
  }
};

// 获取中奖名单
const getWinners = async () => {
  try {
    const res = await httpRequest.post('/10021/winners');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data, ...winnerDemo);
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container', {
        autoplay: {
          delay: 1000,
          stopOnLastSlide: false,
          disableOnInteraction: false,
        },
        direction: 'vertical',
        loop: true,
        slidesPerView: 7,
        loopedSlides: 8,
        centeredSlides: true,
      });
    });
  } catch (error) {
    console.error(error);
  }
};

const myLucky = ref();
// 抽奖接口
const lotteryDraw = async () => {
  try {
    const res = await httpRequest.post('/10021/lotteryDraw');
    if (res.data.prizeType) {
      award.value = {
        prizeType: res.data.prizeType,
        prizeName: res.data.prizeName,
        showImg: res.data.prizeImg,
        result: res.data.result ?? '',
        activityPrizeId: res.data.activityPrizeId ?? '',
        userPrizeId: res.data.userPrizeId,
      };
      const index = prizeInfo.findIndex((item) => item.index === res.data.sortId);
      myLucky.value.stop(index);
    } else {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };

      const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
      myLucky.value.stop(index);
    }
  } catch (error) {
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
    myLucky.value.stop(index);
    console.error(error);
  }
  getChanceNum();
  getWinners();
};
const startCallback = async () => {
  lzReportClick('kscj');
  if (baseInfo.status === 1) {
    showToast('活动未开始');
    return;
  }
  if (baseInfo.status === 3) {
    showToast('活动已结束');
    return;
  }
  if (chanceNum.value <= 0) {
    console.log('tasks.length', tasks.length);
    if (tasks.length > 0) {
      showSku.value = true;
    } else {
      showToast('您的抽奖次数已用完');
    }
    return;
  }
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  lotteryDraw();
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};

// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/10021/getPrizes');
    prizeInfo.splice(0);
    prizeInfo.push(...data);
  } catch (error) {
    console.error(error);
  }
};

const drawAgain = () => {
  showAward.value = false;
  startCallback();
};
// 获取任务列表
const getTask = async () => {
  try {
    const { data } = await httpRequest.post('/10021/getTask');
    tasks.splice(0);
    tasks.push(...data);
  } catch (error) {
    console.error(error);
  }
};

// 展示任务弹窗
const showTaskPopup = () => {
  showSku.value = true;
};
const getIsShowTask = async () => {
  try {
    const { data } = await httpRequest.post('/10021/isShowTask ');
    isShowTask.value = data;
  } catch (error) {
    console.error(error);
  }
};
// 做任务后刷新信息
const refreshTask = async () => {
  getChanceNum();
  getTask();
};
const doShareTask = async (shareId: any) => {
  try {
    const res = await httpRequest.post('/10021/invitationHelp/excute', { shareId });
    if (res.code === 200) {
      showToast('助力成功');
    }
  } catch (error) {
    showToast(error.message);
  }
};
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getIsShowTask(), getChanceNum(), getPrizes(), getWinners()]);
    await getTask();
    if (baseInfo.thresholdResponseList.length === 0 && pathParams.shareId) {
      doShareTask(pathParams.shareId);
    }
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style scoped lang='scss'>

@font-face {
  font-family: 'zqkddt';
  src: url('../assets/zqkddt.ttf') format('truetype');
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  padding-top: 4.4rem;
  background-repeat: no-repeat;
  position: relative;
  font-family: zqkddt;
}

.header-kv {

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.1rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.wheel-view {
  height: 11rem;
}

.wheel {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .pointer-icon {
    width: .59rem;
    position: absolute;
    left: 50%;
    top: -.4rem;
    transform: translateX(-50%);
    z-index: 9;
  }

  .wheel-img {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    object-fit: contain;
  }
}

.draws-num {
  text-align: center;
  font-size: 0.3rem;
  margin-bottom: 0.2rem;
  margin-top: 2.9rem;
}

.draw-btn {
  width: 4rem;
  margin: 0 auto;

  img {
    width: 100%;
  }
}

.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 7.5rem;
  height: 14.3rem;
  margin: 0.49rem auto 0;
  padding-top: 5.1rem;

  .winners-content {
    width: 6.6rem;
    height: 8rem;
    background-color: transparent;
    border-radius: 0.1rem;
    margin: 0 auto;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.1rem 0;
  //border-bottom: 1px dashed rgb(230, 230, 230);

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
