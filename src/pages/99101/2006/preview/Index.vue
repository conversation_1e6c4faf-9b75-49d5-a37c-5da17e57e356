<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="kv" >
      <img :src="furnish.actBg" alt="" />
      <div class="btn-list">
        <img @click="toast" :src="furnish.ruleBtn" alt="" />
        <img @click="toast" :src="furnish.myPrizeBtn" alt="" />
      </div>
    </div>
    <div class="series-list-all" v-if="seriesPrizeList.find(option => option.seriesPic)">
      <div class="series-list" v-for="(item, index) in seriesPrizeList" :key="index">
        <div class="prize-item" >
          <img :src="item.seriesPic" alt="" class="prize-img" />
          <img v-if="item.seriesPic" :src="furnish.exchangeBtn" alt="" class="exchange-btn" @click="toast" />
        </div>
      </div>
    </div>

    <div v-if="seriesPrizeList.find(option => option.previewSkuList?.length>0)">
        <div class="skuListTitle"></div>
        <div class="sku-content">
          <div class="title">
            <div class="tabBox">
              <div  v-for="(item, index) in seriesPrizeList"
                    :key="index"
                    class="tabItem"
                    :style="skuActStep==index ? {...furnishStyles.stepBtnSelectBg.value,color: '#086f37'} :{...furnishStyles.stepBtnBg.value,color: '#ffffb9'}"
                    @click="changeSkuActStep(index)"
              >
                {{item.tabName}}
              </div>
            </div>
          </div>
          <div class="sku-sc">
            <div class="sku-list">
              <div class="sku-item" v-for="(item, index) in seriesPrizeList[skuActStep]?.previewSkuList" :key="index" :style="furnishStyles.skuBg.value">
                <img :src="item.skuMainPicture" alt="" class="sku-img" />
                <div class="sku-text" :style="furnishStyles.skuTitleBg.value">{{ item.skuName }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    <img style=" margin: 0 auto;width: 7.1rem"  :src="furnish.ruleImg" alt="" />
    <div class="goToShopDiv" :style="{'backgroundImage': 'url(' + furnish.goToShopBg  + ')'}" @click="toast()"></div>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import { showToast } from 'vant';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import html2canvas from 'html2canvas';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const seriesPrizeList = ref<any[]>([]);

const skuActStep = ref(0);
const changeSkuActStep = (idx: number) => {
  skuActStep.value = idx;
};

const isLoadingFinish = ref(false);
// 保存实物地址相关
const showSaveAddress = ref(false);

const toast = () => {
  showToast('活动预览，仅供查看');
};
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showSaveAddress.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    console.log(data.seriesPrizeList, 'data111');
    seriesPrizeList.value = data.seriesPrizeList;
  } else if (type === 'screen') {
    createImg();
  }
};

// 设置活动后的预览
onMounted(() => {
  console.log('onMounted装修实时数据修改');
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  console.log(activityData, 'activityData');
  if (activityData) {
    seriesPrizeList.value = activityData.seriesPrizeList;
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  min-height: 100vh;
  position: relative;
  padding-bottom: 0.35rem;
  .goToShopDiv{
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/308572/17/959/44869/682548d6F0c4cd4f2/4efdc25ab2dc0b14.png');
    background-repeat:no-repeat;
    background-size: 100% 100%;
    width: 100;
    height: 1.23rem;
    margin-top: 0.2rem;
  }
  .kv {
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .btn-list{
      position: absolute;
      top:3.58rem;
      right: 0rem;
      img {
        display: flex;
        justify-content: flex-end;
        width: 1.28rem;
        margin-bottom: 0.1rem;
     }
    }
  }
  .series-list-all{
    margin: -1.8rem auto auto;
    position: relative;
    z-index: 1;
  }
  .series-list {
    width: 7.06rem;
    transform: translateX(-50%);
    margin-left: 50%;
    margin-bottom: 0.5rem;
    .prize-item {
      position: relative;
      // width: 7.5rem;
      .prize-img {
        width: 7.06rem;
        height: auto;
      }
      .exchange-btn {
        position: absolute;
        bottom: 0.3rem;
        left: 50%;
        width: 3.16rem;
        height: 0.78rem;
        transform: translateX(-50%);
      }
    }
  }
  .skuListTitle{
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/308207/21/846/3712/68255460Fb64d7d4e/e2d86907cfd7f322.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 2.82rem;
    height: 0.42rem;
    margin-left: calc(50% - 2.82rem / 2);
    margin-top: 0.2rem;
  }
  .sku-content {
    position: relative;
    width: 7.4rem;
    // height: 13.45rem;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0 auto;
    .title {
      width: 6.9rem;
      height: 1.2rem;
      margin: 0.4rem auto auto;
      overflow-x: scroll;
      .tabBox {
        display: flex;
        align-items: center;
        width: max-content;
        .tabItem {
          white-space: nowrap;
          flex: 1;
          margin-right: 0.1rem;
          width: 2.32rem;
          height: 0.75rem;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #ffffb9;
          font-weight: bold;
          font-size: 0.24rem;
        }
      }
    }
    .sku-sc {
      overflow-y: scroll;
      max-height: 9.6rem;
    }
    .sku-list {
      width: 6.9rem;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .sku-item {
        position: relative;
        width: 3.36rem;
        height: 3.71rem;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-bottom: 0.3rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .sku-img {
        width: 1.9rem;
        height: 1.9rem;
        border-radius: 0.2rem;
      }
      .sku-text {
        position: absolute;
        // left: 0;
        bottom: 0;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 3rem;
        height: 0.5rem;
        white-space: nowrap;
        font-size: 0.2rem;
        line-height: 0.48rem;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        padding-left: 0.05rem;
        padding-right: 0.25rem;
      }
      .btn {
        position: absolute;
        bottom: 0.15rem;
        left: 50%;
        transform: translateX(-50%);
        width: 2rem;
      }
      .more-btn {
        text-align: center;
        font-size: 0.2rem;
        color: #a86117;
        width: 100%;
      }
    }
  }
}
.step {
  width: 2.1rem;
  height: 0.6rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  text-align: center;
  padding-top: 0.05rem;
  line-height: 0.6rem;
  font-size: 0.22rem;
  .step-svg {
    width: 100%;
    height: 100%;
    stroke-linejoin: round;
    font-weight: bold;
    vertical-align: middle;
  }
}
.gray {
  filter: grayscale(1);
}
</style>
