<template>
  <div class="rule-bk" :style="furnishStyles.ruleBk.value">
    <div class="content" :style="furnishStyles.ruleTextColor.value">{{ rule }}</div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
import furnishStyles from '../ts/furnishStyles';

const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/276322/8/10475/16238/67e3a4c5F63a8e247/7c7d23488e0e018d.png');
  background-size: 100% 100%;
  width: 6.54rem;
  height: 7.35rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.3rem;
  padding-left: 0.2rem;
  padding-right: 0.2rem;
  .content {
    height: 5.6rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #7038d2;
    padding: 0 0.15rem;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
.close {
  width: 0.6rem;
  height: 0.6rem;
  background: url('../assets/closeBtn.png') no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
