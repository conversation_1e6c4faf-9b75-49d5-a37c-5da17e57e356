<template>
  <div class="rule-bk">
    <div class="title">
      <div class="close" @click="close" />
    </div>
    <div class="content">
      <div class="form">
        <VanField v-model="phone" required label="充值手机号：" maxlength="11" type="number" placeholder="请输入"></VanField>
      </div>
      <div class="tipTitleDiv">
        <p class="tip-title">重要充值说明:</p>
        <div class="tip">{{ planDesc }}</div>
      </div>
      <div class="submit" @click="checkForm">提交</div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="confirmPopup">
    <div class="affiem-inner" @click.stop>
      <div class="affiem-title">确认号码</div>
      <div class="affiem-text">{{ phone }}</div>
      <div class="affiem-hint">请再次确定号码填写无误</div>
      <div class="affiem-word">
        <div class="affiem-balck">重要提示：</div>
        <p>① 充值成功后 无法退换；</p>
        <p>② 切勿写错手机号，如充错，责任自行承担；</p>
        <p>③ 点击【确认提交】后，权益领取手机号会无法修改，请 确认无误后再点击确认。</p>
      </div>
      <div class="flex">
        <div class="affirm-cancel-btn m-r-37" @click="confirmPopup = false">返回修改</div>
        <div class="affiem-btn" @click="submit">确认提交</div>
      </div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, showLoadingToast, closeToast } from 'vant';
import { ref } from 'vue';
import { httpRequest } from '@/utils/service';

const props = defineProps({
  userPrizeId: {
    type: String,
    required: true,
  },
  planDesc: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const phone = ref('');

// 二次确认弹窗
const confirmPopup = ref(false);

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10110/fuLuReceive', {
      userPrizeId: props.userPrizeId,
      phone: phone.value,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!phone.value) {
    showToast('请输入充值手机号');
  } else if (!checkPhone.test(phone.value)) {
    showToast('请输入正确的手机号');
  } else {
    // submit();
    confirmPopup.value = true;
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 100vw;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/247440/22/2452/35142/659b58ccF6d44eba1/21b2c103780c3c0a.png');
  background-repeat: no-repeat;
  background-size: 100%;
  height: 7.5rem;
  padding-top: 1.43rem;
  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.55rem;
    height: 0.56rem;
  }

  .content {
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    .form {
      width: 6.9rem;
      margin-left: calc(50% - 6.9rem / 2);

      .van-cell {
        border-radius: 0.12rem;
        margin-bottom: 0.1rem;
        height: 0.9rem;
        ::v-deep .van-field__label {
          text-align: right;
          width: auto;
        }

        &::after {
          display: none;
        }
      }
    }
    .tipTitleDiv {
      width: 7.18rem;
      height: 3.2rem;
      margin-left: calc(50% - 7.18rem / 2);
      overflow-y: scroll;
      padding: 0.35rem 0.24rem;
      .tip-title {
        font-size: 0.25rem;
        color: #262626;
      }
      .tip {
        height: 2rem;
        overflow-y: scroll;
        font-size: 0.2rem;
        color: #262626;
      }
    }
    .submit {
      width: 6.9rem;
      height: 0.8rem;
      line-height: 0.8rem;
      background-color: #513c28;
      color: #fff;
      text-align: center;
      font-size: 0.28rem;
      margin: 0 auto;
    }
  }
}

// 二次确认弹窗
.affirm-box {
  z-index: 300;
  display: flex;
  align-items: center;
  justify-content: center;
}
.affiem-inner {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/246204/9/2174/41126/6597eb80Fabb92632/9b6b0e13f19b6189.png');
  background-size: 100%;
  background-repeat: no-repeat;
  width: 5.96rem;
  height: 7rem;
  padding-top: 0.8rem;
  .closeDiv {
    position: absolute;
    top: 0.64rem;
    right: 0.28rem;
    width: 0.24rem;
    height: 0.24rem;
  }
}
.affiem-title {
  text-align: center;
  font-size: 0.4rem;
  color: #8d6a35;
}
.affiem-text {
  margin-top: 0.09rem;
  font-size: 0.48rem;
  text-align: center;
  line-height: 0.54rem;
  color: #8d6a35;
}
.affiem-hint {
  color: #8d6a35;
  font-weight: 400;
  font-size: 0.22rem;
  text-align: center;
  line-height: 0.54rem;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.22rem;
}
.affirm-cancel-btn {
  width: 2rem;
  height: 0.7rem;
  border-radius: 0.35rem;
  line-height: 0.7rem;
  text-align: center;
  color: #000;
  background-color: #fff;
  font-size: 0.27rem;
}
.affiem-btn {
  width: 2rem;
  height: 0.7rem;
  border-radius: 0.35rem;
  line-height: 0.7rem;
  text-align: center;
  color: #000;
  background-color: #fff;
  font-size: 0.27rem;
}
.m-r-37 {
  margin-right: 0.18rem;
}
.affiem-word {
  color: #8c8c8c;
  font-size: 0.2rem;
  padding: 0.1rem 0.2rem;
  line-height: 0.4rem;
  width: 5.54rem;
  height: 2.43rem;
  margin-left: calc(50% - 5.54rem / 2);
}
.affiem-balck {
  color: #262626;
  line-height: 0.43rem;
}
</style>
