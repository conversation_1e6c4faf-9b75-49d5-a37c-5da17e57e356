<template>
  <div class="rule-bk" :style="furnishStyles.myOrderDialogBg.value">
    <div class="h-[40vh]pb-2 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="title">
        <span class="orderNum">订单号</span>
        <span class="orderTime">下单时间</span>
      </div>
      <div v-if="orderList.length" class="orderListDivAll">
        <div class="orderItemList" v-for="(order, index) in orderList" :key="index">
          <div class="orderBox">
            <span class="orderNum">{{order.orderId}}</span>
            <span class="orderTime">{{dayjs(order.orderStartTime).format('YYYY-MM-DD HH:mm:ss')}}</span>
          </div>
        </div>
      </div>
      <div v-else class="nodataDiv text-white text-sm h-[80%] flex justify-center items-center">暂无订单记录哦～</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';
import furnishStyles, { furnish } from '../ts/furnishStyles';

interface Order {
  orderId: string;
  orderStatus: string;
  orderPrice: string;
  orderEndTime: number;
  orderStartTime: string;
}

const orderList = ref<Order[]>([]);
const emits = defineEmits(['close']);

const getRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90015/getUserOrders');
    orderList.value = data;
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};

!isPreview && getRecord();
// getRecord();
</script>

<style lang="scss" scoped>
.rule-bk {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/255977/20/12275/129606/67889e8eF0f1d2d07/dd7a444a0bef47eb.png) no-repeat;
  background-size: 100% 100%;
  width:6.48rem;
  height: 8rem;
  position:relative;
  .nodataDiv{
    padding-top: 4rem;
    font-size: 0.27rem;
    color: #f13033;
    font-weight: 600;
  }
  .title{
    position: absolute;
    top:1.8rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.27rem;
    color: #f13033;
    font-weight: 600;
    display: flex;
    width:6rem;
    justify-content: space-between;
    align-items: center;
    height: 0.42rem;
    //padding: 0 0.3rem;
    text-align:center;
    .orderNum{
      flex:1;
    }
    .orderTime{
      flex:1;
    }
  }
  .orderListDivAll {
    max-height: 5.30rem;
    overflow-y: scroll;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 2.2rem;
    width:6rem;
    .orderItemList {
      height: 1.06rem;
      line-height: 1.06rem;
    }
    .orderBox {
      display: flex;
      justify-content: space-between;
      align-items: center;
      text-align:center;
      font-size: 0.27rem;
      color: #f13033;
      font-weight: 600;
      .orderNum{
        flex:1;
      }
      .orderTime{
        flex:1;
      }
    }
  }
}
</style>
