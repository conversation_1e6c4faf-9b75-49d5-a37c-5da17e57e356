<template>
  <div class="address-bk" :style="furnishStyles.addressDialog.value">
    <div class="form">
      <div class="form">
        <VanField v-model="form.realName" :label="'收货人'" maxlength="20" placeholder="请输入收货人姓名" :readonly="isPreview" autocomplete="off"></VanField>
        <VanField v-model="form.mobile" label="手机号" maxlength="11" type="number" placeholder="请输入收货人手机号" :readonly="isPreview" autocomplete="off"></VanField>
        <VanField v-model="addressCode" label="所在地区" readonly @click="showAddressSelects" placeholder="请选择省市区" autocomplete="off"></VanField>
        <VanField v-model="form.address" label="详细地址" maxlength="100" placeholder="请输入详细地址" :readonly="isPreview" autocomplete="off"></VanField>
      </div>
    </div>
    <div class="tip">地址提交成功后需修改地址请联系客服</div>
    <div class="sure" @click="closeAddress" v-if="isPreview"></div>
    <div class="submit" @click="checkForm" v-else-if="isPreview===false&&isUpdate===0"></div>
    <div class="change" @click="checkForm" v-else-if="isPreview===false&&isUpdate===1"></div>
  </div>
  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>

</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import { areaList } from '@vant/area-data';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import GetSuccess from './GetSuccess.vue';

const props = defineProps(['isPreview', 'echoInfo', 'prizeInfo', 'isUpdate']);
const emits = defineEmits(['close', 'confirm', 'getSuccess']);
const isSubmitting = ref(false);
// 成功弹窗
const showGetSuccess = ref(false);

const form = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
  postalCode: '',
  userPrizeId: '',
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});

const addressSelects = ref(false);

const showAddressSelects = () => {
  if (!props.isPreview) addressSelects.value = true;
};

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  if (isSubmitting.value) {
    return;
  }
  isSubmitting.value = true;
  if (props.isUpdate === 1) {
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const res = await httpRequest.post('/90015/saveAddress', {
        realName: form.realName,
        mobile: form.mobile,
        province: form.province,
        city: form.city,
        county: form.county,
        address: form.address,
        postalCode: form.postalCode,
        userPrizeId: form.userPrizeId,
      });
      closeToast();
      if (res.code === 200) {
        showToast('修改成功');
        emits('close', true);
      }
    } catch (error: any) {
      closeToast();
      if (error.message) {
        showToast(error.message);
      }
      isSubmitting.value = false;
    }
  } else {
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const res = await httpRequest.post('/90015/receivePrize', {
        addressRequest: {
          realName: form.realName,
          mobile: form.mobile,
          province: form.province,
          city: form.city,
          county: form.county,
          address: form.address,
          postalCode: form.postalCode,
          userPrizeId: form.userPrizeId,
        },
        prizeId: props.prizeInfo.activityPrizeId,
      });
      closeToast();
      if (res.code === 200) {
        emits('getSuccess', props.prizeInfo);
      }
    } catch (error: any) {
      closeToast();
      if (error.message) {
        showToast(error.message);
      }
      isSubmitting.value = false;
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  const reg = /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (reg.test(form.realName)) {
    showToast('姓名不能包含表情');
  } else if (!form.mobile) {
    showToast('请输入手机号');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的手机号');
  } else if (!form.province) {
    showToast('请选择所在地区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else if (reg.test(form.address)) {
    showToast('详细地址不能包含表情');
  } else {
    submit();
  }
};

const closeAddress = () => {
  emits('close', false);
};
onMounted(() => {
  if (props.isUpdate !== 0) {
    console.log('props.echoInfo', props.echoInfo);
    form.realName = props.echoInfo.realName;
    form.mobile = props.echoInfo.mobile;
    form.province = props.echoInfo.province;
    form.city = props.echoInfo.city;
    form.county = props.echoInfo.county;
    form.address = props.echoInfo.address;
    form.postalCode = props.echoInfo.postalCode;
    form.userPrizeId = props.echoInfo.userPrizeId;
  }
});
</script>

<style lang="scss">
.address-bk {
  // background: url('//img10.360buyimg.com/imgzone/jfs/t1/254578/9/13815/15021/67889e8fFc066379d/20e1043304f4de2b.png') no-repeat;
  background-repeat: no-repeat;
  background-size: 100%;
  width: 6.5rem;
  height: 8rem;
  padding-top: 0.5rem;
  position: relative;
  .tip{
    width:6.5rem;
    text-align: center;
    font-size: 0.24rem;
    color: #f13033;
    font-weight: 600;
    position: absolute;
    bottom: 2.2rem;
    left: 50%;
    transform: translateX(-50%);
  }
  .submit {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/268350/12/12530/8408/67889ebbFde7f8883/3d21c4d1aeecb9e9.png') no-repeat;
    background-size: 100% 100%;
    width: 3.56rem;
    height: 0.68rem;
    position: absolute;
    //background: red;
    bottom: 1.3rem;
    left: 50%;
    transform: translateX(-50%);
  }
  .sure{
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/267740/39/13372/3350/678b5193F84ddeb05/030a978b36799eef.png') no-repeat;
    background-size: 100% 100%;
    width: 2.26rem;
    height: 0.72rem;
    position: absolute;
    bottom: 1.3rem;
    left: 50%;
    transform: translateX(-50%);
  }
  .change{
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/259731/17/13777/11112/678db2b6F2103767f/5c295a8ec6c4576b.png') no-repeat;
    background-size: 100% 100%;
    width: 1.74rem;
    height: 0.55rem;
    position: absolute;
    bottom: 1.3rem;
    left: 50%;
    transform: translateX(-50%);
  }
  .close {
    width: 0.8rem;
    height: 0.8rem;
    position: absolute;
    bottom: 0.3rem;
    //background: red;
    left: 50%;
    transform: translateX(-50%);
  }
}
.form {
  width: 5rem;
  /* margin: 0.23rem auto 0.1rem auto; */
  margin: 0.13rem auto 0;
  height: 3.22rem;
  display: flex;
  flex-direction: column;
  row-gap: 0.17rem;
  .van-cell {
    padding: 0;
    background-color: transparent;
    align-items: center;
    font-size: 0.28rem;
    height: 0.6rem;
    line-height: 1;
    /* background: azure; */
    /* outline: solid 1px blue; */
    .van-cell__title {
      width: 1.25rem;
      color: #f13033;
      font-weight: 600;
    }
    .van-field__value {
      /* margin: 0.1rem 0; */
      background-repeat: no-repeat;
      background-size: 100%;
      line-height: 1;
      /* height: 1; */
      input {
        text-align: center;
        color: #f13033;
        //padding-bottom: 0.24rem;
        &::placeholder {
          color: #f13033;
        }
      }
    }
  }
}
</style>
<style>
@font-face {
  font-family: 'FZZZHJT';
  font-style: normal;
  font-weight: normal;
  src: url(data:../assets/FZZZHJT.TTF) format('TTF');
  font-display: swap;
}
* {
  font-family: 'FZZZHJT';
}
</style>
