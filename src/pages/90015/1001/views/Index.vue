<template>
  <div class="pageBg relative" style="background-repeat: no-repeat" :style="furnishStyles.pageBg.value">
    <div>
      <div class="relative header-kv">
        <div class="box-border flex flex-row justify-between z-10 absolute w-full">
          <div class="text-white text-sm">
            <span v-if="furnish.disableShopName === 1" >{{ shopName || 'xxx自营旗舰店' }}</span>
          </div>
          <div class="btnAllClass flex flex-col">
            <div class="btnClass" v-click-track="'hdgz'" @click="showRulePopup" :style="furnishStyles.ruleBtnBg.value"></div>
            <div class="btnClass" v-click-track="'wddd'" @click="showOrderRecord = true" :style="furnishStyles.myOrderBtnBg.value"></div>
            <div class="btnClass"  v-click-track=" 'wdjp'" @click="showReceiveRecord = true" :style="furnishStyles.myPrizeBtnBg.value"></div>
            <div class="btnClass"  v-click-track=" 'hdsp'" @click="showGoods = true" :style="furnishStyles.goodsBtnBg.value"></div>
          </div>
        </div>
      </div>
      <div class="prizeBox" :style="furnishStyles.prizeAreaBg.value">
        <!-- 进行中-倒计时 -->
        <div class="countDownDivAll" v-if="timeUtils.isInProgress(baseInfo.startTime, baseInfo.endTime)" :style="furnishStyles.countDownBg.value">
          <div class="text-class text">距活动结束剩余：</div>
          <div class="count-down">{{countdownEndTime}}</div>
        </div>
        <!-- 未开始-倒计时 -->
        <div class="countDownDivAll" v-if="timeUtils.isNotStarted(baseInfo.startTime, baseInfo.endTime)" :style="furnishStyles.countDownBg.value">
          <div class="text-class text">距活动开始还有：</div>
          <div class="count-down">{{countdownStartTime}}</div>
        </div>
        <!--立即下单-->
        <div class="mt-3.5 goBuyClass" v-click-track="'ljxd'" :style="furnishStyles.toBuyBtn.value" @click="toBuyNow"/>
        <div class="pl-2 mt-3.5 pr-3 prizeListAllClass">
          <div class="topTips" :style="furnishStyles.pointTextColor.value">
            <div class="customFont font-bold mb-0.5">
              您当前累计消费{{total}}元，
              <span v-if="receiveLimit === 0 && isReceived">
                请在“我的奖品”中查看您的好礼
              </span>
              <span v-else>
                待使用{{moneyCount}}元，距离下一个大奖还差<span class="mx-0.5">{{nextMoneyCount}}</span>元
              </span>
            </div>
            <div class="text-xs">温馨提示:确认收货后才计算进度哦~</div>
          </div>
          <div class="prizeListClass">
            <div class="flex flex-row items-center mt-3" v-for="(item, index) in ladderInfoList" :key="index">
              <div v-if="index === 0" class="relative">
                <img class="h-4 mr-2.5 " :src="IMAGE_MAP.STEP_ICON" alt="">
                <ul class="process processDiv" v-if="ladderInfoList?.length > 1">
                  <li v-for="item in 9" :key="item"></li>
                </ul>
              </div>
              <div v-else class="relative">
                <div class="circle mr-2.5 ml-0.5"></div>
                <ul class="process" v-if="index !== ladderInfoList?.length - 1" >
                  <li v-for="item in 11" :key="item"></li>
                </ul>
              </div>
              <div class="prizeItem bg-white w-full rounded p-2 chat relative">
                <div class="flex flex-row relative">
                  <div class="prizeImgDivAll">
                    <div class="imgBox">
                      <img class="prizeImgDiv w-24" :src="item.ladderImg" alt="">
                    </div>
                    <div class="absolute bottom-0 text-center w-24 text-white text-xs">{{prizeType[item.prizeType]}}</div>
                  </div>
                  <div class="prizeRight flex-1 ml-3 mr-1 flex justify-around">
                    <div class="nameTips">
                      <div class="prizeNameBox font-bold tracking-widest text-base" :style="furnishStyles.littleTextColor.value">{{item.ladderName}}</div>
                      <div class="customFont text-xs font-bold" :style="furnishStyles.littleTextColor.value">满{{item.moneyNum || 'x'}}元可领奖品</div>
                    </div>
                    <div class="flex flex-row text-xs justify-between items-center">
                      <div class="receive-btn" :class="{'grayBtn' : !item.can }" v-click-track="'ljlq'" :style="furnishStyles.getPrizeBtn.value" @click="receivePrize(item)"/>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="skuListDiv p-3.5" :style="furnishStyles.showGoodsBg.value" v-if="skuList.length">
        <div class="gridDiv grid grid-cols-2 gap-1.5 absolute pl-4 pr-7 overflow-auto">
          <div v-for="(item, index) in skuList" class="gridItemDiv py-2 px-3.5" :key="index" @click="gotoSkuPage(item.skuId)">
            <div class="skuImgDiv flex justify-center" >
              <img class="skuImg" :src="item.skuMainPicture" alt="">
            </div>
            <div class="skuBottomDiv">
              <div class="text-xs lz-multi-ellipsis--l2">{{item.skuName}}</div>
            </div>
          </div>
          <div class="more-btn" v-if="pagesAll > pageNum" @click="loadMore">点我加载更多</div>
        </div>
      </div>

    </div>
    <!-- 活动门槛 -->
    <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
<!--    规则-->
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
<!--    我的订单-->
    <VanPopup teleport="body" v-model:show="showOrderRecord"  v-if="showOrderRecord">
      <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
    </VanPopup>
<!--    我的奖品-->
    <VanPopup teleport="body" v-model:show="showReceiveRecord" v-if="showReceiveRecord">
      <ReceiveRecordPopup @close="showReceiveRecord = false"></ReceiveRecordPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showGoods">
      <GoodsPopup :data="orderSkuListPreview" :orderSkuList="orderSkuList" :isAllOrderSku="isAllOrderSku" @close="showGoods = false"></GoodsPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
    <!-- 选择奖品 -->
    <VanPopup teleport="body" v-model:show="showSelectPrize">
      <SelectPrizePopup :prizeInfoList="confirmPrizeInfoList" v-if="showSelectPrize" @getSuccess="getSuccess" @close="showSelectPrize = false"></SelectPrizePopup>
    </VanPopup>
    <!-- 单次领取提示 -->
    <VanPopup teleport="body" v-model:show="showTip" position="center">
      <div class="drawPromptPopDivALL w-[5rem] h-[0.8rem] relative flex justify-center items-center" :style="furnishStyles.ladderWarningBg.value">
        <div class="rounded-b font-bold text-xs relative contentDiv">
          <div class="leading-5 promptDiv1">活动的奖品非多次领取，满足多个阶梯仅可领取其中的一个奖品</div>
          <div class="leading-5 promptDiv2">请确认是否要继续领奖，领取后不可更换</div>
          <div class="btnDivALL flex justify-between absolute text-xs font-light">
            <div class="reSelectDiv px-4 py-1.5" @click="showTip = false"></div>
            <div class="surePrizeDiv px-4 py-1.5" @click="saveReceivePrize"></div>
          </div>
        </div>
      </div>
    </VanPopup>
    <!--领奖成功-->
    <VanPopup teleport="body" @close="success" v-model:show="showGetSuccess">
      <GetSuccess v-if="showGetSuccess" @close="success" :prizeInfo="successPrizeInfo"></GetSuccess>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { prizeType, IMAGE_MAP } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import ReceiveRecordPopup from '../components/ReceiveRecordPopup.vue';
import GoodsPopup from '../components/GoodsPopup.vue';
import AwardPopup from '../components/AwardPopup.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import useCountdown from '@/hooks/useCountdown';
import { timeUtils } from '@/utils/timeUtils';
import SelectPrizePopup from '../components/SelectPrizePopup.vue';
import GetSuccess from '../components/GetSuccess.vue';
import useThreshold from '@/hooks/useThreshold';
import Threshold2 from '@/components/Threshold2/index.vue';

// 订单商品是否全店 1全店 0指定 2排除
const isAllOrderSku = ref(1);
const decoData = inject('decoData') as DecoData;
const baseInfo: any = inject('baseInfo') as BaseInfo;
const endTime = ref(baseInfo.endTime);
const startTime = ref(baseInfo.startTime);
// 倒计时-进行中
const countdownEndTime = ref(useCountdown(endTime));
// 倒计时-未开始
const countdownStartTime = ref(useCountdown(startTime));
// 店铺名称
const shopName = ref(baseInfo.shopName);
// 门槛弹窗
// const showLimit = ref(false);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 订单记录弹窗
const showOrderRecord = ref(false);
// 领取记录弹窗
const showReceiveRecord = ref(false);
// 活动商品弹窗
const showGoods = ref(false);
// 中奖弹窗
const showAward = ref(false);
// 单次领取弹窗
const showTip = ref(false);
// 选择奖品弹窗
const showSelectPrize = ref(false);
// 成功弹窗
const showGetSuccess = ref(false);
// 门槛
const showLimit = ref(false);
interface LadderInfoListInfo {
  // 是否能领取
  can: boolean;
  // 阶梯封面图
  ladderImg: string;
  // 阶梯名称
  ladderName: string,
  // 金额数
  moneyNum: number,
  // 阶梯奖品信息
  prizeInfoList: [
    {
      // 按钮状态
      can: boolean,
      // 活动资产id
      activityPrizeId: number,
      // 资产封面图
      prizeImg: string,
      // 资产名称
      prizeName: string,
      // 资产类型
      prizeType: number,
      // 剩余份数
      remainCount: number,
      // 奖品状态
      status: number,
    }
  ],
}

// 阶梯列表
const ladderInfoList = ref<LadderInfoListInfo[]>([]);
// 领取限制0-单次领取1-多次领取
const receiveLimit = ref(0);
// 是否领过奖品
const isReceived = ref(true);
// 直播链接
const liveLink = ref('');
// 距离下阶段金额数
const nextMoneyCount = ref(0);
// 累计总消费金额
const total = ref(0);
// 订单类型 0-全店订单 1-直播订单
const orderType = ref(0);
// 累计金额数
const moneyCount = ref(0);

// 曝光商品列表
const skuList = ref<any[]>([]);
const pageNum = ref(1);
const pagesAll = ref(0);

const orderSkuListPreview = ref([]);
const orderSkuList = ref([]);

// 奖品信息
const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});
// 单次领取确认奖品信息
const confirmPrizeInfoList = ref([]);
// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const userReceiveRecordId = ref('');
// 填写地址弹窗
const toSaveAddress = (id: string) => {
  userReceiveRecordId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 初始化
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
});

// 活动规则相关
const showRulePopup = async () => {
  showRule.value = true;
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
      console.log(ruleTest.value, 'ruleTest.value');
    }
    showRule.value = true;
  } catch (error: any) {
    console.error();
  }
};
const successPrizeInfo = ref({});
const getSuccess = (item: any) => {
  successPrizeInfo.value = item;
  showSelectPrize.value = false;
  showGetSuccess.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
// 展示卡密
const showCardNum = (result: any) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};
// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showMyPrize = ref(false);
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

const handleClose = () => {
  showReceiveRecord.value = false;
  savePhonePopup.value = false;
};

// 立即下单按钮
const toBuyNow = () => {
  if (orderType.value === 0) {
    gotoShopPage(baseInfo.shopId);
  } else {
    // 跳转直播
    window.location.href = liveLink.value;
  }
};

// 主接口
const getActivityData = async () => {
  try {
    const res = await httpRequest.post('/90015/activity');
    ladderInfoList.value = res.data.ladderInfoList as any[];
    liveLink.value = res.data.liveLink;
    nextMoneyCount.value = (res.data.nextMoneyCount / 1000).toFixed(2);
    orderType.value = res.data.orderType;
    moneyCount.value = (res.data.moneyCount / 1000).toFixed(2);
    receiveLimit.value = res.data.receiveLimit;
    isReceived.value = res.data.isReceived;
    total.value = (res.data.total / 1000).toFixed(2);
  } catch (error: any) {
    console.error(error);
  }
};
// 获取曝光商品
const getSkuList = async () => {
  try {
    const res = await httpRequest.post('/90015/getSkuListPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    if (res.code === 200) {
      skuList.value.push(...res.data.records);
      pagesAll.value = res.data.pages;
    }
  } catch (error: any) {
    console.error(error);
  }
};
const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivityData(), getSkuList()]);
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};

// 单次领取奖品回调
const saveReceivePrize = async (id: string) => {
  showSelectPrize.value = true;
};
// 报名领取
const receivePrize = async (prizeInfo: {prizeInfoList:[], receiveCount: number; can: boolean;}) => {
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  if (prizeInfo.status) {
    return;
  }
  if (!prizeInfo.can) {
    showToast('暂不符合领奖资格~');
    return;
  }
  confirmPrizeInfoList.value = prizeInfo.prizeInfoList;
  // 单次领取提示
  if (receiveLimit.value === 0) {
    if (isReceived.value === false) {
      showTip.value = true;
      return;
    }
    if (isReceived.value) {
      showToast('您已经选择阶梯奖品，请勿多次领取~');
      return;
    }
  }
  // 选择奖品弹窗
  showSelectPrize.value = true;
};
const success = () => {
  showGetSuccess.value = false;
  window.location.reload();
};
watchEffect(() => {
  // 收集依赖
  const tmp = countdownEndTime.value + countdownStartTime.value;
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
@font-face {
  font-family: 'fzfwqytFont';
  src: url('../style/fzfwqyt-j-E.TTF') format('truetype');
}
</style>
<style scoped lang="scss">
.pageBg {
  min-height:100vh;
  background-size: 100%;
  background-repeat: no-repeat;
  padding-bottom: 0.2rem;
}
.header-kv{
  padding: 0 0 6rem 0;
  .btnAllClass{
    margin-top: 0.1rem;
    .btnClass{
      width: 1.61rem;
      height: 0.47rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.1rem 0.05rem 0 0;
    }
  }
}
.prizeBox{
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 7.3rem;
  height: 11.4rem;
  margin: 0 auto;
  padding: .35rem 0 0 0;
}
.countDownDivAll{
  background-size: 100%;
  background-repeat: no-repeat;
  width: 5.77rem;
  height: 0.85rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
  padding: 0 0 0 0.3rem;
  .text-class{
    font-size: 0.28rem;
    font-weight: bolder;
    color:#f7dfc0;
    font-family: 'fzfwqytFont';
  }
  .count-down{
    position: absolute;
    top: 6.52rem;
    right: 1.1rem;
    font-size: 0.24rem;
    color: #f7dfc0;
    background: transparent;
    font-family: 'fzfwqytFont';
  }
}
.goBuyClass{
  background-size: 100%;
  background-repeat: no-repeat;
  width: 2.78rem;
  height: 0.76rem;
  margin: 0.5rem auto 0;
}
.prizeListAllClass{
  height: 7.8rem;
  margin: 0.5rem auto 0;
  padding-top: 0.6rem;
  .topTips{
    width: 6.4rem;
    margin: 0 auto;
    padding: 0.2rem 0;
    text-align: center;
    background-color: #fff;
    border-radius: 0.1rem;
    .customFont{
      font-family: "fzfwqytFont";
      font-size: 0.26rem;
      width: 90%;
      margin: 0 auto;
      span{
        font-family: "fzfwqytFont";
        font-size: 0.26rem;
      }
    }
  }
  .prizeListClass{
    max-height: 6.2rem;
    overflow-y: scroll;
    margin: 0.2rem 0.16rem;
    .prizeItem{
      min-height: 1.4rem;
      align-items: center;
      display: flex;
    }
    .prizeImgDivAll{
      max-height: 6rem;
      overflow-y: scroll;
      margin: 0 0.16rem;
      display: flex;
      align-items: center;
      .imgBox{
        width: 1.2rem;
      }
      .prizeImgDiv{
        max-height: 1.2rem;
        width: auto;
        margin: 0 auto;
      }
    }
    .prizeRight{
      align-items: center;
    }
    .nameTips{
      width: 2.5rem;
      word-break: break-all;
    }
    .prizeNameBox{
      font-size: 0.24rem;
      font-family: 'fzfwqytFont';
    }
    .text-gray-800{
      font-size: 0.17rem;
    }
    .receive-btn{
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 1.42rem;
      height: 0.44rem;
      font-size:0.21rem;
    }
  }
}
.skuListDiv{
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 7.28rem;
  height: 10.59rem;
  margin: 0.5rem auto 0;
  .gridDiv{
    max-height: 8.4rem;
    margin: 1.2rem 0 0 0;
    .gridItemDiv{
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/253514/20/13434/11985/67887d8eF57003010/6b8785d3069ef3ed.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 3rem;
      height: 4.1rem;
      padding: 0.27rem 0.28rem;
      .skuImgDiv{
        //background: #fff;
        border-radius: 0.16rem 0.16rem 0 0;
        .skuImg{
          height: 2.1rem;
        }
      }
      .skuBottomDiv{
        padding: 0.2rem 0 0rem;
        border-radius: 0 0 0.16rem 0.16rem;
        line-height: 0.3rem;
        .skuPriceDiv{
          margin-top: 0.12rem;
        }
      }
    }
  }
}
.more-btn {
  width: 1.8rem;
  height: 0.5rem;
  font-size: 0.2rem;
  color: #fff;
  background: -webkit-gradient(linear, left top, right top, from(#e21115), to(#f74a49));
  background: linear-gradient(90deg, #e21115 0%, #f74a49 100%);
  border-radius: 0.1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 0.3rem 70%;
}
.my-1{
  width:6.84rem;
}
.drawPromptPopDivALL {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.5rem;
  height: 8rem;
  .btnDivALL {
    bottom: 0.6rem;
    display: flex;
    justify-content: space-between;
    width: 5.3rem;
    margin: 0 0.3rem;
  }
  .contentDiv {
    padding: 1.8rem 0.3rem 0;
    height: 5.9rem;
    margin: 0 auto;
    .promptDiv1 {
      text-align: center;
      font-size: 0.35rem;
    }
    .promptDiv2 {
      color: #ff3333;
      text-align: center;
      font-size: 0.3rem;
      margin-top: 0.2rem;
    }
  }
  .closePopDiv {
    width: 0.6rem;
    height: 0.6rem;
    top: 0;
    right: 0.1rem;
    z-index: 10;
  }
  .reSelectDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/268684/36/17740/6513/67a6c2c6F7368ad20/b015953278c53ae5.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1.78rem;
    height: 0.61rem;
  }
  .surePrizeDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/257030/23/17661/10942/67a6c2c7F63852a59/692436c202327059.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1.78rem;
    height: 0.61rem;
  }
}
</style>
