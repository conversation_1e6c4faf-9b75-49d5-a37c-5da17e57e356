<template>
  <div class='bg' :style='{backgroundImage:`url(${decoData.pageBg})`,backgroundColor:decoData.actBgColor}'>
    <!--     活动未开启点击遮罩层 -->
    <!--    <div class='no-start-mask' @click.stop="showToast('活动未开始')" v-if='baseInfo?.thresholdResponseList[0]?.thresholdCode===1'></div>-->

    <div class='header-btn-box'>
      <div class='header-btn' v-for='(btn, index) in btnList' :key='index' @click='btn.event'></div>
    </div>

    <div class='sign-view'>
      <img :src='getSignBtn()' style='width: 2.6rem' class='sign-btn' alt='' @click='signGift()'>
    </div>

    <div class='act-strategy'>
      <img :src='decoData.actStrategy' alt=''>
    </div>

    <div class='repurchase-gift-view'>
      <img :src='activityInfo.prizeImg' class='gift-image' alt=''>
      <div class='gift-info-view'>
        <div class='gift-name'><i>{{ activityInfo?.prizeName }}</i></div>
        <div class='gift-stock'><i>剩余库存：{{ activityInfo?.remainingCount ?? 0 }}</i></div>
        <img :src='getDrawBtnName()' @click='receiveGift()' class='gift-btn' alt=''>
      </div>
    </div>

    <!--    v-if='activityInfo.skuList?.length>0'-->
    <div class='sku-module'>
      <div class='sku-list' @scroll='handleContainerScroll' :style="{height:skuList?.length>=4?'12rem':'8.7rem'}">
        <div class='sku-item' v-for='(item,index) in skuList' :key='index'>
          <div class='sku-image'>
            <img :src='item?.skuMainPicture' style='width: 2rem;max-height: 2rem' alt=''>
          </div>
          <div class='two-line-omit sku-name'><i>{{ item?.skuName }}</i></div>
          <img @click='gotoSkuPage(item?.skuId)' style='width: 1.72rem' src='//img10.360buyimg.com/imgzone/jfs/t1/299364/12/20690/8741/686736d4F8ef5866f/24080ba45bc078ab.png' alt=''>
        </div>

        <!-- 加载状态 -->
        <div class='loading' v-if='isLoading'>
          <van-loading size='24px'>加载中...</van-loading>
        </div>

        <!-- 没有更多 -->
        <div class='no-more' v-if='isEnd'>
          没有更多了
        </div>
      </div>
    </div>


    <VanPopup v-model:show='popupShow'>
      <!-- 规则弹窗 -->
      <RulePopup :rule='activityInfo.rule' v-if="popupName === 'rulePopup'"></RulePopup>
      <!-- 我的奖品弹窗 -->
      <MyPrize v-if="popupName === 'myPrizePopup'" :activityInfo='activityInfo' @fillAddress='fillAddress'></MyPrize>
      <!-- 领奖成功弹窗 -->
      <DrawSuccessPopup v-if="popupName === 'drawSuccessPopup'" :drawSuccessPrize='drawSuccessPrize' @fillAddress='fillAddress'></DrawSuccessPopup>
      <!-- 领奖失败弹窗 -->
      <DrawFailPopup v-if="popupName === 'drawFailPopup'" :popupImg='decoData.drawFailPopup' :jumpUrl='decoData.drawFailBtnUrl'></DrawFailPopup>
      <!-- 非会员弹窗 -->
      <NoMemberPopup v-if="popupName === 'noMemberPopup'"></NoMemberPopup>
      <!-- 填写地址弹窗 -->
      <AddressPopup v-if="popupName === 'addressPopup'" :addressInfo='addressInfo'></AddressPopup>
      <!-- 报名成功弹窗 -->
      <SignSuccessPopup v-if="popupName === 'signSuccessPopup'" :popupImg='decoData.signSuccessPopup'></SignSuccessPopup>
      <!-- 报名失败弹窗 -->
      <SignFailPopup v-if="popupName === 'signFailPopup'" :popupImg='decoData.signFailPopup' :jumpUrl='activityInfo.signStatus===5?`${decoData.signFailBtnUrl1}`:`${decoData.signFailBtnUrl0}`'></SignFailPopup>
    </VanPopup>

  </div>
</template>

<script setup lang='ts'>
import { ref, inject, reactive, onMounted, nextTick } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { IActivityInfo } from './ts/type';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';

import { popupShow, popupName, openPopup, closePopup } from './ts/popup';
import RulePopup from './components/RulePopup.vue';
import MyPrize from './components/MyPrize.vue';
import AddressPopup from './components/AddressPopup.vue';
import DrawFailPopup from './components/DrawFailPopup.vue';
import SignFailPopup from './components/SignFailPopup.vue';
import NoMemberPopup from './components/NoMemberPopup.vue';
import DrawSuccessPopup from './components/DrawSuccessPopup.vue';
import SignSuccessPopup from './components/SignSuccessPopup.vue';

const baseInfo = inject('baseInfo') as BaseInfo;
const decoData = inject('decoData') as any;
const pathParams = inject('pathParams') as any;
const activityInfo = reactive({}) as IActivityInfo; // 活动相关数据

const userPrizeId = ref('');
const skuList = ref([]);
const currentPage = ref(1);
const isLoading = ref(false);
const isEnd = ref(false);

const currentDrawPrize = ref({});
const drawSuccessPrize = ref({});
const addressInfo = ref({});

const getActStatus = () => {
  if (baseInfo?.thresholdResponseList[0]?.thresholdCode === 1) {
    showToast('活动未开始');
    return false;
  }
  if (baseInfo?.thresholdResponseList[0]?.thresholdCode === 2) {
    showToast('活动已结束');
    return false;
  }
  return true;
};

const getDrawBtnName = () => {
  switch (activityInfo.receivePrizeStatus) {
    case 1:
      // 已领取
      return '//img10.360buyimg.com/imgzone/jfs/t1/314167/12/14120/5288/686736d3F514c86ce/2c6b5c548e8c711b.png';
    case 2:
      // 已领完
      return '//img10.360buyimg.com/imgzone/jfs/t1/299852/34/5853/5941/686736d5Fa4b72c00/b5d17d768e14091f.png';
    case 3:
    // 金额不满足
    case 4:
    // skuId不满足
    case 5:
      // 未报名
      return '//img10.360buyimg.com/imgzone/jfs/t1/301237/11/8632/7365/686736d4F58adb88a/35fd989a1f1baa58.png';
    default:
      // 立即领取
      return '//img10.360buyimg.com/imgzone/jfs/t1/301237/11/8632/7365/686736d4F58adb88a/35fd989a1f1baa58.png';
  }
};

const getSignBtn = () => {
  switch (activityInfo.signStatus) {
    case 2:
      // 已报名
      return '//img10.360buyimg.com/imgzone/jfs/t1/307253/16/14311/12989/686736d3Fabef25d8/a743064118bd461d.png';
    case 3:
    // 不在报名时间范围内
    case 4:
    // 订单金额不满足
    case 5:
    // 等于1笔条件不满足
    case 6:
    // 大于等于一笔的条件不满足
    case 7:
      // 订单不包含指定商品
      return '//img10.360buyimg.com/imgzone/jfs/t1/299484/27/21376/20563/686b61f6Fdd06f1f9/f8f8cd204fc3aa1c.png';
    default:
      // 立即报名
      return '//img10.360buyimg.com/imgzone/jfs/t1/298986/13/20674/17196/686736d4F066e9a93/f60fc1a19a08d20f.png';
  }
};

// 获取活动规则
const getRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    Object.assign(activityInfo, { rule: data });
    openPopup('rulePopup');
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取曝光商品
const getSkuListPage = async () => {
  isLoading.value = true;
  try {
    const { data } = await httpRequest.post('/92016/getExposureSkuPage', { pageNum: currentPage.value, pageSize: 10, type: 2 });
    if (data?.records.length <= 0) {
      isEnd.value = true;
      return;
    } else {
      if (skuList.value?.length > 0) {
        skuList.value.push(...data.records);
      } else {
        skuList.value = data.records;
      }
      currentPage.value += 1;
    }
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

// 获取签到状态&奖品可领取状态
const getSignStatus = async () => {
  try {
    const { data } = await httpRequest.post('/92016/sign/activity');
    Object.assign(activityInfo, { signStatus: data.signStatus });
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取活动信息
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/92016/give/activity');
    Object.assign(activityInfo, { ...data });
    await getSignStatus();
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};


// 获取用户奖品
const getUserPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/92016/getReceive');
    Object.assign(activityInfo, { userPrizeList: data });
    openPopup('myPrizePopup');
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

const fillAddress = (info: any) => {
  addressInfo.value = info;
  openPopup('addressPopup');
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      getRule();
    },
  },
  {
    name: '兑换记录',
    event: () => {
      getUserPrizes();
    },
  },
];

const receiveGift = async () => {
  if(!getActStatus()) {
    return;
  }
  if (activityInfo.receivePrizeStatus === 0) {
    try {
      showLoadingToast({
        forbidClick: true,
        duration: 0,
      });
      const { data } = await httpRequest.post('/92016/receivePrize', { prizeId: activityInfo.prizeId, receivePrizeId: activityInfo.receivePrizeId });
      closeToast();
      if (data.status === 2) { // 如果领取失败
        showToast('领取失败，请稍后再试');
        closePopup();
      }
      if (data.status === 1) { // 如果兑换成功
        drawSuccessPrize.value = data;
        openPopup('drawSuccessPopup');
      }
      await getActivityInfo();
    } catch (error: any) {
      showToast(error.message);
      console.error(error);
    }
  } else if (activityInfo.receivePrizeStatus === 1) {
    return;
  } else if (activityInfo.receivePrizeStatus === 3 || activityInfo.receivePrizeStatus === 4 || activityInfo.receivePrizeStatus === 5) {
    openPopup('drawFailPopup');
    return;
  }
};

const signGift = async () => {
  if(!getActStatus()) {
    return;
  }
  if (activityInfo.signStatus === 2) {
    return;
  } else if (activityInfo.signStatus === 3) {
    showToast('当前不在可报名时间范围内');
    return;
  } else if (activityInfo.signStatus === 1) {
    try {
      showLoadingToast({
        forbidClick: true,
        duration: 0,
      });
      const { code, data } = await httpRequest.post('/92016/sign');
      closeToast();
      if (code === 200) {
        openPopup('signSuccessPopup');
      }
      await getActivityInfo();
    } catch (error: any) {
      showToast(error.message);
      console.error(error);
    }
  } else {
    openPopup('signFailPopup');
  }
};

const handleContainerScroll = (e: any) => {
  const container = e.target;
  const scrollBottom = container.scrollHeight - container.clientHeight - container.scrollTop;

  if (scrollBottom < 100 && !isLoading.value && !isEnd.value) {
    getSkuListPage();
  }
};

onMounted(() => {
  if (!baseInfo.levelQualify) {
    openPopup('noMemberPopup');
  }
  getActStatus();
  getSkuListPage();
  getActivityInfo();
});

</script>

<style scoped lang='scss'>

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
