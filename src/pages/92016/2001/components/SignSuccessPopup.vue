<template>
  <div class='box' :style='{backgroundImage:`url(${popupImg})`}'>
    <div class='know-btn' @click='closePopup()'></div>
  </div>
</template>

<script lang='ts' setup>
import { inject, PropType, defineProps } from 'vue';
import { closePopup } from '../ts/popup';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoSkuPage } from '@/utils/platforms/jump';
import { IActivityInfo } from '../ts/type';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({ popupImg: String });
</script>

<style scoped lang='scss'>
.box {
  width: 5.92rem;
  height: 6.18rem;
  position: relative;
  padding: 1rem .2rem .3rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/304258/33/16255/62262/686b3279Fdd1f1424/a89d3d1797d1adba.png");
    repeat: no-repeat;
    size: contain;
  };

  .know-btn {
    width: 4rem;
    height: 1rem;
    position: absolute;
    bottom: .8rem;
    left: 50%;
    transform: translateX(-50%);
  }

}

</style>
