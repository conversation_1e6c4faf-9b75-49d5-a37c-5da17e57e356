<template>
  <div class="count-down-time">
    <span v-if="props.isStart">距结束：</span>
    <span v-else>距开始：</span>
    <van-count-down :time="props.isStart ? (props.endTime - new Date().getTime()) : (props.startTime - new Date().getTime())" format="DD:HH:mm:ss">
      <template #default="timeData">
        <div class="contentSpan">
          <div class="acblockStyleStyle" >{{ timeData.days }}</div><span>天</span>
          <div class="acblockStyleStyle" >{{ timeData.hours }}</div><span>:</span>
          <div class="acblockStyleStyle" >{{ timeData.minutes }}</div><span>:</span>
          <div class="acblockStyleStyle" >{{ timeData.seconds }}</div>
        </div>
      </template>
    </van-count-down>
  </div>
</template>

<script setup lang="ts">

const props = defineProps({
  isStart: {
    type: Boolean,
    default: false,
    required: true,
  },
  startTime: {
    type: Number,
    default: 0,
    required: true,
  },
  endTime: {
    type: Number,
    default: 0,
    required: true,
  },
});
</script>

<style scoped lang="scss">
.count-down-time {
  position: relative;
  top: -0.4rem;
  max-width: 2.4rem;
  font-size: 0.2rem;
  background-color: #ffffff;
  border-radius: 0.1rem;
  display: flex;
  height: 0.3rem;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
  padding: 0 0.1rem;
  color: #010a83;
  .contentSpan {
    display: flex;

    .acblockStyleStyle {
      color: #c81212;
      border-radius: 0.05rem;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 0.2rem;
    }
    span {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 0.2rem;
      color: #c81212;
    }
  }
}
</style>
