<template>
  <div class="rule-bk">
    <div class="content">
      <div class="title">
        <div>时间：</div>
        <div>奖品：</div>
        <div>领取：</div>
      </div>
      <div class="box" v-if="prizes.length">
        <div class="title" v-for="(item, index) in prizes" :key="index">
          <div>{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          <div>{{item.prizeName}}</div>
          <div>
            <div class="status" v-if="item.status === 2">
              <div>发放失败</div>
            </div>
            <div v-else-if="item.status === 1">
              <div class="status" v-if="item.prizeType === 3">
                <div v-if="!item.deliveryStatus || item.deliveryStatus === 0">待发货</div>
                <div v-else>已发货</div>
                <div @click="changAddress(item)">查看地址</div>
              </div>
              <div class="status" v-else-if="item.prizeType === 7">
                <div>已发放</div>
                <div @click="showCardNum(item)">如何兑换</div>
              </div>
              <div class="status" v-else-if="item.prizeType === 9 || item.prizeType === 10">
                <div>已发放</div>
                <div @click="exchangePlusOrAiqiyi">立即兑换</div>
              </div>
              <div class="status" v-else-if="item.prizeType === 12">
                <div v-if="item.isFuLuWaitingReceive">待领取</div>
                <div v-else>已领取</div>
                <div @click="savePhone(item)" v-if="item.isFuLuWaitingReceive">点击领取</div>
              </div>
              <div class="status" v-else>
                <div>已发放</div>
              </div>
            </div>
            <div class="status" v-else>
              <div>--</div>
            </div>
          </div>
        </div>
      </div>
      <div class="noData" v-else>暂无获奖记录哦</div>
    </div>
    <div class="close" @click="close" />
  </div>

  <VanPopup teleport="body" v-model:show="showSaveAddress">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :userPrizeId="userPrizeId" :echoData="echoData" @close="closeSaveAddress"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';

const pageNum = ref(1);
const pagesAll = ref(0);
const isPreview = inject('isPreview') as boolean;

const baseInfo = inject('baseInfo') as BaseInfo;
const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  status: number;
  realName: string;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90017/userPrizes', {
      pageNum: pageNum.value,
      pageSize: 1000,
    });
    closeToast();
    if (res.data.records && res.data.records.length > 0) {
      prizes.push(...res.data.records);
    }
    pagesAll.value = res.data.pages;
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
if (!isPreview) {
  getUserPrizes();
}

const showSaveAddress = ref(false);
const addressId = ref('');
const activityPrizeId = ref('');
const userPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 修改地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast('活动已结束~');
    return;
  }
  addressId.value = item.addressId;
  activityPrizeId.value = item.activityPrizeId;
  userPrizeId.value = item.userPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, showImg: prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits('savePhone', item.userPrizeId, prizeContent.result.planDesc);
};
</script>

<style scoped lang="scss">
.rule-bk {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.5rem;
  height: 8rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/255047/29/29674/15725/67c7f9dcF3e6cd42e/04587262078e9a89.png);
  background-size: 100%;
  background-repeat: no-repeat;
}

.close {
  width: 0.8rem;
  height: 0.8rem;
  margin: 0 auto;
  //background-color: #fff;
}

.content {
  height: 6.8rem;
  width: 90%;
  border: 0.3rem solid transparent;
  overflow-y: scroll;
  font-size: 0.24rem;
  color: #00047e;
  white-space: pre-wrap;
  padding: 1rem 0 0 0;
  margin: 0 auto 0.2rem;
  .title{
    display: flex;
    justify-content: space-between;
    padding: 0.2rem 0;
    color: #000;
    div{
      text-align: center;
      flex: 1;
    }
  }
  .box{
    overflow: hidden;
    overflow-y: scroll;
    height: 4.2rem;
  }

    .prize {
      background: #ffffff;
      margin-bottom: 0.1rem;
      padding-bottom: 0.24rem;
      border-radius: 0.16rem;

      .type {
        color: #999999;
        font-size: 0.2rem;
        text-align: left;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding-top: 0.16rem;
        padding-bottom: 0.16rem;
        border-bottom: 0.02rem dashed #eee;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 0.24rem;
        margin-left: 0.22rem;
        margin-right: 0.22rem;

        .show-img {
          width: 0.8rem;
          height: 0.8rem;
          border-radius: 50%;
        }

        .detail {
          flex: 1;
          padding-left: 0.28rem;

          .name {
            font-size: 0.28rem;
            color: #ff3333;
          }

          .time {
            color: #999999;
            font-size: 0.2rem;
            margin-top: 0.2rem;
          }
        }

        .status {
          font-size: 0.24rem;
          text-align: right;
          //.red {
          //  color: #FF2323;
          //}
          //.green {
          //  color: #6bce98;
          //}
          //
          //.orange {
          //  color: #ff9900;
          //}
          //
          //.blue {
          //  color: #0083ff;
          //}
        }
      }
    }

    .no-data {
      text-align: center;
      line-height: 35vh;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
  }
  .more-btn-all {
    width:6.9rem;
    margin-top:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
.noData{
  margin: 0 auto;
  text-align: center;
  padding: 1.5rem 0 0 0;
  color:  #000;
}
</style>
