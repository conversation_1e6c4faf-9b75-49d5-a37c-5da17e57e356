<template>
  <div class="rule-bk">
    <div class="content" v-html="rule"></div>
    <div class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.5rem;
  height: 8rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/253135/11/28873/15428/67c7f9dfFa2f09e59/0cd15a1dd0663e07.png);
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 1rem 0 0 0;
  }

  .close {
    width: 0.8rem;
    height: 0.8rem;
    margin: 0 auto;
    //background-color: #fff;
  }

  .content {
    height: 5.8rem;
    width: 5.4rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #00047e;
    white-space: pre-wrap;
    margin: 0 auto;
  }
</style>
