<template>
  <div class="bg" :style="furnishStyles.actBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg ? furnish.actBg : '//img10.360buyimg.com/imgzone/jfs/t1/266051/25/27773/39754/67c67a50Fea60b185/25c3f8856d0ed260.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.btnImgBg.value" @click="showRulePopup" v-click-track="'hdgz'">活动规则</div>
          <div class="header-btn" :style="furnishStyles.btnImgBg.value" @click="showMyPrize = true" v-click-track="'wdjp'">我的奖品</div>
        </div>
      </div>
      <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart"/>
    </div>
    <div class="step1Area" :style="furnishStyles.step1Bg.value">
      <div class="topBox">
        <div>
          <div class="text">每成功邀请{{sharePrizeList[0]?.peopleNum ?? 'XX'}}位好友可领取</div>
          <div class="num">{{sharePrizeList[0]?.prizeName ?? 'XX'}}</div>
        </div>
        <div>
          <div class="btn" @click="shareAct" v-click-track="'yqhyrh'" :style="furnishStyles.toInviteBtn.value"></div>
          <div class="btn" @click="drawPrize(sharePrizeList)" :class="sharePrizeList[0]?.status === 4 ? 'grayBtn' : ''" v-click-track="'lqjl-share'" :style="furnishStyles.receiveInvitedPrizeBtn.value"></div>
        </div>
      </div>
      <div class="rankTitle">
        <div class="left">您获得：
          <span v-if="rankStatus === 0">排名正在进行中...</span>
          <span v-if="rankStatus === 1">
            <span v-if="momentType === 0">未入榜</span>
            <span v-else>第{{momentType}}名</span>
          </span>
        </div>
        <div class="right">
          <div class="receiveBtn" @click="drawRankPrize(rankPrizeList)" v-click-track="'ljlq-rank'" :style="furnishStyles.receiveRankPrizeBtn.value"/>
        </div>
      </div>
      <div class="rankBox" :style="showRankBg">
        <div class="tabBox">
          <div @click="clickLeftTab"></div>
          <div @click="clickRightTab"></div>
        </div>
        <!-- 排行榜榜单内容 -->
        <div class="centerRankBox" v-if="rankBgStatus === 0">
          <div class="mineRank">
            <div class="rankNum">
              <span v-if="mineRank !== 0">{{mineRank}}</span>
              <span v-else style="font-size: 0.16rem;">暂未入榜</span>
            </div>
            <div class="head">
              <img :src="mineHeaderImg ?? '//img10.360buyimg.com/imgzone/jfs/t1/267059/3/28409/2223/67c81a0fFd97b2842/f448f9cd24189b76.png'" alt="">
            </div>
            <div class="nickName">{{mineNickName}}</div>
            <div class="number">{{assNum}}位</div>
            <div class="prizeImg">
              <div v-if="minePrizeImg">
                <img class="img" :src="minePrizeImg" alt="">
              </div>
              <div v-else>...</div>
            </div>
          </div>
          <div class="rankList">
            <div v-for="(item, index) in rankListInfo" :key="index">
              <div class="mineRank">
                <div class="rankNum">{{item.rank}}</div>
                <div class="head">
                  <img :src="item.headerImg" alt="">
                </div>
                <div class="nickName">{{item.nickName}}</div>
                <div class="number">{{item.num}}位</div>
                <div class="prizeImg">
                  <img :src="item.prizeImg" alt="">
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 我邀请的好友内容 -->
        <div class="centerFriendsBox" v-if="rankBgStatus === 1">
          <div class="topText">你已成功邀请 <span>{{assNum}}位</span> 好友</div>
          <div class="friendsListBox">
            <div v-if="inviteList.length > 0">
              <div v-for="(item, index) in inviteList" :key="index">
                <div class="friendsItem">
                  <div>{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                  <div>{{item.nickName}}</div>
                </div>
              </div>
            </div>
            <div class="noData" v-else>暂无邀请成功的好友</div>
          </div>
        </div>
      </div>
    </div>
    <div class="step2Area" :style="furnishStyles.step2Bg.value">
      <div class="firstRemainNum">剩余<br/>{{firstBuyPrize.num}}份</div>
      <div class="getFirstBuyPrizeBtn" :class="firstBuyPrize?.status === 4 ? 'bottomGrayBtn' : ''" @click="getFirstBuyPrize(firstBuyPrize)" v-click-track="'xdcg-lqjl'" :style="furnishStyles.getFirstBuyPrizeBtn.value"></div>
      <div class="invitedRemainNum">剩余<br/>{{inviteBuyPrize.num}}份</div>
      <div class="getInvitedPrizeBtn" :class="inviteBuyPrize?.status === 4 ? 'bottomGrayBtn' : ''" @click="getInvitedBuyPrize(inviteBuyPrize)" v-click-track="'yqxdcg-lqjl'" :style="furnishStyles.getInvitedPrizeBtn.value"></div>
      <div class="invitedShowBottomText">
        已邀请下单{{assOrderNum}}人
      </div>
      <div class="invitedBottomBtn">
        <div class="bottomBtn" @click="showMyFriends = true" :style="furnishStyles.myFriendsBtn.value"></div>
        <div class="bottomBtn" @click="checkShowOrder" :style="furnishStyles.myOrderBtn.value"></div>
      </div>
    </div>
    <div class="bottomImg" :style="furnishStyles.moreActImg.value"/>
    <div class="bannerBox">
      <div class="swiper-banner" ref="swiperRef">
        <div class="swiper-wrapper">
          <div class="bannerImg swiper-slide" v-for="(item, index) in furnish.bannerList" :key="index">
            <!-- 动态生成的热区按钮 -->
            <HotZone :swiperIndex="index" :width="7.21" :data="item" @hotClick="hotClick" reportKey="" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress">
    <SaveAddress
      v-if="showSaveAddress"
      :addressId="addressId"
      :activityPrizeId="activityPrizeId"
      :userPrizeId="userPrizeId"
      @close="showSaveAddress = false"
      @submitAddress="submitAddress"
    ></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup teleport="body" v-model:show="savePhonePopup">
    <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
  </VanPopup>
  <!-- 我的好友 -->
  <VanPopup teleport="body" v-model:show="showMyFriends">
    <MyFriendsPopup @close="showMyFriends = false"></MyFriendsPopup>
  </VanPopup>
  <!-- 我的订单 -->
  <VanPopup teleport="body" v-model:show="showMyOrder">
    <MyOrderPopup @close="showMyOrder = false"></MyOrderPopup>
  </VanPopup>
  <!-- 您无可领取的奖励 -->
  <VanPopup teleport="body" v-model:show="showNoAward">
    <NoAward @close="showNoAward = false"></NoAward>
  </VanPopup>
  <!-- 您没有订单 -->
  <VanPopup teleport="body" v-model:show="showCanNotShowOrder">
    <CanNotShowOrder @close="showCanNotShowOrder = false"></CanNotShowOrder>
  </VanPopup>
  <!-- 您没有领取首购资格 -->
  <VanPopup teleport="body" v-model:show="showCanNotReceive">
    <CanNotReceive @close="showCanNotReceive = false"></CanNotReceive>
  </VanPopup>
  <!-- 很遗憾擦肩而过 -->
  <VanPopup teleport="body" v-model:show="showWhatAPity">
    <WhatAPity @close="showWhatAPity = false"></WhatAPity>
  </VanPopup>
  <!-- 非会员 -->
  <VanPopup teleport="body" v-model:show="showOpenCard">
    <OpenCard @close="showOpenCard = false"></OpenCard>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, inject, nextTick, computed } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import CountDown from '../components/CountDown.vue';
import HotZone from '../components/HotZone.vue';
import RulePopup from '../components/RulePopup.vue';
import MyFriendsPopup from '../components/MyFriendsPopup.vue';
import MyOrderPopup from '../components/MyOrderPopup.vue';
import NoAward from '../components/NoAward.vue';
import CanNotShowOrder from '../components/CanNotShowOrder.vue';
import CanNotReceive from '../components/CanNotReceive.vue';
import WhatAPity from '../components/WhatAPity.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import SavePhone from '../components/SavePhone.vue';
import OpenCard from '../components/OpenCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType, PrizeInfo, Rank } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';

Swiper.use([Autoplay]);
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const shopName = ref(baseInfo.shopName);
const pathParams = inject('pathParams') as any;

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

// tab 切换 排行榜和邀请好友
const rankBg = ref(furnish.rankListBg);
const rankBgStatus = ref(0);
const showRankBg = computed(() => ({ backgroundImage: `url(${rankBg.value})` }));
const clickLeftTab = () => {
  rankBgStatus.value = 0;
  rankBg.value = furnish.rankListBg;
};
const clickRightTab = () => {
  rankBgStatus.value = 1;
  rankBg.value = furnish.invitedFriendsListBg;
};
// 活动规则弹窗
const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error: any) {
    console.error(error);
  }
};

// 我的好友弹窗
const showMyFriends = ref(false);
// 我的订单弹窗
const showMyOrder = ref(false);
// 您无可领取的奖励弹窗
const showNoAward = ref(false);
// 您没有领取首购资格
const showCanNotReceive = ref(false);
// 擦肩而过
const showWhatAPity = ref(false);
// 因为没有订单，无法查看订单记录
const showCanNotShowOrder = ref(false);
// 我的奖品弹窗
const showMyPrize = ref(false);
// 领取成功弹窗
const showAward = ref(false);
// 非会员弹窗
const showOpenCard = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
  prizeImg: '',
});
// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const userPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string, userPrizeId1: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  userPrizeId.value = userPrizeId1;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};
const isShowShareBtn = ref(true); // 是否显示邀请按钮
const rank = ref(0); // 是否开启排行榜 0 未开启 1开启
const assNum = ref(0); // 已邀请人数
const mineNickName = ref(''); // 我的昵称
const mineHeaderImg = ref(''); // 我的头像
const minePrizeImg = ref(''); // 我的预估奖品
const mineRank = ref(0); // 我的排名
const minePirzeType = ref(0); // 我排行榜可领取的奖品类型
const sharePrizeList = ref<PrizeInfo[]>([]);
const rankPrizeList = ref<PrizeInfo[]>([]);
const rankListInfo = ref<Rank[]>([]);
const rankType = ref(2); // 排行榜奖品  1可以领取 2不能领取(未入榜) 3已领取
const rankStatus = ref(0); // 0未排榜1已排榜
const momentType = ref(-1); // 排名顺序 0未入榜 1-n 榜单排名
const prizeSendNum = ref(0); // 单一型邀请领奖机会
const prizeSendType = ref(1); // 奖品类型 1 单一型 2阶梯型
const rankStartTime = ref(''); // 开奖开始时间
const statisticsEndTime = ref('');
const shareAct = () => {
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
    showOpenCard.value = true;
    console.log('非会员');
    return;
  }
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
const getRankList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90017/rankList');
    rankListInfo.value = data as any[];
    closeToast();
  } catch (e) {
    showToast(e.message);
  }
};
const doTask = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90017/openCard/doTask', {
      shareId: pathParams.shareId,
    });
    showToast('助力成功');
  } catch (e) {
    console.log(e, 'doTask=========');
    showToast(e.message);
  }
};
const swiperSharePrize = ref<any>(null); // 或者其他初始值 ;
const swiperRankPrize = ref<any>(null); // 或者其他初始值 ;
const getPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90017/getPrizes');
    closeToast();
    // console.log(res, '奖品接口========');
    assNum.value = res.data.assNum;
    mineNickName.value = res.data.rankResponses.nickName;
    mineHeaderImg.value = res.data.rankResponses.headerImg ?? '';
    minePrizeImg.value = res.data.rankResponses.prizeImg ?? '';
    mineRank.value = res.data.rankResponses.rank;
    minePirzeType.value = res.data.rankResponses.prizeType;
    rankType.value = res.data.rankType; // 1可以领取2不能领取3已领取
    rankStatus.value = res.data.rankStatus; // 0未排榜1已排榜
    momentType.value = res.data.momentType; // 排名顺序 0未入榜 1-n 榜单排名
    prizeSendNum.value = res.data.prizeSendNum;
    prizeSendType.value = res.data.prizeSendType;
    if (res.data.prizeResponses && res.data.prizeResponses.length > 0) {
      // 邀请奖品
      sharePrizeList.value = res.data.prizeResponses.filter((e: Pick<PrizeInfo, 'type'>): boolean => e.type === 0);
      sharePrizeList.value.forEach((item, index) => {
        if (item.num === 0 && item.status !== 3) {
          item.status = 4;
        }
      });
    }
    rank.value = res.data.rank;
    rankStartTime.value = res.data.rankStartTime;
    statisticsEndTime.value = res.data.statisticsEndTime;
    rankPrizeList.value = res.data.prizeResponses.filter((e: Pick<PrizeInfo, 'type'>): boolean => e.type === 1);
    await getRankList();
    if (sharePrizeList.value && sharePrizeList.value.length > 1) {
      nextTick(() => {
        if (!swiperSharePrize.value) {
          swiperSharePrize.value = new Swiper('.swiper-share-prize', {
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
            },
            loop: true,
            slidesPerView: 1,
            loopedSlides: 10,
            allowTouchMove: true,
          });
        }
      });

    }
    if (rankPrizeList.value && rankPrizeList.value.length > 1) {
      nextTick(() => {
        if (!swiperRankPrize.value) {
          swiperRankPrize.value = new Swiper('.swiper-rank-prize', {
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
            },
            loop: true,
            slidesPerView: 1,
            loopedSlides: 10,
            allowTouchMove: true,
          });
        }
      });

    }
    if (pathParams.shareId) {
      await doTask();
    }
  } catch (e) {
    showToast(e.message);
  }
};
interface InviteList {
  createTime: string;
  nickName: string;
  orderStatus: number;
}
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);
// 已邀请下单人数
const assOrderNum = ref(0);
// 首购奖品
const firstBuyPrize = ref({});
// 邀请下单奖品
const inviteBuyPrize = ref({});
// 是否有订单
const hasOrdered = ref(false);
// 获取首购奖品信息
const getFirstBuyPrizeList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90017/getOrderPrizes');
    closeToast();
    assOrderNum.value = data.assOrderNum;
    hasOrdered.value = data.hasOrdered;
    firstBuyPrize.value = data.newPrizeResponses;
    inviteBuyPrize.value = data.leaderPrizeResponses;
  } catch (e) {
    showToast({
      message: e.message,
      duration: 2000,
      onClose: (() => {
        getPrizes();
      }),
    });
  }
};
const inviteList = ref<InviteList[]>([]);
// 获取助力好友列表
const getInviteList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90017/inviteList', {
      pageNum: 1,
      pageSize: 10000,
      selectType: 0,
    });
    closeToast();
    inviteList.value = data.records;
    pagesAll.value = data.pages;
    pageNum.value = data.current;
    total.value = data.total;
  } catch (e) {
    showToast({
      message: e.message,
      duration: 2000,
      onClose: (() => {
        getPrizes();
        getFirstBuyPrizeList();
      }),
    });
  }
};

// 当前领取的奖品是四个类型的哪个
const checkedPrizeType = ref(0);
// 当前领取的奖品id
const selectedPrizeId = ref('');

// 领取邀请奖品
const drawPrize = async (prizeData:any) => {
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
    showOpenCard.value = true;
    console.log('非会员');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    if (prizeData[0].status === 1) {
      if (prizeData[0].prizeType === 3) {
        showSaveAddress.value = true;
        closeToast();
        checkedPrizeType.value = prizeData[0].type;
        selectedPrizeId.value = prizeData[0].prizeId;
      } else {
        const { data } = await httpRequest.post('/90017/sendCommonPrize', {
          prizeId: prizeData[0].prizeId,
        });
        closeToast();
        await getPrizes();
        if (data.status === 0) {
          award.value = data;
          showAward.value = true;
        } else if (data.status === 1) {
          showWhatAPity.value = true;
        }
      }
      return;
    }
    if (prizeData[0].status === 2) {
      showToast('暂无领取资格~');
      return;
    }
    if (prizeData[0].status === 3) {
      showToast('中奖次数已达到上限~');
      return;
    }
    if (prizeData[0].status === 4) {
      showToast('奖品库存不足~');
      return;
    }
  } catch (e) {
    showToast({
      message: e.message,
      duration: 2000,
      onClose: (() => {
        getPrizes();
        getFirstBuyPrizeList();
      }),
    });
  }
};
// 排行榜领取奖品
const drawRankPrize = async (prizeData:any) => {
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
    showOpenCard.value = true;
    console.log('非会员');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    if (rankType.value === 1) {
      if (minePirzeType.value === 3) {
        showSaveAddress.value = true;
        checkedPrizeType.value = 1;
        closeToast();
      } else {
        const { data } = await httpRequest.post('/90017/sendRankPrize');
        closeToast();
        await getPrizes();
        if (data.status === 0) {
          award.value = data;
          showAward.value = true;
        } else if (data.status === 1) {
          showWhatAPity.value = true;
        }
      }
      return;
    }
    if (rankType.value === 2) {
      showToast('暂无领取资格~');
      return;
    }
    if (rankType.value === 3) {
      showToast('中奖次数已达到上限~');
      return;
    }
  } catch (e) {
    showToast({
      message: e.message,
      duration: 2000,
      onClose: (() => {
        getPrizes();
        getFirstBuyPrizeList();
      }),
    });
  }
};
/**
 * 领取首购奖品
 */
const getFirstBuyPrize = async (firstBuyPrize:any) => {
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
    showOpenCard.value = true;
    console.log('非会员');
    return;
  }
  if (firstBuyPrize.status === 2) {
    closeToast();
    showCanNotReceive.value = true;
    return;
  }
  if (firstBuyPrize.status === 3) {
    showToast('奖励已领过，于“我的奖品”中查看~');
    return;
  }
  if (firstBuyPrize.status === 4) {
    showToast('奖品库存不足~');
    return;
  }
  if (firstBuyPrize.status === 1) {
    if (firstBuyPrize.prizeType === 3) {
      showSaveAddress.value = true;
      closeToast();
      selectedPrizeId.value = firstBuyPrize.prizeId;
      checkedPrizeType.value = firstBuyPrize.type;
    } else {
      try {
        showLoadingToast({
          message: '加载中...',
          forbidClick: true,
          duration: 0,
        });
        const { data } = await httpRequest.post('/90017/sendNewPrize', {
          prizeId: firstBuyPrize.prizeId,
        });
        if (data.status === 0) {
          award.value = data;
          showAward.value = true;
        } else if (data.status === 1) {
          showWhatAPity.value = true;
        }
        closeToast();
      } catch (e) {
        closeToast();
        showToast(e.message);
      }
    }
  }
};
/**
 * 领取邀请下单奖品
 */
const getInvitedBuyPrize = async (invitedBuyPrize) => {
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
    showOpenCard.value = true;
    console.log('非会员');
    return;
  }
  if (invitedBuyPrize.status === 2) {
    closeToast();
    showNoAward.value = true;
    return;
  }
  if (invitedBuyPrize.status === 3) {
    showToast('中奖次数已达到上限~');
    return;
  }
  if (invitedBuyPrize.status === 4) {
    showToast('奖品库存不足~');
    return;
  }
  if (invitedBuyPrize.status === 1) {
    if (invitedBuyPrize.prizeType === 3) {
      showSaveAddress.value = true;
      closeToast();
      selectedPrizeId.value = invitedBuyPrize.prizeId;
      checkedPrizeType.value = invitedBuyPrize.type;
    } else {
      try {
        showLoadingToast({
          message: '加载中...',
          forbidClick: true,
          duration: 0,
        });
        const { data } = await httpRequest.post('/90017/sendLeaderPrize', {
          prizeId: invitedBuyPrize.prizeId,
        });
        if (data.status === 0) {
          award.value = data;
          showAward.value = true;
        } else if (data.status === 1) {
          showWhatAPity.value = true;
        }
        closeToast();
      } catch (e) {
        closeToast();
        showToast(e.message);
      }
    }
  }
};

/**
 * 我的订单是否能展示
 */
const checkShowOrder = () => {
  if (!hasOrdered.value) {
    showCanNotShowOrder.value = true;
  } else {
    showMyOrder.value = true;
  }
};

/**
 * 提交地址回调
 * @param {string} address - 用户提交的地址
 */
const submitAddress = async (address) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });

    const apiMap = {
      0: '/90017/sendCommonPrize',
      1: '/90017/sendRankPrize',
      2: '/90017/sendNewPrize',
      3: '/90017/sendLeaderPrize',
    };

    const payload = {
      address,
      ...(checkedPrizeType.value !== 1 && { prizeId: selectedPrizeId.value }),
    };
    const res = await httpRequest.post(apiMap[checkedPrizeType.value], payload);
    console.log(res, '666++');
    if (res.data.status === 0) {
      closeToast();
      // 业务逻辑成功
      award.value = res.data;
      showSaveAddress.value = false;
      showAward.value = true;
    } else if (res.data.status === 1) {
      closeToast();
      // 业务逻辑失败
      showWhatAPity.value = true;
    }
    getPrizes();
    getFirstBuyPrizeList();
  } catch (e) {
    console.log(e, e.message);
    showToast(e.message);
  }
};

const swiperBanner = ref();
const slideClick = (e: Event) => {
  console.log(e);
  const swiperIndex = (e.target as HTMLElement).dataset.swiperindex ?? '';
  const hotZoneIndex = (e.target as HTMLElement).dataset.hotzoneindex ?? '';
  console.log(swiperIndex, hotZoneIndex);
  window.location.href = furnish.bannerList[swiperIndex].hotZoneList[hotZoneIndex].url;
  // console.log(furnish.bannerList[swiperIndex].hotZoneList[hotZoneIndex].url);
};
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    rankBg.value = furnish.rankListBg;
  }
  getTime();
  nextTick(() => {
    if (furnish.bannerList.length > 1) {
      swiperBanner.value = new Swiper('.swiper-banner', {
        autoplay: {
          delay: 2000,
          disableOnInteraction: false,
        },
        loop: true,
        slidesPerView: 1,
        loopedSlides: 10,
        allowTouchMove: true,
        on: {
          slidesLengthChange: () => {
            // 选择所有的swiper-sku-toPage
            const ElList = document.querySelectorAll('.hotZoneClick');
            // 先解绑所有的点击事件
            ElList.forEach((item) => {
              item.removeEventListener('click', slideClick);
            });
            // 给每个sku绑定点击事件
            ElList.forEach((item) => {
              item.addEventListener('click', slideClick);
            });
          },
        },
      });
    } else {
      // 选择所有的swiper-sku-toPage
      const ElList = document.querySelectorAll('.hotZoneClick');
      // 先解绑所有的点击事件
      ElList.forEach((item) => {
        item.removeEventListener('click', slideClick);
      });
      // 给每个sku绑定点击事件
      ElList.forEach((item) => {
        item.addEventListener('click', slideClick);
      });
    }
  });
  try {
    await Promise.all([getPrizes(), getInviteList(), getFirstBuyPrizeList()]);
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
      showOpenCard.value = true;
      console.log('非会员');
      return;
    }
  } catch (error: any) {
    console.log(error);
  }
};
init();

</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.bg {
  min-height: 100vh;
  background-size: 100%;
  background-repeat: no-repeat;
  //margin: 0 auto;
  //background-color:#f2f2f2;
  overflow: hidden;
}
.grayBtn{
  filter: grayscale(1);
}
.bottomGrayBtn{
  filter: grayscale(70%) contrast(20%) brightness(170%);
  width: 2.73rem !important;
}
.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 2.4rem;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
    position: relative;
    top: -2.2rem;
    left: 0.2rem;
  }

  .header-btn {
    width: 1.26rem;
    background-size: 100%;
    background-repeat: no-repeat;
    line-height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.step1Area{
  width: 7.2rem;
  height: 13.32rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 0 auto;
  padding: 1rem 0 0 0;
  .topBox{
    width: 5.7rem;
    height: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    font-size: 0.2rem;
    div{
      margin: 0 auto;
      text-align: center;
    }
    .text{
      color: #010a83;
    }
    .num{
      color: #c81212;
    }
    .btn{
      width: 1.65rem;
      height: 0.38rem;
      background-size: 100%;
      background-repeat: no-repeat;
    }
  }
  .rankTitle{
    width: 6.64rem;
    height: 0.62rem;
    padding: 0.1rem 0.53rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 3.45rem auto 0;
    color: #7c7c7c;
    font-size: 0.2rem;
    .left{
      flex: 2;
      text-align: center;
    }
    .right {
      flex: 1;
    }
    .receiveBtn{
      width: 1.83rem;
      height: 0.4rem;
      background-size: 100%;
      background-repeat: no-repeat;
    }
  }
  .rankBox{
    width: 6.64rem;
    height: 7.14rem;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0.2rem auto;
    .tabBox{
      width: 6.3rem;
      height: 1rem;
      display: flex;
      margin: 0 auto;
      div{
        flex:1;
      }
    }
    .centerRankBox{
      width: 6rem;
      //background-color: #ffffff70;
      height: 5.2rem;
      margin: 0.52rem auto 0;
      font-size: 0.21rem;
      color: #7c7c7c;
      .mineRank{
        //width: 4.8rem;
        height: 0.94rem;
        display: flex;
        justify-content: space-between;
        //background-color: #fff;
        margin: 0 auto;
        align-items: center;
        .rankNum{
          text-align: left;
          flex:1;
          font-size: 0.3rem;
          color: #010a83;
        }
        .head{
          width: 0.59rem;
          height: 0.59rem;
          flex:2;
          img {
            width: 0.59rem;
            height: 0.59rem;
            border-radius: 100%;
          }
        }
        .nickName{
          flex:3;
          text-align: left;
          word-break: break-all;
        }
        .number{
          flex: 4;
          text-align: center;
        }
        .prizeImg{
          width: 0.59rem;
          height: 0.59rem;
          flex:4;
          .img{
            width: 0.59rem;
            height: 0.59rem;
            margin: 0 auto;
          }
          img{
            width: 0.59rem;
            height: 0.59rem;
            margin: 0 auto;
          }
        }
        div{
          flex:2;
          text-align: center;
        }
      }
      .rankList{
        //background-color: #fff;
        height: 3.74rem;
        overflow: hidden;
        overflow-y: scroll;
      }
    }
    .centerFriendsBox{
      width: 6rem;
      //background-color: rgba(232, 161, 161, 0.44);
      height: 5.7rem;
      margin: 0.23rem auto 0;
      font-size: 0.21rem;
      color: #7c7c7c;
      .topText{
        margin: 0 auto;
        text-align: center;
        height: 0.4rem;
        //background-color: #fff;
        line-height: 0.4rem;
        span{
          color: #c81212;
        }
      }
      .friendsListBox{
        //background-color: rgba(30, 7, 7, 0.44);
        width: 6rem;
        height: 4.4rem;
        margin: 0.8rem auto 0;
        overflow-y: scroll;
        .friendsItem {
          display: flex;
          height: 0.94rem;
          justify-content: space-between;
          padding: 0 0.3rem;
          text-align: center;
          align-items: center;
          div{
            flex:1;
          }
        }
        .noData{
          margin: 0 auto;
          padding: 2rem 0 0 0;
          text-align: center;
        }
      }
    }
  }
}

.step2Area{
  width: 7.2rem;
  height: 9.57rem;
  background-size: 100%;
  background-repeat: no-repeat;
  margin: 0.2rem auto 0;
  padding: 4.25rem 0 0;
  position: relative;
  .firstRemainNum{
    margin: 0 auto;
    text-align: center;
    width: 0.7rem;
    height: 0.7rem;
    border-radius: 100%;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/298869/17/13741/3159/6842b19dF97d600ce/930458cef26d565b.png) no-repeat;
    background-size: 100%;
    color: #785419;
    font-weight: bold;
    position: absolute;
    right: 1.17rem;
    top: 2.64rem;
    font-size: 0.14rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .getFirstBuyPrizeBtn{
    width: 2.71rem;
    height: 0.3rem;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0 0 0 3.58rem;
  }
  .invitedRemainNum{
    margin: 0 auto;
    text-align: center;
    width: 0.7rem;
    height: 0.7rem;
    border-radius: 100%;
    color: #785419;
    font-weight: bold;
    position: absolute;
    right: 1.14rem;
    top: 5.05rem;
    font-size: 0.14rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/298869/17/13741/3159/6842b19dF97d600ce/930458cef26d565b.png) no-repeat;
    background-size: 100%;
  }
  .getInvitedPrizeBtn{
    width: 2.71rem;
    height: 0.3rem;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 2.1rem 0 0 3.58rem;
  }
  .invitedShowBottomText{
    margin: 0.26rem auto 0;
    width: 4rem;
    height: 0.5rem;
    text-align: center;
    color: #fff;
    line-height: 0.5rem;
    font-size: 0.23rem;
  }
  .invitedBottomBtn{
    width: 6rem;
    height: 0.74rem;
    margin: 0.4rem auto 0;
    display: flex;
    justify-content: space-between;
    .bottomBtn{
      flex:1;
      width: 2.93rem;
      height: 0.91rem;
      background-size: 100%;
      background-repeat: no-repeat;
    }
  }
}

.bottomImg{
  width: 7.2rem;
  height: 0.77rem;
  background-size: 100%;
  background-repeat: no-repeat;
  margin: 0.4rem auto;
}

.bannerBox{
  width: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  margin: 0 auto;
  .bannerImg{
    width: 7.5rem;
    height: 2rem;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0 auto;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
