<template>
  <div class="bg" :style="furnishStyles.actBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg ? furnish.actBg : '//img10.360buyimg.com/imgzone/jfs/t1/266051/25/27773/39754/67c67a50Fea60b185/25c3f8856d0ed260.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.btnImgBg.value" @click="btnClick('rule')">活动规则</div>
          <div class="header-btn" :style="furnishStyles.btnImgBg.value" @click="btnClick('prize')">我的奖品</div>
        </div>
      </div>
      <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart"/>
    </div>
    <div class="step1Area" :style="furnishStyles.step1Bg.value">
      <div class="topBox">
        <div>
          <div class="text">每成功邀请{{sharePrize[0]?.peopleNum ?? '0'}}位好友可领取</div>
          <div class="num">{{sharePrize[0]?.prizeName ?? ''}}</div>
        </div>
        <div>
          <div class="btn" @click="toast" :style="furnishStyles.toInviteBtn.value"></div>
          <div class="btn" @click="toast" :style="furnishStyles.receiveInvitedPrizeBtn.value"></div>
        </div>
      </div>
      <div class="rankTitle">
        <div>您获得：
          <span>排名正在进行中...</span>
        </div>
        <div class="receiveBtn" @click="toast" :style="furnishStyles.receiveRankPrizeBtn.value"></div>
      </div>
      <div class="rankBox" :style="showRankBg">
        <div class="tabBox">
          <div @click="clickLeftTab"></div>
          <div @click="clickRightTab"></div>
        </div>
        <div class="centerBox"></div>
      </div>
    </div>
    <div class="step2Area" :style="furnishStyles.step2Bg.value">
      <div class="firstRemainNum">剩余<br/>0份</div>
      <div class="getFirstBuyPrizeBtn" @click="toast" :style="furnishStyles.getFirstBuyPrizeBtn.value"></div>
      <div class="invitedRemainNum">剩余<br/>0份</div>
      <div class="getInvitedPrizeBtn" @click="toast" :style="furnishStyles.getInvitedPrizeBtn.value"></div>
      <div class="invitedShowBottomText">
        已邀请下单0人
      </div>
      <div class="invitedBottomBtn">
        <div class="bottomBtn" @click="btnClick('friends')" :style="furnishStyles.myFriendsBtn.value"></div>
        <div class="bottomBtn" @click="btnClick('order')" :style="furnishStyles.myOrderBtn.value"></div>
      </div>
    </div>
    <div class="bottomImg" :style="furnishStyles.moreActImg.value"/>
    <div class="bannerBox">
      <div class="swiper-banner" ref="swiperRef">
        <div class="swiper-wrapper">
          <div class="bannerImg swiper-slide" v-for="(item, index) in furnish.bannerList" :key="index">
            <!-- 动态生成的热区按钮 -->
            <HotZone :width="7.21" :data="item" @hotClick="hotClick" reportKey="" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-if="!isCreateImg">
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress">
      <SaveAddress :addressId="addressId" :activityPrizeId="''" :userPrizeId="''" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
    <!-- 我的好友 -->
    <VanPopup teleport="body" v-model:show="showMyFriends">
      <MyFriendsPopup @close="showMyFriends = false"></MyFriendsPopup>
    </VanPopup>
    <!-- 您没有订单 -->
    <VanPopup teleport="body" v-model:show="showCanNotShowOrder">
      <CanNotShowOrder @close="showCanNotShowOrder = false"></CanNotShowOrder>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, inject, computed } from 'vue';
import furnishStyles, { furnish, prizeInfo, prizeType } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';

import dayjs from 'dayjs';
import { showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import CountDown from '../components/CountDown.vue';
import usePostMessage from '@/hooks/usePostMessage';
import HotZone from '../components/HotZone.vue';
import MyFriendsPopup from '../components/MyFriendsPopup.vue';
import CanNotShowOrder from '../components/CanNotShowOrder.vue';

Swiper.use([Autoplay]);
const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);

const shopName = ref('xxx旗舰店');
const isLoadingFinish = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
const sharePrize = ref([]);
const showMyPrize = ref(false);
// 我的好友弹窗
const showMyFriends = ref(false);
// 您无订单弹窗
const showCanNotShowOrder = ref(false);
const times = ref(0);

const rankBg = ref(furnish.rankListBg);
const showRankBg = computed(() => ({ backgroundImage: `url(${rankBg.value})` }));
const clickLeftTab = () => {
  rankBg.value = furnish.rankListBg;
};
const clickRightTab = () => {
  rankBg.value = furnish.invitedFriendsListBg;
};

// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
  prizeImg: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

const btnClick = (type:string) => {
  if (type === 'rule') {
    showRule.value = true;
  } else if (type === 'prize') {
    showMyPrize.value = true;
  } else if (type === 'friends') {
    showMyFriends.value = true;
  } else if (type === 'order') {
    showCanNotShowOrder.value = true;
  }
};

const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showAward.value = false;
  showSaveAddress.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
const swiperBanner = ref();
// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  rankBg.value = furnish.rankListBg;
  isLoadingFinish.value = true;
  nextTick(() => {
    // 如果 Swiper 实例已经存在，先销毁它
    if (swiperBanner.value) {
      swiperBanner.value.destroy(true, true);
      swiperBanner.value = null;
    }
    if (furnish.bannerList.length > 1) {
      swiperBanner.value = new Swiper('.swiper-banner', {
        autoplay: {
          delay: 2000,
          disableOnInteraction: false,
        },
        loop: true,
        slidesPerView: 1,
        loopedSlides: 10,
        allowTouchMove: true,
      });
    }
  });
});

// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  sharePrize.value = data.prizeList;
  ruleTest.value = data.rules;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    ruleTest.value = activityData.rules;
    shopName.value = activityData.shopName;
    sharePrize.value = activityData.prizeList;
    endTime.value = dayjs(activityData.endTime).valueOf();
    startTime.value = new Date(activityData.startTime).getTime();
    if (startTime.value > new Date().getTime()) {
      isStart.value = false;
    }
    if (startTime.value < new Date().getTime()) {
      isStart.value = true;
    }
    endTime.value = new Date(activityData.endTime).getTime();
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    rankBg.value = furnish.rankListBg;
    isLoadingFinish.value = true;
    nextTick(() => {
      // 如果 Swiper 实例已经存在，先销毁它
      if (swiperBanner.value) {
        swiperBanner.value.destroy(true, true);
        swiperBanner.value = null;
      }
      if (furnish.bannerList.length > 1) {
        swiperBanner.value = new Swiper('.swiper-banner', {
          autoplay: {
            delay: 2000,
            disableOnInteraction: false,
          },
          loop: true,
          slidesPerView: 1,
          loopedSlides: 10,
          allowTouchMove: true,
        });
      }
    });
  }
});
const toast = () => {
  showToast('活动预览，仅供参考');
};
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.bg {
  min-height: 100vh;
  background-size: 100%;
  background-repeat: no-repeat;
  //margin: 0 auto;
  //background-color:#f2f2f2;
  overflow: hidden;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 2.4rem;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
    position: relative;
    top: -2.2rem;
    left: 0.2rem;
  }

  .header-btn {
    width: 1.26rem;
    background-size: 100%;
    background-repeat: no-repeat;
    line-height: 0.39rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.step1Area{
  width: 7.2rem;
  height: 13.32rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 0 auto;
  padding: 1rem 0 0 0;
  .topBox{
    width: 5.7rem;
    height: 1.18rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    font-size: 0.2rem;
    div{
      margin: 0 auto;
      text-align: center;
    }
    .text{
      color: #010a83;
    }
    .num{
      color: #c81212;
    }
    .btn{
      width: 1.83rem;
      height: 0.54rem;
      background-size: 100%;
      background-repeat: no-repeat;
    }
  }
  .rankTitle{
    width: 6.64rem;
    height: 0.62rem;
    padding: 0.1rem 0.53rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 3.1rem auto 0;
    color: #7c7c7c;
    font-size: 0.2rem;
    .receiveBtn{
      width: 1.83rem;
      height: 0.4rem;
      background-size: 100%;
      background-repeat: no-repeat;
    }
  }
  .rankBox{
    width: 7.00rem;
    height: 7.50rem;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0.02rem auto;
    .tabBox{
      width: 6.3rem;
      height: 1.1rem;
      display: flex;
      margin: 0 auto;
      div{
        flex:1;
      }
    }
  }
}

.step2Area{
  width: 7.35rem;
  height: 9.57rem;
  background-size: 100%;
  background-repeat: no-repeat;
  margin-top: 0.2rem;
  margin-left:calc(50% - 7.2rem / 2);
  padding: 4.3rem 0 0;
  position: relative;
  .firstRemainNum{
    margin: 0 auto;
    text-align: center;
    width: 0.7rem;
    height: 0.7rem;
    border-radius: 100%;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/298869/17/13741/3159/6842b19dF97d600ce/930458cef26d565b.png) no-repeat;
    background-size: 100%;
    color: #785419;
    font-weight: bold;
    position: absolute;
    right: 1.17rem;
    top: 2.64rem;
    font-size: 0.14rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .getFirstBuyPrizeBtn{
    width: 2.71rem;
    height: 0.3rem;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0 0 0 3.58rem;
  }
  .invitedRemainNum{
    margin: 0 auto;
    text-align: center;
    width: 0.7rem;
    height: 0.7rem;
    border-radius: 100%;
    color: #785419;
    font-weight: bold;
    position: absolute;
    right: 1.14rem;
    top: 5.05rem;
    font-size: 0.14rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/298869/17/13741/3159/6842b19dF97d600ce/930458cef26d565b.png) no-repeat;
    background-size: 100%;
  }
  .getInvitedPrizeBtn{
    width: 2.71rem;
    height: 0.3rem;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 2.1rem 0 0 3.6rem;
  }
  .invitedShowBottomText{
    margin: 0.26rem auto 0;
    width: 4rem;
    height: 0.5rem;
    text-align: center;
    color: #fff;
    line-height: 0.5rem;
  }
  .invitedBottomBtn{
    width: 6rem;
    height: 0.74rem;
    margin: 0.4rem auto 0;
    display: flex;
    justify-content: space-between;
    .bottomBtn{
      flex:1;
      width: 2.93rem;
      height: 0.91rem;
      background-size: 100%;
      background-repeat: no-repeat;
    }
  }
}

.bottomImg{
  width: 7.2rem;
  height: 0.77rem;
  background-size: 100%;
  background-repeat: no-repeat;
  margin: 0.4rem auto;
}

.bannerBox{
  width: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  margin: 0 auto;
  .bannerImg{
    width: 7.5rem;
    height: 2rem;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0 auto;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
