<script lang="ts" setup>
/* eslint-disable */
import { ref, Ref, computed, onBeforeMount, onMounted, inject, reactive } from 'vue';
import { useStore } from 'vuex';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import { ruleFormat } from '../common';
import { gotoSkuPage } from '@/utils/platforms/jump';

const baseInfo: BaseInfo = inject('baseInfo');
console.log('=>(Index.vue:11) baseInfo', baseInfo);

const store = useStore();

const count = computed(() => store.state.count);

type DefineEmits = {
  (e: 'toggle-component', componentName: string): void;
};
const emits = defineEmits<DefineEmits>();

const isShowPopup: Ref<boolean> = ref(false);
const popupType: Ref<string> = ref('');

const handleOpenPopup = (type: string) => {
  isShowPopup.value = true;
  popupType.value = type;
};
const handleClosePopup = () => {
  isShowPopup.value = false;
  popupType.value = '';
};
const getStatus: Ref<number> = ref(1);
const handleGetActivityInfo = async (): Promise<any> => {
  try {
    showLoadingToast({});
    const res = await httpRequest.post('/kabrita/1888067948627755009/activityContent');
    const { status } = res.data;
    getStatus.value = status;
  } catch (error: any) {
    showToast(error);
  } finally {
    closeToast();
  }
};
const ruleContent: Ref<string> = ref();
const handleGetRuleInfo = async (): Promise<any> => {
  try {
    showLoadingToast({});
    const res = await httpRequest.get('/common/getRule');
    ruleContent.value = ruleFormat(res.data);
    handleOpenPopup('rule');
  } catch (error: any) {
    showToast(error);
  } finally {
    closeToast();
  }
};
const handleToOpenCard = (): void => {
  window.location.href = openCardLink.value;
};
// 领取按钮
const handleGetBtn = async (): Promise<any> => {
  if (baseInfo?.thresholdLevelsStatus === 3) {
    handleOpenPopup('joinMember');
    return;
  }
  try {
    showLoadingToast({});
    const res = await httpRequest.post('/kabrita/1888067948627755009/checkNewCus');
    const { data } = res;
    if (data) {
      emits('toggle-component', 'BeforeParticipating');
    } else {
      emits('toggle-component', 'FailStatus');
    }
  } catch (error: any) {
    showToast(error);
  } finally {
    closeToast();
  }
};

const skuList: Ref<any[]> = ref([]);
const handleSkuInfo = async (): Promise<any> => {
  try {
    showLoadingToast({});
    const res = await httpRequest.post('/kabrita/1888067948627755009/getSkuList');
    skuList.value = res.data;
  } catch (error: any) {
    showToast(error);
  } finally {
    closeToast();
  }
};

const myPrizeRecord: any = reactive({
  data: {
    createTime: '',
    rightsName: '',
  },
});
const handleGetMyPrizeRecord = async (): Promise<any> => {
  try {
    showLoadingToast({});
    const res = await httpRequest.post('/kabrita/1888067948627755009/getRightsRecord');
    myPrizeRecord.data = res.data;
    handleOpenPopup('getRecord');
  } catch (error: any) {
    showToast(error);
  } finally {
    closeToast();
  }
};
onBeforeMount(() => {
  handleGetActivityInfo();
  handleSkuInfo();
});
const openCardLink: Ref<string> = ref('');
onMounted(() => {
  if (baseInfo?.thresholdLevelsStatus === 3) {
    openCardLink.value = `${baseInfo?.openCardLink}&returnUrl=${encodeURIComponent(window.location.href)}`;
    handleOpenPopup('joinMember');
  }
});
</script>
<template>
  <div id="home">
    <div class="rule-btn" @click="handleGetRuleInfo()"></div>
    <div class="main-btn">
      <img v-if="getStatus===1" @click="handleGetBtn()" src="https://img10.360buyimg.com/imgzone/jfs/t1/256481/36/19803/22057/67ab0d1dF4d4b54ea/c4b0e60589b8944c.png" alt="">
      <img v-if="getStatus===2" src="https://img10.360buyimg.com/imgzone/jfs/t1/261319/32/18785/6368/67ab0d1dFc3997119/3bb620c355c0eb55.png" alt="">
    </div>
    <div class="record-btn" @click="handleGetMyPrizeRecord()">
      <img src="https://img10.360buyimg.com/imgzone/jfs/t1/255482/31/19805/4271/67ab0ddaF69dbf791/b22ef0bed2f48798.png" alt="">
    </div>
    <div class="sku-container">
      <div class="sku-list">
        <div class="sku-item" v-for="(it,index) in skuList" :key="index">
          <div class="sku-item-img">
            <img :src="it.imagePath" alt="">
          </div>
          <div class="sku-item-btn" @click="gotoSkuPage(it.skuId)"></div>
        </div>
      </div>
    </div>
    <!--    入会弹窗-->
    <van-popup :lock-scroll="false" :show="isShowPopup && popupType === 'joinMember'" safe-area-inset-bottom safe-area-inset-top @close="handleClosePopup">
      <div class="join-member-popup">
        <div class="btn" @click="handleToOpenCard()"></div>
      </div>
    </van-popup>
    <!--    规则弹窗-->
    <van-popup :lock-scroll="false" :show="isShowPopup && popupType==='rule'" @close="handleClosePopup">
      <div class="rule-popup">
        <div class="content" v-html="ruleContent"></div>
      </div>
    </van-popup>
    <!--    领取记录弹窗-->
    <van-popup :lock-scroll="false" :show="isShowPopup && popupType==='getRecord'" @close="handleClosePopup">
      <div class="get-record-popup">
        <div class="content">
          <div class="item" v-if="myPrizeRecord.data.createTime!==''">
            <div class="coupon-name">{{ myPrizeRecord.data.rightsName }}</div>
            <div class="get-time">{{ myPrizeRecord.data.createTime }}</div>
          </div>
          <div class="null-data" v-else>
            <img src="https://img10.360buyimg.com/imgzone/jfs/t1/267821/1/18703/6216/67ab1222F9a9449fc/77a53c8cf123aec5.png" alt="">
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
#home {
  width: 7.5rem;
  height: 19.85rem;
  background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/262843/11/22200/213573/67b84055F74785062/126874c9a28e83f4.jpg");
  background-size: contain;
  background-repeat: no-repeat;
  padding-top: 12.83rem;
  box-sizing: border-box;

  .rule-btn {
    width: .58rem;
    height: 1.86rem;
    position: absolute;
    right: 0;
    top: 4.48rem;
  }

  .main-btn {
    width: 3.46rem;
    height: .79rem;
    margin: 0 auto;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .record-btn {
    width: 1.2rem;
    height: .37rem;
    margin: .24rem auto 0;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .sku-container {
    width: 6.62rem;
    overflow-x: scroll;
    margin: 1.26rem auto 0;

    .sku-list {
      display: flex;
      justify-content: flex-start;

      .sku-item {
        width: 3.35rem;
        height: 3.98rem;
        background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/268191/31/18610/12203/67ab0ec8Faf9df9f1/07ec73c060ef1120.png");
        background-size: contain;
        background-repeat: no-repeat;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: .43rem;
        box-sizing: border-box;

        .sku-item-img {
          width: 2.3rem;
          height: 2.3rem;
          //border-radius: .2rem;
          //overflow: hidden;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .sku-item-btn {
          width: 100%;
          height: .7rem;
          margin-top: .28rem;
        }
      }
    }
  }

  .van-popup--center {
    max-width: initial;
  }

  .join-member-popup {
    width: 6.54rem;
    height: 7.49rem;
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/265864/40/18615/131201/67aaf480F39c8e811/079e478a4045d58b.png");
    background-size: contain;
    background-repeat: no-repeat;
    position: relative;

    .btn {
      width: 3.76rem;
      height: .84rem;
      position: absolute;
      left: 1.33rem;
      bottom: 0
    }
  }

  .rule-popup {
    width: 7.5rem;
    height: 11.66rem;
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/258857/20/18897/77484/67aaf795Fec58d7cb/77523e837ec6576c.png");
    background-size: contain;
    background-repeat: no-repeat;
    padding-top: 2.95rem;
    padding-left: 1.28rem;
    box-sizing: border-box;

    .content {
      width: 5.06rem;
      height: 6.7rem;
      overflow-y: scroll;
      font-size: .28rem;
      color: #4d4e50;
      line-height: .35rem;
    }

  }

  .get-record-popup {
    width: 7.3rem;
    height: 6.49rem;
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/264437/37/18700/56653/67ab100fF2f350cc2/4ac880c36fc440f5.png");
    background-size: contain;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 4.1rem;
    box-sizing: border-box;

    .content {
      width: 5.33rem;
      height: .55rem;

      .null-data {
        width: 2.23rem;
        height: .26rem;
        margin: 0 auto;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .item {
        display: flex;
        justify-content: space-between;
        padding-left: .51rem;
        box-sizing: border-box;

        .coupon-name {
          width: 2.15rem;
          font-size: .27rem;
          color: #363636;
          text-align: center;
        }

        .get-time {
          width: 2.1rem;
          font-size: .23rem;
          color: #363636;
          text-align: center;
        }
      }
    }
  }
}
</style>
