<script lang="ts" setup>
/* eslint-disable */
import { ref, Ref, reactive } from 'vue';
import { useStore } from 'vuex';
import { RootState } from '../store/state';
import { buryPointBehaviors } from '../common';

const store = useStore<RootState>();

type DefineEmits = {
  (e: 'toggle-component', componentName: string): void;
};
const emits = defineEmits<DefineEmits>();

const curSelectIndex: Ref<number> = ref(0);
const select = reactive({
  list: [
    {
      image1: 'https://img10.360buyimg.com/imgzone/jfs/t1/230188/7/34457/22850/67ac0dccF9aced0ae/45ff936921dfb6af.png',
      image2: 'https://img10.360buyimg.com/imgzone/jfs/t1/264433/37/18834/24919/67ac0dccFb571f5a1/9a9f5f251ce8d9dd.png',
      value: 1,
      name: '已出生',
    },
    {
      image1: 'https://img10.360buyimg.com/imgzone/jfs/t1/261056/36/19077/25694/67ac0dcdF7fb665b4/8d7c06b70c141a18.png',
      image2: 'https://img10.360buyimg.com/imgzone/jfs/t1/259783/6/18963/27681/67ac0dccFb3f23058/3b339515ed99fff9.png',
      value: 2,
      name: '未出生',
    },
  ],
});

const handleNextPage = () => {
  store.commit('setBirthStatus', select.list[curSelectIndex.value].value);
  buryPointBehaviors('custom_click', select.list[curSelectIndex.value].name);
  emits('toggle-component', 'UploadPhoto');
};
</script>
<template>
  <div id="before-participating">
    <div class="select-container">
      <div class="item" v-for="(it,index) in select.list" :key="index" @click="curSelectIndex = index">
        <img :src="curSelectIndex === index ? it.image2 : it.image1" alt="">
      </div>
    </div>
    <div class="btn" @click="handleNextPage()"></div>
  </div>
</template>

<style lang="scss" scoped>
#before-participating {
  width: 7.5rem;
  height: 18rem;
  background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/254831/5/20295/56523/67ac0dcbF7a9d4d83/b70808a3c3318fec.jpg");
  background-size: contain;
  background-repeat: no-repeat;
  padding-top: 7.33rem;
  box-sizing: border-box;

  .select-container {
    width: 6.42rem;
    height: 5.77rem;
    margin: 0 auto;

    .item {
      width: 6.42rem;
      height: auto;

      img {
        width: 100%;
        height: 100%;
      }

      &:nth-child(2) {
        margin-top: .47rem;
      }
    }
  }

  .btn {
    width: 4.17rem;
    height: .95rem;
    margin: 1.95rem auto 0;
  }
}
</style>
