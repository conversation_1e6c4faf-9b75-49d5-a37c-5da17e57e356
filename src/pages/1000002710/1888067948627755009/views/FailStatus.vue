<script lang="ts" setup>
/* eslint-disable */
import { ref, Ref, onMounted } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const url: Ref<string> = ref('');
const handleGetActivityInfo = async (): Promise<any> => {
  try {
    showLoadingToast({});
    const res = await httpRequest.post('/common/getActivityConfig');
    url.value = JSON.parse(res.data).url;
  } catch (error: any) {
    showToast(error);
  } finally {
    closeToast();
  }
};
const handleJump = () => {
  window.location.href = url.value;
};
onMounted(() => {
  handleGetActivityInfo();
});
</script>

<template>
  <div id="error">
    <div class="btn" @click="handleJump()"></div>
  </div>
</template>

<style lang="scss" scoped>
#error {
  width: 7.5rem;
  height: 18rem;
  background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/266132/39/19360/130035/67ad8ba7Fd754c982/02a736f42919cd7b.jpg");
  background-size: contain;
  background-repeat: no-repeat;
  padding-top: 15.15rem;
  box-sizing: border-box;

  .btn {
    width: 5.06rem;
    height: .95rem;
    margin: 0 auto;
  }
}
</style>
