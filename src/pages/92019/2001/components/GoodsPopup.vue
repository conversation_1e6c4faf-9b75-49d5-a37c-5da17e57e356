<template>
  <CommonDrawer title="活动商品" @close="emits('close')">
    <div class="h-[40vh] px-4 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="topTab" v-if="skuList.length || data.length">
        <van-tabs v-model:active="active" @click-tab="onClickTab">
          <van-tab v-for="(item, index) in seriesInfo" :key="index" :title="item.name">
            <div class="text-gray-400 text-sm flex justify-center pb-4">
              <div class="grid grid-cols-2 gap-2">
                <div v-for="(item, index) in skuList.length ? skuList : data" class="bg-white py-2 px-3.5" :key="index" @click="goToSku(item)">
                  <div class="flex justify-center">
                    <img class="w-32 h-32" :src="item.skuMainPicture" alt="" />
                  </div>
                  <div class="text-xs mt-5 lz-multi-ellipsis--l2" v-text="item.skuName"></div>
                  <div class="text-red-500 text-xs mt-3">¥ <span v-text="item.jdPrice"></span></div>
                </div>
              </div>
            </div>
            <div class="more-btn" v-if="!isPreview && skuList.length && skuList.length < total" @click="loadMore">点我加载更多</div>
            <div class="more-btn" v-if="isPreview && data.length && data.length !== total" @click="loadMorePreview">点我加载更多</div>
          </van-tab>
        </van-tabs>
      </div>
      <div v-else class="no-data">
        活动商品为本店全部商品
        <div class="btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
      </div>
    </div>
  </CommonDrawer>
</template>

<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { ref, defineEmits, defineProps, inject, watch } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps(['data']);
const emits = defineEmits(['close']);
const skuList = ref<any[]>([]);
const pageNum = ref(1);
const total = ref(0);

type Series = {
  id: string;
  name: string;
};

const seriesInfo = ref<Series[]>([]);
const active = ref(0);
const activeSeriesId = ref('');

// 获取系列参与活动商品
const getOrderSkuList = async (seriesId: any) => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/90007/getSeriesTypeSkuListPage', {
      seriesId,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    if (pageNum.value === 1) {
      skuList.value = [];
    }
    skuList.value.push(...data.orderSkuList.records);
    total.value = data.orderSkuList.total;
    seriesInfo.value = data.seriesInfo;
    closeToast();
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
};

// 切换系列tab
const onClickTab = (value: any) => {
  console.log('aaa', value);
  seriesInfo.value.forEach((item) => {
    if (item.name === value.title) {
      console.log('bbb', item.id);
      pageNum.value = 1;
      activeSeriesId.value = item.id;
      getOrderSkuList(item.id);
    }
  });
  // getOrderSkuList(value.);
};

const goToSku = (item: any) => {
  if (isPreview) {
    showToast('活动预览,仅供查看');
    return;
  }
  gotoSkuPage(item.skuId);
};

const loadMore = async () => {
  pageNum.value++;
  await getOrderSkuList(activeSeriesId.value);
};
const loadMorePreview = () => {
  showToast('活动预览,仅供查看');
};
if (!isPreview) {
  getOrderSkuList(999);
} else {
  watch(props.data, () => {
    skuList.value = props.data;
  });
}
</script>

<style scoped lang="scss">
.title {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
}
.topTab {
  display: flex;
}
.no-data {
  text-align: center;
  padding-top: 3rem;
  font-size: 0.3rem;
  .btn {
    width: 2.4rem;
    height: 0.9rem;
    line-height: 0.9rem;
    text-align: center;
    color: white;
    font-size: 0.3rem;
    border-radius: 0.1rem;
    background-color: #ff9900;
    margin: 0.3rem auto;
  }
}
.more-btn {
  width: 1.8rem;
  height: 0.5rem;
  font-size: 0.2rem;
  color: #fff;
  background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
  background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
  border-radius: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 0.3rem;
}
</style>
