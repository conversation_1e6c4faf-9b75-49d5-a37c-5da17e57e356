<template>
  <div class='bg' :style='furnishStyles.pageBg.value' v-if='isLoadingFinish'>
    <div class='header-kv select-hover'>
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/301889/2/22100/153244/68956786F10c41962/4f47759f0824faef.jpg'" alt='' class='kv-img' />
      <!--      <img src='https://img10.360buyimg.com/imgzone/jfs/t1/301889/2/22100/153244/68956786F10c41962/4f47759f0824faef.jpg' alt='' class='kv-img' />-->
      <div class='header-content'>
        <!-- 活动规则 -->
        <div class='header-btn header-btn1' v-click-track="'hdgz'" @click='rulePopup=true'></div>
        <!-- 我的奖品 -->
        <div class='header-btn header-btn2' v-click-track="'wdjp'" @click='myPrizePopup = true'></div>
      </div>

      <div class='ac-Introduction' :style='furnishStyles.acIntroductionBg.value'></div>

      <div class='series-box' v-for='(item, index) in seriesList' :key='index'>
        <div class='serise-top' :style="{backgroundImage:`url(${furnish?.seriesBoxBkHead || '//img10.360buyimg.com/imgzone/jfs/t1/313836/18/23643/7541/68956aecFe13e6ecb/bb5728dae4788609.png'})`}">
          {{ item.seriesName }}
        </div>
        <div class='serise-main' :style="{backgroundImage:`url(${furnish?.seriesBoxBkBody || '//img10.360buyimg.com/imgzone/jfs/t1/314370/18/24082/643/68956aecF452fd525/9f1db27544ec2225.png'})`}">
          <span class='serise-tip'>注意:<span style='color: #e52d26'>奖品随时更换，建议锁定奖品尽快完成兑换</span><br />请先选定好自己的目标奖品集罐，因为选择一个奖品领取后罐数会被消耗，需要重新积累获取下一级奖品哦！</span>

          <div class='serise-prize' v-for='(_item,_index) in item.prizes'>
            <div class='serise-title'>购买{{ item.seriesName }}系列商品满{{ _item.standardNum }}罐，加赠{{ _item.prizeName }}</div>
            <div class='serise-stock gradient-heading'><i>限量{{ _item.totalNum }}份!</i></div>
            <div class='exchange-view'>
              <div class='display-line'>
                <div class='product-view'>
                  <img style='width: 1.8rem' :src='item.seriesImg' alt=''>
                  <img class='buy-btn' @click='getBuySkuList(item.seriesId)' :src="decoData.buyBtn??'//img10.360buyimg.com/imgzone/jfs/t1/324723/15/2039/11054/68956785F6cf7052f/072727ad3273a9e2.png'" alt=''>
                  <div class='tank-num'>x{{ _item.standardNum }}</div>
                </div>
                <img style='width: .6rem' src='//img10.360buyimg.com/imgzone/jfs/t1/323907/6/2352/1966/6895c8a5F5534385b/44a867756dae79db.png' alt=''>
                <div class='product-view'>
                  <img style='width: 1.8rem' :src='_item.prizeImg' alt=''>
                  <img class='draw-btn' @click='sureDrawPrize(item.seriesId, _item)' :src='getDrawBtn(_item)' alt=''>
                  <div class='price-num' v-if="_item.prizeValue!=='0.01'">价值:￥<span style='font-size: .24rem;letter-spacing: -.02rem'>{{ _item.prizeValue?.split('.')[0] }}</span></div>
                  <div class='prize-stock'>奖品剩余：{{ _item.stockNum }}</div>
                </div>
              </div>

              <div class='process-view' v-if="_item.reason !== '每人每种奖品最多兑换1个'">
                <div class='process-message'>{{ item.userPotNum }} / {{ _item.standardNum }}</div>
                <div class='process' :style='{width:`${item.userPotNum/_item.standardNum*5.92}rem`}'></div>
              </div>

              <div class='process-tip' v-if="_item.reason === '每人每种奖品最多兑换1个'">
                您已领取过该赠品，看看其他阶段吧~
              </div>
              <div class='process-tip' v-else-if='_item.lackNum>0'>
                您已购买 {{ item.userPotNum }} 罐<br />再买 {{ _item.lackNum }} 罐就可以拿到第 {{ _index + 1 }} 步赠品了哦
              </div>
              <div class='process-tip' v-else-if="_item.lackNum<=0&&(_item.canClick||_item.reason === '奖品已兑完')">
                您已购买 {{ item.userPotNum }} 罐<br />您已满足领取条件，快来领取吧~
              </div>


              <div class='process-tip-2'>
                *以订单确认收货状态为准，不含小罐奶粉
              </div>
            </div>

          </div>

          <img class='go-buy-btn' @click='getBuySkuList(item.seriesId)' src='//img10.360buyimg.com/imgzone/jfs/t1/292109/35/27084/7432/68956785F7378a15d/a571b9134f0c537c.png' />
        </div>
        <div class='serise-bottom' :style="{backgroundImage:`url(${furnish?.seriesBoxBkFooter || '//img10.360buyimg.com/imgzone/jfs/t1/319866/15/22127/2634/68956aecF0dd9c2c8/0e8d7b8a120e74f6.png'})`}"></div>
      </div>

      <div class='rule-box'>
        <div class='rule-message' v-html='ruleText'></div>
      </div>
    </div>

    <VanPopup teleport='body' v-model:show='rulePopup' :closeOnClickOverlay='false'>
      <Rule @close='rulePopup = false' :rule='ruleText'></Rule>
    </VanPopup>

    <VanPopup teleport='body' v-model:show='myPrizePopup' :closeOnClickOverlay='false'>
      <MyPrize v-if='myPrizePopup' @close='myPrizePopup = false'></MyPrize>
    </VanPopup>

    <!-- 去购买sku展示弹窗 -->
    <VanPopup teleport='body' v-model:show='showSkuPopup'>
      <ShowSkuPopup :skuList='buySkuList' @close='showSkuPopup = false'></ShowSkuPopup>
    </VanPopup>

    <!--    &lt;!&ndash; 中奖弹窗 &ndash;&gt;-->
    <!--    <VanPopup teleport='body' v-model:show='awardPopup'>-->
    <!--      <Award :prize='award' @close='awardPopup = false' @saveAddress='toSaveAddress'></Award>-->
    <!--    </VanPopup>-->

    <!-- 保存地址弹窗 -->
    <VanPopup teleport='body' v-model:show='showSaveAddress' :closeOnClickOverlay='false'>
      <SaveAddress @close='showSaveAddress = false' @handleSaveAddress='handleSaveAddress'></SaveAddress>
    </VanPopup>

    <!-- 确认兑换弹窗 -->
    <VanPopup teleport='body' v-model:show='confirmExange' :closeOnClickOverlay='false'>
      <ConfirmExchangePopup @close='confirmExange = false' @sureDraw='handleConfirmExchange'></ConfirmExchangePopup>
    </VanPopup>
    <!-- 活动门槛 -->
    <Threshold2 v-model:show='showLimit' :data='baseInfo?.thresholdResponseList' />
  </div>
</template>

<script lang='ts' setup>
import { computed, inject, reactive, ref } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import Threshold2 from '@/components/Threshold2/index.vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import MyPrize from './components/MyPrize.vue';
import Rule from './components/Rule.vue';
import ShowSkuPopup from './components/ShowSkuPopup.vue';
import SaveAddress from './components/SaveAddress.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import useThreshold from '@/hooks/useThreshold';
import ConfirmExchangePopup from './components/ConfirmExchangePopup.vue';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const confirmExange = ref(false);

const isLoadingFinish = ref(false);
const showSkuPopup = ref(false);
// const awardPopup = ref(false);
// 系列列表
const seriesList = ref<any[]>([]);

// 展示门槛显示弹框
const showLimit = ref(false);
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
});

const exchangeObj = {
  stepId: '',
  prizeId: '',
  recordId: '',
  seriesId: 0,
};

const prizeList = ref([]); // 奖品列表
const buySkuList = ref([]); // 去购买sku列表
const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);

const getDrawBtn = (prize: any) => {
  if (prize.reason === '奖品已兑完') {
    return decoData.soldOutBtn ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/176285/22/28766/4595/621453a1Ef270f32a/5e390c712a901163.png';
  } else if (prize.reason === '每人每种奖品最多兑换1个') {
    return decoData.hasDrawBtn ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/215057/7/13029/4853/621453a1E12a9d850/eca5820bed1c1f8e.png';
  } else {
    return decoData.exchangeBtn ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/307713/18/23863/7455/68956785Febe2928c/68e7950642808bad.png';
  }
};

// 展示活动规则，首次获取规则
const getRuleMessage = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleText.value = data;
    }
  } catch (error: any) {
    console.error();
  }
};

// const award = ref({
//   prizeType: 1,
//   prizeName: '',
//   showImg: '',
//   result: '',
//   activityPrizeId: '',
//   userPrizeId: '',
// });

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const addressForm = ref({});
// 计算用户罐数
const calcUserPotNum = () => {
  httpRequest.post('/92019/computePinSeriesPotNum').then((res) => {
    console.log(res);
    if (res.code === 200) {
      httpRequest.post('/92019/getSeriesPrizes').then((res) => {
        console.log(res);
        if (res.code === 200) {
          seriesList.value = res.data;
        }
      });
    }
  }).catch((err) => {
    console.log(err);
  });
};

// const toSaveAddress = (id: string, prizeId: string) => {
//   addressId.value = id;
//   activityPrizeId.value = prizeId;
//   showSaveAddress.value = true;
// };

// 确认兑换
const handleConfirmExchange = () => {
  confirmExange.value = false;
  showSaveAddress.value = true;
};

// 暂存地址
const handleSaveAddress = (data: any) => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  addressForm.value = data;
  showSaveAddress.value = false;
  httpRequest.post('/92019/cashPrize', { prizeId: exchangeObj.prizeId, stepId: exchangeObj.stepId, seriesId: exchangeObj.seriesId, ...data }).then((data) => {
    if (data.code === 200) {
      closeToast();
      calcUserPotNum();
      exchangeObj.recordId = data.data.userPrizeId;
      if (data.data.status === 1) {
        showToast('提交成功，请等待发放');
        // awardPopup.value = true;
      } else {
        showToast('领取失败 请联系客服~');
      }
    } else {
      closeToast();
      showToast(data.message);
    }
  }).catch((err) => {
    closeToast();
    console.log(err);
  });
  // httpRequest.post('/92019/saveAddress', { ...data, ...exchangeObj }).then((data) => {
  //   if (data.code === 200) {
  //     showSaveAddress.value = false;
  //     calcUserPotNum();
  //     closeToast();
  //     showToast('提交成功，请等待发放');
  //   }
  // }).catch((err) => {
  //   closeToast();
  //   console.log(err);
  // });
};

const getBuySkuList = (seriesId: string) => {
  showLimit.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList,
  });
  if (baseInfo.thresholdResponseList?.length > 0) {
    return;
  }
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  httpRequest.post('/92019/getExporeSkuList', { seriesId }).then((res) => {
    console.log(res);
    if (res.code === 200) {
      buySkuList.value = res.data;
      showSkuPopup.value = true;
      // if (res.data?.length > 0) {
      // } else {
      //   // 如果没配置 展示奖品 跳转商店首页
      //   gotoShopPage(baseInfo.shopId);
      // }
    }
    closeToast();
  }).catch((err) => {
    console.log(err);
    closeToast();
    showToast(err.message);
  });
};

const sureDrawPrize = (seriesId: number, prizeObj: any) => {
  showLimit.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList,
  });
  if (baseInfo.thresholdResponseList?.length > 0) {
    return;
  }

  if (prizeObj.canClick) {
    Object.assign(exchangeObj, prizeObj);
    exchangeObj.seriesId = seriesId;
    confirmExange.value = true;
  } else {
    showToast({
      message: `${prizeObj.reason}`,
      duration: 2000,
    });
  }
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([calcUserPotNum(), getRuleMessage()]);
    isLoadingFinish.value = true;

    closeToast();
  } catch (error: any) {
    console.error(error);

    closeToast();
  }
};
init();
</script>

<style scoped lang='scss'>

</style>
