export const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};
export const defaultStateList = [
  {
    // 阶梯封面图
    ladderImg: '//img10.360buyimg.com/imgzone/jfs/t1/266715/15/12659/8403/67887d8cF9127725f/393a13d97b97c4e2.png',
    // 阶梯名称
    ladderName: 'XXXXXX',
    // 罐数
    potNum: 0,
    // 阶梯奖品信息
    prizeInfoList: [
      {
        // 按钮状态
        can: true,
        // 活动资产id
        activityPrizeId: 1,
        // 资产封面图
        prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png',
        // 资产名称
        prizeName: 'xx积分',
        // 资产类型
        prizeType: 4,
        // 剩余份数
        remainCount: 0,
        // 奖品状态
        status: 0,
      },
    ],
  },
];

export const IMAGE_MAP = {
  // 倒计时背景
  COUNT_DOWN_BG: 'https://img10.360buyimg.com/imgzone/jfs/t1/249344/31/1668/23181/6593bcc5Fc9cca6bd/2da62e837e4ff0f9.png',
  // 立即下单图
  IMMEDIATELY_PAY: 'https://img10.360buyimg.com/imgzone/jfs/t1/226966/6/11196/24408/6593bcc4Fb181614a/507fef69f187dce7.png',
  // 步骤tag
  STEP_ICON: 'https://img10.360buyimg.com/imgzone/jfs/t1/229749/8/10382/818/6593bcc5F44a7689a/600eddbd71ae6fd8.png',
  // 奖品背景
  PRIZE_BORDER: 'https://img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png',
  // 爆款商品背景
  HOT_ITEM_BG: 'https://img10.360buyimg.com/imgzone/jfs/t1/226687/36/9528/90302/6593bcc6Fd3f4b982/57f4f5986afece13.png',
};
