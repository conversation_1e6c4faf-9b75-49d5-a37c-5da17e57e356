<template>
  <div class="task-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>获得更多游戏机会</div>
      <div class="rightLineDiv"></div>
      <img alt="" data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="times">
      今天还有 <span>{{ times }}</span> 次游戏机会
    </div>
    <div class="content">
      <div v-for="(item, index) in tasks" :key="item.id" class="task">
        <img :src="taskInfo[item.taskType].icon" alt="" class="icon" />
        <div class="info">
          <div class="name">{{ taskInfo[item.taskType].label }}</div>
          <div class="rule">{{ taskRule[item.taskType](item) }}</div>
        </div>
        <div class="button" v-if="item.taskFinishCount < item.limit && item.isClickable" @click="doTask(index)">{{ taskInfo[item.taskType].button }}</div>
        <div class="button button-dis" v-else>{{ taskInfo[item.taskType].buttonDIs }}</div>
      </div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSku" position="bottom">
    <ShowSku v-if="showSku" :detail="tasks[taskDetailIndex]" @close="showSku = false" @refreshTask="refreshTask" @openShowGoShop="openShowGoShop"></ShowSku>
  </VanPopup>
  <!-- 进店逛逛 -->
  <VanPopup teleport="body" v-model:show="showGoShop" position="bottom" z-index="10000">
    <GoShopPop v-if="showGoShop" @close="showGoShop = false" @hasGo="hasGoShop"></GoShopPop>
  </VanPopup>
</template>

<script lang="ts" setup>
import { PropType, inject, ref } from 'vue';
import { showToast, showLoadingToast, closeToast } from 'vant';
import ShowSku from './ShowSku.vue';
import GoShopPop from './GoShopPop.vue';
import { callShare } from '@/utils/platforms/share';
import { httpRequest } from '@/utils/service';
import { Task } from '../ts/type';
import constant from '@/utils/constant';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';
import { taskInfo, taskRule } from '@/utils/taskTypedes';
import dayjs from 'dayjs';

const baseInfo = inject('baseInfo') as BaseInfo;

const isPreview = (inject('isPreview') as boolean) ?? false;

const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) as string);

const pros = defineProps({
  times: {
    type: Number,
    default: 0,
  },
  tasks: {
    type: Array as PropType<Task[]>,
    default: () => [],
    required: true,
  },
  shareImg: {
    type: String,
    default: '',
  },
  shareTitle: {
    type: String,
    default: '',
  },
  shopId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close', 'refreshTask', 'openShowGoShop']);

const openShowGoShop = () => {
  emits('openShowGoShop');
};
const close = () => {
  emits('close');
};

const refreshTask = () => {
  emits('refreshTask');
};

const showSku = ref(false);
const taskDetailIndex = ref(0);

// 进店逛逛
const showGoShop = ref(false);

// 上报任务完成
const reportTask = async (taskId: number) => {
  try {
    const apis = {
      1: '/80082/followShop/execute',
      2: '/80082/browseShop/shareShop',
      4: '/80082/browseLive/execute',
      14: '/80082/signIn/excute',
    };
    const res = await httpRequest.post(apis[taskId]);
    if (res.code === 200) {
      refreshTask();
    } else {
      showToast(res.msg);
    }
  } catch (error: any) {
    console.error(error);
  }
};

const doTask = (index: number) => {
  if (isPreview) return;
  // console.log(dayjs().valueOf(), 'dayjs().valueOf()');
  if (pros.tasks[index].taskTimeLimitWay === 2) {
    // 指定时间的任务进行时间判断
    if (dayjs().valueOf() < pros.tasks[index].taskStartTime || dayjs().valueOf() > pros.tasks[index].taskEndTime) {
      showToast('当前时间未处于任务时间范围内');
      return;
    }
  }
  if (pros.tasks[index].taskType === 1) {
    reportTask(pros.tasks[index].taskType);
  } else if (pros.tasks[index].taskType === 2) {
    // gotoShopPage(pros.shopId);
    // reportTask(pros.tasks[index].taskType);
    showGoShop.value = true;
  } else if (pros.tasks[index].taskType === 4) {
    reportTask(pros.tasks[index].taskType);
    window.location.href = pros.tasks[index].pageLink;
  } else if ([3, 5, 7].includes(pros.tasks[index].taskType)) {
    taskDetailIndex.value = index;
    showSku.value = true;
  } else if (pros.tasks[index].taskType === 13) {
    window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1&isTask=true`)}`;
  } else if (pros.tasks[index].taskType === 14) {
    reportTask(pros.tasks[index].taskType);
  } else if (pros.tasks[index].taskType === 15) {
    callShare({
      title: shareConfig.shareTitle,
      content: shareConfig.shareTitle,
      shareUrl: `${window.location.href}&shareId=${shareConfig.shareId}&taskType=${pros.tasks[index].taskType}`,
      imageUrl: shareConfig.shareImage,
      afterShare: () => {
        reportTask(pros.tasks[index].taskType);
      },
    });
  }
};

const hasGoShop = () => {
  reportTask(2);
};
</script>

<style scoped lang="scss">
.task-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .times {
    text-align: center;
    color: #262626;
    font-size: 0.24rem;
    margin-top: 0.3rem;

    span {
      color: rgb(242, 39, 12);
    }
  }

  .content {
    height: 8.5rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;

    .task {
      background-color: #fff;
      margin-bottom: 0.1rem;
      border-radius: 0.1rem;
      padding: 0.2rem 0.3rem;
      display: flex;
      align-items: center;

      .icon {
        width: 0.83rem;
      }

      .info {
        flex: 1;
        padding: 0 0.39rem;
      }

      .name {
        font-size: 0.3rem;
        color: rgb(255, 153, 0);
      }

      .rule {
        font-size: 0.24rem;
        color: #8c8c8c;
      }

      .button {
        width: 1.36rem;
        height: 0.46rem;
        background: linear-gradient(90deg, rgb(242, 39, 12) 0%, rgb(255, 100, 32) 100%);
        font-size: 0.2rem;
        color: rgb(255, 255, 255);
        border-radius: 0.23rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .button-dis {
        filter: grayscale(100%);
      }
    }
  }
}
</style>
