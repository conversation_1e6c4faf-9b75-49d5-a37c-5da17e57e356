<template>
  <div class="gameAll">
    <div id="gameDiv" class="gameDiv">
      <canvas id="GameCanvas" width="750" height="100vh"></canvas>
    </div>
    <div class="bizDiv" v-if="ifShowPop">
      <div :class="gamePopDiv">
        <!--popStatus 0介绍 1成功 没有游戏机会 2成功还有机会  3失败没有机会 4失败还有机会 -->
        <div :class="[popStatus===0 ? 'popTitleClass' : 'popTitleClass1']">{{ popTitle }}</div>
        <div class="introduce" v-if="popStatus===0">
          <div class="intContent">①得分超<span>{{ gameScore }}</span>分即可抽奖</div>
          <div class="intContent">②点击屏幕控制角色跳动，躲避出现的障碍物</div>
          <img class="clickIcon" :src="clickIcon" alt="">
          <div class="intContent">③躲避障碍物越多，分数越高</div>
          <div class="intContent">④小鸟碰到障碍物则游戏结束</div>
        </div>
        <div class="gameResult" v-else>
          <!-- 成功 -->
          <img class="trophyImg" :src="trophy1" v-if="popStatus===1||popStatus===2" alt="">
          <!-- 失败 -->
          <img class="trophyImg" :src="trophy2" v-else alt="">
          <div class="resultText1">当前成绩</div>
          <div class="resultText2 marB20" v-if="popStatus===1||popStatus===2">{{ currentScore }}分</div>
          <div class="resultText2" v-else>{{ currentScore }}分</div>

          <div class="resultText3" v-if="popStatus==3||popStatus==4">达到<span>{{ gameScore }}</span>分才可获得抽奖机会哦~</div>

        </div>
        <div class="popBtnDiv">
          <!-- 红 我知道了/再次挑战 -->
          <div class="popBtn1" v-if="popStatus===0 || popStatus===2" @click="btnClick1">{{ popBtn1Text }}</div>
          <!--红 获取更多游戏机会 -->
          <div class="popBtn3" v-if="popStatus===1||popStatus===4" @click="btnClick1">{{ popBtn1Text }}</div>
          <!--黄 popStatus=0 不再提醒 popStatus=1 点击抽奖 popStatus=4 再次挑战 -->
          <div class="popBtn2" @click="btnClick2" v-if="popStatus!==3">{{ popBtn2Text }}</div>
          <!--popStatus=3 获取更多机会 -->
          <div class="popBtn4" v-if="popStatus===3" @click="btnClick1">{{ popBtn1Text }}</div>
        </div>
      </div>
    </div>
    </div>
</template>
<script lang="ts" setup>
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { ref } from 'vue';
import { eventKey, registerEvent, dispatchEvent } from '@/utils/gameEvent';

const ifShowPop = ref(true);
ifShowPop.value = sessionStorage.getItem('isGameReminderIntro') !== 'false';

const popTitle = ref('游戏介绍');
const popStatus = ref(0); // 0介绍 1成功 没有游戏机会 2成功还有机会  3失败没有机会 4失败还有机会
const clickIcon = ref('//img10.360buyimg.com/imgzone/jfs/t1/143093/8/28977/4331/638070ffE7b991ec1/1c1f837b32f5e5c1.png');

const trophy1 = ref('//img10.360buyimg.com/imgzone/jfs/t1/26081/33/20036/1735/63808f1dEd84bbe5d/0dda349f76b7d1f5.png');
const trophy2 = ref('//img10.360buyimg.com/imgzone/jfs/t1/96745/28/34944/1697/63808f1dEce2577ff/13f6ec048e490baa.png');
const gameScore = ref(0); // 达到游戏分数
const currentScore = ref(0);// 游戏结束当前游戏获得分数
const popBtn1Text = ref('我知道了');
const popBtn2Text = ref('不再提醒');
const uuid = ref('');
const props = defineProps({
  gameChanceNum: {
    type: Number,
    required: true,
  },
});
const gameChanceNumData = ref(props.gameChanceNum);// 接收游戏机会
const gamePopDiv = ref('gamePopDiv');
const emits = defineEmits(['close']);
const close = () => {
  // emits('close');
  if (window.history.replaceState) {
    window.history.replaceState(null, document.title, window.location.href);
    window.history.go(0);
  } else {
    window.location.replace(window.location.href);
  }
  // window.location.reload();
};

const gameEnd = async () => {
  if (popStatus.value === 1 || popStatus.value === 2) {
    // 挑战成功
    await httpRequest.post('/80082/gameEnd', {
      uuid: uuid.value,
    });
  } else {
    // 挑战失败
    await httpRequest.post('/80082/gameEnd', {
      uuid: '',
    });
  }
};
const getGameInit = async () => {
  const res = await httpRequest.post('/80082/gameStart');
  console.log(res, '开始游戏');
  if (res.code === 200) {
    gameScore.value = res.data.drawScoreLimit;
    uuid.value = res.data.uuid;
  } else {
    showToast(res.message);
  }
};
const plagGameAgain = async () => {
  console.log('再次挑战。。。');
  await getGameInit();
  dispatchEvent(eventKey.onGameReplay, (callback: any) => { callback && callback(); });
};
const btnClick1 = () => {
  console.log('左按钮', popStatus.value);
  ifShowPop.value = false;
  if (popStatus.value === 2) {
    // 再次挑战
    plagGameAgain();
  } else if (popStatus.value === 4 || popStatus.value === 1 || popStatus.value === 3) {
    // 获取更多游戏机会
    close();

  }
};

const btnClick2 = () => {
  if (popStatus.value === 0) {
    // 不再提醒
    sessionStorage.setItem('isGameReminderIntro', 'false');
    ifShowPop.value = false;
  } else if (popStatus.value === 4) {
    ifShowPop.value = false;
    // 再次挑战
    plagGameAgain();
  } else if (popStatus.value === 2 || popStatus.value === 1) {
    // 点击抽奖
    close();
  }
};
const loadScript = (src: string) => new Promise((resolve) => {
  const script = document.createElement('script');
  script.type = 'text/javascript';
  script.crossOrigin = 'anonymous';
  script.onload = () => {
    resolve(true);
  };
  script.src = `${src}?v=${new Date().getTime()}`;
  document.body.append(script);
});
const initPage = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    // https://lzkj-isv.isvjcloud.com/games/ylgames/CarParkour/index.js
    // https://lzkj-isv.isvjcloud.com/games/CarParkour/index.js
    await loadScript('https://lzkj-isv.isvjcloud.com/games/ylgames/FlappyBird/index.js');
    await getGameInit();
    await window.boot && window.boot();
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};
initPage();

registerEvent(eventKey.onGameStart, (callback:any) => {
  console.log('游戏开始 c端请求服务器。。。');
  callback && callback(60);
  gameChanceNumData.value -= 1;
});
registerEvent(eventKey.onGameOver, (score:any, callback:any) => {
  // 0介绍 1成功 没有游戏机会 2成功还有机会  3失败没有机会 4失败还有机会
  ifShowPop.value = true;
  gamePopDiv.value = 'gamePopDiv1';
  if (gameScore.value <= score && gameChanceNumData.value <= 0) {
    popStatus.value = 1;
    popTitle.value = '挑战成功';
    popBtn1Text.value = '获取更多游戏机会';
    popBtn2Text.value = '点击抽奖';
  } else if (gameScore.value <= score && gameChanceNumData.value > 0) {
    popStatus.value = 2;
    popTitle.value = '挑战成功';
    popBtn1Text.value = '再次挑战';
    popBtn2Text.value = '点击抽奖';
  } else if (gameScore.value > score && gameChanceNumData.value <= 0) {
    popStatus.value = 3;
    popTitle.value = '挑战失败';
    popBtn1Text.value = '获取更多机会';
  } else if (gameScore.value > score && gameChanceNumData.value > 0) {
    popStatus.value = 4;
    popTitle.value = '挑战失败';
    popBtn1Text.value = '获取更多游戏机会';
    popBtn2Text.value = '再次挑战';
  }
  currentScore.value = score;
  // 调用游戏结束接口
  gameEnd();
  console.log(`游戏结束:${score} c端上报分数。。。`);
  callback && callback();
  // dispatchEvent(eventKey.onGameReplay, (callback) => {callback && callback();})
});
</script>
<style scoped lang="scss">
.gameAll{
  width:100%;
}
.gameDiv{
  width: 7.5rem !important;
  height: 100vh !important;
}
.bizDiv {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;

  .gamePopDiv {
    width: 5.49rem;
    height: 6.08rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/220320/30/17391/3069/63805a96Ee25b1f1a/ce72eaf8391c9e29.png);
    background-repeat: no-repeat;
    background-size: 100%;
    margin: auto auto;
    display: flex;
    text-align:center;
    flex-direction:column;
    align-items:center;
  }

  .gamePopDiv1 {
    width: 5.49rem;
    height: 6.08rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/100070/28/32457/38273/639c0908Ea0ecbb95/8cb3e1fd5830985f.png);
    background-repeat: no-repeat;
    background-size: 100%;
    margin: auto auto;
    display: flex;
    text-align:center;
    flex-direction:column;
    align-items:center;
  }

  .popTitleClass {
    font-size: 0.36rem;
    color: white;
    margin-top: 0.3rem;
  }
  .popTitleClass1 {
    font-size: 0.36rem;
    color: white;
    margin-top: 0.1rem;
  }
  .introduce {
    font-size: 0.24rem;
    margin-top: 0.5rem;
    margin-bottom: 0.3rem;
    color: black;
    .contectImgUrl {
      width: 2rem;
      margin:auto;
    }
    .clickIcon {
      width: 0.68rem;
      height: 0.98rem;
      margin: auto auto;
      margin-top: 0.3rem;
      margin-bottom: 0.25rem;
    }
  }
  .gameResult {
    .trophyImg {
      width: 1.32rem;
      height: 1.39rem;
      margin: auto auto;
      margin-top: 0.4rem;
      margin-bottom: 0.2rem;
    }

    .resultText1 {
      font-size: 0.24rem;
      color: black;
      font-weight: bold;
    }

    .resultText2 {
      font-size: 0.6rem;
      color: black;
      font-weight: bold;
    }

    .marB20 {
      margin-bottom: 0.2rem;
    }

    .resultText3 {
      margin-top: 0.05rem;
      margin-bottom: 0.2rem;
      font-size: 0.24rem;

      span {
        color: #f40a51;
      }
    }

  }
  .popBtnDiv {
    width: 100%;
    display: flex;
    flex-direction: row;
    font-size: 0.24rem;
    color: black;
    justify-content:space-around;
  }

  .popBtn1 {
    font-size: 0.24rem;
    letter-spacing: 0.04rem;
    //margin-left: 0.17rem;
    width: 2.17rem;
    height: 0.74rem;
    line-height: 0.74rem;
    //border-radius: 0.5rem;
    //background:linear-gradient(to right, #f229d, #ff5d1d);
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/168653/27/33349/816/63807dcaE43a18054/1ac066e681d61e09.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #000000;
  }

  .popBtn2 {
    font-size: 0.24rem;
    letter-spacing: 0.04rem;
    width: 2.17rem;
    height: 0.74rem;
    line-height: 0.74rem;
    //border-radius: 0.5rem;
    //background:#ff9900;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/168653/27/33349/816/63807dcaE43a18054/1ac066e681d61e09.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #000000;
  }
  .popBtn3 {
    font-size: 0.24rem;
    letter-spacing: 0.04rem;
    width: 2.47rem;
    height: 0.74rem;
    line-height: 0.74rem;
    //border-radius: 0.5rem;
    //background: linear-gradient(to right, #f2290d, #ff5d1d);
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/168653/27/33349/816/63807dcaE43a18054/1ac066e681d61e09.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #000000;
  }
  .popBtn4 {
    font-size: 0.24rem;
    letter-spacing: 0.04rem;
    width: 2.48rem;
    height: 0.74rem;
    line-height: 0.74rem;
    //border-radius: 0.5rem;
    //background: linear-gradient(to right, #f2290d, #ff5d1d);
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/168653/27/33349/816/63807dcaE43a18054/1ac066e681d61e09.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #000000;
  }
  //.popBtn1Small {
  //  margin-left: 1.01rem;
  //  width: 1.81rem;
  //  height: 0.72rem;
  //  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/168653/27/33349/816/63807dcaE43a18054/1ac066e681d61e09.png);
  //  background-repeat: no-repeat;
  //  background-size: 100% 100%;
  //}
  //
  //.popBtn2Small {
  //  margin-left: 0.09rem;
  //  width: 1.81rem;
  //  height: 0.72rem;
  //  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/91937/39/31845/733/63807dcaEb97aeafa/19fb8a9aab027966.png);
  //  background-repeat: no-repeat;
  //  background-size: 100% 100%;
  //}

}

</style>
