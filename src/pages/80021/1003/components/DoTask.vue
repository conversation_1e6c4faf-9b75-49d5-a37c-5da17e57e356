<template>
  <div class="task-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>获得更多抽奖机会</div>
      <div class="rightLineDiv"></div>
      <img alt="" data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="times">
      今天还有 <span>{{ times }}</span> 次抽奖机会
    </div>
    <div class="content">
      <div v-for="(item, index) in tasks" :key="item.id" class="task">
        <img :src="taskInfo[item.taskType].icon" alt="" class="icon" />
        <div class="info">
          <div class="name">{{ taskInfo[item.taskType].label }}</div>
          <div class="rule">{{ taskRule[item.taskType](item) }}</div>
        </div>
        <div class="button" v-if="item.taskFinishCount < item.limit || item.taskType === 8" @click="doTask(index)">{{ taskInfo[item.taskType].button }}</div>
        <div class="button button-dis" v-else>{{ taskInfo[item.taskType].buttonDIs }}</div>
      </div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSku" position="bottom">
    <ShowSku v-if="showSku" :detail="tasks[taskDetailIndex]" @close="showSku = false" @refreshTask="refreshTask" @openShowGoShop="openShowGoShop"></ShowSku>
  </VanPopup>
</template>

<script lang="ts" setup>
import { PropType, inject, ref } from 'vue';
import { showToast, showLoadingToast, closeToast } from 'vant';
import ShowSku from './ShowSku.vue';
import { callShare } from '@/utils/platforms/share';
import { httpRequest } from '@/utils/service';
import { Task } from 'pages/80021/1001/ts/type';
import constant from '@/utils/constant';

const isPreview = (inject('isPreview') as boolean) ?? false;

const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) as string);

const pros = defineProps({
  times: {
    type: Number,
    default: 0,
  },
  tasks: {
    type: Array as PropType<Task[]>,
    default: () => [],
    required: true,
  },
  shareImg: {
    type: String,
    default: '',
  },
  shareTitle: {
    type: String,
    default: '',
  },
  shopId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close', 'refreshTask', 'openShowGoShop']);

const openShowGoShop = () => {
  emits('openShowGoShop');
};
const close = () => {
  emits('close');
};

const refreshTask = () => {
  emits('refreshTask');
};

const showSku = ref(false);
const taskDetailIndex = ref(0);

// 上报任务完成
const reportTask = async (taskId: number, skuId = '') => {
  try {
    const apis = {
      9: '/80021/shareSku',
      10: '/80021/shareShop',
      12: '/80021/shareActivity',
    };
    const res = await httpRequest.post(apis[taskId], { skuId });
    if (res.code === 200) {
      refreshTask();
    } else {
      showToast(res.msg);
    }
  } catch (error: any) {
    console.error(error);
  }
};

const doTask = (index: number) => {
  if (isPreview) return;
  if (pros.tasks[index].taskType <= 9) {
    taskDetailIndex.value = index;
    showSku.value = true;
  } else if (pros.tasks[index].taskType === 10) {
    callShare({
      title: shareConfig.shareTitle,
      shareUrl: `https://shop.m.jd.com/?shopId=${pros.shopId}`,
      afterShare: () => {
        reportTask(pros.tasks[index].taskType);
      },
    });
  } else if (pros.tasks[index].taskType === 12) {
    callShare({
      title: shareConfig.shareTitle,
      content: shareConfig.shareTitle,
      shareUrl: window.location.href,
      imageUrl: shareConfig.shareImage,
      afterShare: () => {
        reportTask(pros.tasks[index].taskType);
      },
    });
  }
};

const taskInfo = {
  5: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/130074/15/38243/3438/64f69302Fd12b0cf0/35b5e9ff4af1b814.png',
    label: '关注商品',
    button: '去关注',
    buttonDIs: '已关注',
  },
  6: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/130074/15/38243/3438/64f69302Fd12b0cf0/35b5e9ff4af1b814.png',
    label: '预约商品',
    button: '去预约',
    buttonDIs: '已预约',
  },
  7: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/220268/34/34613/3439/64f69302F6be558d0/46a7306a053e6566.png',
    label: '加购商品',
    button: '去加购',
    buttonDIs: '已加购',
  },
  8: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/195708/20/37590/3718/64f69302F5e77bce5/89a743dce75d66af.png',
    label: '购买商品',
    button: '去购买',
    buttonDIs: '已购买',
  },
  9: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/194604/33/36971/3642/64f69302F3e1dd493/748871879c8de744.png',
    label: '分享商品',
    button: '去分享',
    buttonDIs: '已分享',
  },
  10: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/194604/33/36971/3642/64f69302F3e1dd493/748871879c8de744.png',
    label: '分享店铺',
    button: '去分享',
    buttonDIs: '已分享',
  },
  12: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/194604/33/36971/3642/64f69302F3e1dd493/748871879c8de744.png',
    label: '分享活动',
    button: '去分享',
    buttonDIs: '已分享',
  },
};

const taskRule = {
  5: (info: any) => {
    if (info.optWay === 1) {
      return `每关注${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`;
    }
    return `成功关注全部商品，可获得${info.lotteryCount}次抽奖机会`;
  },
  6: (info: any) => `每预约${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`,
  7: (info: any) => {
    if (info.optWay === 1) {
      return `每加购${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`;
    }
    return `成功加购全部商品，可获得${info.lotteryCount}次抽奖机会`;
  },
  8: (info: any) => `每成功下${info.perOperateCount}单，可获得${info.perLotteryCount}次抽奖机会`,
  9: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
  10: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
  12: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
};
</script>

<style scoped lang="scss">
.task-bk {
  background-color: #fefcf6;
  border-radius: 0.4rem 0.4rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/136328/12/40019/30712/64f6a3a7F41c5b828/0ce6a4721582222e.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.35rem;
    padding-bottom: 0.2rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.4rem;
      height: 0.04rem;
      background-color: #fff;
      margin-right: 0.2rem;
    }

    .rightLineDiv {
      width: 0.4rem;
      height: 0.04rem;
      background-color: #fff;
      margin-left: 0.2rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .times {
    text-align: center;
    color: #262626;
    font-size: 0.24rem;

    // span {
    //   color: rgb(242, 39, 12);
    // }
  }

  .content {
    height: 8.5rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;

    .task {
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/137540/10/39047/1696/64f6a538Febd3ec16/82c274602643ed31.png) no-repeat;
      background-size: 100%;
      margin-bottom: 0.1rem;
      padding: 0.2rem 0.3rem;
      display: flex;
      align-items: center;

      .icon {
        width: 0.83rem;
      }

      .info {
        flex: 1;
        padding: 0 0.39rem;
      }

      .name {
        font-size: 0.3rem;
        color: #dc84aa;
      }

      .rule {
        font-size: 0.24rem;
        color: #8c8c8c;
      }

      .button {
        width: 1.36rem;
        height: 0.46rem;
        background: #f56264;
        font-size: 0.2rem;
        color: rgb(255, 255, 255);
        border-radius: 0.23rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .button-dis {
        background: #bfbfbf;
      }
    }
  }
}
</style>
