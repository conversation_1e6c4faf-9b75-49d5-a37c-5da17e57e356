<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <img :src="prize.showImg" alt="" class="prize-img" />
    <div class="content">
      <p class="prize-name">{{ prize.prizeName }}</p>
      <div>
        <p class="p3" v-if="prize.prizeType === 5">仅中奖人可享受优惠,仅可使用一次</p>

        <p class="p3" v-if="prize.prizeType === 2">京豆已放到您的账户中 京东-我的-京豆 中查看</p>

        <p class="p3" v-if="prize.prizeType === 1">已发放到您的账户 京东-我的-我的钱包-优惠券 中查看</p>
        <p class="p3" v-if="prize.prizeType === 4">积分已发放到您的账户中 店铺会员页 中查看</p>
        <p class="p3" v-if="prize.prizeType === 6">红包已发放到您的账户中 京东-我的-我的钱包-红包 中查看</p>
        <p class="p3" v-if="prize.prizeType === 7">礼品卡需手动兑换，请根据 兑换指引 前往兑换</p>
        <p class="p3" v-if="prize.prizeType === 8">京东E卡已发放到您的账户中 京东-我的-我的钱包-礼品卡 中查看</p>
        <p class="p3" v-if="prize.prizeType === 3">实物奖品系统不能自动发放，请<span>填写邮寄地址</span></p>

        <p class="p3" v-if="prize.prizeType === 12">
          1:权益非自动发放,需首先填写权益领取信息 <br />
          2:如放弃领取权益,活动结束权益不予补发
        </p>
        <p class="p3" v-if="prize.prizeType === 9 || prize.prizeType === 10"></p>
      </div>
      <div class="btn-list">
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/185244/9/42650/7780/65f25970F5ebc37b3/776878f083205f4f.png" alt="" @click="shareAct" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/234021/32/13593/6783/65f25970F4d601ba1/74f80f69bad8fa12.png" alt="" v-if="prize.prizeType === 3" @click="saveAddress" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/227019/26/13731/6966/65f25970F9f77cca6/9ead5b41ccd41540.png" alt="" v-else-if="prize.prizeType === 12" @click="savePhone" />

        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/165015/13/29843/7150/65f25970F39549d82/d7bdc669c898988f.png" alt="" v-else-if="prize.prizeType === 5" @click="gotoSkuPage(prize.result)" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/236889/35/14782/7225/65f25970F73f606af/ac12dc4d55bc51b1.png" alt="" v-else-if="prize.prizeType === 7" @click="showCardNum" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/192836/6/42758/7046/65f25970F3c8ee1b7/a47eb2fbaabfe4c9.png" alt="" v-else-if="prize.prizeType === 9 || prize.prizeType === 10" @click="exchangePlusOrAiqiyi" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/225624/28/5370/7165/65f25970Fb65bcb43/7ba1302641aa1864.png" alt="" v-else @click="gotoShopPage" />
      </div>
    </div>
    <div class="close" @click="close"></div>
  </div>
  <div class="thanks-join" v-else>
    <div class="close" @click="close"></div>
    <div class="btn" @click="gotoShopPage">进店逛逛</div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { gotoSkuPage, exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  showImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone', 'openShowGoShop']);

const close = () => {
  emits('close');
};
const gotoShopPage = () => {
  emits('openShowGoShop');
};
const saveAddress = () => {
  emits('saveAddress', props.prize.result.result, props.prize.activityPrizeId);
};

const showCardNum = () => {
  emits('showCardNum', { ...props.prize.result, showImg: props.prize.showImg, prizeName: props.prize.prizeName });
};

const savePhone = () => {
  emits('savePhone', props.prize.userPrizeId, props.prize.result.result.planDesc);
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bk {
  height: 7.65rem;
  width: 6.21rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/195009/8/42789/18836/65f25971Fdd13fcd4/ceb176d9649727d1.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 2.3rem;
  .close {
    height: 0.25rem;
    width: 0.25rem;
    position: absolute;
    top: 0.4rem;
    right: 0.38rem;
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/228300/32/13668/389/65f25a48F488935b6/fee5d10c55951e5b.png");
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .prize-img {
    height: 1.8rem;
    margin: 0rem auto 0 auto;
  }
  .content {
    padding: 0 0.5rem;
    .p1 {
      display: block;
      color: #262626;
      font-size: 0.24rem;
      font-weight: 500;
    }
    .prize-name {
      font-size: 0.4rem;
      font-weight: bold;
      margin: 0.2rem 0 0;
      text-align: center;
      color: #000;
    }
    .p3 {
      font-size: 0.22rem;
      color: #000;
      text-align: center;
      height: 1.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      padding:0 0.2rem;
      max-height:1rem;
      overflow-y:scroll;
      span {
        color: #f2270c;
      }
    }
    .btn-list {
      display: flex;
      justify-content: space-around;
      //padding: 0 0.5rem;
      margin-top: 0.2rem;
      .btn {
        width: 2.49rem;
        height:0.76rem;
      }
      .btn-left {
        background: linear-gradient(to right, #f2270c, #ff6320);
      }
      .btn-right {
        background: #ff9900;
      }
    }
  }
}
.thanks-join {
  width: 6.21rem;
  height: 7.04rem;
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/180366/40/42644/28826/65f25d6bF1f6accfe/bff8a8b36f6b9562.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 5.4rem;
  .btn {
    display: block;
    margin: 0 auto;
    width: 2.49rem;
    height: 0.76rem;
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/225624/28/5370/7165/65f25970Fb65bcb43/7ba1302641aa1864.png);
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .close {
    height: 0.25rem;
    width: 0.25rem;
    position: absolute;
    top: 0.4rem;
    right: 0.4rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}

</style>
