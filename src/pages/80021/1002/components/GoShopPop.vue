<template>
  <CommonDrawer title="进店逛逛" @close="emits('close')">
    <div class="h-[40vh]  overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="no-data">
        <div v-for="item in shopInfo" class="btn" :key="item.shopId" v-click-track="{ c: JSON.stringify({ code: 'lldp', value: item.shopId }) }" @click="gotoShopPage(item.shopId)">{{ item.shopName }}</div>
      </div>
    </div>
  </CommonDrawer>
</template>

<script lang="ts" setup>
import { gotoShopPage } from '@/utils/platforms/jump';
import { ref, defineEmits, defineProps, inject, watch } from 'vue';

const emits = defineEmits(['close']);

const shopInfo = ref<any>([
  {
    shopName: '波司登京东自营旗舰店  ',
    shopId: 1000384206,
  },
  {
    shopName: '波司登官方旗舰店',
    shopId: 44892,
  },
  {
    shopName: '波司登户外官方旗舰店',
    shopId: 12622397,
  },
  {
    shopName: '波司登奥特莱斯旗舰店',
    shopId: 12558108,
  },
  {
    shopName: '波司登服饰官方旗舰店 ',
    shopId: 82047,
  },
]);
</script>

<style scoped lang="scss">
.title {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
}
.no-data {
  text-align: center;
  // padding-top: 3rem;
  font-size: 0.3rem;
  .btn {
    width: 3.5rem;
    height: 0.7rem;
    line-height: 0.7rem;
    text-align: center;
    color: white;
    font-size: 0.3rem;
    border-radius: 0.1rem;
    background-color: #ff9900;
    margin: 0.3rem auto;
  }
}
.sku-list-box {
  position: relative;
  padding-bottom: 1rem;
  .load-more {
    width: 2rem;
    height: 0.4rem;
    line-height: 0.4rem;
    text-align: center;
    background: linear-gradient(to right, #ff1f53, #ffd102);
    border-radius: 0.2rem;
    color: white;
    position: absolute;
    bottom: 0.24rem;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 600;
  }
}
</style>
