<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <img :src="prize.showImg" alt="" class="prize-img" />
    <div class="content">
      <p class="prize-name">{{ prize.prizeName }}</p>
      <div>
        <p class="p3" v-if="prize.prizeType === 5">仅中奖人可享受优惠,仅可使用一次</p>

        <p class="p3" v-if="prize.prizeType === 2">京豆已放到您的账户中 京东-我的-京豆 中查看</p>

        <p class="p3" v-if="prize.prizeType === 1">已发放到您的账户 京东-我的-我的钱包-优惠券 中查看</p>
        <p class="p3" v-if="prize.prizeType === 4">积分已发放到您的账户中 店铺会员页 中查看</p>
        <p class="p3" v-if="prize.prizeType === 6">红包已发放到您的账户中 京东-我的-我的钱包-红包 中查看</p>
        <p class="p3" v-if="prize.prizeType === 7">礼品卡需手动兑换，请根据 兑换指引 前往兑换</p>
        <p class="p3" v-if="prize.prizeType === 8">京东E卡已发放到您的账户中 京东-我的-我的钱包-礼品卡 中查看</p>
        <p class="p3" v-if="prize.prizeType === 3">实物奖品系统不能自动发放，请<span>填写邮寄地址</span></p>

        <p class="p3" v-if="prize.prizeType === 12">
          1:权益非自动发放,需首先填写权益领取信息 <br />
          2:如放弃领取权益,活动结束权益不予补发
        </p>
        <p class="p3" v-if="prize.prizeType === 9 || prize.prizeType === 10"></p>
      </div>
      <div class="btn-list">
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/93657/21/38582/12362/64e31a33F7dff3e2a/8d702e160d03fafd.png" alt="" @click="shareAct" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/85346/6/39404/12049/64e31a34F6e527c85/c3d933472ae34213.png" alt="" v-if="prize.prizeType === 3" @click="saveAddress" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/148507/6/39686/9129/64f59395Fec885e5f/59cbd373b8496ba2.png" alt="" v-else-if="prize.prizeType === 5" @click="gotoSkuPage(prize.result)" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/218625/14/34787/35610/64f59395F98647ddf/700c7adc8b3be69b.png" alt="" v-else-if="prize.prizeType === 7" @click="showCardNum" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/185602/21/37340/35530/64f59395Fe7fbe206/a7752e65e2650693.png" alt="" v-else-if="prize.prizeType === 9 || prize.prizeType === 10" @click="exchangePlusOrAiqiyi" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/105662/36/43650/35339/64f59395F2aff554c/e6ca48e4ecaafbf8.png" alt="" v-else-if="prize.prizeType === 12" @click="savePhone" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/161939/33/39722/12162/64e31a33F1858b61a/b90ad98bd65af044.png" alt="" v-else @click="gotoShopPage" />
      </div>
    </div>
    <div class="close" @click="close"></div>
  </div>
  <div class="thanks-join" v-else>
    <div class="close" @click="close"></div>
    <div class="btn" @click="gotoShopPage">进店逛逛</div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import { isPreview } from '@/utils';
import constant from '@/utils/constant';
import { gotoSkuPage, exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject } from 'vue';
import { showToast } from 'vant';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  showImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone', 'openShowGoShop']);

const gotoShopPage = () => {
  if (isPreview) {
    showToast('活动预览，仅供查看');
    return;
  }
  emits('openShowGoShop');
};
const close = () => {
  emits('close');
};

const saveAddress = () => {
  emits('saveAddress', props.prize.result.result, props.prize.activityPrizeId);
};

const showCardNum = () => {
  emits('showCardNum', { ...props.prize.result, showImg: props.prize.showImg, prizeName: props.prize.prizeName });
};

const savePhone = () => {
  emits('savePhone', props.prize.userPrizeId, props.prize.result.result.planDesc);
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bk {
  height: 8rem;
  width: 6rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/221077/30/27192/110502/64e31a52Fc249d4ed/1081187e18c8b560.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 1.67rem;
  .prize-img {
    height: 2.2rem;
    width: 2.2rem;
    border-radius: 50%;
    margin: 0 auto;
  }
  .content {
    .p1 {
      display: block;
      color: #262626;
      font-size: 0.24rem;
      font-weight: 500;
    }
    .prize-name {
      font-size: 0.4rem;
      font-weight: bold;
      margin: 0.27rem 0 0.15rem;
      text-align: center;
      color: #000;
    }
    .p3 {
      font-size: 0.24rem;
      color: #000;
      display: block;
      text-align: center;
    }
    .btn-list {
      display: flex;
      justify-content: space-between;
      padding: 0 0.3rem;
      margin-top: 0.4rem;
      .btn {
        width: 2.6rem;
      }
      .btn-left {
        background: linear-gradient(to right, #f2270c, #ff6320);
      }
      .btn-right {
        background: #ff9900;
      }
    }
  }
}
.thanks-join {
  height: 8rem;
  width: 6rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/89323/36/41909/117503/64e5ce6cF15303aa9/a6551f383b02ca11.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 5.4rem;
  .btn {
    display: block;
    margin: 0 auto;
    width: 4rem;
    height: 0.76rem;
  }
}
.close {
  height: 0.7rem;
  width: 0.6rem;
  position: absolute;
  left: 2.7rem;
  bottom: 0.15rem;
}
</style>
