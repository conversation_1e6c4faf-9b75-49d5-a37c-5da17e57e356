import{g as e,y as t,b as n,Q as r,R as o,U as s}from"./index-eb2ba678.js";const i=e=>{var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(a(t.getDate())+" ")},a=e=>(e=e.toString())[1]?e:"0"+e,c=e=>({channelId:"TM"==e?"aptamil-baby-tmall-h5":"aptamil-baby-jd-h5",channelSecret:"TM"==e?"CKBkEai":"YCcwhW7"}),u=e=>{var t=new RegExp("(^|&)"+e+"=([^&]*)(&|$)");let n=window.location.href.split("#")[0].split("?")[1];if(n){var r=n.substring(0).match(t);return null!==r?decodeURIComponent(r[2]):null}return null},l={LZ_JD_ENCRYPT_PIN:"LZ_JD_ENCRYPT_PIN",LZ_JD_USER_NAME:"LZ_JD_USER_NAME",LZ_JD_USER_AVATAR:"LZ_JD_USER_AVATAR",LZ_PIN_TOKEN:"LZ_PIN_TOKEN",LZ_WHITE_USER:"LZ_WHITE_USER",LZ_SHOP_ID:"LZ_SHOP_ID",LZ_ACTIVITY_ID:"LZ_ACTIVITY_ID",LZ_ACTIVITY_TYPE:"LZ_ACTIVITY_TYPE",LZ_TEMPLATE_CODE:"LZ_TEMPLATE_CODE",LZ_JD_ACTIVITY_ID:"LZ_JD_ACTIVITY_ID",LZ_SHARE_TYPE:"LZ_SHARE_TYPE",LZ_SHARE_CONFIG:"LZ_SHARE_CONFIG",LZ_SERVER_TIME:"LZ_SERVER_TIME",LZ_LOST_PAGE_URL:"https://3.cn/103gf-qfm",LZ_JD_TOKEN:"LZ_JD_TOKEN",LZ_SUCCESS_CODE:200},f=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n};function d(e,t){return function(){return e.apply(t,arguments)}}const{toString:p}=Object.prototype,{getPrototypeOf:h}=Object,m=(g=Object.create(null),e=>{const t=p.call(e);return g[t]||(g[t]=t.slice(8,-1).toLowerCase())});var g;const b=e=>(e=e.toLowerCase(),t=>m(t)===e),y=e=>t=>typeof t===e,{isArray:E}=Array,w=y("undefined");const O=b("ArrayBuffer");const S=y("string"),_=y("function"),T=y("number"),R=e=>null!==e&&"object"==typeof e,A=e=>{if("object"!==m(e))return!1;const t=h(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)},P=b("Date"),C=b("File"),N=b("Blob"),v=b("FileList"),L=b("URLSearchParams");function j(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,o;if("object"!=typeof e&&(e=[e]),E(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let i;for(r=0;r<s;r++)i=o[r],t.call(null,e[i],i,e)}}function x(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const U="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,D=e=>!w(e)&&e!==U;const k=(F="undefined"!=typeof Uint8Array&&h(Uint8Array),e=>F&&e instanceof F);var F;const B=b("HTMLFormElement"),I=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),M=b("RegExp"),q=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};j(n,((n,o)=>{let s;!1!==(s=t(n,o,e))&&(r[o]=s||n)})),Object.defineProperties(e,r)},H="abcdefghijklmnopqrstuvwxyz",Z="0123456789",J={DIGIT:Z,ALPHA:H,ALPHA_DIGIT:H+H.toUpperCase()+Z};const V=b("AsyncFunction"),z={isArray:E,isArrayBuffer:O,isBuffer:function(e){return null!==e&&!w(e)&&null!==e.constructor&&!w(e.constructor)&&_(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||_(e.append)&&("formdata"===(t=m(e))||"object"===t&&_(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&O(e.buffer),t},isString:S,isNumber:T,isBoolean:e=>!0===e||!1===e,isObject:R,isPlainObject:A,isUndefined:w,isDate:P,isFile:C,isBlob:N,isRegExp:M,isFunction:_,isStream:e=>R(e)&&_(e.pipe),isURLSearchParams:L,isTypedArray:k,isFileList:v,forEach:j,merge:function e(){const{caseless:t}=D(this)&&this||{},n={},r=(r,o)=>{const s=t&&x(n,o)||o;A(n[s])&&A(r)?n[s]=e(n[s],r):A(r)?n[s]=e({},r):E(r)?n[s]=r.slice():n[s]=r};for(let o=0,s=arguments.length;o<s;o++)arguments[o]&&j(arguments[o],r);return n},extend:(e,t,n,{allOwnKeys:r}={})=>(j(t,((t,r)=>{n&&_(t)?e[r]=d(t,n):e[r]=t}),{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,s,i;const a={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],r&&!r(i,e,t)||a[i]||(t[i]=e[i],a[i]=!0);e=!1!==n&&h(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:m,kindOfTest:b,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(E(e))return e;let t=e.length;if(!T(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:B,hasOwnProperty:I,hasOwnProp:I,reduceDescriptors:q,freezeMethods:e=>{q(e,((t,n)=>{if(_(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];_(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return E(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>(e=+e,Number.isFinite(e)?e:t),findKey:x,global:U,isContextDefined:D,ALPHABET:J,generateString:(e=16,t=J.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n},isSpecCompliantForm:function(e){return!!(e&&_(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(R(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=E(e)?[]:{};return j(e,((e,t)=>{const s=n(e,r+1);!w(s)&&(o[t]=s)})),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:V,isThenable:e=>e&&(R(e)||_(e))&&_(e.then)&&_(e.catch)};function K(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o)}z.inherits(K,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:z.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const W=K.prototype,Y={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{Y[e]={value:e}})),Object.defineProperties(K,Y),Object.defineProperty(W,"isAxiosError",{value:!0}),K.from=(e,t,n,r,o,s)=>{const i=Object.create(W);return z.toFlatObject(e,i,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),K.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};function G(e){return z.isPlainObject(e)||z.isArray(e)}function $(e){return z.endsWith(e,"[]")?e.slice(0,-2):e}function X(e,t,n){return e?e.concat(t).map((function(e,t){return e=$(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const Q=z.toFlatObject(z,{},null,(function(e){return/^is[A-Z]/.test(e)}));function ee(e,t,n){if(!z.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=z.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!z.isUndefined(t[e])}))).metaTokens,o=n.visitor||u,s=n.dots,i=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&z.isSpecCompliantForm(t);if(!z.isFunction(o))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(z.isDate(e))return e.toISOString();if(!a&&z.isBlob(e))throw new K("Blob is not supported. Use a Buffer instead.");return z.isArrayBuffer(e)||z.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,o){let a=e;if(e&&!o&&"object"==typeof e)if(z.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(z.isArray(e)&&function(e){return z.isArray(e)&&!e.some(G)}(e)||(z.isFileList(e)||z.endsWith(n,"[]"))&&(a=z.toArray(e)))return n=$(n),a.forEach((function(e,r){!z.isUndefined(e)&&null!==e&&t.append(!0===i?X([n],r,s):null===i?n:n+"[]",c(e))})),!1;return!!G(e)||(t.append(X(o,n,s),c(e)),!1)}const l=[],f=Object.assign(Q,{defaultVisitor:u,convertValue:c,isVisitable:G});if(!z.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!z.isUndefined(n)){if(-1!==l.indexOf(n))throw Error("Circular reference detected in "+r.join("."));l.push(n),z.forEach(n,(function(n,s){!0===(!(z.isUndefined(n)||null===n)&&o.call(t,n,z.isString(s)?s.trim():s,r,f))&&e(n,r?r.concat(s):[s])})),l.pop()}}(e),t}function te(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function ne(e,t){this._pairs=[],e&&ee(e,this,t)}const re=ne.prototype;function oe(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function se(e,t,n){if(!t)return e;const r=n&&n.encode||oe,o=n&&n.serialize;let s;if(s=o?o(t,n):z.isURLSearchParams(t)?t.toString():new ne(t,n).toString(r),s){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}re.append=function(e,t){this._pairs.push([e,t])},re.toString=function(e){const t=e?function(t){return e.call(this,t,te)}:te;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const ie=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){z.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},ae={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ce={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:ne,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},ue="undefined"!=typeof window&&"undefined"!=typeof document,le=(fe="undefined"!=typeof navigator&&navigator.product,ue&&["ReactNative","NativeScript","NS"].indexOf(fe)<0);var fe;const de="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,pe={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ue,hasStandardBrowserEnv:le,hasStandardBrowserWebWorkerEnv:de},Symbol.toStringTag,{value:"Module"})),...ce};function he(e){function t(e,n,r,o){let s=e[o++];if("__proto__"===s)return!0;const i=Number.isFinite(+s),a=o>=e.length;if(s=!s&&z.isArray(r)?r.length:s,a)return z.hasOwnProp(r,s)?r[s]=[r[s],n]:r[s]=n,!i;r[s]&&z.isObject(r[s])||(r[s]=[]);return t(e,n,r[s],o)&&z.isArray(r[s])&&(r[s]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}(r[s])),!i}if(z.isFormData(e)&&z.isFunction(e.entries)){const n={};return z.forEachEntry(e,((e,r)=>{t(function(e){return z.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null}const me={transitional:ae,adapter:["xhr","http"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=z.isObject(e);o&&z.isHTMLForm(e)&&(e=new FormData(e));if(z.isFormData(e))return r?JSON.stringify(he(e)):e;if(z.isArrayBuffer(e)||z.isBuffer(e)||z.isStream(e)||z.isFile(e)||z.isBlob(e))return e;if(z.isArrayBufferView(e))return e.buffer;if(z.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let s;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return ee(e,new pe.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return pe.isNode&&z.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((s=z.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return ee(s?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if(z.isString(e))try{return(t||JSON.parse)(e),z.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||me.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(e&&z.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(o){if(n){if("SyntaxError"===o.name)throw K.from(o,K.ERR_BAD_RESPONSE,this,null,this.response);throw o}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:pe.classes.FormData,Blob:pe.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};z.forEach(["delete","get","head","post","put","patch"],(e=>{me.headers[e]={}}));const ge=me,be=z.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ye=Symbol("internals");function Ee(e){return e&&String(e).trim().toLowerCase()}function we(e){return!1===e||null==e?e:z.isArray(e)?e.map(we):String(e)}function Oe(e,t,n,r,o){return z.isFunction(r)?r.call(this,t,n):(o&&(t=n),z.isString(t)?z.isString(r)?-1!==t.indexOf(r):z.isRegExp(r)?r.test(t):void 0:void 0)}class Se{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=Ee(t);if(!o)throw new Error("header name must be a non-empty string");const s=z.findKey(r,o);(!s||void 0===r[s]||!0===n||void 0===n&&!1!==r[s])&&(r[s||t]=we(e))}const s=(e,t)=>z.forEach(e,((e,n)=>o(e,n,t)));return z.isPlainObject(e)||e instanceof this.constructor?s(e,t):z.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim())?s((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach((function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&be[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t):null!=e&&o(t,e,n),this}get(e,t){if(e=Ee(e)){const n=z.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(z.isFunction(t))return t.call(this,e,n);if(z.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Ee(e)){const n=z.findKey(this,e);return!(!n||void 0===this[n]||t&&!Oe(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=Ee(e)){const o=z.findKey(n,e);!o||t&&!Oe(0,n[o],o,t)||(delete n[o],r=!0)}}return z.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!Oe(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return z.forEach(this,((r,o)=>{const s=z.findKey(n,o);if(s)return t[s]=we(r),void delete t[o];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(o):String(o).trim();i!==o&&delete t[o],t[i]=we(r),n[i]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return z.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&z.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[ye]=this[ye]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Ee(e);t[r]||(!function(e,t){const n=z.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})}))}(n,e),t[r]=!0)}return z.isArray(e)?e.forEach(r):r(e),this}}Se.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),z.reduceDescriptors(Se.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),z.freezeMethods(Se);const _e=Se;function Te(e,t){const n=this||ge,r=t||n,o=_e.from(r.headers);let s=r.data;return z.forEach(e,(function(e){s=e.call(n,s,o.normalize(),t?t.status:void 0)})),o.normalize(),s}function Re(e){return!(!e||!e.__CANCEL__)}function Ae(e,t,n){K.call(this,null==e?"canceled":e,K.ERR_CANCELED,t,n),this.name="CanceledError"}z.inherits(Ae,K,{__CANCEL__:!0});const Pe=pe.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];z.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),z.isString(r)&&i.push("path="+r),z.isString(o)&&i.push("domain="+o),!0===s&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Ce(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Ne=pe.hasStandardBrowserEnv?function(){const e=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a");let n;function r(n){let r=n;return e&&(t.setAttribute("href",r),r=t.href),t.setAttribute("href",r),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:"/"===t.pathname.charAt(0)?t.pathname:"/"+t.pathname}}return n=r(window.location.href),function(e){const t=z.isString(e)?r(e):e;return t.protocol===n.protocol&&t.host===n.host}}():function(){return!0};function ve(e,t){let n=0;const r=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,s=0,i=0;return t=void 0!==t?t:1e3,function(a){const c=Date.now(),u=r[i];o||(o=c),n[s]=a,r[s]=c;let l=i,f=0;for(;l!==s;)f+=n[l++],l%=e;if(s=(s+1)%e,s===i&&(i=(i+1)%e),c-o<t)return;const d=u&&c-u;return d?Math.round(1e3*f/d):void 0}}(50,250);return o=>{const s=o.loaded,i=o.lengthComputable?o.total:void 0,a=s-n,c=r(a);n=s;const u={loaded:s,total:i,progress:i?s/i:void 0,bytes:a,rate:c||void 0,estimated:c&&i&&s<=i?(i-s)/c:void 0,event:o};u[t?"download":"upload"]=!0,e(u)}}const Le={http:null,xhr:"undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){let r=e.data;const o=_e.from(e.headers).normalize();let s,i,{responseType:a,withXSRFToken:c}=e;function u(){e.cancelToken&&e.cancelToken.unsubscribe(s),e.signal&&e.signal.removeEventListener("abort",s)}if(z.isFormData(r))if(pe.hasStandardBrowserEnv||pe.hasStandardBrowserWebWorkerEnv)o.setContentType(!1);else if(!1!==(i=o.getContentType())){const[e,...t]=i?i.split(";").map((e=>e.trim())).filter(Boolean):[];o.setContentType([e||"multipart/form-data",...t].join("; "))}let l=new XMLHttpRequest;if(e.auth){const t=e.auth.username||"",n=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";o.set("Authorization","Basic "+btoa(t+":"+n))}const f=Ce(e.baseURL,e.url);function d(){if(!l)return;const r=_e.from("getAllResponseHeaders"in l&&l.getAllResponseHeaders());!function(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new K("Request failed with status code "+n.status,[K.ERR_BAD_REQUEST,K.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}((function(e){t(e),u()}),(function(e){n(e),u()}),{data:a&&"text"!==a&&"json"!==a?l.response:l.responseText,status:l.status,statusText:l.statusText,headers:r,config:e,request:l}),l=null}if(l.open(e.method.toUpperCase(),se(f,e.params,e.paramsSerializer),!0),l.timeout=e.timeout,"onloadend"in l?l.onloadend=d:l.onreadystatechange=function(){l&&4===l.readyState&&(0!==l.status||l.responseURL&&0===l.responseURL.indexOf("file:"))&&setTimeout(d)},l.onabort=function(){l&&(n(new K("Request aborted",K.ECONNABORTED,e,l)),l=null)},l.onerror=function(){n(new K("Network Error",K.ERR_NETWORK,e,l)),l=null},l.ontimeout=function(){let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const r=e.transitional||ae;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(new K(t,r.clarifyTimeoutError?K.ETIMEDOUT:K.ECONNABORTED,e,l)),l=null},pe.hasStandardBrowserEnv&&(c&&z.isFunction(c)&&(c=c(e)),c||!1!==c&&Ne(f))){const t=e.xsrfHeaderName&&e.xsrfCookieName&&Pe.read(e.xsrfCookieName);t&&o.set(e.xsrfHeaderName,t)}void 0===r&&o.setContentType(null),"setRequestHeader"in l&&z.forEach(o.toJSON(),(function(e,t){l.setRequestHeader(t,e)})),z.isUndefined(e.withCredentials)||(l.withCredentials=!!e.withCredentials),a&&"json"!==a&&(l.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&l.addEventListener("progress",ve(e.onDownloadProgress,!0)),"function"==typeof e.onUploadProgress&&l.upload&&l.upload.addEventListener("progress",ve(e.onUploadProgress)),(e.cancelToken||e.signal)&&(s=t=>{l&&(n(!t||t.type?new Ae(null,e,l):t),l.abort(),l=null)},e.cancelToken&&e.cancelToken.subscribe(s),e.signal&&(e.signal.aborted?s():e.signal.addEventListener("abort",s)));const p=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(f);p&&-1===pe.protocols.indexOf(p)?n(new K("Unsupported protocol "+p+":",K.ERR_BAD_REQUEST,e)):l.send(r||null)}))}};z.forEach(Le,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}}));const je=e=>`- ${e}`,xe=e=>z.isFunction(e)||null===e||!1===e,Ue=e=>{e=z.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){let t;if(n=e[s],r=n,!xe(n)&&(r=Le[(t=String(n)).toLowerCase()],void 0===r))throw new K(`Unknown adapter '${t}'`);if(r)break;o[t||"#"+s]=r}if(!r){const e=Object.entries(o).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));throw new K("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(je).join("\n"):" "+je(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function De(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ae(null,e)}function ke(e){De(e),e.headers=_e.from(e.headers),e.data=Te.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Ue(e.adapter||ge.adapter)(e).then((function(t){return De(e),t.data=Te.call(e,e.transformResponse,t),t.headers=_e.from(t.headers),t}),(function(t){return Re(t)||(De(e),t&&t.response&&(t.response.data=Te.call(e,e.transformResponse,t.response),t.response.headers=_e.from(t.response.headers))),Promise.reject(t)}))}const Fe=e=>e instanceof _e?{...e}:e;function Be(e,t){t=t||{};const n={};function r(e,t,n){return z.isPlainObject(e)&&z.isPlainObject(t)?z.merge.call({caseless:n},e,t):z.isPlainObject(t)?z.merge({},t):z.isArray(t)?t.slice():t}function o(e,t,n){return z.isUndefined(t)?z.isUndefined(e)?void 0:r(void 0,e,n):r(e,t,n)}function s(e,t){if(!z.isUndefined(t))return r(void 0,t)}function i(e,t){return z.isUndefined(t)?z.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function a(n,o,s){return s in t?r(n,o):s in e?r(void 0,n):void 0}const c={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(e,t)=>o(Fe(e),Fe(t),!0)};return z.forEach(Object.keys(Object.assign({},e,t)),(function(r){const s=c[r]||o,i=s(e[r],t[r],r);z.isUndefined(i)&&s!==a||(n[r]=i)})),n}const Ie="1.6.8",Me={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Me[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const qe={};Me.transitional=function(e,t,n){function r(e,t){return"[Axios v1.6.8] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,o,s)=>{if(!1===e)throw new K(r(o," has been removed"+(t?" in "+t:"")),K.ERR_DEPRECATED);return t&&!qe[o]&&(qe[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,s)}};const He={assertOptions:function(e,t,n){if("object"!=typeof e)throw new K("options must be an object",K.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const t=e[s],n=void 0===t||i(t,s,e);if(!0!==n)throw new K("option "+s+" must be "+n,K.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new K("Unknown option "+s,K.ERR_BAD_OPTION)}},validators:Me},Ze=He.validators;class Je{constructor(e){this.defaults=e,this.interceptors={request:new ie,response:new ie}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e;Error.captureStackTrace?Error.captureStackTrace(e={}):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}throw n}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=Be(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&He.assertOptions(n,{silentJSONParsing:Ze.transitional(Ze.boolean),forcedJSONParsing:Ze.transitional(Ze.boolean),clarifyTimeoutError:Ze.transitional(Ze.boolean)},!1),null!=r&&(z.isFunction(r)?t.paramsSerializer={serialize:r}:He.assertOptions(r,{encode:Ze.function,serialize:Ze.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=o&&z.merge(o.common,o[t.method]);o&&z.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete o[e]})),t.headers=_e.concat(s,o);const i=[];let a=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,i.unshift(e.fulfilled,e.rejected))}));const c=[];let u;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let l,f=0;if(!a){const e=[ke.bind(this),void 0];for(e.unshift.apply(e,i),e.push.apply(e,c),l=e.length,u=Promise.resolve(t);f<l;)u=u.then(e[f++],e[f++]);return u}l=i.length;let d=t;for(f=0;f<l;){const e=i[f++],t=i[f++];try{d=e(d)}catch(p){t.call(this,p);break}}try{u=ke.call(this,d)}catch(p){return Promise.reject(p)}for(f=0,l=c.length;f<l;)u=u.then(c[f++],c[f++]);return u}getUri(e){return se(Ce((e=Be(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}z.forEach(["delete","get","head","options"],(function(e){Je.prototype[e]=function(t,n){return this.request(Be(n||{},{method:e,url:t,data:(n||{}).data}))}})),z.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,o){return this.request(Be(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Je.prototype[e]=t(),Je.prototype[e+"Form"]=t(!0)}));const Ve=Je;class ze{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,o){n.reason||(n.reason=new Ae(e,r,o),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}static source(){let e;return{token:new ze((function(t){e=t})),cancel:e}}}const Ke=ze;const We={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(We).forEach((([e,t])=>{We[t]=e}));const Ye=We;const Ge=function e(t){const n=new Ve(t),r=d(Ve.prototype.request,n);return z.extend(r,Ve.prototype,n,{allOwnKeys:!0}),z.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Be(t,n))},r}(ge);Ge.Axios=Ve,Ge.CanceledError=Ae,Ge.CancelToken=Ke,Ge.isCancel=Re,Ge.VERSION=Ie,Ge.toFormData=ee,Ge.AxiosError=K,Ge.Cancel=Ge.CanceledError,Ge.all=function(e){return Promise.all(e)},Ge.spread=function(e){return function(t){return e.apply(null,t)}},Ge.isAxiosError=function(e){return z.isObject(e)&&!0===e.isAxiosError},Ge.mergeConfig=Be,Ge.AxiosHeaders=_e,Ge.formToJSON=e=>he(z.isHTMLForm(e)?new FormData(e):e),Ge.getAdapter=Ue,Ge.HttpStatusCode=Ye,Ge.default=Ge;const $e=Ge;$e.defaults.timeout=6e4,$e.defaults.baseURL="/prod/cc/custom/api",$e.interceptors.request.use((t=>(t.headers={"X-Access-Token":e("ph_access_token"),"Content-Type":"application/json","Pin-Token":e(l.LZ_PIN_TOKEN)||""},t)),(e=>Promise.reject(e))),$e.interceptors.response.use((e=>e.data),(e=>{const{response:n}=e;if(n)return Xe(n.status),Promise.reject(n.data);t("网络连接异常,请稍后再试!")}));const Xe=e=>{let t="";switch(e){case 400:t="请求错误(400)";break;case 401:t="未授权，请重新登录(401)";break;case 403:t="拒绝访问(403)";break;case 404:t="请求出错(404)";break;case 408:t="请求超时(408)";break;case 500:t="服务器错误(500)";break;case 501:t="服务未实现(501)";break;case 502:t="网络错误(502)";break;case 503:t="服务不可用(503)";break;case 504:t="网络超时(504)";break;case 505:t="HTTP版本不受支持(505)";break;default:t=`连接出错(${e})!`}return`${t}，请检查网络或联系管理员！`},Qe=t=>{const r=new Date;return function(t="",n={},r="POST"){return new Promise(((o,s)=>{let i;n.accessToken=e("access_token"),console.log(e("access_token")),"GET"===r.toUpperCase()?i=$e({url:t,params:n}):"POST"===r.toUpperCase()&&(i=$e({method:"POST",url:t,data:n})),i.then((e=>{o(e)})).catch((e=>{s(e)}))}))}("/1000002668/aigc/dz/eventReport",{source:c(n.state.channel).channelId,content:t.content,name:t.isView?"maievent-page-view":"maievent-page-operate",openId:n.state.uniqueId,page:t.pageUrl,occurredAt:r,sdk:"js",reportQm:!1,weAppName:!1},"post")},et={computed:{},data:()=>({}),onShow(){},methods:{report({pageUrl:e=this.pageUrl,name:t="",view:n="",isView:o=!1}){const s=t||"",i=n||(o&&this.pageView?this.pageView:""),a=r(),c={content:s,viewName:i,isView:o,pageUrl:e||a[a.length-1].route};c.content&&Qe(c)},goBabysAiPageFun(e){e.num=e.num||0;if(-1!=r().findIndex((t=>t.route==e.page))||e.optional)s({delta:1});else{let t=e.page;e.str&&(t=e.page+e.str),o({url:`/${t}`})}}}};export{f as _,c as a,l as c,u as g,et as i,i as t};
