<!--
@Description:caoshijie
@Date: 2024/12/03
@Description: 爱他美 小转大
@FilePath:src\pages\1000002668\1848177292955844610\App.vue
-->
<template>
  <div>
    <img class="w100 kv" :src="actConfig.kv" alt="" />
    <img class="prize-btn" @click="showMyPrizePopup = true" :src="actConfig.prizeBtn" alt="" />
    <!--  满足条件-->
    <template v-if="isShowMain">
      <div class="w100 step" :style="{ backgroundImage: `url(${actConfig.stepBg})` }" v-for="item in actInfo" :key="item.seriesId">
        <img class="step-til" :src="item.seriesPic" alt="" />
        <div v-for="it in item.rights" :key="it.id" :style="{ '--giftBg': `url(${it.rightPic})` }" class="step-gift w100">
          <img v-if="it.cashPrizeType === 1" @click="handleCouponGift(it, item.seriesId, it.id)" :class="{ 'gift-btn-disabled': !it.crowd }" class="gift-btn" src="https://img10.360buyimg.com/imgzone/jfs/t1/173980/2/49315/10272/671872e5F4bff61af/3a0134dbb19e391d.png" alt="" />
          <template v-else>
            <img v-for="prize in it.prizes" :key="prize.id" @click="handleOntherGift(prize, item.seriesId, it.id)" :class="{ 'gift-btn-disabled': prize.status != 1 }" class="gift-btn" src="https://img10.360buyimg.com/imgzone/jfs/t1/173980/2/49315/10272/671872e5F4bff61af/3a0134dbb19e391d.png" alt="" />
          </template>
        </div>
      </div>
    </template>
    <!--  不满足条件-->
    <img v-else class="w100" @click="goLink(actConfig.unsatisfiedLink)" :src="actConfig.unsatisfiedImg" alt="" />
    <!--  曝光商品-->
    <img class="sku-til" :src="actConfig.skuTitle" alt="" />

    <div class="sku" :style="{ backgroundImage: `url(${actConfig.stepBg})` }">
      <div class="sku-list">
        <div class="sku-item" v-for="item in skuList" :key="item.skuId" @click="gotoSkuPage(item.skuId)">
          <img class="w100" :src="item.imagePath" alt="" />
          <div class="sku-name van-multi-ellipsis--l2">{{ item.name }}</div>
        </div>
      </div>
    </div>
    <!-- 规则-->
    <img class="sku-til" :src="actConfig.ruleTitle" alt="" />

    <div class="sku" :style="{ backgroundImage: `url(${actConfig.stepBg})` }">
      <div class="rule" v-html="ruleText"></div>
    </div>
    <div class="btn-group">
      <img @click="gotoShopPage(baseInfo.shopId)" :src="actConfig.shopBtn" set="" />
      <img @click="goPageTop" :src="actConfig.topBtn" alt="" srcset="" />
    </div>
    <draw-success @saveAddress="(showSaveAddressPopup = true), (showSuccessPopup = false)" :showPopup="showSuccessPopup" :prizeInfo="prizeInfo" @closeDialog="showSuccessPopup = false"></draw-success>
    <open-card :bg="actConfig.openCard" :showPopup="showOpenCardPopup" @closeDialog="showOpenCardPopup = false"></open-card>
    <my-prize :bg="actConfig.dialogBg" @saveAddress="saveFormPrize" :showPopup="showMyPrizePopup" @closeDialog="showMyPrizePopup = false"></my-prize>
    <save-address :bg="actConfig.dialogBg" :addressInfo="addressInfo" :id="addressId" :showPopup="showSaveAddressPopup" @closeDialog="showSaveAddressPopup = false"></save-address>
    <coupon-list :bg="actConfig.dialogBg" :couponTitle="couponTitle" @drawGift="drawGift" :coupons="couponList" :showPopup="showCouponListPopup" @closeDialog="showCouponListPopup = false"></coupon-list>
  </div>
</template>
<script setup lang="ts">
import { inject, reactive, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { getBaseInfo, getSkuList, getActivityConfig, getRules, drawGiftById } from './script/ajax';
import { gotoSkuPage, gotoShopPage } from '@/utils/platforms/jump';
import type { Sku, ActInfo, Right, DrawResult, Prize, PrizeType } from './script/type';
import { showToast, Toast } from 'vant';
import DrawSuccess from './components/DrawSuccess.vue';
import OpenCard from './components/OpenCard.vue';
import MyPrize from './components/MyPrize.vue';
import SaveAddress from './components/SaveAddress.vue';
import CouponList from './components/CouponList.vue';

const baseInfo = inject('baseInfo') as BaseInfo;
const showSuccessPopup = ref(false);
const showOpenCardPopup = ref(false);
const showMyPrizePopup = ref(false);
const isShowMain = ref(false);
const showSaveAddressPopup = ref(false);
const showCouponListPopup = ref(false);
const actInfo = reactive<ActInfo[]>([]);
const init = async () => {
  Object.assign(actInfo, await getBaseInfo());
  isShowMain.value = actInfo.length > 0;
};
const skuList = ref<Sku[]>([]);
const getSku = async () => {
  skuList.value = await getSkuList();
};
const actConfig = reactive<any>({});
const getActCon = async () => {
  const data = await getActivityConfig();
  Object.assign(actConfig, JSON.parse(data));
  console.log('🚀 ~ getActCon ~ actConfig:', actConfig);
};
const ruleText = ref('');
const getRule = async () => {
  ruleText.value = await getRules();
};
Promise.all([init(), getSku(), getActCon(), getRule()]);
const prizeInfo = ref<DrawResult>();
const couponList = ref<Prize[]>([]);
const checkSeriesId = ref(''); //  兑换的阶段id
const checkRightId = ref('');
const addressId = ref('');
const drawGift = async (prize: Prize) => {
  if (prize.status === 0) {
    showToast('您不满足条件~');
    return;
  }
  if (prize.status === 2) {
    showToast('您今日已领取~');
    return;
  }
  if (prize.status === 3) {
    showToast('您已领取过~');
    return;
  }
  if (prize.status === 4) {
    showToast('您已领其他奖品~');
    return;
  }
  const res = (await drawGiftById(prize.id, checkSeriesId.value, checkRightId.value)) as DrawResult;
  if (!res.prizeName) return;
  //   优惠券领取成功
  if (res.prizeType === 1) {
    showToast('优惠券领取成功~');
    prize.status = 3;
    return;
  }
  prizeInfo.value = res;
  addressId.value = res.userPrizeId as string;
  showSuccessPopup.value = true;
  await init();
};
const handleOntherGift = async (it: Prize, seriesId: string, rightId: string) => {
  console.log('🚀 ~ handleOntherGift ~ it:', it);
  checkSeriesId.value = seriesId;
  checkRightId.value = rightId;
  await drawGift(it);
};
const couponTitle = ref('');
const handleCouponGift = async (it: Right, seriesId: string, rightId: string) => {
  if (!it.crowd) {
    showToast('您不满足领取条件~');
    return;
  }
  //   优惠券 需要打开选择弹窗
  if (it.cashPrizeType === 1) {
    couponTitle.value = it.rightTitle;
    couponList.value = it.prizes;
    showCouponListPopup.value = true;
  }
  checkSeriesId.value = seriesId;
  checkRightId.value = rightId;
};
const goLink = (link: string) => {
  if (!link) return;
  window.location.href = link;
};
// 校验是否开卡
if (baseInfo.thresholdResponseList.some((item) => item.type === 1)) {
  showOpenCardPopup.value = true;
}
const addressInfo = ref({});
const saveFormPrize = (prize: PrizeType) => {
  // 实物填写地址
  if (prize.rightsType === 3) {
    addressInfo.value = {
      realName: prize.receiverName,
      mobile: prize.mobile,
      province: prize.province,
      city: prize.city,
      county: prize.county, // 区
      addressCode: prize.areaCode,
      address: prize.address,
    };
    addressId.value = prize.id;
    showMyPrizePopup.value = false;
    showSaveAddressPopup.value = true;
  }
  //   卡密查看卡密
  if (prize.rightsType === 7) {
    prizeInfo.value = {
      giftCardCode: prize.giftCardCode,
      giftCardSecret: prize.giftCardSecret,
      prizeImg: prize.rightsImg,
      prizeName: prize.rightsName,
      prizeType: prize.rightsType,
    };
    addressId.value = prize.id;
    showMyPrizePopup.value = false;
    showSuccessPopup.value = true;
  }
};
const goPageTop = () => {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: 'smooth',
  });
};
</script>
<style lang="scss" scoped>
.kv {
  margin-bottom: 0.63rem;
}

.prize-btn {
  position: absolute;
  right: 0.19rem;
  top: 4rem;
  width: 1.3rem;
}

.step {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/200376/33/46273/9444/671872e2Ffa5b0307/e08e82f42bcd9136.png) no-repeat;
  background-size: 100% 100%;
  height: auto;
  margin-top: 0.31rem;
  padding: 0.5rem 0.1rem 0.2rem;
  box-sizing: border-box;
  text-align: center;

  .step-til {
    width: 6.62rem;
    display: inline-block;
  }

  .step-gift {
    background: var(--giftBg) no-repeat;
    background-size: 100%;
    height: 3.23rem;
    position: relative;

    .gift-btn {
      width: 1.32rem;
      position: absolute;
      right: 1.15rem;
      bottom: 0.3rem;
    }
    .gift-btn-disabled {
      filter: grayscale(1);
    }
  }
}
.sku-til {
  width: 6.27rem;
  margin: 0.4rem auto 0;
}

.sku {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/217753/23/45433/10269/6718a59fFae5bc5e0/22d841f6504f0147.png) no-repeat;
  background-size: 100% 100%;
  height: 8.41rem;
  padding: 0.2rem;
  box-sizing: border-box;
  text-align: center;
  margin-top: 0.4rem;

  .sku-list {
    margin-top: 0.1rem;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 0.2rem;
    max-height: 7.7rem;
    overflow-y: auto;
    box-sizing: border-box;
    color: #fff;
    .sku-name {
      font-size: 0.24rem;
    }
  }

  .rule {
    margin-top: 0.1rem;
    font-size: 0.2rem;
    text-align: left;
    max-height: 7.7rem;
    color: #fff;
    line-height: 1.6;
    overflow-y: auto;
    font-family: 'FZLTZHK--GBK1-0';
  }
}
.btn-group {
  margin: 0.25rem auto;
  display: flex;
  justify-content: space-around;
  align-items: center;
  img {
    width: 1.86rem;
  }
}
</style>

<style>
.w100 {
  width: 100%;
}

html {
  background-color: #fff;
  padding: 0.26rem 0.19rem;
  box-sizing: border-box;
}

@font-face {
  font-family: 'FZLTZHK--GBK1-0';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTZHK/FZLTZHK--GBK1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTZHK/FZLTZHK--GBK1-0.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/*修改vant popup样式*/
.van-popup {
  background: transparent;
}

.van-toast {
  background-color: rgba(0, 0, 0, 0.7);
}

::-webkit-scrollbar {
  display: none;
}
</style>
