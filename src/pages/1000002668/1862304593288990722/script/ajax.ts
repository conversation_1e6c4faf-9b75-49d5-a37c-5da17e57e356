import { httpRequest } from '@/utils/service';
import { showToast, showLoadingToast, closeToast } from 'vant';

export const getBaseInfo = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/dz/1862304593288990722/loading');
    closeToast();
    return data || [];
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
  return [];
};
export const getSkuList = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/dz/1862304593288990722/getSkuList');
    closeToast();
    return data || [];
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
  return [];
};
export const getRules = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.get('/common/getRule');
    closeToast();
    return data.replaceAll('\n', '<br>') || '';
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
  return '';
};
export const getActivityConfig = async () => {
  try {
    const { data } = await httpRequest.post('/common/getActivityConfig');
    return data || '';
  } catch (error: any) {
    console.error(error);
  }
  return '';
};
export const drawGiftById = async (prizeId: string, seriesId: string, rightId: string) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { code, data, message } = await httpRequest.post('/dz/1862304593288990722/cashPrize', {
      rightId,
      seriesId,
      prizeId,
    });
    // const { code, data, message } = {
    //   code: 200,
    //   data: {
    //     addressId: '',
    //     cashPrizeType: 0,
    //     distributeResult: '',
    //     giftCardCode: '4783ac3e5',
    //     giftCardSecret: '',
    //     prizeImg: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com//f-8699693363408581129/image/hskfbx0nben1vem57up2nl6blilqac13.png',
    //     prizeName: '爱奇艺季卡',
    //     prizeType: 7,
    //     userPrizeId: '1851248934540726274',
    //   },
    //   message: '',
    // };
    closeToast();
    if (code !== 200) {
      showToast(message);
      return {};
    }
    return data || {};
  } catch (error: any) {
    closeToast();
    showToast(error?.message as unknown as string);
    console.error(error);
    return {};
  }
  return {};
};
export const writeAddress = async (form: any) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { code, data, message } = await httpRequest.post('/dz/1862304593288990722/writeAddress', {
      ...form,
    });
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return true;
  } catch (error: any) {
    closeToast();
    showToast(error?.message);
    console.error(error);
    return false;
  }
  return false;
};
export const getMyPrize = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { code, data, message } = await httpRequest.post('/dz/1862304593288990722/myPrize', {});
    closeToast();
    if (code !== 200) {
      showToast(message);
      return {};
    }
    return data || {};
  } catch (error: any) {
    closeToast();
    showToast(error?.message as unknown as string);
    console.error(error);
    return {};
  }
  return {};
};
