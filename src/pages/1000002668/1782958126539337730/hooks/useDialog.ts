/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2024/4/8 14:46
 * Description:
 */
import { ref } from 'vue';

export default function useDialog() {
  const current = ref('');
  const popup = ref(false);

  function closePopup() {
    popup.value = false;
  }

  function handleClick(type: string) {
    console.log(type, popup.value);
    current.value = type;
    popup.value = true;
  }

  return {
    current,
    popup,
    handleClick,
    closePopup,
  };
}
