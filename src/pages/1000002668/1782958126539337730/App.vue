<template>
  <div class="container">
    <img class="container-bg" :src="decoData?.joinBg" alt="">
    <div class="container-prize" @click="handleClick('prize')"></div>
    <div class="container-wheel" @click="handleWheel"></div>
    <div class="container-task" @click="handleTask"></div>
    <div class="container-rule" @click="handleClick('rule')"></div>

    <div class="container-prizeList swiper-container">
      <div class="container-prizeList-content swiper-wrapper">
        <div class="swiper-slide" v-for="(item, index) in prizeList" :key="index">
          <img :src="item.prizeImg" alt="" class="">
          <div>{{ item.prizeName }}</div>
        </div>
      </div>
    </div>
    <div class="container-change" @click="fetchChance(true)">
      <div>剩余抽奖次数: {{ chance?.remainChance }}</div>
    </div>
    <VanDialog
      width="6.15rem"
      v-model:show="popup"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      @close="handleClose">
      <DialogLayout
        @close="popup = false"
        :dialogImgList="decoData?.dialogImgList"
        :dialogBtnFunction="dialogBtnFunction"
        :current="current">
        <template v-slot:default>
          <component
            v-if="popup"
            :is="dialogComponent[current]"
            :data="prizeData"
            :chance="chance"
            :roomUrl="roomUrl"
            @handleDelivery="handleDelivery"
            @refresh="refresh"
            @openCard="handleClick('focus')"
            ref="dialogRef"/>
        </template>
      </DialogLayout>
    </VanDialog>
  </div>
</template>
<script setup lang="ts">
import { inject, nextTick, onMounted, ref } from 'vue';
import DialogLayout from './component/DialogLayout.vue';
import { checkUserInfo, dialogComponent, getRoomUrl, PRIZE_TYPE } from './util';
import useRequest from './hooks/useRequest';
import useDialog from './hooks/useDialog';
import { showToast } from 'vant';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import Swiper, { Autoplay } from 'swiper';

Swiper.use([Autoplay]);

const baseInfo: any = inject('baseInfo');
const dialogRef = ref();
const decoData = ref();
const {
  current,
  popup,
  handleClick,
  closePopup,
} = useDialog();
const { request } = useRequest();
const prizeData = ref();
const prizeList = ref([]);
const chance = ref<any>({});
const roomUrl = ref('');
const mySwiper = ref();

const getLiveId = async () => {
  const { data } = await request('/live/getLiveId', {}, { isLoading: false });
  roomUrl.value = getRoomUrl(data.liveId);
};

// 关注店铺
const fellowShop = async () => {
  lzReportClick('fellowShop');
  try {
    await request('/common/followShop');
    showToast({
      message: '关注成功',
      forbidClick: true,
    });
  } catch (error: any) {
    error.message && showToast(error.message);
  }
};
// 实物奖品填写信息
const handleDelivery = (value?: any) => {
  value && (prizeData.value = value);
  handleClick('info');
};
// 弹窗按钮点击事件
const dialogBtnFunction = {
  join: async () => {
    lzReportClick('join');
    window.jmfe.toAny(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(roomUrl.value)}`);
  },
  focus: async () => {
    await fellowShop();
    setTimeout(() => {
      window.location.reload();
    }, 1500);
  },
  joinAndFocus: async () => {
    await fellowShop();
    lzReportClick('join');
    window.jmfe.toAny(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(roomUrl.value)}`);
  },
  // 提交地址信息
  info: async () => {
    const checkResult = checkUserInfo(dialogRef.value.userInfo);
    if (checkResult) {
      const result = await request('/live/aptamil/userAddressInfo', dialogRef.value.userInfo);
      if (result) {
        showToast('提交成功');
        closePopup();
      }
    }
  },
  noWin: () => {
    closePopup();
  },
  winJdBean: () => {
    closePopup();
  },
  // 实物按钮点击事件/弹出填写中奖信息弹窗
  winProduct: () => {
    handleClick('info');
  },
  winCoupon: () => {
    closePopup();
  },
  winPoint: () => {
    closePopup();
  },
  winGiftCard: () => {
    closePopup();
  },
  task: () => {
    closePopup();
  },
};
// 获取抽奖次数
const fetchChance = async (isLoading: boolean) => {
  const { data } = await request('/live/aptamil/chanceNum', {}, { isLoading });
  chance.value = data;
};

const handleTask = () => {
  handleClick('task');
};

const refresh = async () => {
  await fetchChance(true);
};

const fetchData = async () => {
  const { data } = await request('/common/getActivityConfig', {}, { isLoading: false });
  decoData.value = JSON.parse(data);
  await fetchChance(false);
};

const fetchPrizeList = async () => {
  const { data } = await request('/live/aptamil/getPrizes', {}, { isLoading: false });
  prizeList.value = data;
};

const changeSpeed = () => {
  mySwiper.value.params.speed = 100;
};
const startSwiper = () => {
  mySwiper.value.autoplay.start();
};
const resetSpeed = () => {
  startSwiper();
  mySwiper.value.params.speed = 1000;
};

const stopSwiper = () => {
  mySwiper.value.autoplay.stop();
};

const handleClose = () => {
  resetSpeed();
  const repeatFetchChangeList = Object.values(PRIZE_TYPE);
  // 抽奖后刷新抽奖次数
  if (repeatFetchChangeList.includes(current.value)) {
    fetchChance(true);
  }
};

// 抽奖
const handleWheel = async () => {
  // if (!baseInfo.followQualify && !baseInfo.memberLevel) {
  //   handleClick('joinAndFocus');
  //   return;
  // }
  if (!baseInfo.memberLevel) {
    handleClick('join');
    return;
  }
  if (!baseInfo.followQualify) {
    handleClick('focus');
    return;
  }
  if (!chance.value.remainChance) {
    showToast('抽奖机会不足');
    return;
  }
  if (chance.value.todayChance >= 4) {
    showToast('今日抽奖已经达上线');
    return;
  }
  changeSpeed();
  try {
    const { data } = await request('/live/aptamil/lotteryDraw');
    prizeData.value = data;
    console.log(data.prizeType);
    handleClick(PRIZE_TYPE[data.prizeType ?? 0]);
  } finally {
    stopSwiper();
  }
};
fetchData();
getLiveId();
lzReportClick({
  code: 'isXview',
  value: window.jmfe.isWebview('xview')
    .toString(),
});
onMounted(async () => {
  await fetchPrizeList();
  nextTick(() => {
    !mySwiper.value && (mySwiper.value = new Swiper('.swiper-container', {
      loop: true,
      speed: 800,
      autoplay: {
        delay: 1,
        stopOnLastSlide: false,
        disableOnInteraction: false,
      },
      slidesPerView: 'auto',
      loopFillGroupWithBlank: true,
      normalizeSlideIndex: true,
      direction: 'horizontal',
      centeredSlides: true,
      spaceBetween: 0,
    }));
  });
});

window.addEventListener('visibilitychange', async () => {
  if (document.visibilityState === 'visible') {
    closePopup();
    await fetchChance(true);
  }
});

</script>
<style scoped lang="scss">
.container {
  position: relative;

  :deep(.swiper-container>.swiper-wrapper) {
    transition-timing-function: linear;
    margin: 0 auto;
  }

  .color {
    //background: rgba(0, 0, 0, .5)
  }

  &-prize {
    @extend .color;
    position: absolute;
    width: 1.7rem;
    height: 0.49rem;
    top: 1.5rem;
    right: 0;
    //background-color: #000;
  }

  &-wheel {
    @extend .color;
    position: absolute;
    width: 3.7rem;
    height: 1.2rem;
    left: 50%;
    top: 9rem;
    transform: translateX(-50%);
  }

  &-task {
    @extend .color;
    position: absolute;
    width: 2rem;
    height: 0.6rem;
    left: 50%;
    top: 10.4rem;
    transform: translateX(-50%);
  }

  &-rule {
    @extend .color;
    position: absolute;
    width: 1.7rem;
    height: 0.49rem;
    top: 0.7rem;
    right: 0;
    //background-color: #ab5252;
  }

  &-change {
    position: absolute;
    bottom: 4rem;
    width: 100%;
    font-size: 0.2rem;
    display: flex;
    left: 0;
    align-items: center;
    justify-content: center;
    letter-spacing: .02rem;

    > div {
      flex-wrap: nowrap;
    }

    img {
      height: .3rem;
      margin-left: .1rem;
    }
  }

  &-prizeList {
    display: flex;
    align-items: center;
    position: absolute;
    top: 5.75rem;
    height: 2.2rem;
    width: 5.1rem;
    left: 50%;
    overflow: hidden;
    transform: translateX(-50%);

    &-content {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      width: 100%;
      height: 1.2rem;
      position: relative;

      .swiper-slide {
        text-align: center;
        font-size: .2rem;
      }

      img {
        height: 1.2rem;
        width: 1.2rem;
      }

      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  &-bg {
    width: 100%;
    height: 100%;
  }
}
</style>
