<template>
  <div class="rule-bk">
    <div class="title">
      <div class="close" @click="close"></div>
    </div>
    <div class="content">
      <div class="form">
        <VanField v-model="form.realName" required label="姓名：" maxlength="20" input-align="right"></VanField>
        <VanField v-model="form.mobile" required label="电话：" maxlength="11" type="number" input-align="right"></VanField>
        <VanField v-model="addressCode" required label="省市区：" readonly @click="addressSelects = true" input-align="right"></VanField>
        <VanField v-model="form.address" required label="详细地址：" maxlength="100" input-align="right"></VanField>
      </div>
      <div class="tip">请注意：【地址不详】或【手机号错误】将影响名单，导致您无法收到商品！
        （超过1小时未填写收货地址则视为放弃，<span style="color:#a02525">提交后不可修改请谨慎填写</span>）</div>
      <img class="submit" src="//img10.360buyimg.com/imgzone/jfs/t1/234497/25/11728/1712/65977106Fe5c96cb5/3a8281531f17b62e.png" alt="" @click="checkForm" />
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { reactive, ref, computed, PropType, onMounted } from 'vue';
import { areaList } from '@vant/area-data';
import { FormType } from '../ts/type';
import { httpRequest } from '@/utils/service';

const props = defineProps({
  userPrizeId: {
    type: String,
    required: true,
  },
  addressId: {
    type: String,
    required: true,
  },
  activityPrizeId: {
    type: String,
    required: true,
  },
  itemData: {
    type: Object,
    required: true,
  },
  echoData: {
    type: Object as PropType<FormType>,
    default: () => ({
      realName: '',
      mobile: '',
      province: '',
      city: '',
      county: '',
      address: '',
    }),
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close', false, props.itemData);
};

const addressSelects = ref(false);

const form: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

onMounted(() => {
  // 回显地址
  Object.keys(form).forEach((key: string) => {
    form[key] = props.echoData[key];
  });
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});
// const addressCode = ref('');

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90001/userAddressInfo', {
      addressId: props.addressId,
      userPrizeId: props.userPrizeId,
      activityPrizeId: props.activityPrizeId,
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
      addressCode: '', // 一般情况下都是使用addressId，可以传空
    });
    if (res.code === 200) {
      closeToast();
      showToast('保存成功');
      emits('close', true, props.itemData);
    } else {
      showToast(res.message);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的电话');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/247054/6/1962/29810/65960d8dF71efedd2/c4dbce80c7e1e229.png) no-repeat;
  background-size: 100%;
  width: 100vw;

  .title {
    position: relative;
    height: 1.20rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem;
    .text {
      height: 0.6rem;
    }
  }

  .close {
    width: 0.55rem;
    height: 0.55rem;
  }

  .content {
    padding: 0 0.3rem 0.3rem 0.3rem;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .form {
      padding-top: 0.68rem;
      .van-cell {
        color: #262626;
        height: 0.94rem;
        align-items: center;
        font-size: 0.24rem;
        border-radius: 0.15rem;
        margin-bottom: 0.15rem;

        &::after {
          display: none;
        }
      }
    }

    .tip {
      font-size: 0.2rem;
      color: #646464;
      margin-top: 0.35rem;
    }

    .submit {
      width: 6.16rem;
      margin: 0.6rem auto 0;
    }
  }
}
</style>
