import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/', // 主页
    name: 'index',
    component: () => import('./views/home.vue'),
  },
  {
    path: '/result',
    name: 'result',
    component: () => import('./views/result.vue'),
  },
  {
    path: '/uploadImage',
    name: 'uploadImage',
    component: () => import('./views/uploadImage.vue'),
  },
  {
    path: '/equityPage',
    name: 'equityPage',
    component: () => import('./views/equityPage.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/',
    name: 'notFound',
    hidden: true,
  },
];

const paramsObj: any = {};
// 路由
function initializeRouter() {
  // 从链接中移出shopId和activityId
  const url = new URL(window.location.href);
  const params = new URLSearchParams(url.search || url.hash.split('?')[1] || '');
  // 删除字段
  params.delete('mockCode');
  params.delete('token');
  params.delete('source');
  params.delete('fromType');
  // params转object
  params.forEach((value, key) => {
    paramsObj[key] = value;
  });
  window.history.replaceState(null, '', `${url.pathname}${url.hash.split('?')[0]}?${params.toString()}`);

  const router = createRouter({
    history: createWebHashHistory(`${process.env.VUE_APP_PATH_PREFIX_NO_CDN}90018/1001/`),
    routes,
  });
  router.beforeEach((to, from, next) => {
    if (!to.query.shopId || !to.query.activityMainId) {
      const query = {
        ...to.query,
        ...paramsObj,
      };
      next({ ...to, query });
    } else {
      next();
    }
  });
  return router;
}

export default initializeRouter;
