<template>
  <!-- 我的奖品/领取记录弹窗 -->
  <div class="box">
    <div class="rule-view">
      <div class="rule">
        <div class="row title">
          <div class="name">优惠券名称</div>
          <div class="time">领取时间</div>
          <div class="btn"></div>
        </div>
        <div v-if="recordList.length" class="content">
          <div class="row" v-for="(item, index) in recordList" :key="index">
            <div class="name">{{item.prizeName}}</div>
            <div class="time">{{dayjs(item.createTime).format('YYYY-MM-DD')}}</div>
            <div class=btn>
              <div class="jump-btn" @click="gotoSkuPage(item.prizeSkuId)"></div>
            </div>
          </div>
        </div>
        <div v-else class="no-data">
          您还没有领取记录
        </div>
      </div>
    </div>
    <!-- 关闭按钮 -->
    <div class="close-btn"
         @click="closeDialog()"></div>
  </div>
</template>

<script lang='ts' setup>
import { defineEmits, defineProps, inject, ref } from 'vue';
import { getDataInterface } from '../ts/port';
import { gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';

const emit = defineEmits(['closeDialog']);
const props = defineProps({ rule: String });
const decoData = inject('decoData', {}) as any;
const isPreview = inject('isPreview') as boolean;
const recordList = ref([]);
const getRecordList = async () => {
  const res = await getDataInterface('getUserReceiveRecord', 'post');
  if (res.data) {
    recordList.value = res.data;
  }
};

const closeDialog = () => {
  emit('closeDialog', 'record');
};

!isPreview && getRecordList();
</script>

<style lang='scss' scoped>
.box {
  align-items: center;
  justify-content: center;
  width: 6.36rem;
  img {
    margin: 0 auto;
  }

  .popup {
    width: 6rem;
  }

  .rule-view {
    margin: 0 auto;
    position: relative;
    padding: 1.2rem 0.2rem 0;
    box-sizing: border-box;
    background-image: url(../assets/img/dialog-record.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: 1;
    width: 6.36rem;
    height: 5.01rem;

    .rule {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      font-size: .26rem;
      text-align: left;
      white-space: break-spaces;
      color: #333;
      padding: 0 0 0 0.1rem;
      .title{
        color: #0033be;
        font-size: 0.3rem;
      }
      .row{
        display: flex;
        margin: 0 auto 0.1rem;
        justify-content: center;
      }
      .name{
        width: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
      }
      .time{
        width: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
      }
      .btn{
        flex:1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .jump-btn{
        width: 1.58rem;
        height: 0.55rem;
        background: url(../assets/img/to-Buy-Btn.png) no-repeat;
        background-size: contain;
        cursor: pointer;
      }
      .content{
        overflow: hidden;
        height: 2.8rem;
        overflow-y: scroll;
      }
      .no-data{
        width: 100%;
        height: 2.8rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #000;
        font-size: 0.35rem;
      }
    }
  }

  .close-btn {
    width: 0.6rem;
    height: 0.6rem;
    background: url(../assets/img/dialog-close-icon.png) no-repeat;
    background-size: contain;
    cursor: pointer;
    z-index: 10;
    margin: 0.3rem auto 0;
  }
}
</style>
