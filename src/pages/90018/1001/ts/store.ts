import { createStore } from 'vuex';

export default createStore({
  state: {
    actType: 0 as number | null, // 定义 actType 状态 活动链路配置新客礼2配置0-关闭1-开启上传出生证2-开启上传其他照片
    successPhone: '' as string, // 定义 successPhone 状态 手机号
    uploadName: '' as string, // 定义 uploadName 状态 上传照片名称
    userType: '' as string, // 定义 userType 状态 用户身份 ’new‘-新用户 ‘old’-老用户 ’pass' - 通过 'fail' - 未通过 'default' - 默认(不满足领取条件)
    userBornBindStatus: true as boolean, // 定义 userBornBindStatus 状态 用户手机是否绑定过出生证号
    userReceivedGift2: true as boolean, // 定义 userReceivedGift2 状态 用户是否领取过新客礼2
    prizeList: [] as any[], // 定义 prizeList 状态 奖品列表
  },
  mutations: {
    // 定义 set 方法，用于更新 actType等
    setActType(state, actType: number) {
      state.actType = actType;
    },
    setSuccessPhone(state, successPhone: string) {
      state.successPhone = successPhone;
    },
    setUploadName(state, uploadName: string) {
      state.uploadName = uploadName;
    },
    setUserType(state, userType: string) {
      state.userType = userType;
    },
    setUserBornBindStatus(state, userBornBindStatus: boolean) {
      state.userBornBindStatus = userBornBindStatus;
    },
    setUserReceivedGift2(state, userReceivedGift2: boolean) {
      state.userReceivedGift2 = userReceivedGift2;
    },
    setPrizeList(state, prizeList: any[]) {
      state.prizeList = prizeList;
    },
  },
  actions: {
    // 如果需要异步操作，可以在这里定义
  },
  getters: {
    // 定义get方法，用于获取 actType等
    getActType: (state) => state.actType,
    getSuccessPhone: (state) => state.successPhone,
    getUploadName: (state) => state.uploadName,
    getUserType: (state) => state.userType,
    getUserBornBindStatus: (state) => state.userBornBindStatus,
    getUserReceivedGift2: (state) => state.userReceivedGift2,
    getPrizeList: (state) => state.prizeList,
  },
});
