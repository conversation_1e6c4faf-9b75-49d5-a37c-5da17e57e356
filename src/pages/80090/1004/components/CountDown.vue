<template>
  <div class="count-down-time" :style="furnishStyles.cutDownColor.value">
    <span v-if="props.isStart">距离活动结束剩余：</span>
    <span v-else>距离活动开始还有：</span>
    <van-count-down :time="props.isStart ? (props.endTime - new Date().getTime()) : (props.startTime - new Date().getTime())" format="DD:HH:mm:ss">
      <template #default="timeData">
        <div class="contentSpan">
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.days }}</div><span :style="furnishStyles.cutDownColor.value">天</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.hours }}</div><span :style="furnishStyles.cutDownColor.value">时</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.minutes }}</div><span :style="furnishStyles.cutDownColor.value">分</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.seconds }}</div><span :style="furnishStyles.cutDownColor.value">秒</span>
        </div>
      </template>
    </van-count-down>
  </div>
</template>

<script setup lang="ts">
import furnishStyles, { furnish } from '../ts/furnishStyles';

const props = defineProps({
  isStart: {
    type: Boolean,
    default: false,
    required: true,
  },
  startTime: {
    type: Number,
    default: 0,
    required: true,
  },
  endTime: {
    type: Number,
    default: 0,
    required: true,
  },
});
</script>

<style scoped lang="scss">
.count-down-time {
  position: relative;
  top: 1.05rem;
  left: 0.7rem;
  width: 5rem;
  font-size: 0.25rem;
  //color: #f2270c;
  .contentSpan {
    //margin-left: 0rem;
    display: flex;
    position: absolute;
    top: 0;
    left: 2.32rem;

    .acblockStyleStyle {
      //color: #f2270c;
      border-radius: 0.05rem;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
      //background-color: #8b4004;
      //background: url("https://img10.360buyimg.com/imgzone/jfs/t1/77902/16/19674/1310/63070b2eEbcbdb636/bf6602f462367c3f.png") no-repeat;
      //background-size: 100%;
      width: 0.44rem;
      height: 0.44rem;
    }
    span {
      width: 0.4rem;
      height: 0.44rem;
      color: #ffffff;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
