<template>
  <div>
    <CommonDrawer title="邮寄地址" @close="emits('close')">
      <div class="px-4 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs pb-5">
        <div>
          <VanField :readonly="isLookEd" v-model="form.realName" required label="姓名：" maxlength="20"></VanField>
          <VanField :readonly="isLookEd" v-model="form.mobile" required label="电话：" maxlength="11" type="number"></VanField>
          <VanField  v-model="addressCode" required label="省市区：" readonly @click="addressSelects = !isLookEd"></VanField>
          <VanField :readonly="isLookEd" v-model="form.address" required label="详细地址：" maxlength="100"></VanField>
        </div>
        <div class="text-gray-400 mt-2">请注意：地址填写简略、手机号填写错误皆会影响派单，导致您无法收到商品！</div>
        <div class="w-full bg-red-500 text-white rounded flex justify-center items-center h-9 mt-2 text-sm" @click="checkForm">提交</div>
      </div>
    </CommonDrawer>
    <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
      <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { reactive, ref, computed, PropType, onMounted } from 'vue';
import { areaList } from '@vant/area-data';
import { FormType } from '../ts/type';
import { httpRequest } from '@/utils/service';
import CommonDrawer from '@/components/CommonDrawer/index.vue';

const props = defineProps({
  userReceiveRecordId: {
    type: String,
    required: true,
  },
  activityPrizeId: {
    type: String,
    required: true,
  },
  echoData: {
    type: Object as PropType<any>,
    default: () => ({
      realName: '',
      mobile: '',
      province: '',
      city: '',
      county: '',
      address: '',
    }),
  },
});

const emits = defineEmits(['close']);

const addressSelects = ref(false);

const form: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

const isLookEd = ref(false);
onMounted(() => {
  // 回显地址
  Object.keys(form).forEach((key: string) => {
    form[key] = props.echoData[key];
  });
  if (props.echoData.realName) {
    isLookEd.value = true;
  }
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});
// const addressCode = ref('');

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/80090/userAddressInfo', {
      userReceiveRecordId: props.userReceiveRecordId,
      activityPrizeId: props.activityPrizeId,
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (isLookEd.value) {
    showToast('已提交过地址，不可修改');
    return;
  }
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的电话');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};
</script>
