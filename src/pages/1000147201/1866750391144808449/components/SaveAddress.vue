<template>
  <popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false" @closed="handleClose" @opened="handleOpen">
    <div class="box">
      <div class="dialog">
        <div class="dialog-title">邮寄地址</div>
        <div class="mask" v-if="addressInfo.isWithin48Hours" @click="showTips"></div>
        <div class="row">
          <div>收货人：</div>
          <input type="text" v-model="form.receiver" placeholder="请输入收货人" maxlength="10" />
        </div>
        <div class="row">
          <div>手机号：</div>
          <input type="text" v-model="form.phone" placeholder="请填写手机号" oninput="value=value.replace(/[^\d]/g,'')" maxlength="11" />
        </div>
        <div class="row" v-if="!addressInfo.isHistory">
          <div>地区：</div>
          <input type="text" v-model="form.addressCode" readonly="true" placeholder="选择省/市/区" @click="addressSelects = true" />
        </div>
        <div class="row">
          <div>详细地址：</div>
          <input type="text" v-model="form.address" placeholder="请填写地址" maxlength="30" />
        </div>
        <!-- 确认提交 -->
        <img v-if="!addressInfo.isHistory" class="submit-btn" @click="checkForm" src="https://img10.360buyimg.com/imgzone/jfs/t1/240892/38/28479/9705/675b9f3bFc4c4c3cb/d12df19e6a0a69a0.png" alt="" />
      </div>
      <!--地址选择-->
      <Popup v-model:show="addressSelects" teleport="#app" position="bottom">
        <VanArea title="请输入详细地址" :area-list="areaList" @confirm="confirmAddress" @cancel="onCancel" />
      </Popup>
      <icon @click="handleClose" class="close-btn" name="close" size="44" color="#fff"> </icon>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps, inject, ref, reactive, watch, watchEffect } from 'vue';
import { Popup, Icon, Area, showToast } from 'vant';
import type { BaseInfo } from '@/types/BaseInfo';
import { areaList } from '@vant/area-data';
import { containsEmoji, containsSpecialChars, isPhoneNumber, validateDataWithRules } from '@/utils/platforms/validator';
import { writeAddress } from '../script/ajax';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  addressInfo: {
    type: Object,
    default: () => ({}),
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog']);
const form = reactive({
  receiver: '',
  phone: '',
  province: '',
  district: '',
  city: '', // 区
  addressCode: '',
  address: '',
  infoId: '',
  isHistory: false,
});
const handleClose = () => {
  Object.assign(form, {
    receiver: '',
    phone: '',
    province: '',
    district: '',
    city: '', // 区
    addressCode: '',
    infoId: '',
    address: '',
  });
  emits('closeDialog', false);
};
const addressCode = ref('');

const addressSelects = ref(false);
// 关闭三联地址框
const onCancel = () => {
  addressSelects.value = false;
};
// 确认三联地址信息
const confirmAddress = (addressItemList: any) => {
  console.log('🚀 ~ confirmAddress ~ addressItemList:', addressItemList);
  form.province = addressItemList?.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.district = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
  form.addressCode = addressItemList.selectedOptions.map((item: any) => item.text).join('/');
};
const ruleValidate = {
  receiver: [
    {
      required: true,
      message: '请输入姓名',
    },
    {
      validator: containsSpecialChars,
      message: '姓名不能包含特殊字符',
    },
    {
      validator: containsEmoji,
      message: '姓名不能包含表情',
    },
  ],
  phone: [
    {
      required: true,
      message: '请输入电话号码',
    },
    {
      validator: isPhoneNumber,
      message: '请输入正确的电话号码',
    },
  ],
  province: [
    {
      required: true,
      message: '请选择省/市/区',
    },
  ],
  address: [
    {
      required: true,
      message: '请输入详细地址',
    },
    {
      validator: containsEmoji,
      message: '详细地址不能包含表情',
    },
  ],
};
const checkForm = async () => {
  const valid = validateDataWithRules(ruleValidate, form);
  if (!valid) return;
  const res = await writeAddress({ ...form });
  if (res) {
    showToast('保存成功~');
    emits('closeDialog', true);
  }
};
const handleOpen = () => {
  if (props.addressInfo.isHistory) {
    Object.assign(form, { ...props.addressInfo });
  } else {
    Object.assign(form, { ...props.addressInfo, province: props.addressInfo.province || '', district: props.addressInfo.district || '', city: props.addressInfo.city || '', addressCode: `${props.addressInfo.province || ''}/${props.addressInfo.city || ''}/${props.addressInfo.district || ''}` || '' });
    console.log(form);
  }
};
const showTips = () => {
  showToast('若您需修改/补填地址，请联系店铺客服');
};
</script>
<style lang="scss" scoped>
.box {
  padding-bottom: 1.5rem;
  position: relative;
  .close-btn {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0.1rem;
  }
  .dialog {
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/232254/31/30911/7570/675b9ea3F5616b65a/7e0d42feb62a7514.png) no-repeat center center;
    background-size: contain;
    width: 6.5rem;
    height: 9.25rem;
    box-sizing: border-box;
    padding: 1.5rem 0.35rem 0;
    position: relative;
    font-size: 0.32rem;
    text-align: center;
    position: relative;
    .dialog-title {
      font-size: 0.66rem;
      margin-bottom: 0.3rem;
    }
    .row {
      display: flex;
      align-items: center;
      border-bottom: 0.02rem solid #968f80;
      padding: 0.1rem 0.2rem 0.15rem;
      margin-top: 0.32rem;
      color: #1c1c1c;
      div {
        min-width: 1rem;
        text-align: justify;
      }
      input {
        flex: 1;
        border: none;
        background: none;
        width: 3rem;
      }
    }
  }
  .submit-btn {
    width: 3.2rem;
    margin: 1.6rem auto;
  }
}
.mask {
  position: absolute;
  inset: 0;
  background-color: transparent;
  z-index: 1;
}
</style>
