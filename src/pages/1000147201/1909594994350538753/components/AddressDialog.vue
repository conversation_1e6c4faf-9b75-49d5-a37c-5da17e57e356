<template>
  <!-- 填写地址弹窗 -->
  <div class='box'>
    <!-- 规则 -->
    <div class='address-view'>
      <div class='row'>
        <div>收货人：</div>
        <input type='text' v-model='form.realName' placeholder='请输入姓名' maxlength='10' />
      </div>
      <div class='row'>
        <div>手机号：</div>
        <input type='text' v-model='form.mobile' placeholder='请填写手机号' oninput="value=value.replace(/[^\d]/g,'')" maxlength='11' />
      </div>
      <div class='row'>
        <div>所在地区：</div>
        <input type='text' v-model='addressCode' readonly placeholder='选择省/市/区' @click='addressSelects = true' />
      </div>
      <div class='row'>
        <div>详细地址：</div>
        <input type='text' v-model='form.address' placeholder='请填写地址' maxlength='30' />
      </div>
    </div>

    <div class='handle-btn' style='bottom: 1.5rem' @click='commitAddress()'></div>

    <div class='close-btn' @click='closeDialog()'></div>
  </div>

  <!--地址选择-->
  <Popup v-model:show='addressSelects' teleport='#app' position='bottom'>
    <VanArea title='请输入详细地址' :area-list='areaList' @confirm='confirmAddress' @cancel='onCancel' />
  </Popup>
</template>

<script lang='ts' setup>
import { defineEmits, defineProps, ref, reactive, onMounted } from 'vue';
import { containsEmoji } from '@/utils/platforms/validator';
import { Popup, showToast } from 'vant';
import { closeDialog } from '../ts/dialog';
import { areaList } from '@vant/area-data';

const props = defineProps({ addressInfo: Object });
const emits = defineEmits(['commitAddress']);

const form = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '', // 区
  address: '',
});

const addressCode = ref('');

const addressSelects = ref(false);
// 关闭三联地址框
const onCancel = () => {
  addressSelects.value = false;
};
// 确认三联地址信息
const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList?.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
  addressCode.value = addressItemList.selectedOptions.map((item: any) => item.text).join('/');
};

const commitAddress = () => {
  console.log(form);
  const regex = /^1[3-9]\d{9}$/;

  if (!form.realName) {
    showToast('请输入姓名');
    return;
  }
  if (!containsEmoji(form.realName)) {
    showToast('姓名不能包含表情');
    return;
  }
  if (!form.mobile) {
    showToast('请输入电话');
    return;
  }
  if (!regex.test(form.mobile)) {
    showToast('手机号格式不正确');
    return;
  }
  if (!form.province || !form.city || !form.county) {
    showToast('请输入地区');
    return;
  }
  if (!form.address) {
    showToast('请输入详细地址');
    return;
  }
  if (!containsEmoji(form.address)) {
    showToast('详细地址不能包含表情');
    return;
  }
  emits('commitAddress', form);
};

onMounted(() => {
  Object.assign(form, { ...props.addressInfo });
  addressCode.value = form.province ? `${form.province}/${form.city}/${form.county}` : '';
});

</script>

<style lang='scss' scoped>
.box {
  background-image: url("../assets/dialog/address.png");

  .row {
    display: flex;
    align-items: center;
    padding: .2rem 0.4rem 0.11rem;
    margin-top: 0.15rem;
    color: #1c1c1c;

    div {
      min-width: 1rem;
      text-align: justify;
      font-family: MFJinHuaNoncommercial;
    }

    input {
      border: none;
      background: none;
      width: 3.6rem;
      font-family: FZYASHS;
    }

    ::-webkit-input-placeholder {
      /* WebKit browsers，webkit内核浏览器 */
      color: #8d877b;
      font-size: 0.27rem;
    }

    :-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #8d877b;
      font-size: 0.27rem;
    }

    ::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #8d877b;
      font-size: 0.27rem;
    }

    :-ms-input-placeholder {
      /* Internet Explorer 10+ */
      color: #8d877b;
      font-size: 0.27rem;
    }
  }
}
</style>
