export interface Task {
  id: number;
  taskType: number;
  optWay: number;
  buyType: number;
  perOperateCount: number;
  perLotteryCount: number;
  lotteryCount: number;
  taskFinishCount: number;
  limit: number;
}
export interface FormType {
  realName: string;
  mobile: string;
  province: string;
  city: string;
  county: string;
  address: string;
}
export interface CardType {
  id: number;
  prizeName: string;
  prizeImg: string;
  cardDesc: string;
  cardNumber: string;
  cardPassword: string;
}
