import { computed, reactive } from 'vue';

export const furnish = reactive({
  actBg: '',
  // 页面背景图
  pageBg: '',
  actBgColor: '',
  shopNameColor: '',
  btnColor: '',
  btnBg: '',
  btnBorderColor: '',
  ruleBg: '',
  myPrizeBg: '',
  prizeBg: '',
  getPrizeBtn: '',
  getPrizeGrayBtn: '',
  prizeNameColor: '',
  showSkuBg: '',
  priceColor: '',
  btnToShop: '',
  // 是否能关闭入会弹窗
  canNotCloseJoinPopup: 1,
  jumpUrl: '',
  isShowJump: true,
  moreActLink: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  hotZoneList: [],
  hotZoneSetting: {
    bg: '',
    hotZoneList: [],
  },
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const headerBtnRules = computed(() => ({
  backgroundImage: furnish.ruleBg ? `url("${furnish.ruleBg}")` : '',
}));

const headerBtnMyPrizes = computed(() => ({
  backgroundImage: furnish.myPrizeBg ? `url("${furnish.myPrizeBg}")` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const prizeNameColor = computed(() => ({
  color: furnish.prizeNameColor ?? '',
}));

const priceColor = computed(() => ({
  color: furnish.priceColor ?? '',
}));

const getPrizeBtn = computed(() => ({
  backgroundImage: furnish.getPrizeBtn ? `url("${furnish.getPrizeBtn}")` : '',
}));

const getPrizeGrayBtn = computed(() => ({
  backgroundImage: furnish.getPrizeGrayBtn ? `url("${furnish.getPrizeGrayBtn}")` : '',
}));

const showSkuBg = computed(() => ({
  backgroundImage: furnish.showSkuBg ? `url("${furnish.showSkuBg}")` : '',
}));

const btnToShop = computed(() => ({
  backgroundImage: furnish.btnToShop ? `url("${furnish.btnToShop}")` : '',
}));

export default {
  pageBg,
  shopNameColor,
  headerBtnRules,
  headerBtnMyPrizes,
  prizeNameColor,
  getPrizeBtn,
  getPrizeGrayBtn,
  showSkuBg,
  priceColor,
  btnToShop,
};
