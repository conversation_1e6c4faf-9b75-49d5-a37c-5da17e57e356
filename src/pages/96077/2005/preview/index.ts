import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const a = {
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/254544/39/21686/66658/67b29eb2F56635643/5756a4f08cd32f7e.jpg',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/254573/20/21685/71638/67b29eb1F33493e7e/6c5053a97994dcc3.jpg',
  actBgColor: '#d3d779',
  shopNameColor: '#000000',
  btnColor: '#ffffff',
  btnBg: '#d1d479',
  btnBorderColor: '#ffffff',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/104024/39/51067/5518/66f23170Fe7d68af4/670adf4d56282414.png',
  myPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/85841/5/51473/5905/66f23171F15f5f315/a0621a39a21a5b24.png',
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/261856/1/21149/3041/67b3f1c3F9f7172db/1291990005f68468.png',
  getPrizeGrayBtn: '//img10.360buyimg.com/imgzone/jfs/t1/149518/23/42554/12205/66da5e92Fa31db838/b6a7ffd840679bbe.png',
  prizeNameColor: '#ffffff',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/273060/29/9269/41078/67e146abFecc15c22/7a2663ac12faecdf.png',
  priceColor: '#ffffff',
  btnToShop: '//img10.360buyimg.com/imgzone/jfs/t1/262739/4/20604/12311/67b29eb6F77dce11f/0033efdbd13184be.png',
  canNotCloseJoinPopup: '1',
  jumpUrl: '',
  isShowJump: true,
  moreActLink: '',
  hotZoneSetting: {
    bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/276185/7/8875/295366/67e0cbd9F19f2410e/2bacedef1809b03b.png',
    hotZoneList: [],
  },
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/254653/33/21801/236026/67b30680F87f326ce/c862bdc160321ed7.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/268439/13/20908/382925/67b30682F18cfb022/ab050520e7765952.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/254304/9/21759/323912/67b30682F1ab4fd30/fb25c7e8436c8849.png',
  hotZoneList: [],
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '新单有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  // app.provide('decoData', a);
  app.provide('isPreview', true);
  app.mount('#app');
});
