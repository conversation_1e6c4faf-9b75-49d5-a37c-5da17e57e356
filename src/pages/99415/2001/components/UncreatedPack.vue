<template>
  <div class="uncreated-pack">
    <img class="bg-image" src="https://img20.360buyimg.com/imgzone/jfs/t1/307492/24/23024/130670/6892b122F94992fc5/153817a65178b9bf.png" />
    <div class="content">
      <div class="no-pack-tips">
        <div class="no-pack-tips-title">备孕小锦囊</div>
        <div class="no-pack-tips-step">
          <span>Step1:</span>
          <span>填写备孕信息</span>
        </div>
        <div class="no-pack-tips-step">
          <span>Step2:</span>
          <span>生成待产清单，领{{ prizeNames }} </span>
        </div>
      </div>
      <ImageAutoResize imgUrl="https://img20.360buyimg.com/imgzone/jfs/t1/313690/1/23520/126815/6892b122Ff1779095/92fe523337e6f10c.png" @click="() => handleClick()" />
    </div>
  </div>
  <!-- 选择生日/预产期弹窗 -->
  <ChooseBirthDialog v-model:show="showBirthDialog" @next="openBornTypeDialog" />
  <!-- 选择生产方式弹窗 -->
  <ChooseBornTypeDialog v-model:show="showBornTypeDialog" :selectedDate="selectedDate" @prev="backToBirthDialog" @submit="handleSubmitBornType" />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();

import ImageAutoResize from '../components/ImageAutoResize.vue';

import ChooseBirthDialog from './ChooseBirthDialog.vue';
import ChooseBornTypeDialog from './ChooseBornTypeDialog.vue';

const emit = defineEmits(['createPack']);
const props = defineProps<{ prizeNames: string; checkActivityTime: Function; homeData?: any }>();

const showBirthDialog = ref(false); // 控制生日弹窗
const showBornTypeDialog = ref(false); // 控制生产方式弹窗
const selectedDate = ref(''); // 存储上一步选择的日期

const handleClick = async () => {
  await props.checkActivityTime();
  showBirthDialog.value = true;
};

// 打开生产方式弹窗
const openBornTypeDialog = (date: string) => {
  console.log('UncreatedPack openBornTypeDialog', date);
  selectedDate.value = date;
  showBirthDialog.value = false;
  showBornTypeDialog.value = true;
};
// 返回上一步
const backToBirthDialog = () => {
  showBornTypeDialog.value = false;
  showBirthDialog.value = true;
};

// 提交最终结果
const handleSubmitBornType = async (bornType: string) => {
  // showBornTypeDialog.value = false;
  // 这里可以处理selectedDate.value和bornType
  console.log('提交：日期=', selectedDate.value, ' 生产方式=', bornType);

  const params = {
    birthDate: selectedDate.value,
    birthMethod: bornType,
  };
  try {
    showLoadingToast({
      message: '提交中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/99415/pack/create', params);
    console.log('UncreatedPack 生成待产包返回：', data);
    // 通知父组件
    emit('createPack', data);
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  } finally {
    closeToast();
  }
};
</script>

<style scoped lang="scss">
.uncreated-pack {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  .bg-image {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    object-fit: cover; // 铺满不变形
  }

  .content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    padding-top: 1rem;

    .section {
      display: block;
    }
  }

  .no-pack-tips {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 7.5rem;
    height: 1.87rem;
    margin-bottom: 0.7rem;
    padding-left: 2.1rem;
    background-image: url('https://img20.360buyimg.com/imgzone/jfs/t1/293499/32/26406/19282/6892b125F0e0173d2/d34e9d5a5b377774.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }

  .no-pack-tips-title {
    margin-bottom: 0.2rem;
    font-size: 0.32rem;
  }

  .no-pack-tips-step {
    display: flex;
    margin-bottom: 0.08rem;
    font-size: 0.22rem;

    span {
      line-height: 1.1;

      &:first-child {
        width: 0.8rem;
      }

      &:last-child {
        width: 4.1rem;
        padding-left: 0.05rem;
      }
    }
  }
}
</style>
