// 顶部样式
.common-message-header-cpb {
  padding-top: 1.4rem;
  font-size: 0.4rem;
  color: #8d6a35;
  font-weight: bold;
}
// 利用footer来做关闭按钮
.common-message-footer-cpb {
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/238451/21/3203/2852/659be4dbFb70d85c4/f5a4035d532c3e03.png');
  background-size: 100%;
  position: absolute;
  width: 4rem;
  height: 0.7rem;
  right: 0.65rem;
  top: 3.6rem;
}
// 内容
.common-message-content-cpb {
  font-size: 0.28rem;
  line-height: 0.36rem;
  margin-top: 0.3rem;
  color: #8d6a35;
  padding: 0 0.2rem;
}

// 通用主题
.common-message-cpb {
  width: 5.4rem !important;
  height: 5rem !important;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/248767/15/2511/23267/659bdc16F4950db42/156738ef56267dfc.png') no-repeat !important;
  background-size: 100% 100% !important;

  .van-dialog__header {
    @extend .common-message-header-cpb;
  }
  .van-dialog__footer {
    @extend .common-message-footer-cpb;
    .van-dialog__confirm {
      color: transparent;
      background: transparent;
    }
  }
  .van-dialog__message {
    @extend .common-message-content-cpb;
  }
}
