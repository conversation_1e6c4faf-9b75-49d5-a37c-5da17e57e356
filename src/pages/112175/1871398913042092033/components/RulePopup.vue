<template>
  <div class="rule-bk" >
    <div class="rule">
      <div v-html="rule"></div>
    </div>
    <div class="close"  @click="closeRule"></div>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref } from 'vue';

const emits = defineEmits(['close']);
const rule = ref('');
const closeRule = () => {
  emits('close');
};

const getRule = async () => {
  // rule.value = '测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测测试规则规则贵测';
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    closeToast();
    const res = await httpRequest.get('/common/getRule');
    rule.value = res.data;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
getRule();
</script>

<style scoped lang="scss" >
::-webkit-scrollbar {
  display: none;
}
.rule-bk {
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/267973/38/1230/58795/6766c8c4F577ffbc8/d12155223b5ae221.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.5rem;
  height: 8rem;
  padding-top: 1.6rem;
  padding-bottom: 0.3rem;
  position: relative;
}
.rule {
  height: 3.8rem;
  width: 6.4rem;
  padding: 0 0.3rem;
  font-size: 0.24rem;
  //color: #a5bf1a;
  white-space: pre-wrap;
  word-break: break-all;
  position: absolute;
  top: 2.5rem;
  left: 50%;
  transform: translateX(-50%);
  div {
    height: 100%;
    overflow-y: scroll;
  }
}
.close {
  width: 0.9rem;
  height: 0.9rem;
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  bottom: 0.3rem;
  //background: #2d8cf0;
}
</style>
