<!--
 * @actName:
 * @author: <PERSON><PERSON><PERSON> lin
-->
<template>
  <template v-if='pathParams?.reservation'>
    <div>
      <img :src='configData.Reminder.reservePageImg' style='width: 7.5rem;marginTop:.2rem' @click='goToReminder()' alt=''>
    </div>
  </template>
  <template v-else>
    <!-- background -->
    <div class='background' :style='{backgroundImage:`url(${configData.KV.bgImg})`}'>
      <div class='kv-module' :style='{backgroundImage:`url(${userMemberInfo.bgImg ?? DEFAULT_INFO.kvBg})`}'>
        <div class='user-info-view'>
          <div class='user-img'>
            <img :src='userMemberInfo.headerImg ?? DEFAULT_INFO.headImg' alt='' />
          </div>
          <div class='user-info'>
            <div class='user-nick one-line-omit' :style='{color:`${configData.KV.nickTextColor}`}'>{{ userMemberInfo.nickname }}</div>
            <div class='user-level' :style='{color:`${configData.KV.levelTextColor}`}'>{{ userMemberInfo.vipLevelName ?? '--' }}</div>
          </div>
        </div>

        <div class='pos-view'>
          <div class='user-point-view' @click='goToUrl(`https://shop.m.jd.com/member/scores?venderId=${baseInfo.venderId??baseInfo.shopId}&shopId=${baseInfo.shopId}`)'>
            <div class='user-level' :style='{color:`${configData.KV.levelTextColor}`}'>
              {{ userMemberInfo.vipLevel ?? '--' }}
            </div>
            <div class='user-point'>
              我的积分
              <br />
              <span style='font-size: .66rem;letter-spacing: -0.05rem' :style='{color:`${configData.KV.pointTextColor}`}'>{{ userMemberInfo.pointNum ?? 0 }}</span>
            </div>
          </div>

          <img style='width: 2.32rem;margin: .16rem auto 0' :src='configData.memberPageData.memberEquityBtn' @click='goToUrl(configData.memberPageData.memberEquityLink)' alt=''>
        </div>
        <img class='ip-img' :src='userMemberInfo.dyCardImg?? DEFAULT_INFO.skin' alt=''>
      </div>


      <div class='tab-module' :style='{backgroundImage:`url(${configData.Tab.bgImg})`}'>
        <template v-for='(item,index) in configData.Tab?.tabBtnImgList' :key='index'>
          <img :src='getTabImage(item,index)' @click='selectTab(index)' class='tab-icon-item' alt='' />
        </template>
      </div>

      <div class='profile-module'>
        <template v-if='currentTabIndex===0'>
          <div class='tab-icon-view'>
            <template v-for='(item,index) in configData.Profile?.tabBtnImgList' :key='index'>
              <img :src='getGenderImage(item,index)' alt='' style='width: .6rem' @click='selectGender(item)'>
            </template>
          </div>

          <div class='list-view'>
            <div class='list-item' v-for='(item,index) in IPList'>
              <div class='ip-bg' :style='{backgroundImage:`url(${configData.Profile.skinCardBgImage})`}'>
                <img :src='item.stCardImg' class='ip-img' alt=''>
                <div class='ip-name' :style='{color: `${configData.Profile.skinDrawBtnTextColor}`}'>{{ item.content }}</div>
              </div>
              <div class='ip-btn' :class='{gray:item.buttonStatus>6}' @click='handleDrawSkin(item)' :style='{backgroundImage:`url(${configData.Profile.skinDrawBtnImage})`, color: `${configData.Profile.skinDrawBtnTextColor}`}'>
                {{ getDrawBtnStatus(item) }}
                <img style='width: .5rem;margin-left: .1rem' v-if='item.buttonStatus<=5' src='//img10.360buyimg.com/imgzone/jfs/t1/341721/5/4867/3787/68cbd280Fa4aac969/f5a594c1ba8747e4.png' alt=''>
              </div>
            </div>
          </div>

          <div class='pointsGuide-module' :style='{backgroundImage:`url(${configData.Profile.hotZone.repeatBg})`}'>
            <div v-for='(item,index) in configData.Profile.hotZone.repeatHotZoneList' v-click-track="{ c: JSON.stringify({ code: 'llgl', value: item.rectId }) }" :key='index' @click='goToUrl(item.url)' class='hotZone-item' :style='{left: `${item.left}px`,top: `${item.top}px`,width: `${item.width}px`, height: `${item.height}px`}'></div>
          </div>
        </template>

        <template v-if='currentTabIndex===1'>
          <div class='list-view'>
            <div class='list-item' v-for='(item,index) in backgroundList' :key='index'>
              <div class='bg-bg' :style='{backgroundImage:`url(${configData.Profile.bgCardBgImage})`}'>
                <img :src='item.backgroundImg' class='bg-img' alt=''>
              </div>
              <div class='ip-btn' @click='updateCustomConfig(item, 1)' :style='{backgroundImage:`url(${item.isSelect===1?configData.Profile.bgCurrentBtnImage:configData.Profile.bgBtnImage})`}'></div>
            </div>
          </div>
        </template>

        <template v-if='currentTabIndex===2'>
          <div class='list-view'>
            <div class='list-item' style='height: 2.8rem' v-for='(item,index) in configData.Profile.skuList' :key='index'>
              <div class='sku-bg' :style='{backgroundImage:`url(${configData.Profile.skuCardBgImage})`}' @click='gotoSkuPage(item.skuId)'>
                <div class='sku-name' :style='{color: `${configData.Profile.skuNameTextColor}`}'>
                  {{ item.skuName }}
                </div>
                <img :src='item.skuImage' class='sku-img' alt='' />
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </template>


  <Popup class='popup' v-model:show='dialogShow' :close-on-click-overlay='false'>
    <SureExchangeDialog v-if="dialogName === 'sureExchangeDialog'" :skinInfo='currentDrawSkin' @sureDrawSkin='sureDrawSkin' :configData='configData.memberPageData'></SureExchangeDialog>
    <SuccessExchangeDialog v-if="dialogName === 'successExchangeDialog'" :skinInfo='currentDrawSkin' @wearSkin='wearSkin' :configData='configData.memberPageData'></SuccessExchangeDialog>
  </Popup>

</template>

<script lang='ts' setup>
import { ref, inject, Ref, onMounted, reactive, nextTick } from 'vue';
import { Toast, Popup, showToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import type { IConfigData, IActivityData, IUserMember } from './ts/type';
import { gotoSkuPage, gotoShopPage } from '@/utils/platforms/jump';
import { DEFAULT_INFO } from './ts/utils';

/* ---------------------------------  接口  ------------------------------- */
import { getDataInterface, setBaseInfo } from './ts/port';
import { dialogName, dialogShow, openDialog, closeDialog } from './ts/dialog';
import SureExchangeDialog from './components/SureExchangeDialog';
import SuccessExchangeDialog from './components/SuccessExchangeDialog';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import dayjs from 'dayjs';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
setBaseInfo(baseInfo);
const pathParams = inject('pathParams') as any;
/* -------------------------------------------------------------------------- */
const configData = reactive({}) as IConfigData;
const activityData = reactive({}) as IActivityData;
const userMemberInfo = reactive({}) as IUserMember;

const currentTabIndex = ref(0);
const currentGender = ref(0);


const goToReminder = async () => {
  try {
    await getDataInterface('subscribe', { activityName: configData.Reminder.reserveActName });
  } catch (e) {
    console.log(e);
  }
  lzReportClick({
    code: 'yuyue',
  });
  try {
    // @ts-ignore
    window.jmfe.addReminder({
      id: baseInfo.activityId, //可以使用相关活动id、页面id、广告组id或广告id等
      tag: '预约活动',
      time: dayjs(configData.Reminder.reserveTime).valueOf(), //可以使用Date对象的getTime方法获取，例如：new Date().getTime()
      title: configData.Reminder.reserveActName,
      type: 'HUODONG',
      url: configData.Reminder.reserveUrl, //支持h5页面地址或jump协议对象
    }).then(({ appointed }) => {
      if (appointed) {
        showToast('预约活动成功');
      } else {
        console.log('预约失败', 'appointed:', appointed);
      }
    });
  } catch (e) {
    console.log(e);
  }
};


const goToUrl = (url: string) => {
  if (url) {
    window.location.href = url;
  }
};

const getDrawBtnStatus = (item: any) => {
  switch (item.buttonStatus) {
    case 0:
      return '已领取';
    case 1:
      return '立即领取';
    case 2:
      return '去下单';
    case 3:
      return '去兑换';
    case 4:
      return '已装扮';
    case 5:
      return '已解锁';
    case 6:
      return `${item.exchangePointNum}积分兑换`;
    case 7:
      return `${item.exchangePointNum}积分解锁`;
    case 8:
      return `V${Number(item.prizeManyLevel.split(',')[0])}可兑换`;

    default:
      return '';
  }
};

const getTabImage = (iconVo: any, index: number) => {
  if (index === currentTabIndex.value) {
    return iconVo.activeIcon;
  }
  return iconVo.icon;
};

const getGenderImage = (iconVo: any, index: number) => {
  if (iconVo.iconIndex === currentGender.value) {
    return iconVo.activeIcon;
  }
  return iconVo.icon;
};

// 主接口
const activityContent = async () => {
  const res = await getDataInterface('activityContent');
  if (res.result) {
    Object.assign(activityData, res.data);
  }
};

const wearSkin = async () => {
  // 去装扮
  await updateCustomConfig(currentDrawSkin.value, 0);
  await getIPList(currentGender.value);
  closeDialog();
};

const currentDrawSkin = ref({});
const sureDrawSkin = async () => {
  const res = await getDataInterface('pointsExchangeSkin', { cardId: currentDrawSkin.value.id, type: 1 });
  if (res.result) {
    openDialog('successExchangeDialog');

    await getIPList(currentGender.value);
  }
};


// 通用配置接口
const getActivityConfig = async () => {
  lzReportClick({
    code: 'myTwo',
  });
  const memberPageData = await getDataInterface('getDecorationData', { pageType: 1 });
  const res = await getDataInterface('getDecorationData', { pageType: 2 });
  if (res.result) {
    Object.assign(configData,
      {
        KV: JSON.parse(res.data.filter((i: any) => i.moduleId === 1)[0].decorationData).jsonData,
        Tab: JSON.parse(res.data.filter((i: any) => i.moduleId === 2)[0].decorationData).jsonData,
        Profile: JSON.parse(res.data.filter((i: any) => i.moduleId === 3)[0].decorationData).jsonData,
        Reminder: JSON.parse(memberPageData.data.filter((i: any) => i.moduleId === 4)[0].decorationData).jsonData,
        memberPageData: JSON.parse(memberPageData.data.filter((i: any) => i.moduleId === 1)[0].decorationData).jsonData,
      });
    console.log(configData);

    await getMemberInfo();
    await selectGender(configData.Profile?.tabBtnImgList[0]);
  }
};

// 获取会员信息
const getMemberInfo = async () => {
  const res = await getDataInterface('getMemberInfo', { pageType: 2 });
  if (res.result) {
    Object.assign(userMemberInfo, res.data);
  }
};

// 获取背景列表
const backgroundList = ref([]);
const getBackgroundList = async () => {
  const res = await getDataInterface('getBackgroundList', { pageType: 2 });
  if (res.result) {
    backgroundList.value = res.data;
  }
};

// 获取IP形象列表
const IPList = ref([]);
const getIPList = async (gender: number) => {
  const res = await getDataInterface('getIPList', { type: 1, gender });
  if (res.result) {
    IPList.value = res.data;
  }
};

// 更新装修信息（ip形象：0，背景：1）
const updateCustomConfig = async (item: any, type: number) => {
  if (type === 1 && item.isSelect === 1) {
    // 如果是 背景 && 已选择 重复点击不操作
    return;
  }
  const res = await getDataInterface('updateCustomConfig', { id: item.id, type });
  if (res.result) {
    await getMemberInfo();
    setTimeout(() => {
      showToast('装扮成功');
    }, 300);
    if (type === 1) {
      await getBackgroundList();
    } else if (type === 0) {
      await getIPList(currentGender.value);
    }

  }
};

const handleDrawSkin = (item: any) => {
  currentDrawSkin.value = item;
  if (item.buttonStatus === 2) {
    // 跳转会员中心
    // window.jmfe.toShop('1000429402');
    // gotoShopPage(1000429402);
    window.location.href = 'https://shop.m.jd.com/member/home?shopId=1000429402&venderId=1000429402&sceneval=2&venderType=1&channel=406'
  } else if (item.buttonStatus === 5) {
    // 去装扮
    updateCustomConfig(currentDrawSkin.value, 0);
  } else if (item.buttonStatus === 6) {
    openDialog('sureExchangeDialog');
  } else if (item.buttonStatus === 7) {
    showToast('抱歉，您的积分不足，快去赚取更多积分兑专属形象~');
  } else if (item.buttonStatus === 8) {
    showToast('抱歉，您的等级不足，快去赚取更多积分兑专属形象~');
  }
};

// 切换形象列表
const selectGender = async (genderVo: any) => {
  currentGender.value = genderVo.iconIndex ?? 0;
  await getIPList(genderVo.iconIndex ?? 0);
};

// 切换Tab
const selectTab = async (tabIndex: number) => {
  currentTabIndex.value = tabIndex;
  if (tabIndex === 0) {
    await getIPList(currentGender.value);
  } else if (tabIndex === 1) {
    await getBackgroundList();
  } else if (tabIndex === 2) {

  }
};


getActivityConfig();

</script>

<style lang='scss'>
.gray {
  /*grayscale(val):val值越大灰度就越深*/
  -webkit-filter: grayscale(100%) brightness(1);
  -moz-filter: grayscale(100%) brightness(1);
  -ms-filter: grayscale(100%) brightness(1);
  -o-filter: grayscale(100%) brightness(1);
  filter: grayscale(100%) brightness(1);
  filter: gray brightness;
}

#app .van-popup {
  background-color: transparent;
}
</style>
