<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }" @click="onSelected(1)">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/139876/9/21637/89337/619f33f3E28ef638d/7aa9c2fad2d8275b.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="rulePopup = true"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="myPrizePopup = true"><div>我的奖品</div></div>
        </div>
      </div>
      <div class="winener-list" v-if="winnerList.length">
        <van-swipe
          :loop="true"
          :autoplay="2000"
          style="height: 0.6rem"
          vertical
          :show-indicators="false"
        >
          <div v-for="(item,index) in winnerList" :key="index">
            <van-swipe-item>
              <div class="winner-item">
                恭喜{{item.userName}}获得{{item.prizeName}}
              </div>
            </van-swipe-item>
          </div>
        </van-swipe>
      </div>
    </div>
    <div :class="{ 'on-select': selectedId === 2 }" @click="onSelected(2)">
      <div>
        <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart" :receiveStartTime="receiveStartTime"/>
      </div>
      <div class="calendar-bg" :style="furnishStyles.calendarBg.value">
        <div class="sign-in-area">
          <img :src="furnish.signInBeforeIcon" alt="" class="icon" />
          <div class="text">
            <p class="title title-before">今日未签到</p>
            <p class="tip">赶紧点击按钮签到哦~</p>
          </div>
          <img :src="furnish.signInBeforeBt" alt="" class="btn" @click="toSign" />
        </div>
        <div class="info">
          <div>
            累计签<span>{{ signDays }}</span
          >天
          </div>
          <div class="now">{{ dayjs().format('YYYY年MM月DD日') }}</div>、
          <div>
            再签<span>{{ nextSignDays }}</span>天可领奖
          </div>
        </div>
        <Calendar></Calendar>
      </div>
    </div>
    <div class="draw-btn" >
      <div class="select-hover prize-select-box" :class="{ 'on-select': selectedId === 3 }" @click="onSelected(3)">
        <div class="prizes"
             :style="{'background-image':'url('+furnish.prizeBg ?? `https://img10.360buyimg.com/imgzone/jfs/t1/225772/13/1562/67020/6541b15aF50956eed/af8fcb05fc4e6e5a.png`+`)`}"
        >
          <div class="gift-show" v-for="(item,index) in prizeList" :key="index">
            <div class="gift-img">
              <div class="img-box">
                <img class="imgs" :src="item.prizeImg ? item.prizeImg : 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png'" alt="">
              </div>
              <div class="back">{{item.prizeName}}</div>
            </div>
            <div class="gift-info">
              <div :style="furnishStyles.prizeNameColor.value">{{item.prizeName}}</div>
              <div class="remain-prize" :style="furnishStyles.prizeNameColor.value">奖品剩余：{{item.remainCount || item.sendTotalCount || 'xx'}}份</div>
            </div>
            <div class="get-prize-btn" @click="toSign">
              立即领取
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="prizeTipPopup" position="bottom" :closeOnClickOverlay="false"><PrizeTip @close="prizeTipPopup = false"></PrizeTip></VanPopup>
  <VanPopup teleport="body" v-model:show="rulePopup" position="bottom" :closeOnClickOverlay="false">
    <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="myPrizePopup" position="bottom" :closeOnClickOverlay="false">
    <MyPrize @close="myPrizePopup = false"></MyPrize>
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useSendMessage from '@/hooks/useSendMessage';
import { showToast } from 'vant';
import Calendar from '../components/Calendar.vue';
import PrizeTip from '../components/PrizeTip.vue';
import MyPrize from '../components/MyPrize.vue';
import Rule from '../components/Rule.vue';
import dayjs from 'dayjs';
import CountDown from '../components/CountDown.vue';
import { defaultStateList, prizeType } from '../ts/default';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const shopName = ref('xxx京东旗舰店');

const isLoadingFinish = ref(false);

const continuousSignDays = ref(0); // 连续签到天数
const signDays = ref(0); // 签到总天数
const nextSignDays = ref(0);
const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const prizeTipPopup = ref(false);

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const receiveStartTime = ref(0);

const toSign = () => {
  showToast('活动预览，仅供查看');
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  useSendMessage('deco', 'changeSelect', id);
  selectedId.value = id;
};

const winnerList = ref([
  {
    userName: '用户xxxx',
    prizeName: '1积分',
  },
  {
    userName: '用户xxxx',
    prizeName: '充电宝',
  },
  {
    userName: '用户xxxx',
    prizeName: '3积分',
  },
]);

// type Prize = {
//   prizeImg: string;
//   prizeType: number;
//   prizeName: string;
//   stepAmount: number;
//   remainCount: number;
//   sendTotalCount: number;
// };

const prizeList = ref(defaultStateList);

const createImg = async () => {
  rulePopup.value = false;
  // showGoods.value = false;
  // showOrderRecord.value = false;
  // showOrderRecord.value = false;
  // showLimit.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleText.value = data.rules;
  shopName.value = data.shopName;
  console.log(data.prizeList, 'data.prizeList');
  const efficientPrizeList = data.prizeList.filter((e: { prizeType: number }) => e.prizeType);
  if (efficientPrizeList.length) {
    prizeList.value = efficientPrizeList;
  } else {
    prizeList.value = defaultStateList;
  }
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    prizeList.value = activityData.prizeList;
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    showToast('活动预览，仅供查看');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .winener-list{
    height: 0.6rem;
    color: #ffffff;
    z-index: 20;
    background: rgba(0,0,0,0.5);
    border-radius: 0.5rem;
    padding: 0.12rem 0.33rem;
    width: 3.2rem;
    font-size: 0.24rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    position: absolute;
    top: 0.7rem;
    left: 0.18rem;
    .winner-item{
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.calendar-bg {
  background-repeat: no-repeat;
  background-size: 100%;
  width: 6.9rem;
  height: 9.2rem;
  margin: 0.6rem auto 0;
  padding: 0.15rem;
  .sign-in-area {
    height: 2.6rem;
    display: flex;
    align-items: center;
    padding: 0 0.3rem;
    .icon {
      width: 1.4rem;
    }
    .text {
      flex: 1;
      padding-left: 0.26rem;
      .title {
        font-size: 0.36rem;
        color: rgb(139, 133, 255);
        font-weight: bold;
      }
      .title-before {
        color: rgb(242, 39, 12);
      }
      .tip {
        font-size: 0.24rem;
        color: rgb(140, 140, 140);
      }
    }
    .btn {
      width: 1.5rem;
    }
  }
  .info {
    margin-top: 0.05rem;
    height: 1.1rem;
    padding: 0.1rem 0.2rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: rgb(140, 140, 140);
    font-size: 0.24rem;
    .now {
      font-size: 0.34rem;
      color: rgb(38, 38, 38);
      font-weight: bold;
    }
    span {
      color: rgb(242, 39, 12);
    }
  }
}
.draw-btn {
  width: 6.9rem;
  margin: 0.4rem auto 0 auto;
  img {
    width: 100%;
  }
}
.prizes {
  margin: 0.4rem auto 0 auto;
  border-radius: 0.3rem;
  background-repeat: no-repeat;
  background-size: 100%;
  //height: 3.71rem;
  padding-top: 0.72rem;
  background-color: #ffffff;

  .gift-show {
    margin-left: 0.6rem;
    margin-top: 0.54rem;
    margin-right: 0.26rem;
    padding-bottom: 0.22rem;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    font-size: 0.25rem;
    align-items: center;

    .gift-img {
      width: 2rem;
      height: 2rem;
      flex:1;
      border: 0.02rem solid #ff3633;
      margin: 0 0.3rem 0 0;
      border-radius: 0.16rem;
      overflow: hidden;
      .img-box{
        width: 2rem;
        height: 2rem;
        margin: 0 auto;
        .imgs{
          width: 2rem;
          height: auto;
          border-radius: 0.16rem;
          margin: 0 auto;
        }
      }
      .back{
        width: 2.1rem;
        height: 0.7rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png);
        background-size: 100%;
        background-repeat: no-repeat;
        position: relative;
        top:-0.7rem;
        left: 0;
        text-align: center;
        padding: 0.32rem 0.1rem 0 0.1rem;
        font-size: 0.24rem;
        color: #fff;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .gift-info {
      -webkit-box-align: end;
      align-items: flex-end;
      flex:1.1;
      text-align: left;
      .remain-prize{
        color: #999999;
        margin: 0.6rem 0 0 0;
      }
    }

    .get-prize-btn {
      width: 1.5rem;
      height: 0.5rem;
      background: linear-gradient(90deg, rgb(242, 39, 12) 0%, rgb(255, 100, 32) 100%);
      border-radius: 0.25rem;
      font-size: 0.24rem;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      color: rgb(255, 255, 255);
      text-align: center;
      line-height: 0.5rem;
      position: relative;
      top: 0.5rem;
      right: 0.4rem;
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
