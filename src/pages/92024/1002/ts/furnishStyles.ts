import { computed, reactive } from 'vue';

export const furnish = reactive({
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/225907/4/1703/146102/654209bcF54fbc0cc/5f39c38175a2cbb0.png',
  pageBg: '',
  actBgColor: '#ff6775',
  disableShopName: 0,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

export default {
  pageBg,
};
