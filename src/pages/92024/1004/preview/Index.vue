<template>
  <div class="relative" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="h-screen overflow-auto">
      <div class="relative header-kv">
        <div class="box-border flex flex-row justify-between z-10 absolute w-full top-3 px-3">
          <div class="text-white text-sm"></div>
          <div class="btnAllClass flex flex-col gap-y-2">
            <div
                class="btnClass"
                v-for="(btn, index) in btnList"
                :style="{backgroundImage:`url(${btn.btnBgUrl})`}"
                :key="index"
                @click="btn.event"
            ></div>
          </div>
        </div>
        <img class="w-full"
             :src="furnish.actBg" alt="">
      </div>
      <div class="py-3.5">
        <div class="mx-2 relative countDownDivAll">
          <img class="w-full" :src="IMAGE_MAP.COUNT_DOWN_BG" alt="">
          <div class="text-red-class text-xs absolute leading-[0.32rem]">距活动结束剩余：</div>
          <div class="count-down">{{countdownTime}}</div>
        </div>
        <div class="goBuyClass" @click="toast">
          <img :src="IMAGE_MAP.IMMEDIATELY_PAY" alt="">
        </div>
        <div class="pl-2 mt-3.5 pr-3 prizeListAllClass">
          <div class="prizeListClass">
            <div class="flex flex-row items-center">
            <div class="relative">
              <img class="h-4 mr-2.5 " :src="IMAGE_MAP.STEP_ICON" alt="">
              <ul class="process processDiv" v-if="prizeList.length">
                <li v-for="item in 9" :key="item"></li>
              </ul>
            </div>
            <div class="bg-white w-full rounded px-5 py-1 chat relative">
              <div class="text-xs font-bold mb-0.5 blue-tips">您当前累计消费 <span :style="{color:'#ae1c1b'}" class="mx-0.5">0</span> 元，距离下一个大奖还差<span :style="{color:'#ae1c1b'}" class="mx-0.5">{{nextStateAmount.toFixed(2)}}</span>元</div>
              <div class="text-xs" :style="{color:'#ae1c1b'}">(确认收货后才会发放对应礼品)</div>
            </div>
          </div>
            <div class="flex flex-row items-center mt-3" v-for="(item, index) in prizeList" :key="index">
              <div class="relative">
                <div class="circle mr-2.5 ml-0.5"></div>
                <ul class="process" v-if="index !== prizeList.length - 1" >
                  <li v-for="item in 14" :key="item"></li>
                </ul>
              </div>
              <div class="bg-white w-full rounded p-2 chat relative">
                <div class="flex flex-row relative">
                  <div class="prizeImgDivAll">
                    <img class="prizeImgDiv" :src="item.prizeImg" alt="">
<!--                    <img class="bottom-0 absolute w-24" :src="IMAGE_MAP.PRIZE_BORDER" alt="">-->
<!--                    <div class="absolute bottom-0 text-center w-24 text-white text-xs">{{prizeType[item.prizeType]}}</div>-->
                  </div>
                  <div class="flex-1 ml-3 mr-1 flex flex-col justify-around">
                    <div class="font-bold tracking-widest text-base" :style="{color:'#ae1c1b'}">{{item.prizeName}}</div>
                    <div class="text-xs font-bold blue-tips">满{{item.stepAmount || 'x'}}元可领奖品</div>
                    <div class="flex flex-row text-xs justify-between items-center">
                      <div class="text-gray-800">奖品剩余：{{item.remainCount || item.sendTotalCount || 'xx'}}份</div>
                      <div class="receive-btn" @click="toast">点击领取</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>
      </div>
      <div class="skuListDiv p-3.5 relative" v-if="skuList.length">
        <img :src="IMAGE_MAP.HOT_ITEM_BG" alt="">
        <div class="gridDiv grid grid-cols-2 gap-1.5 absolute pl-4 pr-7 overflow-auto">
          <div v-for="(item, index) in skuListPreview" class="gridItemDiv" :key="index">
            <div class="skuImgDiv flex justify-center">
              <img class="skuImg" :src="item.skuMainPicture" alt="">
            </div>
            <div class="skuBottomDiv">
              <div class="text-xs lz-multi-ellipsis--l2">{{item.skuName}}</div>
              <div class="skuPriceDiv text-xs">¥ <span style="font-size:0.3rem;font-weight:bold">{{item.jdPrice}}</span>券后价</div>
            </div>
          </div>
          <div class="more-btn" v-if="skuList.length > 18" @click="toast">点我加载更多</div>
        </div>
      </div>

    </div>
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showReceiveRecord" position="bottom">
      <ReceiveRecordPopup @close="showReceiveRecord = false"></ReceiveRecordPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
      <GoodsPopup :data="orderSkuListPreview" :orderSkuList="orderSkuList" :pagesAllOrder="0" @close="showGoods = false"></GoodsPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList, IMAGE_MAP, prizeType } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import ReceiveRecordPopup from '../components/ReceiveRecordPopup.vue';
import GoodsPopup from '../components/GoodsPopup.vue';
import AwardPopup from '../components/AwardPopup.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useCountdown from '@/hooks/useCountdown';
import { showToast } from 'vant';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();
// 默认设置结束时间戳为1小时后
const endTime = ref(dayjs().add(30, 'day').valueOf());
const countdownTime = useCountdown(endTime);

const isLoadingFinish = ref(true);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
}
const prizeList = ref<Prize[]>(defaultStateList);
type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuListPreview = ref<Sku[]>([]);
const skuList = ref<Sku[]>([]); // 曝光商品
const orderSkuListPreview = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);

const nextStateAmount = ref(0);
const isAllOrderSku = ref(1);
const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
const showOrderRecord = ref(false);
const showReceiveRecord = ref(false);
const showGoods = ref(false);
const showAward = ref(false);

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};
const btnList: {
  name: string;
  btnBgUrl: string;
  event?: () => void;
}[] = [
  { name: '活动规则',
    btnBgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/219290/38/39501/1461/663b3577F98757ff5/285a1bf087c4407b.png',
    event: () => { showRule.value = true; } },
  { name: '订单记录',
    btnBgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/228381/8/16190/1648/663b3577F0176e8d4/5827824a01da1baa.png',
    event: () => { showOrderRecord.value = true; },
  },
  { name: '领奖记录',
    btnBgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/103294/15/24760/1695/663b3577F856f1190/2514500218007815.png',
    event: () => { showReceiveRecord.value = true; } },
  { name: '活动商品',
    btnBgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/223326/2/40221/1412/663b3577Ffa791afe/f896953e4b7c64b8.png',
    event: () => { showGoods.value = true; },
  },
];

const close = () => {
  showLimit.value = false;
};
const createImg = async () => {
  showRule.value = false;
  showGoods.value = false;
  showOrderRecord.value = false;
  showOrderRecord.value = false;
  showLimit.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  const efficientPrizeList = data.prizeList.filter((e: { prizeType: number }) => e.prizeType);
  if (efficientPrizeList.length) {
    prizeList.value = efficientPrizeList;
    nextStateAmount.value = prizeList.value[0].stepAmount || 0;
  } else {
    prizeList.value = defaultStateList;
  }
  if (data.skuList) {
    skuListPreview.value = data.skuListPreview;
    skuList.value = data.skuList;
  }
  if (data.orderSkuList) {
    orderSkuList.value = data.orderSkuList;
    orderSkuListPreview.value = data.orderSkuListPreview;

  }
  ruleTest.value = data.rules;
  isAllOrderSku.value = data.isAllOrderSku;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    prizeList.value = activityData.prizeList;
    ruleTest.value = activityData.rules;
    orderSkuList.value = activityData.orderSkuList;
    orderSkuListPreview.value = activityData.orderSkuListPreview;
    endTime.value = dayjs(activityData.endTime).valueOf();
    nextStateAmount.value = prizeList.value[0].stepAmount || 0;
    shopName.value = activityData.shopName;
    skuList.value = activityData.skuList;
    skuListPreview.value = activityData.skuListPreview;

  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style scoped lang="scss">
.header-kv{
  .btnAllClass{
    margin-top: 2rem;
    .btnClass{
      width: 1.23rem;
      height: 0.31rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
}
.countDownDivAll{
  position:relative;
  top: -1.95rem;
  .text-red-class{
    color: #052451;
    top: 0.38rem;
    left:1.22rem;
  }
  .count-down{
    position:absolute;
    top:0.3rem;
    right:0.8rem;
    font-size:0.24rem;
    background: rgba(255, 255, 255, 0);
  }
}
.goBuyClass{
  width: 3.66rem;
  height: 0.81rem;
  margin-bottom:0.6rem;
  margin-left: calc(50% - 3.66rem / 2);
}
.prizeListAllClass{
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/162727/27/25468/18773/663b3576F627a7ee3/7525d2f14759aaf3.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 7.11rem;
  height: 7.88rem;
  margin-left: calc(50% - 7.11rem / 2);
  padding-top: 1.3rem;
  .prizeListClass{
    max-height: 5.71rem;
    overflow-y: scroll;
    margin: 0 0.16rem;
    .blue-tips{
      color: #052451;
    }
    .prizeImgDivAll{
      //background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/224935/30/9869/20695/6593bcc4F51119329/eb8edc8f2917aa89.png");
      //background-repeat: no-repeat;
      //background-size: 100% 100%;
      width: 2.14rem;
      height: 2.14rem;
      display: flex;
      justify-content:center;
      .prizeImgDiv{
        width: 2.14rem;
        height: 2.14rem;
      }
    }
    .text-gray-800{
      font-size: 0.17rem;
    }
    .receive-btn{
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/195827/25/44406/842/663b3577F1fe4b7db/3c1b159b111a917b.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 1.42rem;
      height: 0.44rem;
      font-size:0.21rem;
      color: #a91a19;
      font-weight: bold;
    }
  }
}
.skuListDiv{
  .gridDiv{
    max-height: 8.25rem;
    top: 1.6rem;
    .gridItemDiv{
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/228890/24/17771/3174/663b4215Fd4323990/bbf3a783c8f29ad5.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 3.07rem;
      height: 4.42rem;
      padding:0 0 0.28rem;
      .skuImgDiv{
        width:2.9rem;
        margin:0.1rem auto 0;
        .skuImg{
          border-radius: 0.16rem 0.16rem 0 0;
          height: 2.9rem;
        }
      }
      .skuBottomDiv{
        border-radius: 0 0 0.16rem 0.16rem;
        width: 2.52rem;
        margin: 0 auto;
        color: #052451;
        .skuPriceDiv{
      margin-top: 0.12rem;
      color: #ae1c1b;
    }
  }
}
}
}
.more-btn {
  width: 1.8rem;
  height: 0.5rem;
  font-size: 0.2rem;
  color: #fff;
  background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
  background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
  border-radius: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 0.3rem;
  margin-left:70%;

}
.my-1{
  width:6.84rem;
}
</style>
