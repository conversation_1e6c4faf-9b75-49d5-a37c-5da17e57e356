import { computed, reactive } from 'vue';

export const furnish = reactive({
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/114867/33/33101/60177/64127a84Fe8217081/23ad880d780bc7b6.png',
  pageBg: '',
  actBgColor: '#F7D8D5',
  disableShopName: 0,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

export default {
  pageBg,
};
