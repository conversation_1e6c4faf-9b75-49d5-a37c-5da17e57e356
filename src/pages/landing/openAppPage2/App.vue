<template>
  <div class="app">
    <div class="open-title" @click="openappfun" v-show="isMobile && !isWeChatOrWeibo">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/61969/13/3552/18361/5d1c37a2E6a1811d2/b3a056ac7eea7a26.jpg">
    </div>
    <div class="textdiv">亲爱的，由于您的浏览器限制，无法直接启动京东APP打开链接，需要您选择以下方式进行打开：</div>
    <div class="bluetip">用京东APP扫一扫打开</div>
    <div class="textdiv textdiv-second" v-show="!isPc">长按二维码，保存二维码到相册。</div>
    <div id="qrcodegroup" ref="qrcodegroup"></div>
    <div class="qrcode"><img :src="qrcodeImgUrl"></div>
    <div class="jump-to-shop" @click="jumpToShop" v-show="showJumpToShop">去店铺逛逛</div>
    <div class="mask-overlay" v-show="isWeChatBrowser"></div>
    <img class="wechat-na" src="//img10.360buyimg.com/imgzone/jfs/t1/292943/10/10071/87314/683ed69bF166ab0ac/ed4d7fbeba2ce98a.png" alt="" v-show="isWeChatBrowser">
  </div>
</template>

<script>
import QRCode from 'qrcode';

export default {
  name: 'OpenAppPage',
  data() {
    return {
      acturl: '',
      acturldecode: '',
      hostnamne: '',
      isPc: false,
      isMobile: false,
      isWeChatOrWeibo: false,
      isWeChatBrowser: false,
      qrcodeImgUrl: '',
      showJumpToShop: false,
      shopid: ''
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    getUrlQueryValueByKey(url, keyName) {
      const reg = new RegExp('(^|[&?])' + keyName + '=([^&]*)(&|$)');
      const r = url.match(reg);
      if (r != null) {
        return unescape(r[2]);
      }
      return null;
    },

    isPC() {
      const p = navigator.platform;
      const pc1 = p.indexOf('Win') === 0;
      const pc2 = p.indexOf('Mac') === 0;
      return pc1 || pc2;
    },

    isMobileBrowser() {
      const mobileKeywords = ['Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry', 'Windows Phone', 'Opera Mini', 'IEMobile'];
      return mobileKeywords.some(keyword => navigator.userAgent.includes(keyword));
    },

    isWeChatOrWeiboApp() {
      const ua = navigator.userAgent.toLowerCase();
      return ua.includes('micromessenger') || ua.includes('weibo');
    },

    isWechatMiniProgram() {
      const ua = navigator.userAgent.toLowerCase();
      return ua.includes('miniprogram');
    },

    init() {
      try {
        this.acturl = this.getUrlQueryValueByKey(window.location.href, 'actlink');
        this.acturl = this.acturl.replace('-isv.isvjd', '-isv.isvjcloud');
        this.acturldecode = decodeURIComponent(this.acturl);
        const targetUrl = new URL(this.acturldecode);
        this.hostnamne = targetUrl.hostname;

        console.log(targetUrl, this.hostnamne);


        this.makeqrcode(this.acturldecode, 250);
        this.isMobile = this.isMobileBrowser();
        this.isPc = this.isPC();
        this.isWeChatOrWeibo = this.isWeChatOrWeiboApp();
        
        // 特别区分微信浏览器（不是小程序）
        const ua = navigator.userAgent.toLowerCase();
        this.isWeChatBrowser = ua.includes('micromessenger') && !ua.includes('miniprogram');

        if (this.isMobile && !this.isWeChatOrWeibo) {
          this.openappfun();
        }

        this.jumptoh5shop();
      } catch (e) {
        console.error(e);
      }
    },

    async makeqrcode(url, size) {
      try {
        // 使用qrcode库生成二维码URL
        this.qrcodeImgUrl = await QRCode.toDataURL(url, {
          width: size,
          height: size,
          margin: 0,
          color: {
            dark: '#000000',
            light: '#ffffff'
          },
          errorCorrectionLevel: 'H'
        });
      } catch (err) {
        console.error(err);
      }
    },

    openappfun() {
      if (this.hostnamne.indexOf('lzkj') > -1 || this.hostnamne.indexOf('//3.cn') > -1 || this.hostnamne.indexOf('jd.com') > -1 || this.hostnamne.indexOf('crmcjyy') > -1 ) {
        try {
          const params = {
            category: 'jump',
            des: 'm',
            url: this.acturl,
          };
          const encodedParams = encodeURIComponent(JSON.stringify(params));
          const linkUrl = 'openApp.jdMobile://virtual?params=' + encodedParams;
          window.location.href = linkUrl;
        } catch (err) {
          console.error(err);
        }
      }
    },

    jumptoh5shop() {
      this.shopid = this.getUrlQueryValueByKey(window.location.href, 'shopid');
      if (this.shopid) {
        this.showJumpToShop = true;
      }
    },

    jumpToShop() {
      window.location.href = 'https://shop.m.jd.com/?shopId=' + this.shopid;
    }
  }
};
</script>

<style scoped>
.app {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.open-title {
  position: relative;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  img {
    width: 100%;
  }
}

#qrcodegroup {
  position: fixed;
  left: -99999px;
}

.qrcode {
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: 0.2rem;
}

.textdiv {
  position: relative;
  width: 6rem;
  font-size: 0.27rem;
  color: #8a8a8a;
  text-align: center;
  display: flex;
  margin-top: 0.2rem;
  justify-content: center;
}

.bluetip {
  position: relative;
  width: 5rem;
  height: 0.6rem;
  border-radius: 0.3rem;
  font-size: 0.24rem;
  background: #2d8cf0;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.2rem;
}

.value-input {
  position: relative;
  width: 6.5rem;
  margin: 0.2rem auto;
  display: flex;
  justify-content: center;
}

.clip-txt {
  color: #1a7fd4;
  border: 1px solid #1a7fd4;
  border-radius: 6rem;
  height: 0.72rem;
  border-radius: 0.36rem;
  width: 2rem;
  text-align: center;
  margin: 0.2rem auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.jump-to-shop {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  background: #1a7fd4;
  border-radius: 0.1rem;
  width: 2rem;
  font-size: 0.3rem;
  padding: 0.2rem;
  margin: 0.3rem auto;
}

.wechat-na {
  width: 3rem;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 101;
}

.mask-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 100;
}

</style>
