<template>
  <div class="box">
    <div class='dialog'>
      <div class="title">- 活动规则 -</div>
      <div class="dialog_rule" v-html="rule"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);
</script>

<style scoped lang="scss">
.box {

  .dialog {
    width: 6.9rem;
    margin: 0 auto;
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/227345/13/27509/102377/66da7777F85c25ba6/d10d7399c5fa1b61.png) no-repeat;
    height: 8rem;
    background-size: cover;
    box-sizing: border-box;
    padding: 1.1rem 0.85rem;
    .title {
      font-size: 0.37rem;
      text-align: center;
      color: #1b3f7d;
    }

    .dialog_rule {
      max-height: 3.73rem;
      overflow-y: auto;
      font-size: 0.21rem;
      font-weight: normal;
      letter-spacing: 0.01rem;
      color: #1b3f7d;
      margin-top: 0.1rem;
      text-align: left;
      white-space: pre-wrap;
    }
  }

}
</style>
