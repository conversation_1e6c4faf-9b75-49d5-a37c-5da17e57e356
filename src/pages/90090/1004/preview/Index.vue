<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv ">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img"/>
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
<!--          {{ shopName }}-->
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtnRules.value" @click="showRulePop"/>
          <div class="header-btn" :style="furnishStyles.headerBtnMyPrizes.value" @click="showMyPrizePop"/>
        </div>
      </div>
    </div>
    <div class="hotZoneBox" >
      <img class="hotZone" :src="furnish.prizeBg" alt="">
      <div class="prizeBox">
        <div class="qualificationBg">
          <div class="text" :style="furnishStyles.qualificationTextColor.value">
            {{text}}
          </div>
          <div class="toBuyBtn" @click="buyNow"/>
        </div>
        <div class="progressBarBg">
          <div :style="furnishStyles.topTextColor.value">
            <div class="num">×{{itemTotal}}</div>
            <div class="get">得</div>
            <div class="progressLineBox">
              <div class="progress"/>
              <div class="rate">0/{{itemTotal}}</div>
            </div>
          </div>
          <div :class="orderNum > 1 ? 'bottomSmallText' : 'bottomText'" :style="furnishStyles.bottomTextColor.value">{{orderNum}}笔{{orderNum > 1 ? '及以上' : ''}}订单购买{{orderSkuisExposure === 0 ? '全店' : '指定'}}商品<span style="font-size: 0.3rem">任意{{itemTotal}}件</span>，订单完成24小时后更新进度。</div>
        </div>
        <div class="orderTipsText" :style="furnishStyles.orderLimitTextColor.value">
<!--          <div>*{{dayjs(formerOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}-{{dayjs(orderRestrainStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}在金典SATINE旗舰店有已完成的订单 </div>-->
          <div>*{{dayjs(formerOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}-{{dayjs(orderRestrainStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}在{{shopName}}有已完成的订单 </div>
          <div>*在活动期间复购下单，{{orderNum}}笔{{orderNum > 1 ? '及以上' : ''}}订单购买{{'全店'}}商品任意{{itemTotal}}件及以上，订单完成24小时后更新</div>
          <div>订单可能存在延迟，请订单完成48h来领取权益</div>
        </div>
        <div class="choosePrizes">
          <div class="choosePrizesBox">
            <div class="list-view" v-for="(item, index) in multiplePrizeList" :key="index">
              <div class="itemBg" :style="furnishStyles.prizeItemBg.value">
                <div class="equity_name">
                  <svg width="100%" height="100%" viewBox="0 0 300 300">
                    <defs>
                      <path id="semi" d="M30 100a50 18 0 1 1 250 0"/>
                    </defs>
                    <use xlink:href="#semi" stroke="none" fill="none"/>
                    <text text-anchor="middle" :style="[furnishStyles.prizeItemTitleBg.value, FindFontSize(item.prizeName.length)]" style="font-weight: bolder">
                      <textPath xlink:href="#semi" startOffset="50%">
                        {{item.prizeName}}
                      </textPath>
                    </text>
                  </svg>
                </div>
                <img class="equity_img" :src="item.prizeImg ? item.prizeImg : '//img10.360buyimg.com/imgzone/jfs/t1/176585/24/10488/6916/60a4cb50E562734ab/f9ab956ec09a4146.png'" alt="">
                <div class="equity_num">剩余数量：{{item.sendTotalCount}}</div>
              </div>
              <div class="equity_btn" :style="furnishStyles.getPrizeBtn.value" @click="ShowToast">
                <span>立即领取</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div ref="skuTitle" class="sku" v-if="isExposure === 1">
      <img :src="furnish.showSkuBg" class="sku-list-img" alt="">
      <div class="sku-list">
        <div class="sku-item" v-for="(item,index) in skuListPreview" :key="index">
          <div class="sku-text" :style="furnishStyles.priceColor.value">
            <div class="sku-price">{{item.jdPrice}}</div>
            <div class="go-sku-btn" @click="ShowToast"/>
          </div>
        </div>
      </div>
    </div>
    <div v-if="furnish.footerIsOpen === '1'" class="bottom-shop-share">
      <div class="to-shop" :style="furnishStyles.btnToTop.value" @click="goTop"/>
    </div>
    <VanPopup teleport="body" v-model:show="showRule" >
      <RulePopup :rule="ruleTest" @close="showRule = false"/>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" >
      <MyPrize @close="showMyPrize = false"/>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, nextTick, computed } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultLadderPrizeList, defaultMultiplePrizeList, defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';
import html2canvas from 'html2canvas';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const total = ref(0);

const isLoadingFinish = ref(false);

const text = '尊敬的金典老客，恭喜您获得特权资格，下单复购即可领取视频月卡，快去选购吧！';
// 订单笔数
const orderNum = ref(1);
// 件数
const itemTotal = ref(1);
const receiveNum = ref(1);

type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
}

const multiplePrizeList = ref<Prize>([]);
multiplePrizeList.value = defaultMultiplePrizeList;

const Size = [
  {
    10: '0.22rem',
  },
  {
    9: '0.24rem',
  },
  {
    8: '0.28rem',
  },
  {
    7: '0.3rem',
  },
  {
    6: '0.34rem',
  },
  {
    5: '0.4rem',
  },
  {
    4: '0.4rem',
  },
  {
    3: '0.4rem',
  },
  {
    2: '0.4rem',
  },
  {
    1: '0.4rem',
  },
];
const fontSize = ref('');
const FindFontSize = (length: number) => {
  if (!length || length === 0) {
    return null;
  }
  // 使用 find 方法找到匹配的对象
  const item = Size.find((obj) => Object.keys(obj).includes(length.toString()));
  if (item) {
    // 获取第一个（也是唯一一个）键值对中的值
    const [key, value] = Object.entries(item)[0];
    fontSize.value = value;
    // console.log(fontSize.value);
    return { fontSize: value };
  }
  return { fontSize: null };
};

// const fontSizeComputed = computed(() => (length: number) => ({
//   fontSize: FindFontSize(length),
// }));

type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuListPreview = ref<Sku[]>([
  {
    jdPrice: 99.99,
  },
  {
    jdPrice: 99.99,
  },
  {
    jdPrice: 99.99,
  },
  {
    jdPrice: 99.99,
  },
  {
    jdPrice: 99.99,
  },
  {
    jdPrice: 99.99,
  },
]);
const orderSkuListPreview = ref<Sku[]>([]);
const nextStateAmount = ref(0);
const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const showGoods = ref(false);
const orderRestrainStartTime = ref(new Date().getTime());
const days = ref(180);
const formerOrderStartTime = ref();

const showRulePop = () => {
  showRule.value = true;
};

const showMyPrizePop = () => {
  showMyPrize.value = true;
};

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

// const toSaveAddress = (id: string) => {
//   addressId.value = id;
//   showSaveAddress.value = true;
// };

const orderSkuisExposure = ref(1);
const isExposure = ref(1);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;
    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);
    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;
    const blob = dataURLToBlob(croppedBase64);
    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

const skuTitle = ref();
const buyNow = () => {
  skuTitle.value.scrollIntoView({ behavior: 'smooth' });
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  const list3 = data.multiplePrizeList;
  if (list3.prizeType !== 0) {
    multiplePrizeList.value = list3;
  }
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuListPreview) {
    skuListPreview.value = data.skuListPreview;
  }
  if (data.orderSkuListPreview) {
    orderSkuListPreview.value = data.orderSkuListPreview;
  }
  orderNum.value = data.orderStrokeStatus.toString();
  itemTotal.value = data.itemTotal;
  receiveNum.value = data.receiveNum;
  ruleTest.value = data.rules;
  orderSkuisExposure.value = data.orderSkuisExposure;
  isExposure.value = data.isExposure;
  orderRestrainStartTime.value = dayjs(data.orderRestrainStartTime).subtract(1, 'seconds').valueOf();
  days.value = data.days;
  formerOrderStartTime.value = dayjs(orderRestrainStartTime.value).subtract(days.value, 'days').valueOf();
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    const list3 = activityData.multiplePrizeList;
    if (list3.prizeType !== 0) {
      multiplePrizeList.value = list3;
    }
    ruleTest.value = activityData.rules;
    orderSkuListPreview.value = activityData.orderSkuListPreview;
    shopName.value = activityData.shopName;
    skuListPreview.value = activityData.skuListPreview;
    itemTotal.value = activityData.itemTotal;
    // orderSkuisExposure.value = activityData.orderSkuisExposure;
    isExposure.value = activityData.isExposure;
    orderNum.value = activityData.orderStrokeStatus.toString();
    orderRestrainStartTime.value = dayjs(activityData.orderRestrainStartTime).subtract(1, 'seconds').valueOf();
    days.value = activityData.days;
    formerOrderStartTime.value = dayjs(orderRestrainStartTime.value).subtract(days.value, 'days').valueOf();
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};

const goTop = () => {
  document.documentElement.scrollTop = 0;
  document.body.scrollTop = 0;
};
</script>
<style lang="scss" scoped>
@font-face {
  font-family: 'FZZZHJTFont';
  src: url('../style/fzzzhjt.ttf') format('truetype');
}
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  margin-bottom: 1.2rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.28rem;
    height: 0.45rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat:no-repeat;
    background-size: 100%;
  }
}

.hotZoneBox{
  width: 7.5rem;
  margin: 0 auto;
  position: relative;
  top: 0.2rem;
  .hotZone {
    width: 7.5rem;
  }
  .prizeBox {
    position: absolute;
    top: 2.4rem;
    .qualificationBg{
      width: 7.5rem;
      height: 1.56rem;
      margin: 0 auto;
      background-repeat: no-repeat;
      background-size: 100%;
      padding: 0.4rem 0 0 0.48rem;
      font-weight: bolder;
      .text{
        font-size: 0.2rem;
        font-stretch: normal;
        letter-spacing: 0;
        width: 4.7rem;
        height: 0.89rem;
        margin: 0 0 0 0.6rem;
        padding: 0.15rem;
        font-weight: bolder;
        text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;
      }
      .toBuyBtn {
        width: 0.95rem;
        height: 0.94rem;
        border-radius: 100%;
        margin: 0 auto;
        background-repeat: no-repeat;
        background-size: 100%;
        position: absolute;
        top: 0.4rem;
        right: 0.5rem;
        //background-color: #fff;
      }
    }
    .orderTipsText{
      font-size: 0.15rem;
      text-align: left;
      padding: 0.1rem 0.4rem 0 0.5rem;
      div{
        line-height: 0.26rem;
      }
    }
    .progressBarBg{
      width: 6.65rem;
      height: 2.36rem;
      margin: 0 auto;
      background-repeat:no-repeat;
      background-size: 100%;
      .num {
        position: relative;
        margin: 0 auto;
        text-align: center;
        top: 0.3rem;
        font-size: 0.3rem;
      }
      .get {
        position: relative;
        margin: 0 auto;
        text-align: center;
        top: 0.46rem;
        font-size: 0.3rem;
      }
      .progressLineBox {
        display: flex;
        margin: 0 auto;
        width: 6rem;
        position: relative;
        top: 0.44rem;
        .progress {
          background: url(//img10.360buyimg.com/imgzone/jfs/t1/167858/31/38810/7626/66e4f188F4067c97f/c71efac3074ef1f3.png) no-repeat;
          background-size: 100%;
          width: 5.58rem;
          height: 0.23rem;
          position: relative;
          transform:translateY(25%);
        }
        .rate {
          text-align: right;
          font-size: 0.24rem;
          margin: 0 0 0 0.1rem;
        }
      }
      .bottomText {
        position: relative;
        margin: 0 auto;
        text-align: center;
        top: 0.45rem;
        font-size: 0.21rem;
        font-weight: 600;
      }
      .bottomSmallText {
        position: relative;
        margin: 0 auto;
        text-align: center;
        top: 0.45rem;
        font-size: 0.18rem;
        font-weight: 600;
      }
    }
    .choosePrizes{
      width: 6.7rem;
      height: 3.5rem;
      background-repeat:no-repeat;
      background-size: 100%;
      margin: 0.4rem auto 0;
      .chooseTitle {
        line-height:0.6rem;
        box-sizing: border-box;
        white-space: nowrap;
        display: inline-block;
        width: 6.7rem;
        text-align: center;
        margin: 0.2rem auto 0;
        font-family: FZZZHJTFont,serif;
        font-size: 0.4rem;
        //background: linear-gradient(#2b66d9, #429dec);
        //-webkit-background-clip: text;
        //color: transparent; /* 隐藏文字本身的颜色 */
      }
      .choosePrizesBox{
        margin: 1.65rem auto 0;
        width: 6.86rem;
        height: auto;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
        grid-gap: .1rem;
        box-sizing: border-box;
        overflow: hidden;
        .list-view{
          width: 100%;
          height: auto;
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
          grid-gap: .1rem;
          box-sizing: border-box;
          overflow: hidden;
          .itemBg{
            width: 2.05rem;
            height: 2.98rem;
            border-radius: .25rem;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            color: #fff;
            padding: .13rem;
            box-sizing: border-box;
            text-align: center;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            border: 1px solid hwb(0deg 100% 0/27%);
            position: relative;
            margin-bottom: .1rem;
            img {
              width: 1.5rem;
              height: 1.5rem;
              margin-bottom: 0.3rem;
            }
            .equity_name{
              background-repeat:no-repeat;
              height: 1.8rem;
              background-size: contain;
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              line-height: .65rem;
              // 单行超出展示...
              word-break: break-all;
              display: -webkit-box;
              overflow: hidden;
              text-overflow: ellipsis;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }
            .equity_img{
              position: relative;
              top: 0.56rem;
              left: 0;
              right: 0;
              width: 1.3rem;
              height: 1.3rem;
            }
            .equity_num{
              width: 100%;
              margin-top: .4rem;
              background-color: hsla(0, 0%, 100%, .02);
              border-top: 1px solid rgba(38, 87, 171, .2);
              border-bottom: 1px solid rgba(38, 87, 171, .2);
              font-size: .23rem;
              padding-top: 0.1rem;
              text-align: center;
            }
          }
          .equity_btn {
            width: 1.62rem;
            height: 0.5rem;
            border-radius: 0.22rem;
            background-repeat: no-repeat;
            background-size: cover;
            font-size: 0.28rem;
            padding: 0.08rem;
            z-index: 99;
            margin: 3.1rem 0 0 -0.95rem;
            line-height: 0.3rem;
            color:#975c07;
            font-weight: bold;
            text-align: center;
          }
        }
      }
    }
  }
}

.sku{
  width: 7.5rem;
  padding: 0.2rem 0;
  margin: 0.2rem auto 0.1rem auto;
  position: relative;
  .sku-list-img {
    width: 7.5rem;
    height: auto;
  }
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    width: 7.5rem;
    place-content: flex-start space-between;
    padding: 2.32rem 0 0;
    height: auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 0;
  }
  .sku-item{
    width: 3.75rem;
    height: 4.88rem;
    margin: 0 0 0.36rem 0;
    overflow: hidden;
    //background-color: rgba(140, 42, 42, 0.55);
    .sku-text{
      display: flex;
      width: 3.4rem;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      font-size: 0.32rem;
      height: 0.8rem;
      margin: 4.25rem auto 0;
      box-sizing: border-box;
      .sku-price {
        width: 1.07rem;
        height: 0.5rem;
      }
      .go-sku-btn {
        margin: 0 0 0 0.52rem;
        width: 1.3rem;
        height: 0.4rem;
        //background-color: #fff;
      }
    }
  }
  .sku-item:nth-child(odd) {
    padding-left: 0.7rem;
  }
  .sku-item:nth-child(even) {
    padding-left: 0.6rem;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.bottom-shop-share{
  margin: 0 auto;
  .to-shop{
    margin: 0 auto;
    height: 2.8rem;
    width: 7.5rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
