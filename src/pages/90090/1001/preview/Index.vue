<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv ">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img"/>
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
<!--          {{ shopName }}-->
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtnRules.value" @click="showRulePop"/>
          <div class="header-btn my-prizes" :style="furnishStyles.headerBtnMyPrizes.value" @click="showMyPrizePop"/>
        </div>
      </div>
    </div>
    <div class="hotZoneBox" :style="furnishStyles.prizeBg.value">
<!--      <img class="hotZone" :src="furnish.prizeBg" alt="">-->
      <div class="qualificationBg" :style="furnishStyles.qualificationBg.value">
        <div class="text" :style="furnishStyles.qualificationTextColor.value">
          {{text}}
        </div>
        <div class="toBuyBtn" @click="buyNow"/>
      </div>
      <div class="orderTipsText" :style="furnishStyles.orderLimitTextColor.value">
<!--        <div>*{{dayjs(formerOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}-{{dayjs(orderRestrainStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}在QQ星儿童牛奶京东自营旗舰店有已完成的订单 </div>-->
        <div>*{{dayjs(formerOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}-{{dayjs(orderRestrainStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}在{{shopName}}有已完成的订单 </div>
        <div>*在活动期间复购下单，{{orderNum}}笔{{orderNum > 1 ? '及以上' : ''}}订单有{{itemTotal}}件及以上商品，订单完成48h内可来领取权益</div>
      </div>
      <div class="progressBarBg" :style="furnishStyles.progressBarBg.value">
        <div :style="furnishStyles.topTextColor.value">
          <div class="num">×{{itemTotal}}</div>
          <div class="get">得</div>
          <div class="progressLineBox">
            <div class="progress"/>
            <div class="rate">0/{{itemTotal}}</div>
          </div>
        </div>
        <div class="bottomText" :style="furnishStyles.bottomTextColor.value">{{orderNum}}笔{{orderNum > 1 ? '及以上' : ''}}订单购买{{orderSkuisExposure === 0 ? '全店' : '指定'}}商品任意{{itemTotal}}件，订单完成24小时后更新进度。</div>
      </div>
      <div class="timeLimitedPrizeBg" :style="furnishStyles.timeLimitedPrizeBg.value">
        <div class="circle">
          <div class="text">+{{prizeName}}100京豆</div>
        </div>
        <div class="timeLimitedText" :style="furnishStyles.timeLimitedTextColor.value">
          <div>
            <span class="fontLarge">
              为您准备1份惊喜福利<br>
              本月完成购买后<br>
            </span>
            <span class="fontSmall">可以额外领取{{ prizeName }}100京豆！</span>
          </div>
        </div>
        <div class="timeLimitedBtnBg" @click="buyNow" :style="furnishStyles.timeLimitedBtnBg.value">选购<br>商品</div>
      </div>
      <div class="choosePrizes">
        <div class="chooseTitle">
          <div class="box" :style="furnishStyles.choosePrizeTitleColor.value">
            <div class="chooseTitleTextBefore">{{multiplePrizeList.length}}</div>
            <div class="chooseTitleText">大权益任选其</div>
            <div class="chooseTitleTextAfter">{{receiveNum}}</div>
          </div>
        </div>
        <div class="choosePrizesBox">
          <div class="list-view" v-for="(item, index) in multiplePrizeList" :key="index">
            <div class="itemBg" :style="furnishStyles.prizeItemBg.value">
              <img :src="item.prizeImg ? item.prizeImg : '//img10.360buyimg.com/imgzone/jfs/t1/176585/24/10488/6916/60a4cb50E562734ab/f9ab956ec09a4146.png'" alt="">
              <div class="equity_name" :style="furnishStyles.prizeItemTitleBg.value">{{item.prizeName}}</div>
              <div class="equity_num">剩余数量：{{item.sendTotalCount}}</div>
              <div class="equity_btn" :style="furnishStyles.getPrizeBtn.value">
                <span>立即领取</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="barrierBg" :style="furnishStyles.barrierBg.value">
      <div class="barrierScroll">
        <div class="barrierItems" v-for="(prizes, index) in ladderPrizeList" :key="index">
          <div class="text">当月购买{{prizes.buyTimes}}次</div>
          <div>
            <div class="relativeBox">
              <ul class="processBox" v-if="index !== ladderPrizeList.length - 1">
                <li v-for="prizes in 7" :key="prizes"></li>
              </ul>
            </div>
          </div>
          <div class="rightItem">
            <div class="rightText">可领取{{prizes.prizeName}}</div>
            <div class="rightBtn" :style="furnishStyles.barrierGetBtn.value">
              <span>未开启</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div ref="skuTitle" class="sku" v-if="isExposure === 1">
      <img :src="furnish.showSkuBg" class="sku-list-img" alt="">
      <div class="sku-list">
        <div class="sku-item" v-for="(item,index) in skuListPreview" :key="index">
          <div class="sku-text" :style="furnishStyles.priceColor.value">
            <div class="sku-price">{{item.jdPrice}}</div>
            <div class="go-sku-btn" @click="ShowToast"/>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-shop-share">
      <div class="to-shop" :style="furnishStyles.btnToTop.value" @click="goTop"/>
    </div>
    <VanPopup teleport="body" v-model:show="showRule" >
      <RulePopup :rule="ruleTest" @close="showRule = false"/>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" >
      <MyPrize @close="showMyPrize = false"/>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, nextTick } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultLadderPrizeList, defaultMultiplePrizeList, defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';
import html2canvas from 'html2canvas';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const total = ref(0);

const isLoadingFinish = ref(false);

const text = '尊敬的QQ星老客，恭喜您获得特权资格，下单复购即可领取视频月卡，快去选购吧！';
// 订单笔数
const orderNum = ref(1);
// 件数
const itemTotal = ref(1);
const receiveNum = ref(1);

type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
}

const ladderPrizeList = ref<Prize>([]);
ladderPrizeList.value = defaultLadderPrizeList;
const registrationPrizeList = ref<Prize>([defaultStateList]);
const multiplePrizeList = ref<Prize>([]);
multiplePrizeList.value = defaultMultiplePrizeList;

type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuListPreview = ref<Sku[]>([
  {
    jdPrice: 99.99,
  },
  {
    jdPrice: 99.99,
  },
  {
    jdPrice: 99.99,
  },
  {
    jdPrice: 99.99,
  },
  {
    jdPrice: 99.99,
  },
  {
    jdPrice: 99.99,
  },
]);
const orderSkuListPreview = ref<Sku[]>([]);
const nextStateAmount = ref(0);
const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const showGoods = ref(false);
const orderRestrainStartTime = ref(new Date().getTime());
const days = ref(180);
const formerOrderStartTime = ref();

const showRulePop = () => {
  showRule.value = true;
};

const showMyPrizePop = () => {
  showMyPrize.value = true;
};

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

// const toSaveAddress = (id: string) => {
//   addressId.value = id;
//   showSaveAddress.value = true;
// };

const orderSkuisExposure = ref(1);
const isExposure = ref(1);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;
    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);
    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;
    const blob = dataURLToBlob(croppedBase64);
    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

const skuTitle = ref();
const buyNow = () => {
  skuTitle.value.scrollIntoView({ behavior: 'smooth' });
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  const list1 = data.ladderPrizeList;
  const list2 = data.registrationPrizeList;
  const list3 = data.multiplePrizeList;
  if (list1.prizeType !== 0) {
    ladderPrizeList.value = list1;
  }
  if (list2.prizeType !== 0) {
    registrationPrizeList.value = list2;
  }
  if (list3.prizeType !== 0) {
    multiplePrizeList.value = list3;
  }
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuListPreview) {
    skuListPreview.value = data.skuListPreview;
  }
  if (data.orderSkuListPreview) {
    orderSkuListPreview.value = data.orderSkuListPreview;
  }
  orderNum.value = data.orderStrokeStatus.toString();
  itemTotal.value = data.itemTotal;
  receiveNum.value = data.receiveNum;
  ruleTest.value = data.rules;
  orderSkuisExposure.value = data.orderSkuisExposure;
  isExposure.value = data.isExposure;
  orderRestrainStartTime.value = dayjs(data.orderRestrainStartTime).subtract(1, 'seconds').valueOf();
  days.value = data.days;
  formerOrderStartTime.value = dayjs(orderRestrainStartTime.value).subtract(days.value, 'days').valueOf();
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    const list1 = activityData.ladderPrizeList;
    const list2 = activityData.registrationPrizeList;
    const list3 = activityData.multiplePrizeList;
    if (list1.prizeType !== 0) {
      ladderPrizeList.value = list1;
    }
    if (list2.prizeType !== 0) {
      registrationPrizeList.value = list2;
    }
    if (list3.prizeType !== 0) {
      multiplePrizeList.value = list3;
    }
    ruleTest.value = activityData.rules;
    orderSkuListPreview.value = activityData.orderSkuListPreview;
    shopName.value = activityData.shopName;
    skuListPreview.value = activityData.skuListPreview;
    // orderSkuisExposure.value = activityData.orderSkuisExposure;
    isExposure.value = activityData.isExposure;
    orderRestrainStartTime.value = dayjs(activityData.orderRestrainStartTime).subtract(1, 'seconds').valueOf();
    days.value = activityData.days;
    formerOrderStartTime.value = dayjs(orderRestrainStartTime.value).subtract(days.value, 'days').valueOf();
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};

const goTop = () => {
  document.documentElement.scrollTop = 0;
  document.body.scrollTop = 0;
};
</script>
<style lang="scss" scoped>
@font-face {
  font-family: 'FZZZHJTFont';
  src: url('../style/fzzzhjt.ttf') format('truetype');
}
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  margin-bottom: 1rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.28rem;
    height: 0.45rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat:no-repeat;
    background-size: 100%;
  }
  .my-prizes {
    position: relative;
    top: 2rem;
    right: 0;
    width: 0.8rem;
    height: 0.85rem;
  }
}

.hotZoneBox{
  width: 7.5rem;
  height: 23.6rem;
  margin: 0 auto;
  background-repeat:no-repeat;
  background-size: 100%;
  padding-top: 2.8rem;
  .qualificationBg{
    width: 6.7rem;
    height: 0.89rem;
    margin: 0 auto;
    background-repeat:no-repeat;
    background-size: 100%;
    .text{
      font-size: 0.2rem;
      font-weight: 400;
      font-stretch: normal;
      letter-spacing: 0;
      width: 4.7rem;
      height: 0.89rem;
      margin: 0 0 0 0.6rem;
      padding: 0.15rem;
    }
    .toBuyBtn {
      width: 0.8rem;
      height: 0.8rem;
      border-radius: 100%;
      margin: 0 auto;
      background-repeat:no-repeat;
      background-size: 100%;
      position: absolute;
      //background-color: #fff;
      top: 4.05rem;
      right: 0.5rem;
    }
  }
  .orderTipsText{
    margin: .1rem .6rem;
    font-size: .2rem;
    div{
      line-height: 0.34rem;
    }
  }
  .progressBarBg{
    width: 6.65rem;
    height: 2.16rem;
    margin: 0 auto;
    background-repeat:no-repeat;
    background-size: 100%;
    .num {
      position: relative;
      margin: 0 auto;
      text-align: center;
      top: 0.2rem;
      font-size: 0.3rem;
    }
    .get {
      position: relative;
      margin: 0 auto;
      text-align: center;
      top: 0.34rem;
      font-size: 0.3rem;
    }
    .progressLineBox {
      display: flex;
      margin: 0 auto;
      width: 6rem;
      position: relative;
      top: 0.44rem;
      .progress {
        background: url(//img10.360buyimg.com/imgzone/jfs/t1/167858/31/38810/7626/66e4f188F4067c97f/c71efac3074ef1f3.png) no-repeat;
        background-size: 100%;
        width: 5.58rem;
        height: 0.23rem;
        position: relative;
        transform:translateY(25%);
      }
      .rate {
        text-align: right;
        font-size: 0.24rem;
        margin: 0 0 0 0.1rem;
      }
    }
    .bottomText {
      position: relative;
      margin: 0 auto;
      text-align: center;
      top: 0.45rem;
      font-size: 0.21rem;
      font-weight: 600;
    }
  }
  .timeLimitedPrizeBg{
    width: 6.56rem;
    height: 2.78rem;
    margin: 0.2rem auto;
    background-repeat:no-repeat;
    background-size: 100%;
    position: relative;
    .circle{
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/248158/24/18548/3803/66e50299F4209c3b1/ceb48fbc35c2ea3c.png) no-repeat;
      background-size: 100% 100%;
      width: auto;
      height: auto;
      color: #e42b11;
      font-size: 0.13rem;
      text-align: center;
      line-height: 0.6rem;
      display: inline-block;
      box-sizing: border-box;
      padding: 0.06rem;
      position: absolute;
      top: 0.88rem;
      left: 0.98rem;
      .text{
        width: auto;
        white-space: nowrap;
      }
    }
    .timeLimitedText{
      position: absolute;
      top: 1.12rem;
      left: 2rem;
      font-weight: 550;
      line-height: 0.4rem;
      .fontLarge {
        font-size: .32rem;
      }
      .fontSmall{
        font-size: 0.23rem;
      }
    }
    .timeLimitedBtnBg {
      background-size: 100%;
      width: 1.02rem;
      height: 1.02rem;
      position: absolute;
      top: 1.26rem;
      right: .2rem;
      text-align: center;
      box-sizing: border-box;
      padding-top: .23rem;
      line-height: 1.2;
      font-size: .24rem;
    }
  }
  .choosePrizes{
    width: 6.7rem;
    height: 3.5rem;
    margin: 0 auto;
    background-repeat:no-repeat;
    background-size: 100%;
    .chooseTitle {
      color: #fff;
      line-height:0.6rem;
      box-sizing: border-box;
      white-space: nowrap;
      display: inline-block;
      border-bottom: 0.01rem solid #fff;
      width: auto;
      margin: 0 0 0 0.04rem;
      .box {
        display: flex;
        .chooseTitleText{
          //background: url(//img10.360buyimg.com/imgzone/jfs/t1/97802/9/50801/2619/66e50213Ff6bf9946/94d9b31521d94d86.png) no-repeat;
          //background-size: 100% 100%;
          //width: 2.22rem;
          //height: 0.34rem;
          //margin:0.2rem 0 0 0.05rem;
          margin:0.08rem 0 0;
          font-size: 0.4rem;
          font-family: FZZZHJTFont,serif;
        }
        .chooseTitleTextBefore{
          font-size:0.6rem;
          font-family: FZZZHJTFont,serif;
          width: auto;
          white-space: nowrap;
        }
        .chooseTitleTextAfter {
          margin:0.08rem 0 0;
          font-size: 0.4rem;
          font-family: FZZZHJTFont,serif;
          width: auto;
          white-space: nowrap;
        }
      }
    }
    .choosePrizesBox{
      margin: 0.1rem auto;
      width: 6.9rem;
      height: auto;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
      grid-gap: .1rem;
      box-sizing: border-box;
      overflow: hidden;
      .list-view{
        width: 100%;
        height: auto;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
        grid-gap: .1rem;
        box-sizing: border-box;
        overflow: hidden;
        .itemBg{
          width: 2.2rem;
          height: 3.45rem;
          border-radius: .25rem;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          color: #fff;
          padding: .13rem;
          box-sizing: border-box;
          text-align: center;
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          border: 1px solid hwb(0deg 100% 0/27%);
          position: relative;
          margin-bottom: .1rem;
          img {
            width: 1.5rem;
            height: 1.5rem;
            margin-bottom: 0.3rem;
          }
          .equity_name{
            background-repeat:no-repeat;
            height: .6rem;
            background-size: contain;
            position: absolute;
            top: 1.8rem;
            left: 0;
            right: 0;
            font-size: .22rem;
            line-height: .65rem;
            // 单行超出展示...
            word-break: break-all;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
          }
          .equity_num{
            width: 100%;
            margin-top: .4rem;
            background-color: hsla(0, 0%, 100%, .02);
            border-top: 1px solid rgba(38, 87, 171, .2);
            border-bottom: 1px solid rgba(38, 87, 171, .2);
            font-size: .23rem;
            padding-top: 0.1rem;
            text-align: center;
          }
          .equity_btn {
            width: 1.62rem;
            height: 0.43rem;
            border-radius: .22rem;
            background-repeat: no-repeat;
            background-size: cover;
            font-size: .28rem;
            line-height: 1;
            padding: .08rem;
          }
        }
      }
    }
  }
}

.barrierBg {
  font-size: .23rem;
  width: 7.14rem;
  height: 5.14rem;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin: 0 auto;
  padding: 2.2rem 0.3rem 0.9rem;
  text-align: center;
  font-weight: bold;
  color: #ec3429;
  .barrierScroll {
    height: 2.15rem;
    overflow-y: scroll;
  }
  .barrierItems{
    margin: 0 auto;
    display: flex;
    text-align: center;
    padding: 0 0.3rem 0.2rem 0.3rem;
    font-size: 0.22rem;
    .text {
      width: 1.6rem;
    }
    .relativeBox{
      position: relative;
      width: 0.3rem;
      margin:0 0 0 0.3rem;
      .processBox{
        position: absolute;
        top: 0.13rem;
        left: 0.1rem;
        margin: 0 auto;
        text-align: center;
        li{
          height: 0.03rem;
          width: 0.04em;
          border-radius: 100%;
          --tw-bg-opacity: 1;
          margin: 0 auto 0.05rem;
          background-color: rgba(239, 68, 68, var(--tw-bg-opacity));
        }
        li:nth-child(6n+1) {
          height: 0.08rem;
          margin: 0 auto 0.07rem;
          width: 0.25em;
          border-radius: 100%;
          --tw-bg-opacity: 1;
          background-color: rgba(239, 68, 68, var(--tw-bg-opacity));
        }
      }
    }
    .rightItem{
      width: 4.3rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .rightText {
        flex-grow: 1;
        width: 3rem;
        font-size: 0.23rem;
        text-align: left;
        padding: 0 0 0 0.3rem;
        overflow: hidden; /* 如果需要，可以添加来防止文本溢出 */
        white-space: nowrap; /* 防止文本换行 */
      }
      .rightBtn{
        font-size: .2rem;
        height: 1.26em;
        width: 0.92rem;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        line-height: 0.23rem;
      }
    }
  }
}

.sku{
  width: 7.13rem;
  padding: 0.2rem 0;
  margin: 0.2rem auto 0.1rem auto;
  .sku-list-img {
    width: 7.13rem;
    height: auto;
    position: absolute;
  }
  .sku-list{
    position: relative;
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    width: 7.13rem;
    place-content: flex-start space-between;
    padding: 1.98rem 0 0.2rem;
    height: auto;
    background-repeat: no-repeat;
    background-size: 100% 100%
  }
  .sku-item{
    width: 3.565rem;
    height: 4.42rem;
    margin: 0 0 0.08rem 0;
    overflow: hidden;
    //background-color: rgb(140 42 42 / 55%);
    .sku-text{
      display: flex;
      width: 3.4rem;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      font-size: 0.32rem;
      height: 0.8rem;
      margin: 3.84rem auto 0;
      box-sizing: border-box;
      .sku-price {
        width: 1.07rem;
        height: 0.5rem;
      }
      .go-sku-btn {
        margin-top:0.03rem;
        width: 1.1rem;
        height: 0.3rem;
        //background-color: #fff;
      }
    }
  }
  .sku-item:nth-child(odd) {
    padding-left: 0.6rem;
  }
  .sku-item:nth-child(even) {
    padding-left: 0.4rem;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.bottom-shop-share{
  margin: 0 auto;
  .to-shop{
    margin: 0 auto;
    height: 0.7rem;
    width: 2.77rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
.more-btn-all {
  width:6.9rem;
  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
    background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 0.3rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
