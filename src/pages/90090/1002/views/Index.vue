<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv ">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img"/>
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <!--          {{ shopName }}-->
        </div>
        <div>
          <div class="header-btn" v-click-track="'hdgz'" :style="furnishStyles.headerBtnRules.value" @click="showRulePopup"/>
          <div class="header-btn" v-click-track="'wdjp'" :style="furnishStyles.headerBtnMyPrizes.value" @click="showMyPrizePop"/>
        </div>
      </div>
    </div>
    <div class="hotZoneBox" >
      <img class="hotZone" :src="furnish.prizeBg" alt="">
      <div class="prizeBox">
        <div class="qualificationBg" :style="furnishStyles.qualificationBg.value">
          <div class="text" :style="furnishStyles.qualificationTextColor.value">
            {{text}}
          </div>
          <div class="toBuyBtn" v-click-track="'ljgm'" @click="buyNow"/>
        </div>
        <div class="progressBarBg" :style="furnishStyles.progressBarBg.value">
          <div :style="furnishStyles.topTextColor.value">
            <div class="num">×{{activityItemTotal}}</div>
            <div class="get">得</div>
            <div class="progressLineBox">
              <div class="progress">
                <div class="bubble" :style="{ width: progressWidth }">
                  <div v-if="progressBar !== 0" class="point"/>
                </div>
              </div>
              <div class="rate">{{itemTotal}}/{{activityItemTotal}}</div>
            </div>
          </div>
          <div class="bottomText" :style="furnishStyles.bottomTextColor.value">{{orderStrokeStatus}}笔{{orderStrokeStatus > 1 ? '及以上' : ''}}订单购买{{orderSkuisExposure === 0 ? '全店' : '指定'}}商品<span style="font-size: 0.3rem">任意{{activityItemTotal}}件</span>，订单完成24小时后更新进度。</div>
        </div>
        <div class="orderTipsText" :style="furnishStyles.orderLimitTextColor.value">
<!--          <div>*{{dayjs(oldOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}-{{dayjs(oldOrderEndTime).format('YYYY年MM月DD日 HH:mm:ss')}}在安慕希官方旗舰店有已完成的订单 </div>-->
          <div>*{{dayjs(oldOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}-{{dayjs(oldOrderEndTime).format('YYYY年MM月DD日 HH:mm:ss')}}在{{shopName}}有已完成的订单 </div>
          <div>*在活动期间复购下单，{{orderStrokeStatus}}笔{{orderStrokeStatus > 1 ? '及以上' : ''}}订单购买{{'全店'}}商品任意{{activityItemTotal}}件及以上，订单完成24小时后更新</div>
          <div>订单可能存在延迟，请订单完成48h来领取权益</div>
        </div>
        <div class="choosePrizes">
          <div class="choosePrizesBox">
            <div class="list-view" v-for="(item, index) in multiplePrizeList" :key="index">
              <div class="itemBg" :style="furnishStyles.prizeItemBg.value">
                <div class="equity_name" :style="furnishStyles.prizeItemTitleBg.value">
                  <svg width="100%" height="100%" viewBox="0 0 300 300">
                    <defs>
                      <path id="semi" d="M30 100a50 20 0 1 1 250 0"/>
                    </defs>
                    <use xlink:href="#semi" stroke="none" fill="none"/>
                    <text text-anchor="middle" :style="[furnishStyles.prizeItemTitleBg.value, FindFontSize(item.prizeName.length)]" style="font-weight: bolder">
                      <textPath xlink:href="#semi" startOffset="50%">
                        {{item.prizeName}}
                      </textPath>

                    </text>
                  </svg>
                </div>
                <img class="equity_img" :src="item.prizeImg ? item.prizeImg : '//img10.360buyimg.com/imgzone/jfs/t1/176585/24/10488/6916/60a4cb50E562734ab/f9ab956ec09a4146.png'" alt="">
                <div class="equity_num">剩余数量：{{item.remainCount}}</div>
              </div>
              <div class="equity_btn" :class="{'noJurisdiction': [2, 3, 4].includes(item.status)}" v-click-track="'ljlq-qy'" @click="getPrize(item)" :style="furnishStyles.getPrizeBtn.value">
                <span v-if="item.status === 1">立即领取</span>
                <span v-if="item.status === 2">已领取</span>
                <span v-if="item.status === 3">已兑完</span>
                <span v-if="item.status === 4">立即领取</span>
              </div>
<!--              <div class="equity_btn" v-click-track="'ljlq-qy'" @click="getPrize(item)" v-if="item.status === 3" :style="furnishStyles.noMorePrizeBtn.value"/>-->
<!--              <div class="equity_btn" v-click-track="'ljlq-qy'" @click="getPrize(item)" v-if="item.status === 0 || item.status === 4 || item.status === 2" :style="furnishStyles.getPrizeGrayBtn.value"/>-->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div ref="skuTitle" class="sku" v-if="skuList.length">
      <img :src="furnish.showSkuBg" class="sku-list-img" alt="">
      <div class="sku-list">
        <div class="sku-item" v-for="(item,index) in skuList" :key="index">
          <div class="sku-text" :style="furnishStyles.priceColor.value">
            <div class="sku-price">{{item.jdPrice}}</div>
            <div class="go-sku-btn" @click="gotoSkuPage(item.skuId)"/>
          </div>
        </div>
      </div>
    </div>
    <div v-if="furnish.footerIsOpen === '1'" class="bottom-shop-share">
      <div class="to-shop" :style="furnishStyles.btnToTop.value" @click="goTop"/>
    </div>
    <!-- 活动门槛 -->
    <Threshold :showPopup="showLimit" @closeDialog="showLimit = false" :canNotCloseJoin="furnish.canNotCloseJoinPopup" :data="baseInfo?.thresholdResponseList"/>
    <!-- 非会员拦截 -->
    <OpenCard :showPopup="showOpenCard" @closeDialog="showOpenCard = false" :canNotCloseJoin="furnish.canNotCloseJoinPopup"/>
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule" >
      <RulePopup :rule="ruleTest" @close="showRule = false"/>
    </VanPopup>
    <!--我的奖品-->
    <VanPopup teleport="body" v-model:show="showMyPrize" >
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"/>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup
      teleport="body"
      v-model:show="showSaveAddress"
    >
      <SaveAddress
        :addressId="addressId"
        @close="closeAddress" />
    </VanPopup>
    <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="isShowConfirmPopup" >
      <GiftConfirm
        :giftInfo="giftItem"
        @close="isShowConfirmPopup = false"
        :multiplePrizeNum="multiplePrizeNum"
        :multiplePrizeCanReceiveNum="multiplePrizeCanReceiveNum"
        @drawSuccess="drawSuccessFun"
      />
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { computed, inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { callShare } from '@/utils/platforms/share';
import constant from '@/utils/constant';
import { gotoSkuPage } from '@/utils/platforms/jump';
import RulePopup from '../components/RulePopup.vue';
import Threshold from '../components/Threshold.vue';
import GiftConfirm from '../components/GiftConfirm.vue';
import OpenCard from '../components/OpenCard.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import dayjs from 'dayjs';
import MyPrize from '../components/MyPrize.vue';
import { defaultLadderPrizeList, defaultMultiplePrizeList, defaultStateList } from '../ts/default';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const endTime = ref(0);
const isStart = ref(false);
const isEnd = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
  if (now > endTime.value) {
    isEnd.value = true;
  }
  if (now < endTime.value) {
    isEnd.value = false;
  }
};
// 店铺名称
const shopName = ref(baseInfo.shopName);
// 门槛弹窗
const showOpenCard = ref(false);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 我的奖品弹窗
const showMyPrize = ref(false);
// 单次领取弹窗
const isShowConfirmPopup = ref(false);
// 曝光商品
const showGoods = ref(false);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
  buyTimes: number;
}
const ladderPrizeList = ref<Prize>([]);
ladderPrizeList.value = defaultLadderPrizeList;
const multiplePrizeList = ref<Prize>([]);
multiplePrizeList.value = defaultMultiplePrizeList;

// 进度条
const progressBar = ref(0);
const progressWidth = ref('');

const giftItem = ref({});

const Size = [
  {
    10: '0.35rem',
  },
  {
    9: '0.4rem',
  },
  {
    8: '0.45rem',
  },
  {
    7: '0.5rem',
  },
  {
    6: '0.55rem',
  },
  {
    5: '0.55rem',
  },
  {
    4: '0.6rem',
  },
  {
    3: '0.6rem',
  },
  {
    2: '0.6rem',
  },
  {
    1: '0.6rem',
  },
];
const fontSize = ref('');
const FindFontSize = (length: number) => {
  if (!length || length === 0) {
    return null;
  }
  // 使用 find 方法找到匹配的对象
  const item = Size.find((obj) => Object.keys(obj).includes(length.toString()));
  if (item) {
    // 获取第一个（也是唯一一个）键值对中的值
    const [key, value] = Object.entries(item)[0];
    fontSize.value = value;
    // console.log(fontSize.value);
    return { fontSize: value };
  }
  return { fontSize: null };
};

// const fontSizeComputed = computed(() => (length: number) => ({
//   fontSize: FindFontSize(length),
// }));

// 活动商品列表
type Sku = {
  skuId: string,
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuList = ref<Sku[]>([]);
const skuTitle = ref();
const buyNow = () => {
  skuTitle.value.scrollIntoView({ behavior: 'smooth' });
};
// 展示门槛显示弹框
const showLimit = ref(false);

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const userReceiveRecordId = ref('');

// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error: any) {
    console.error();
  }
};
const showMyPrizePop = () => {
  showMyPrize.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  id: 1,
  prizeName: '',
  prizeImg: '',
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  exchangeImg: '',
});
// 展示卡密
const showCardNum = (distribute: any) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = distribute[item];
  });
  copyCardPopup.value = true;
};
// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);
// 多选奖品可领取数量
const multiplePrizeCanReceiveNum = ref(0);
// 多选奖品数量
const multiplePrizeNum = ref(0);
// 活动要求购买件数
const activityItemTotal = ref(0);
// 订单笔数
const orderStrokeStatus = ref(0);
// 订单商品  0 全店 1 指定 2 排除
const orderSkuisExposure = ref(0);
// 已购数量
const itemTotal = ref(0);
const oldOrderEndTime = ref();
const oldOrderStartTime = ref();
const hasOrdBefore = ref(false);
const text = computed(() => (hasOrdBefore.value ? '尊敬的安慕希老客，恭喜您获得特权资格，下单复购即可领取视频月卡，快去选购吧！'
  : `尊敬的安慕希用户，您${dayjs(oldOrderStartTime.value).format('YYYY年MM月DD日')}~${dayjs(oldOrderEndTime.value).format('YYYY年MM月DD日')}未购，未获得特权领取资格，速速购买并参与${dayjs().month() + 2}月活动`));

// 主接口获取信息
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/90090/activity');
    hasOrdBefore.value = data.hasOrdBefore;
    if (!hasOrdBefore.value) {
      if (furnish.isShowJump && furnish.jumpUrl) {
        window.location.href = furnish.jumpUrl;
        return;
      }
      showLimit.value = true;
    }
    multiplePrizeList.value = data.multiplePrize;
    multiplePrizeCanReceiveNum.value = data.multiplePrizeCanReceiveNum;
    multiplePrizeNum.value = data.multiplePrizeNum;
    activityItemTotal.value = data.activityItemTotal;
    orderStrokeStatus.value = data.orderStrokeStatus;
    itemTotal.value = data.itemTotal;
    oldOrderEndTime.value = data.oldOrderEndTime;
    oldOrderStartTime.value = data.oldOrderStartTime;
    progressBar.value = data.progressBar;
    progressWidth.value = `${(data.progressBar * 100)}%`;
    orderSkuisExposure.value = data.orderSkuisExposure;
    // progressBar.value = 0.6;
    // progressWidth.value = '60%';
  } catch (error: any) {
    console.error(error);
  }
};
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

// 获取曝光商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/90090/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    skuList.value.push(...data.records);
    total.value = data.total;
    pagesAll.value = data.pages;
  } catch (error: any) {
    console.error(error);
  }
};
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivityInfo(), getSkuList()]);
    closeToast();
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
      showOpenCard.value = true;
      console.log('非会员');
      return;
    }
  } catch (error: any) {
    closeToast();
  }
};

// 校验领奖资格
const checkQualification = (val) => {
  if (!isStart.value) {
    showToast('活动未开始~');
    return false;
  }
  if (isEnd.value) {
    showToast('活动已结束~');
    return false;
  }
  if (val.status === 0) {
    showToast('您不符合活动条件，无法领取~');
    return false;
  }
  if (val.status === 2) {
    showToast('您已领取过该奖品~');
    return false;
  }
  if (val.status === 3) {
    showToast('手慢了，奖品已领光~');
    return false;
  }
  if (val.status === 4) {
    showToast('领取数量已经达到上限~');
    return false;
  }
  return val.status === 1;
};

// 领取各个奖品判断
const getPrize = async (prize: any) => {
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 1501) {
    showLimit.value = true;
    return;
  }
  // 校验资格
  if (!checkQualification(prize)) {
    return;
  }
  giftItem.value = prize;
  isShowConfirmPopup.value = true;
};

// 确认领取弹窗的回调
const drawSuccessFun = async () => {
  isShowConfirmPopup.value = false;
  await init();
};

const showSavePhone = (id: string, desc: string) => {
  userReceiveRecordId.value = id;
  planDesc.value = desc;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

const closeAddress = () => {
  showSaveAddress.value = false;
  init();
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};

const goTop = () => {
  document.documentElement.scrollTop = 0;
  document.body.scrollTop = 0;
};
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'FZZZHJTFont';
  src: url('../style/fzzzhjt.ttf') format('truetype');
}
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  margin-bottom: 1.2rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.28rem;
    height: 0.45rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat:no-repeat;
    background-size: 100%;
  }
}

.hotZoneBox{
  width: 7.5rem;
  margin: 0 auto;
  position: relative;
  top: 0.2rem;
  .hotZone {
    width: 7.5rem;
  }
  .prizeBox {
    position: absolute;
    top: 2.4rem;
    .qualificationBg{
      width: 7.5rem;
      height: 1.56rem;
      margin: 0 auto;
      background-repeat: no-repeat;
      background-size: 100%;
      padding: 0.3rem 0 0 0.48rem;
      font-weight: bolder;
      .text{
        font-size: 0.2rem;
        font-weight: 400;
        font-stretch: normal;
        letter-spacing: 0;
        width: 4.7rem;
        height: 0.89rem;
        margin: 0 0 0 0.6rem;
        padding: 0.15rem;
      }
      .toBuyBtn {
        width: 0.95rem;
        height: 0.94rem;
        border-radius: 100%;
        margin: 0 auto;
        background-repeat: no-repeat;
        background-size: 100%;
        position: absolute;
        top: 0.4rem;
        right: 0.5rem;
        //background-color: #fff;
      }
    }
    .orderTipsText{
      margin: 0 .6rem;
      font-size: 0.16rem;
      div{
        line-height: 0.22rem;
      }
    }
    .progressBarBg{
      width: 6.65rem;
      height: 2.36rem;
      margin: 0 auto;
      background-repeat:no-repeat;
      background-size: 100%;
      .num {
        position: relative;
        margin: 0 auto;
        text-align: center;
        top: 0.3rem;
        font-size: 0.3rem;
      }
      .get {
        position: relative;
        margin: 0 auto;
        text-align: center;
        top: 0.46rem;
        font-size: 0.3rem;
      }
      .progressLineBox {
        display: flex;
        margin: 0 auto;
        width: 6rem;
        position: relative;
        top: 0.44rem;
        .progress {
          background: url(//img10.360buyimg.com/imgzone/jfs/t1/167858/31/38810/7626/66e4f188F4067c97f/c71efac3074ef1f3.png) no-repeat;
          background-size: 100%;
          width: 5.58rem;
          height: 0.23rem;
          position: relative;
          transform:translateY(25%);

          .bubble{
            background: url(//img10.360buyimg.com/imgzone/jfs/t1/37594/26/24692/1341/66eb9924Fba700108/a141ab2ae35e4d8b.png) no-repeat;
            background-size: 100% 100%;
            margin-top: 0.025rem;
            height: 0.17rem;
            position: relative;
            border-radius: 0.4rem;
          }
          .point {
            background: url(//img10.360buyimg.com/imgzone/jfs/t1/244014/34/19491/1043/66eb9fcfF008f5122/4dbe781631527f85.png) no-repeat;
            background-size: 100%;
            width: 0.17rem;
            height: 0.17rem;
            position: absolute;
            right: 0;
          }
        }
        .rate {
          text-align: right;
          font-size: 0.24rem;
          margin: 0 0 0 0.1rem;
        }
      }
      .bottomText {
        position: relative;
        margin: 0 auto;
        text-align: center;
        top: 0.45rem;
        font-size: 0.18rem;
        font-weight: 600;
      }
    }
    .choosePrizes{
      width: 6.7rem;
      height: 3.5rem;
      background-repeat:no-repeat;
      background-size: 100%;
      margin: 0.4rem auto 0;
      .chooseTitle {
        line-height:0.6rem;
        box-sizing: border-box;
        white-space: nowrap;
        display: inline-block;
        width: 6.7rem;
        text-align: center;
        margin: 0.2rem auto 0;
        font-family: FZZZHJTFont,serif;
        font-size: 0.4rem;
        //background: linear-gradient(#2b66d9, #429dec);
        //-webkit-background-clip: text;
        //color: transparent; /* 隐藏文字本身的颜色 */
      }
      .choosePrizesBox{
        margin: 1.8rem auto 0;
        width: 6.9rem;
        height: auto;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
        grid-gap: .1rem;
        box-sizing: border-box;
        overflow: hidden;
        .list-view{
          width: 100%;
          height: auto;
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
          grid-gap: .1rem;
          box-sizing: border-box;
          overflow: hidden;
          .itemBg{
            width: 2.05rem;
            height: 2.98rem;
            border-radius: .25rem;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            color: #fff;
            padding: .13rem;
            box-sizing: border-box;
            text-align: center;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            border: 1px solid hwb(0deg 100% 0/27%);
            position: relative;
            margin-bottom: .1rem;
            img {
              width: 1.5rem;
              height: 1.5rem;
              margin-bottom: 0.3rem;
            }
            .equity_name{
              background-repeat:no-repeat;
              //height: .6rem;
              background-size: contain;
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              font-size: .22rem;
              line-height: .65rem;
              // 单行超出展示...
              word-break: break-all;
              display: -webkit-box;
              overflow: hidden;
              text-overflow: ellipsis;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }
            .equity_img{
              position: relative;
              top: 0.56rem;
              left: 0;
              right: 0;
              width: 1.3rem;
              height: 1.3rem;
            }
            .equity_num{
              width: 100%;
              margin-top: .4rem;
              background-color: hsla(0, 0%, 100%, .02);
              border-top: 1px solid rgba(38, 87, 171, .2);
              border-bottom: 1px solid rgba(38, 87, 171, .2);
              font-size: .23rem;
              padding-top: 0.1rem;
              text-align: center;
            }
          }
          .equity_btn {
            width: 1.62rem;
            height: 0.5rem;
            border-radius: 0.22rem;
            background-repeat: no-repeat;
            background-size: 100%;
            font-size: 0.28rem;
            padding: 0.08rem;
            z-index: 99;
            margin: 3.1rem 0 0 -0.95rem;
            text-align: center;
            line-height: 0.3rem;
            color:#975c07;
            font-weight: bold;
          }
          .noJurisdiction {
            filter: grayscale(1);
          }
        }
      }
    }
  }
}

.sku{
  width: 7.5rem;
  padding: 0.2rem 0;
  margin: 0.2rem auto 0.1rem auto;
  position: relative;
  .sku-list-img {
    width: 7.5rem;
    height: auto;
  }
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    width: 7.5rem;
    place-content: flex-start space-between;
    padding: 1.7rem 0 0;
    height: auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 0;
  }
  .sku-item{
    width: 3.75rem;
    height: 4.93rem;
    margin: 0 0 0.26rem 0;
    overflow: hidden;
    //background-color: rgba(140, 42, 42, 0.55);
    .sku-text{
      display: flex;
      width: 3.4rem;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      font-size: 0.32rem;
      height: 0.8rem;
      margin: 4.33rem auto 0;
      box-sizing: border-box;
      .sku-price {
        width: 1.07rem;
        height: 0.5rem;
      }
      .go-sku-btn {
        margin: 0 0 0 0.52rem;
        width: 1.3rem;
        height: 0.4rem;
        //background-color: #fff;
      }
    }
  }
  .sku-item:nth-child(odd) {
    padding-left: 0.7rem;
  }
  .sku-item:nth-child(even) {
    padding-left: 0.6rem;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.bottom-shop-share{
  margin: 0 auto;
  .to-shop{
    margin: 0 auto;
    height: 2.5rem;
    width: 7.5rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
