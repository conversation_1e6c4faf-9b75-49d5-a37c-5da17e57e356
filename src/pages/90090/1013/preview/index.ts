import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData = {
  actBg: '',
  pageBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/276086/34/7209/52414/67dd0f60Fdd159edd/49ead917c16ddc0c.png',
  actBgColor: '#efd8ac',
  shopNameColor: '',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/196549/37/48402/5849/66f51f3cFf371fc6c/2339fed9176b4c96.png',
  myPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/87898/2/46100/5925/66f51f35Ffbf1ce76/e22717ae394558d9.png',
  prizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/283692/7/6521/176697/67dd0f79F4c4bd597/eb76f2fd3c07c485.png',
  isAccordColor: '#85420b',
  orderLimitTextColor: '#85420b',
  thresholdColor: '#f0f8ff',
  prizeItemBg: '//img10.360buyimg.com/imgzone/jfs/t1/228657/31/31061/11137/67b470dcF00e117f3/912df122e9c9316e.png',
  prizeItemTitleColor: '#85420b',
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/253237/2/22515/521/67b470ddF1393b511/cdd91ff2e1f55336.png',
  branZone: 'https://img10.360buyimg.com/imgzone/jfs/t1/277524/7/7231/54992/67dd0f99F109124fb/0eaaf06118553593.png',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/278904/21/6850/291210/67dcd13dFf43d6b70/523b12a7ec7e40fa.png',
  priceColor: '#85420b',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/216973/7/44448/35565/6704a24dF95fd447d/a746e76753d2b4a3.png',
  jumpUrl: '',
  isShowJump: true,
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/174328/18/45163/21611/6704a24cFabf19fb2/6ba253827b895b26.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/180520/22/49437/189250/6704a24cFc1441794/ff6d99d06475ea12.png',
  canNotCloseJoinPopup: '',
  btnToTop: '//img10.360buyimg.com/imgzone/jfs/t1/131310/10/50027/76785/6721ce7fF58f40968/40711efada845b81.png',
  footerIsOpen: '2',
  hotZoneList: [],
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '复购有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  // app.provide('decoData', _decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
