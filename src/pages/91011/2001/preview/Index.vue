<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv">
      <img
        :src="
          furnish.actBg ??
          'https://img10.360buyimg.com/imgzone/jfs/t1/301137/31/18237/686303/68622ef5F2950375e/5708dba47e433889.png'
        "
        alt=""
        class="kv-img"
      />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>
        <div>
          <div
            class="header-btn"
            :style="furnishStyles.headerBtnRules.value"
            @click="showRulePop"
          />
          <div
            class="header-btn"
            :style="furnishStyles.headerBtnMyPrizes.value"
            @click="showMyPrizePop"
          />
        </div>
      </div>
    </div>
    <!-- 权益1 -->
    <div class="qyDivOneAll" :style="{ backgroundImage: `url(${furnish.qy1Bg})` }">
      <!-- 立即领取按钮如果符合首购条件则显示，否则置灰 -->
      <div class="getQyBtn" @click="toast">立即领取</div>
    </div>

    <!-- 权益2 -->
    <div class="qyDivTwoAll">
      <img :src="furnish.qy2Bg" alt="" />
      <div class="qyTwoListDiv">
        <div
          class="qyTwoItemDiv"
          v-for="(it, index) in furnish.qy2BgItemBgArr"
          :key="index"
          :style="{ backgroundImage: `url(${it.skuImg})` }"
        >
          <div class="qyTwoBtn">立即领取</div>
        </div>
      </div>
    </div>

    <div class="qyDivThreeAll">
      <!-- 动态生成的热区按钮 -->
      <HotZone :width="6.88" :data="furnish.hotZoneSetting" reportKey="" />
    </div>

    <div class="sku" v-if="isExposure === 1">
      <img class="sku-list-img" :src="furnish.showSkuBg" alt="" />
      <div class="sku-list">
        <div class="sku-list1">
          <div
            class="sku-item"
            v-for="(item, index) in skuList"
            :key="index"
            @click="ShowToast"
          ></div>
        </div>
      </div>
    </div>

    <div>
      <!-- 非会员拦截 -->
      <OpenCard :showPopup="showOpenCard" @closeDialog="showOpenCard = false" />
      <VanPopup teleport="body" v-model:show="showRule">
        <RulePopup :rule="ruleTest" @close="showRule = false" />
      </VanPopup>
      <VanPopup teleport="body" v-model:show="showMyPrize">
        <MyPrize @close="showMyPrize = false" />
      </VanPopup>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, nextTick } from "vue";
import dayjs from "dayjs";
import furnishStyles, { furnish } from "../ts/furnishStyles";
import { defaultStateList } from "../ts/default";
import RulePopup from "../components/RulePopup.vue";
import MyPrize from "../components/MyPrize.vue";
import usePostMessage from "@/hooks/usePostMessage";
import { showToast } from "vant";
import html2canvas from "html2canvas";
import HotZone from "../components/HotZone.vue";
import OpenCard from "../components/OpenCard.vue";

// 门槛弹窗
const showOpenCard = ref(false);

const activityData = inject("activityData") as any;
const decoData = inject("decoData") as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const total = ref(0);

const isLoadingFinish = ref(false);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
}

const prizeList = ref<Prize>([defaultStateList]);
type Sku = {
  skuName?: string;
  skuMainPicture?: string;
  jdPrice: string | number;
  showSkuImage: string;
};
const skuList = ref<Sku[]>([
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png",
  },
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png",
  },
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png",
  },
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png",
  },
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png",
  },
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png",
  },
]);
const orderSkuListPreview = ref<Sku[]>([]);
const nextStateAmount = ref(0);
const shopName = ref("");

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref("");
const showGoods = ref(false);

const showRulePop = () => {
  showRule.value = true;
};

const showMyPrizePop = () => {
  showMyPrize.value = true;
};

const award = ref<any>({
  prizeType: 0,
  prizeName: "",
  prizeImg: "",
  result: "",
  activityPrizeId: "",
  userReceiveRecordId: "",
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref("");

// const toSaveAddress = (id: string) => {
//   addressId.value = id;
//   showSaveAddress.value = true;
// };

// const orderSkuisExposure = ref(1);
const isExposure = ref(1);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(",");
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement("canvas");
    const ctx = cropCanvas.getContext("2d");
    cropCanvas.width = 375;
    cropCanvas.height = 670;
    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(
      canvas,
      0,
      0,
      canvas.width,
      (canvas.width / 375) * 670,
      0,
      0,
      375,
      670
    );
    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL("image/png");
    isCreateImg.value = false;
    const blob = dataURLToBlob(croppedBase64);
    window.top?.postMessage(
      {
        from: "C",
        type: "screen",
        event: "sendScreen",
        data: blob,
      },
      "*"
    );
  });
};

const ShowToast = () => {
  showToast("活动预览，仅供查看");
};

// 装修数据监听
registerHandler("deco", (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler("activity", (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  const list2 = data.prizeList;
  if (list2.prizeType !== 0) {
    prizeList.value = list2;
  }
  
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuList) {
    skuList.value = data.skuList;
  }
  if (data.orderSkuListPreview) {
    orderSkuListPreview.value = data.orderSkuListPreview;
  }
  ruleTest.value = data.rules;
  isExposure.value = data.isExposure;
  // console.log(skuList.value, "skuList.value========");
});
// 店铺信息监听
registerHandler("shop", (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler("screen", () => {
  createImg();
});

onMounted(() => {
  if (activityData) {

    const list2 = activityData.prizeList;
    
    if (list2.prizeType !== 0) {
      prizeList.value = list2;
    }
    ruleTest.value = activityData.rules;
    orderSkuListPreview.value = activityData.orderSkuListPreview;
    shopName.value = activityData.shopName;
    skuList.value = activityData.skuList;
    isExposure.value = activityData.isExposure;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
  // console.log(skuList.value, "skuList.value========aaaaaaaaaaaa");
});

const toast = () => {
  showToast("活动预览，仅供查看");
};
</script>
<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  //margin-bottom: 9rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.2rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.37rem;
    height: 0.57rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}

.qyDivOneAll {
  background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/301639/17/19105/37030/68622ef8Ff01ded9c/f25480c8ea22c91e.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.88rem;
  height: 2.98rem;
  margin-left: 50%;
  transform: translateX(-50%);
  position: relative;
  .getQyBtn {
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/231343/9/29061/1599/675f9872F87a87c9c/0e76d2742d53fd96.jpg)
      no-repeat;
    background-size: 100%;
    width: 0.7rem;
    height: 1.64rem;
    display: flex;
    align-items: center;
    color: #fff;
    text-align: center;
    font-size: 0.28rem;
    padding: 0.1rem 0.2rem 0.1rem 0.1rem;
    box-sizing: border-box;
    position: absolute;
    right: 0.25rem;
    top: 0.94rem;
    filter: grayscale(1);
  }
}
.qyDivTwoAll {
  width: 6.88rem;
  margin: 0.3rem auto;
  position: relative;
  padding-bottom: 0.2rem;
  min-height: 3.2rem;
  img {
    position: absolute;
    width: 6.88rem;
  }
  .qyTwoListDiv {
    position: relative;
    // top: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding-top: 1rem;
    .qyTwoItemDiv {
      position: relative;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/310318/36/17401/36053/6875ff9fF7a78bd80/52e1449f20c0651d.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 6.26rem;
      height: 1.6rem;
      margin-bottom: 0.1rem;
      .qyTwoBtn {
        position: absolute;
        background: #c56f26;
        width: 1.8rem;
        height: auto;
        font-size: 0.28rem;
        line-height: 1;
        padding: 0.1rem 0.25rem;
        border-radius: 0.3rem;
        color: #fff;
        left: 3.15rem;
        top: 0.9rem;
        filter: grayscale(1);
      }
    }
  }
}
.qyDivThreeAll {
  margin-top: 0.6rem;
}
.sku {
  width: 7.21rem;
  padding: 0.2rem 0;
  position: relative;
  margin: 0.4rem auto 0.1rem auto;
  position: relative;
  // background-color: #000;
  .sku-list-img {
    width: 7.21rem;
    height: auto;
    position: absolute;
    top: 0;
    // background-color: #f2270c;
  }
  .sku-list {
    width: 7.25rem;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto;
    grid-gap: 0.2rem 0.2rem;
    // background-color: aqua;
    position: relative;
    z-index: 1;
    .sku-list1 {
      padding-top: 0.9rem;
      width: 7.25rem;
      height: 100%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: auto;
      grid-gap: 0.2rem 0.2rem;
      // background-color: #c56f26;
    }
  }
  .sku-item {
    width: 3.3rem;
    height: 4rem;
    overflow: hidden;
    margin-bottom: 0.2rem;
    .sku-text {
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      font-size: 0.32rem;
      height: 0.8rem;
      margin: 0.4rem auto 0;
      box-sizing: border-box;
      .go-sku-btn {
        width: 1.4rem;
        height: 0.3rem;
        //background-color: #000;
      }
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
