<template>
<!--  :style="furnishStyles.pageBg.value"-->
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="info" :style="furnishStyles.finishInfo.value">
      <div>
        <div class="info-form">
          <van-form @submit="onSubmit" class="van-form">
            <van-cell-group inset>
              <van-field v-if="showNameItem" class="field" name="姓名" label="姓名" placeholder="姓名" maxlength="0" />
              <van-field
                v-if="showBirthdayItem"
                v-model="birthday"
                class="field"
                name="生日"
                label="生日"
                readonly
                placeholder="点击选择生日"
                maxlength="0"
                @click="changeDate" :rules="[{ required: true, message: '请填写生日' }]"
                />
               <van-field
                v-if="showPhoneItem"
                v-model="phoneNumber"
                class="field"
                name="手机号"
                label="手机号"
                placeholder="手机号"
                maxlength="0"
               />
              <van-field v-if="showGenderItem" v-model="gender" class="field" name="性别" label="性别" @click="changeGender" :rules="[{ required: true, message: '请填写性别' }]" readonly placeholder="点击选择性别" />
              <van-field v-if="showEmailItem" class="field" name="邮箱" label="邮箱" placeholder="邮箱" maxlength="0"  />
              <van-field
                v-if="showAreaItem"
                v-model="area"
                is-link
                readonly
                class="field"
                name="地址"
                label="地址"
                placeholder="点击选择地址"
                @click="changeArea" :rules="[{ required: true, message: '请填写地址' }]"
              />
              <div v-for="(item, index) in itemList" :key="index">
                <van-field
                  v-if="item.type === 1"
                  class="field"
                  :name="item.title"
                  :label="item.title"
                  :placeholder="item.title"
                  maxlength="0"
                  @click="ShowToast" />
              </div>
            </van-cell-group>
          </van-form>
          <div class="check-box">
            <div class="termCheckbox">
              <van-checkbox v-model="termCheckbox" checked-color="#000" icon-size="24px" @click="ShowToast"></van-checkbox>
              <p>
                我已阅读和了解欧舒丹<span class="underline" @click="ShowToast">隐私政策</span>和<span
                  class="underline"
                  @click="ShowToast"
                  >会员权益</span
                >并且同意接受其中所有的条款。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--          :style="furnishStyles.submitBtnBg.value"-->
    <div class="submit" :style="furnishStyles.submitBtnBg.value" @click="ShowToast">确认提交</div>
  </div>

  <div>
    <!-- 聚合弹窗活动门槛 -->
    <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
    <!-- 非会员 -->
    <VanPopup teleport="body" v-model:show="showToJoinVip" position="center" :close-on-click-overlay="closeOnClickOverlay">
      <ToJoinDialog @close="showToJoinVip=false"></ToJoinDialog>
    </VanPopup>

    <!--  时间选择-->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker @confirm="changeBirthday" @cancel="showDatePicker = false" />
    </van-popup>
    <!--  性别选择-->
    <van-popup v-model:show="showGenderPicker" position="bottom">
      <van-picker :columns="genderColumns" @confirm="changeGenderConfirm" @cancel="showGenderPicker = false" />
    </van-popup>
    <!--  住址选择-->
    <van-popup v-model:show="showArea" position="bottom">
      <van-area :area-list="areaList" @confirm="changeAreaConfirm" @cancel="showArea = false" />
    </van-popup>

    <!-- 隐私政策 -->
    <VanPopup teleport="body" v-model:show="privacyPolicy">
      <PrivacyPolicy :rule="ruleTest" @close="privacyPolicy = false"></PrivacyPolicy>
    </VanPopup>
    <!-- 会员权益 -->
    <VanPopup teleport="body" v-model:show="memberEquity">
      <MemberEquity :rule="ruleTest" @close="memberEquity = false"></MemberEquity>
    </VanPopup>
  </div>

</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, inject, toRaw, computed } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import Swiper, { Autoplay } from 'swiper';
import html2canvas from 'html2canvas';
import Threshold2 from '../Threshold2/ThresholdCPB.vue';
import useThreshold from '@/hooks/useThreshold';
import '../Threshold2/CPBStyle.scss';
import { areaList } from '@vant/area-data';
import { showToast } from 'vant';
import ToJoinDialog from '../components/ToJoinDialog.vue';
import { BaseInfo } from '@/types/BaseInfo';

Swiper.use([Autoplay]);


// const baseInfo = inject('baseInfo') as BaseInfo;
// const showToJoinVip = ref(false);
// const thresholdResponseList1 = ref([]);
// // 展示门槛显示弹框
// const showLimit = ref(false);
// const openCardArr = baseInfo.thresholdResponseList.filter((item) => item.type === 1);
// if (openCardArr.length > 0) {
//   showToJoinVip.value = true;
// } else {
//   thresholdResponseList1.value = baseInfo.thresholdResponseList.filter((item) => item.type !== 1);
//   showLimit.value = useThreshold({
//     thresholdList: thresholdResponseList1.value,
//     className: 'common-message-cpb',
//   });
// }
const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const termCheckbox = ref(true);

const isLoadingFinish = ref(false);

const showRule = ref(false);
const ruleTest = ref('');
// 隐私政策
const privacyPolicy = ref(false);
// 会员权益
const memberEquity = ref(false);
const prizeInfo = ref({
  prizeName: '',
  prizeImg: '',
});
const changeDate = () => {
  showDatePicker.value = true;
};
const showMyPrize = ref(false);
const itemList = ref([]);

const phoneNumber = ref('');

// 生日
const birthday = ref();
const showDatePicker = ref(false);
const changeBirthday = ({ date }: any) => {
  birthday.value = date;
  showDatePicker.value = false;
};

// 性别修改
const showGenderPicker = ref(false);
const gender = ref('');
const genderColumns = [
  { text: '男', value: '1' },
  { text: '女', value: '0' },
];
const changeGenderConfirm = ({ selectedOptions }: { selectedOptions: { text: string } }) => {
  gender.value = selectedOptions[0]?.text;
  showGenderPicker.value = false;
};
const changeGender = () => {
  showGenderPicker.value = true;
};
const changeArea = () => {
  showArea.value = true;
};
// 住址修改
const showArea = ref(false);
const area = ref('');
const changeAreaConfirm = ({ selectedOptions }: any) => {
  showArea.value = false;
  area.value = selectedOptions.map((item: any) => item.text).join('/');
};
const onSubmit = () => {
  console.log('submit');
};

const times = ref(0);

const isStart = ref(false);
const startTime = ref(0);
const endTime = ref(0);

// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showAward.value = false;
  showSaveAddress.value = false;

  showSelect.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    showSelect.value = true;
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

const showNameItem = ref(true);
const showBirthdayItem = ref(true);
const showPhoneItem = ref(true);
const showGenderItem = ref(true);
const showEmailItem = ref(true);
const showAreaItem = ref(true);
const showFreeItem = ref(true);

const resetShowItem = () => {
  showNameItem.value = false;
  showBirthdayItem.value = false;
  showPhoneItem.value = false;
  showGenderItem.value = false;
  showEmailItem.value = false;
  showAreaItem.value = false;
  showFreeItem.value = false;
};

const checkInfoItem = (val: any) => {
  val.forEach((item: any) => {
    if (item.title === '姓名') {
      showNameItem.value = true;
    } else if (item.title === '生日') {
      showBirthdayItem.value = true;
    } else if (item.title === '手机号') {
      showPhoneItem.value = true;
    } else if (item.title === '性别') {
      showGenderItem.value = true;
    } else if (item.title === '邮箱') {
      showEmailItem.value = true;
    } else if (item.title === '地址') {
      showAreaItem.value = true;
    }
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    startTime.value = new Date(data.startTime).getTime();
    if (startTime.value > new Date().getTime()) {
      isStart.value = false;
    }
    if (startTime.value < new Date().getTime()) {
      isStart.value = true;
    }
    if (data.itemList.length >= 0) {
      itemList.value = data.itemList;
      resetShowItem();
      checkInfoItem(itemList.value);
    }
    endTime.value = new Date(data.endTime).getTime();
    ruleTest.value = data.rules;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  }
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    startTime.value = new Date(activityData.startTime).getTime();
    if (startTime.value > new Date().getTime()) {
      isStart.value = false;
    }
    if (startTime.value < new Date().getTime()) {
      isStart.value = true;
    }
    if (activityData.itemList.length >= 0) {
      itemList.value = activityData.itemList;
      resetShowItem();
      checkInfoItem(itemList.value);
    }
    endTime.value = new Date(activityData.endTime).getTime();
    ruleTest.value = activityData.rules;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.5rem;
  padding-top:2.90rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/271312/29/25843/16690/6809e621F68b44b24/919f405d872d2445.png');
  background-color: #ecd7ba;
}

.info {
  width: 6.77rem;
  height: 7.94rem;
  margin: 0 auto 0 auto;
  position: relative;
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 0.5rem 0.1rem 0;
  overflow: hidden;
  .info-form {
    height: auto;
    font-size: 5rem;
    border-radius: 0 0 0.3rem 0.3rem;
    overflow: hidden;
    overflow-y: scroll;

    .van-form {
      min-height: 3rem;
      max-height: 6rem;
      overflow: hidden;
      overflow-y: scroll;
    }

    .field {
      border: none;
      font-size: 0.25rem;
      --van-field-label-width: 1rem;
      height: 1rem;
      line-height: 1rem;
      display: flex;
      align-items: center;
      padding-top: 0;
      padding-bottom: 0;
      border-bottom: 0.02rem solid #000;
      border-top: none;
    }
    .check-box {
      font-size: 0.23rem;
      padding: 0 0.7rem;
      position: absolute;
      bottom: 0.3rem;
      .termCheckbox {
        display: flex;
        justify-content: flex-start;
        align-items: start;
        padding-bottom: 0.15rem;
        .van-checkbox {
           width: 0.5rem;
           height: 0.5rem;
          display: flex;
          align-items: flex-start;
          padding-top: 0.06rem;
         }
        .underline {
          text-decoration: underline;
          text-decoration-color: #b0a8a5;
        }
        p {
          text-align: center;
        }
      }
    }
  }
}

.submit {
  width: 2.80rem;
  height: 0.55rem;
  background-size: 100%;
  background-repeat: no-repeat;
  margin: 0.26rem auto 0.2rem auto;
  color: white;
  font-size: 0;
  text-align: center;
  line-height: 0.55rem;
  letter-spacing: 0.08rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/284249/13/25527/12177/6809e301F5949fda6/fd68a32019168c32.png');
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}

.van-cell {
  border-bottom: 1px solid #000;
}
.van-cell-group--inset {
  border-radius: 0;
}
.info-form {
  .van-cell-group {
    background: transparent;
  }
  .van-cell {
    background: transparent;
  }
}
</style>
