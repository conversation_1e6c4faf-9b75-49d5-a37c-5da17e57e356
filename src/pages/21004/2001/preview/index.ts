import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData = {
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/281955/39/20115/124519/67fd0108F33344531/7a218e8e090d806a.png',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/236087/37/11174/14302/6596861eF8a7c5840/596ef1d14b83d307.png',
  actBgColor: '',
  joinBtn: '//img10.360buyimg.com/imgzone/jfs/t1/273229/34/18602/859/67f7af74F27b1bdba/b41e450100d96c49.png',
  successBtn: '//img10.360buyimg.com/imgzone/jfs/t1/281576/19/19803/812/67fd0109Fa1da2cbe/d58b8dd3323cc9e5.png',
  ruleImg: '//img10.360buyimg.com/imgzone/jfs/t1/279162/31/18263/1717/67f7af74F616737c9/aace214116988f93.png',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员令牌复购';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
