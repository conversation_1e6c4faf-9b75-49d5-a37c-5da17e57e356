<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <img :src="furnish.actBg" alt="" class="bg-img" />
    <div>
      <img :src="furnish.ruleImg" alt="" class="rule-btn" @click="showRule = true" v-click-track="'hdgz'" />
      <img v-if="status === 0" :src="furnish.joinBtn" alt="" class="get-prize" @click="getPrizes" v-click-track="'ljlq'" />
      <img v-if="status === 1" :src="furnish.successBtn" alt="" class="get-prize" @click="toSuccessPage" v-click-track="'qwhdy'" />
    </div>
  </div>
  <!-- 活动门槛 -->
  <ThresholdCPB v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
  <!--  新增门槛-->
  <VanPopup teleport="body" v-model:show="showAddLimit">
    <ThresholdNew @close="showAddLimit = false" :actStartTime="actStartTime" :actEndTime="actEndTime" :fullGiftThreshold="fullGiftThreshold" :maxParticipateNum="maxParticipateNum" :orderStartTime="orderStartTime" />
  </VanPopup>
  <!-- 规则 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!--  领取攻略-->
  <VanPopup teleport="body" v-model:show="showStrategyPopup">
    <StrategyPopup @close="showStrategyPopup = false"></StrategyPopup>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="showOrderRecord">
    <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
  </VanPopup>
</template>
<script setup lang="ts">
import { inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import RulePopup from '../components/RulePopup.vue';
import StrategyPopup from '../components/StrategyPopup.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import ThresholdCPB from '../components/Threshold.vue';
import ThresholdNew from '../components/ThresholdNew.vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import useThreshold from '@/hooks/useThreshold';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import '@/components/Threshold2/CPBStyle.scss';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

// 是否领取成功
const success = ref(false);
// 是否参与过活动 0 未参与 1 参与成功
const status = ref(0);

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

// 店铺名称
const shopName = ref(baseInfo.shopName);

// // 门槛弹窗
// const showLimit = ref(true);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 中奖弹窗
const showAward = ref(false);
// 我的订单
const showOrderRecord = ref(false);
// 领取攻略
const showStrategyPopup = ref(false);

// 奖品列表
const prizeList = ref({
  prizeId: 0,
  prizeImg: '',
  prizeName: '',
  prizeType: 0,
  remainCount: 0,
  sendTotalCount: 0,
  // 奖品状态 -1 不满足条件 0 未领取 1 领取成功 2 取消报名 3 发放奖奖品 4剩余份数不足
  status: 0,
  receivePrizeId: 0,
});
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);
// 奖品信息
const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  prizeId: '',
  userReceiveRecordId: '',
});

// 展示门槛显示弹框
const showLimit = ref(false);
// 新增的门槛弹窗
const showAddLimit = ref(false);
const actStartTime = ref();
const actEndTime = ref();
const fullGiftThreshold = ref('');
const maxParticipateNum = ref(0);
const orderStartTime = ref();

const checkLimitDialog = async () => {
  if (baseInfo.status === 1) {
    showToast('活动未开始！');
    return;
  }
  if (baseInfo.status === 3) {
    showToast('活动已结束！');
    return;
  }
  if (baseInfo.thresholdResponseList.findIndex((item) => item.thresholdCode === 4 || item.thresholdCode === 5 || item.thresholdCode === 8) !== -1) {
    showLimit.value = true;
    return;
  }
  if (baseInfo.thresholdResponseList.findIndex((item) => item.thresholdTitle === '没有前置订单') !== -1) {
    const { data } = await httpRequest.post('/21003/parameter');
    actStartTime.value = data.actStartTime;
    actEndTime.value = data.actEndTime;
    fullGiftThreshold.value = data.fullGiftThreshold;
    maxParticipateNum.value = data.maxParticipateNum;
    orderStartTime.value = data.orderStartTime;
    showAddLimit.value = true;
  }
  // if (baseInfo.thresholdResponseList[0].type === 0 && baseInfo.thresholdResponseList[0].thresholdTitle === '没有前置订单') {
  //   const { data } = await httpRequest.post('/21004/parameter');
  //   actStartTime.value = data.actStartTime;
  //   actEndTime.value = data.actEndTime;
  //   fullGiftThreshold.value = data.fullGiftThreshold;
  //   maxParticipateNum.value = data.maxParticipateNum;
  //   orderStartTime.value = data.orderStartTime;
  //   showAddLimit.value = true;
  // } else {
  //   showLimit.value = useThreshold({
  //     thresholdList: baseInfo.thresholdResponseList,
  //     className: 'common-message-cpb',
  //   });
  // }
};

// 活动规则相关
const showRulePopup = async () => {
  try {
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const getRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    ruleTest.value = data;
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

// 领取奖品
const getPrizes = async () => {
  if (baseInfo.thresholdResponseList.length) {
    await checkLimitDialog();
    return;
  }
  try {
    const { data, code } = await httpRequest.post('/21004/sendUser');
    console.log(data, 'ackMessage');
    if (code === 200) {
      success.value = true;
      status.value = 1;
    }
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

// 跳转成功页
const toSuccessPage = () => {
  // success.value = true;
  gotoShopPage(baseInfo.shopId);
};
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);
// 获取曝光商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/21004/skuListPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    skuList.value.push(...data.records);
    total.value = data.total;
    pagesAll.value = data.pages;
  } catch (error) {
    console.error(error);
  }
};

const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};

// 获取参与状态
const getStatus = async () => {
  try {
    const { data } = await httpRequest.post('/21004/prize');
    status.value = data;
  } catch (error) {
    console.error(error);
  }
};

// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getRule(), getSkuList(), getStatus()]);
    closeToast();
    checkLimitDialog();
  } catch (error) {
    closeToast();
  }
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();
</script>
<style lang="scss">
@font-face {
  font-family: 'SourceHanSansCN';
  src: url('https://lzcdn.dianpusoft.cn/fonts/SourceHanSansCN/SourceHanSansCN-Regular.otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'GucciSans';
  src: url('https://lzcdn.dianpusoft.cn/fonts/GucciSans/GucciSans-Bold.otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'GucciSans', 'SourceHanSansCN';
}
</style>
<style lang="scss" scoped>
.bg {
  min-height: 100vh;
  position: relative;
  background-repeat: no-repeat;
  background-size: 100%;
  .bg-img {
    width: 100%;
  }
  .rule-btn {
    position: absolute;
    top: 1.04rem;
    right: 0;
    width: 1.05rem;
  }
  .get-prize {
    position: absolute;
    top: 8.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 1.82rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
