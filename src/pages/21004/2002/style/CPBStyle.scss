// 顶部样式
.common-message-header-cpb {
  padding-top: 1.4rem;
  font-size: 0.4rem;
  color: #000;
  font-weight: normal;
}
// 利用footer来做关闭按钮
.common-message-footer-cpb {
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/19241/17/21974/2872/669f5677Fb0530cbf/70bd484bb3e85826.png');
  background-size: 100%;
  position: absolute;
  width: 3rem;
  height: 0.95rem;
  right: 1.2rem;
  top: 3.6rem;
}
// 内容
.common-message-content-cpb {
  font-size: 0.28rem;
  line-height: 0.36rem;
  margin-top: 0.3rem;
  color: #000;
  padding: 0 0.2rem;
}
// 通用主题
.common-message-cpb {
  width: 5.4rem !important;
  height: 5rem !important;
  background-color: #f7f2e9 !important;
  .van-dialog__header {
    @extend .common-message-header-cpb;
  }
  .van-dialog__footer {
    @extend .common-message-footer-cpb;
    .van-dialog__confirm {
      color: transparent;
      background: transparent;
    }
  }
  .van-dialog__message {
    @extend .common-message-content-cpb;
  }
}
