// 分享配置类型定义
export interface ShareConfig {
  channel?: string;
  title: string;
  content: string;
  url: string;
  img: string;
  subtitle?: string;
  timeline_title?: string;

  // 京口令参数
  keyparam?: {
    keyTitle: string;
    keyContent: string;
    keyImg: string;
    url: string;
    keyEndTime: string;
    keyId: string;
    sourceCode: string;
    keyChannel?: string;
  };

  // 二维码/海报参数
  qrparam?: {
    qr_direct?: string;
    mid_pic_x?: string;
    qr_type?: string;
    qr_title_x2?: Array<{
      title: string;
      ex_content?: string;
    }>;
    mid_pic_x2?: string;
    qr_title?: string;
    mid_pic?: string;
    slogan?: string;
    qr_content?: string;
    top_pic?: string;
  };

  // 分享有礼参数
  shareGiftParam?: {
    giftTitle: string;
    giftSubTitle: string;
    jumpTitle: string;
    jump: string;
    successToast: string;
    failToast: string;
  };

  // 小红书参数
  xhsparam?: {
    title: string;
    content: string;
    images?: string[];
    videos?: Array<{
      videoUrl: string;
      coverUrl: string;
    }>;
  };

  // 京ME参数
  skuBigImg?: string;
  jdMeExtends?: string;
  shareSource?: {
    source: string;
    scene?: string;
  };

  // 微信小程序参数
  mpId?: string;
  mpIconUrl?: string;
  mpPath?: string;
  mpType?: string;

  // 微信内容分享参数
  weiXinContentParam?: {
    titleTag: string;
    price: string;
    secKill: string;
    ranking: string;
    rating: string;
  };

  // 其他参数
  isNewWeixinShareUI?: string;
  hidePlus?: string;
  callback?: (result: any) => void;
  clickcallback?: (result: any) => void;

  // 自定义渠道
  cmChannel?: Array<{
    iconId: string;
    iconUrl: string;
    iconName: string;
    jumpUrl: string;
    source: string;
  }>;
}

// 分享渠道类型
export type ShareChannel =
  | 'Wxfriends'
  | 'Wxmoments'
  | 'QQfriends'
  | 'QQzone'
  | 'Sinaweibo'
  | 'CopyURL'
  | 'CopyJDKey'
  | 'ME'
  | 'QRCode'
  | 'XHS';

// 分享组类型
export interface ShareGroup {
  id: string;
  name: string;
  description: string;
  configs: ShareConfig[];
}

// 表单字段类型
export interface FormField {
  key: string;
  label: string;
  type: 'input' | 'textarea' | 'select' | 'switch' | 'number';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: string | number }>;
  defaultValue?: any;
}
