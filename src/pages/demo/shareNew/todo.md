# 新版分享展示项目

## 背景
京东更新了分享sdk，增加了很多内容，现在想做一个演示页面进行完整功能的展示

## SDK一览
- 默认
```
{
  channel: "",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png"
}
```
- 分享微信好友
```
{
  channel: "Wxfriends",

  title: "发送给朋友的标题",
  content: "发送给朋友的描述",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",
  subtitle: "副标题"
}
```
- 分享微信朋友圈
```
{
  channel: "Wxmoments",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",
  subtitle: "副标题",
  timeline_title:
    "分享到朋友圈的标题"
}
```
- 分享QQ好友
```
{
  channel: "QQfriends",

  title: "发送给朋友的标题",
  content: "发送给朋友的描述",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",
  subtitle: "副标题"
}
```
- 分享QQ空间
```
{
  channel: "QQzone",

  title: "发送给朋友的标题",
  content: "发送给朋友的描述",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",
  subtitle: "副标题"
}
```
- 分享新浪微博
```
{
  channel: "Sinaweibo",

  title: "发送给朋友的标题",
  content: "发送给朋友的描述",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",
  subtitle: "副标题"
}
```
- 复制链接
```
{
  channel: "CopyURL",

  title: "发送给朋友的标题",
  content: "发送给朋友的描述",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",
  subtitle: "副标题"
}

```
- 复制京口令
```
{
  channel: "CopyJDKey",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  keyparam: {
    keyTitle: "京口令京东app内标题",
    keyContent:
      "京口令分享文字内容。\n可以换行。",
    keyImg:
      "https://img11.360buyimg.com/imagetools/jfs/t1/105241/11/38687/5228/6433bc46F0d23c83d/733866745e53690e.png",
    url: "https://m.jd.com",
    keyEndTime: "1757001600000",
    keyId: "0",
    sourceCode: "jdjssdkexample"
  }
}

```
- 分享海报（使用图片地址）
```
{
  channel: "QRCode",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  qrparam: {
    qr_direct:
      "https://img12.360buyimg.com/imagetools/jfs/t1/91516/8/26209/6534/6433bc46F7cd91955/cca0f2a1022c25fb.png"
  }
}
```
- 分享有礼
```
{
  channel: "",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  shareGiftParam: {
    giftTitle: "标题",
    giftSubTitle:
      "副标题[只有callSharePane方法唤起的分享面板支持此能力]",
    jumpTitle: "业务个性化元素",
    jump: "跳转链接",
    successToast: "分享成功文案",
    failToast: "分享失败文案"
  }
}
```
- 分享京ME
```
{
  channel: "ME",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  skuBigImg:
    "https://storage.360buyimg.com/ljd-source/test/testImg.png",
  jdMeExtends: "扩展参数",

  shareSource: {
    source:
      "分享来源(需要分享产品分配)"
  }
}

```
- 分享图片到小红书
```
{
  channel: "XHS",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  xhsparam: {
    title: "标题",
    content:
      "内容-分享图片到小红书【有videos参数就优先取videos】",
    images: [
      "https://storage.360buyimg.com/ljd-source/test/testImg.png",
      "https://storage.360buyimg.com/ljd-source/test/zhogndouIcon.png"
    ]
  }
}
```
- 分享视频到小红书
```
{
  channel: "XHS",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  xhsparam: {
    title: "标题",
    content:
      "内容-分享视频到小红书【有images参数会优先取videos】",
    videos: [
      {
        videoUrl:
          "https://storage.360buyimg.com/ljd-source/test/%E7%83%9F%E8%8A%B1.m4v",
        coverUrl:
          "https://storage.360buyimg.com/ljd-source/test/testImg.png"
      }
    ]
  }
}
```
- 分享海报（使用base64）
```
{
  channel: "QRCode",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  qrparam: {
    qr_direct:
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAsCAMAAACngONPAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAV9QTFRF////srKyY2Nj/f39enl6jYyNtra2t7e39/f3qKeopKOk39/fhYSFjY2N6enp5+bnaWhprq6uSUhJkZGRmpma9vb2fX19REREQ0JDg4ODKCco1dXV4+PjysrKUE9Q29vb+fn5k5OTm5ubyMjINjU2eHh4bm1ueXl5X19fhIOEzs7OcnFyra2t7+/vcnJy8PDwvr6+aWlp1dTVKikqcXBxZGRkYmFi9fX17Ozsf35/XVxdlpaWi4qLk5KTc3JzgH+AkpGSMC8wX15feHd4RkVG2traiYmJTEtMZmVmrKyslZSVjIuMwcHBUlFSNzY3PDs8xsbGamlqqKioWVhZZWRliIeIx8fH2NjYU1JTdHN0h4eH+/v7n56fWFdY1tbWZ2ZnY2Jj7e3tg4KDkI+QLSwtsLCw/v7++vr6enp6sbGxYF9gx8bHrayt3d3dycjJLy4vgICAn5+f4uLigYGBuLi48x4mOQAAAL5JREFUeJxjYBgFwxEwMjGzsLKxs7NzMHBycfMAAS8fRIZfQFBIGAhERMXEJSSlpGVk5eTBEgqKSsoqqmrqGpoMWtpqOlK6DDx6+hA9BoZGxlomjKZm5haWVtZSNrZ29jIQGQtWB0d7J2cXIVc3dw9PKS9vH1+IaQzKfrL+AYFBwSEKot6hYVLhEZFR0RCZmNg4p3izBD+JRIak5DCpRMOU1DSwRHpGJojSycpmyGHKzcsvKCwqLqFPeI0C+gIASjgfSjFu+bYAAAAASUVORK5CYII="
  }
}

```
- 分享海报（带二维码）
```
{
  channel: "QRCode",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  qrparam: {
    mid_pic_x:
      "https://img12.360buyimg.com/imagetools/jfs/t1/107600/32/47767/41009/65e58c09F59d2bb9d/eb4d3b07ba73933e.png",
    qr_type: "2"
  }
}
```
- 分享海报（带二维码和文字）
```
{
  channel: "QRCode",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  qrparam: {
    mid_pic_x2:
      "https://img12.360buyimg.com/imagetools/jfs/t1/94831/35/48006/9139/65e5c3acFbd2c0469/33574f4d44e7baf4.png",
    qr_title_x2: [
      {
        title:
          "文字可以有不同的颜色",
        ex_content: "颜色"
      },
      {
        title:
          "但颜色是固定的，不能修改"
      },
      {
        title:
          "文字大小、布局也不能修改"
      },
      {
        title:
          "一行文字超出长度后会显示省略号"
      }
    ],
    qr_type: "2"
  }
}
```
- 分享海报（中间图片，下面二维码）
```
{
  channel: "QRCode",

  title: "比赛第一",
  content:
    "就差你了，快来帮我助力抽大奖！",
  url: "https://pro.m.jd.com",
  img:
    "https://img12.360buyimg.com/img/jfs/t1/316746/3/13075/5081/68663b8cF61528ef3/480bf7a9e1311108.png",

  qrparam: {
    qr_title: "",
    mid_pic:
      "https://img12.360buyimg.com/homechannel/jfs/t20280715/294733/1/8364/195902/68777f12Fa6148a34/71c3c972da2dbfd8.png",
    slogan: "",
    qr_content: "",
    top_pic:
      "https://img12.360buyimg.com/img/jfs/t1/316746/3/13075/5081/68663b8cF61528ef3/480bf7a9e1311108.png"
  }
}
```
- 分享微信小程序卡片
```
{
  channel: "Wxfriends",

  title: "微信小程序卡片的标题",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  mpId: "gh_45b306365c3d",
  mpIconUrl:
    "https://img13.360buyimg.com/imagetools/jfs/t1/115966/32/35131/2698/6433bc46Fbedb0e90/7bd7f73e0dc8fbd3.png",
  mpPath: "/pages/index/index",
  mpType: "0"
}
```
- 分享京口令（微信好友）
```
{
  channel: "Wxfriends",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  keyparam: {
    keyTitle: "京口令京东app内标题",
    keyContent:
      "京口令分享文字内容。\n可以换行。",
    keyImg:
      "https://img11.360buyimg.com/imagetools/jfs/t1/105241/11/38687/5228/6433bc46F0d23c83d/733866745e53690e.png",
    url: "https://m.jd.com",
    keyEndTime: "1757001600000",
    keyId: "1",
    sourceCode: "jdjssdkexample",
    keyChannel: "Wxfriends"
  }
}

```
- 分享京口令（微信朋友圈）
```
{
  channel: "Wxmoments",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  keyparam: {
    keyTitle: "京口令京东app内标题",
    keyContent:
      "京口令分享文字内容。\n可以换行。",
    keyImg:
      "https://img11.360buyimg.com/imagetools/jfs/t1/105241/11/38687/5228/6433bc46F0d23c83d/733866745e53690e.png",
    url: "https://m.jd.com",
    keyEndTime: "1757001600000",
    keyId: "2",
    sourceCode: "jdjssdkexample",
    keyChannel: "Wxmoments"
  }
}

```
- 分享京口令（QQ好友）
```
{
  channel: "QQfriends",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  keyparam: {
    keyTitle: "京口令京东app内标题",
    keyContent:
      "京口令分享文字内容。\n可以换行。",
    keyImg:
      "https://img11.360buyimg.com/imagetools/jfs/t1/105241/11/38687/5228/6433bc46F0d23c83d/733866745e53690e.png",
    url: "https://m.jd.com",
    keyEndTime: "1757001600000",
    keyId: "3",
    sourceCode: "jdjssdkexample",
    keyChannel: "QQfriends"
  }
}
```
- 分享京口令（QQ空间）
```
{
  channel: "QQzone",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  keyparam: {
    keyTitle: "京口令京东app内标题",
    keyContent:
      "京口令分享文字内容。\n可以换行。",
    keyImg:
      "https://img11.360buyimg.com/imagetools/jfs/t1/105241/11/38687/5228/6433bc46F0d23c83d/733866745e53690e.png",
    url: "https://m.jd.com",
    keyEndTime: "1757001600000",
    keyId: "4",
    sourceCode: "jdjssdkexample",
    keyChannel: "QQzone"
  }
}
```
- 分享京口令（新浪微博）
```
{
  channel: "Sinaweibo",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  keyparam: {
    keyTitle: "京口令京东app内标题",
    keyContent:
      "京口令分享文字内容。\n可以换行。",
    keyImg:
      "https://img11.360buyimg.com/imagetools/jfs/t1/105241/11/38687/5228/6433bc46F0d23c83d/733866745e53690e.png",
    url: "https://m.jd.com",
    keyEndTime: "1757001600000",
    keyId: "5",
    sourceCode: "jdjssdkexample",
    keyChannel: "Sinaweibo"
  }
}
```
- 使用微信内容分享参数
```
{
  channel: "Wxfriends",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  weiXinContentParam: {
    titleTag: "titleTag",
    price: "price",
    secKill: "secKill",
    ranking: "ranking",
    rating: "rating"
  }
}
```
- 使用新样式的分享面板
```
{
  channel: "",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  isNewWeixinShareUI: "1"
}

```
- 分享回调
```
{
  channel: "",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  callback: function(result) {
    console.log("callback", result);
  },
  clickcallback: function(result) {
    console.log(
      "clickcallback",
      result
    );
  }
}
```
- 分享来源
```
{
  channel: "",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  shareSource: {
    source: "分享来源",
    scene: "分享场景"
  }
}

```
- 隐藏“[来自PLUS会员分享]”
```
{
  channel: "",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  hidePlus: "1"
}
```
- h5自定义分享渠道
```
{
  channel: "",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  cmChannel: [
    {
      iconId: "icon0",
      iconUrl:
        "https://img11.360buyimg.com/imagetools/jfs/t1/18327/40/20852/2063/669617ccF1ce5d4a0/6d44511071834033.png",
      iconName: "渠道名称",
      jumpUrl: "https://m.jd.com",
      source: "source0"
    }
  ]
}
```
- openApp自定义分享渠道
```
{
  channel: "",

  title: "兜底title",
  content: "兜底content",
  url: "https://m.jd.com",
  img:
    "https://img11.360buyimg.com/imagetools/jfs/t1/168615/5/36608/1893/6433bc46F970e76d8/b1fc825898371b6d.png",

  cmChannel: [
    {
      iconId: "icon1",
      iconUrl:
        "https://img12.360buyimg.com/imagetools/jfs/t1/18828/9/21462/3122/669617ccFffefd9a5/57186d3fd814c998.png",
      iconName:
        "超过4个字会显示省略号",
      jumpUrl:
        "openApp.jdMobile://virtual?params=%7B%22category%22%3A%22jump%22%2C%22des%22%3A%22cartB%22%7D",
      source: "source1"
    }
  ]
}
```

- 全部完成配置
```
{
  channel:
    "Wxfriends,Wxmoments,XHS,QQfriends,QQzone,Sinaweibo,CopyURL,CopyJDKey,ME,QRCode",

  title: "微信小程序卡片的标题",
  content:
    "就差你了，快来帮我助力抽大奖！",
  url: "https://pro.m.jd.com",
  img:
    "https://img12.360buyimg.com/img/jfs/t1/316746/3/13075/5081/68663b8cF61528ef3/480bf7a9e1311108.png",
  subtitle: "副标题",
  timeline_title:
    "分享到朋友圈的标题",

  qrparam: {
    qr_title: "",
    mid_pic:
      "https://img12.360buyimg.com/homechannel/jfs/t20280715/294733/1/8364/195902/68777f12Fa6148a34/71c3c972da2dbfd8.png",
    slogan: "",
    qr_content: "",
    top_pic:
      "https://img12.360buyimg.com/img/jfs/t1/316746/3/13075/5081/68663b8cF61528ef3/480bf7a9e1311108.png"
  },
  shareGiftParam: {
    giftTitle: "标题",
    giftSubTitle:
      "副标题[只有callSharePane方法唤起的分享面板支持此能力]",
    jumpTitle: "业务个性化元素",
    jump: "跳转链接",
    successToast: "分享成功文案",
    failToast: "分享失败文案"
  },
  xhsparam: {
    title: "标题",
    content:
      "内容-分享视频到小红书【有images参数会优先取videos】",
    videos: [
      {
        videoUrl:
          "https://storage.360buyimg.com/ljd-source/test/%E7%83%9F%E8%8A%B1.m4v",
        coverUrl:
          "https://storage.360buyimg.com/ljd-source/test/testImg.png"
      }
    ]
  },
  skuBigImg:
    "https://storage.360buyimg.com/ljd-source/test/testImg.png",
  jdMeExtends: "扩展参数",

  mpId: "gh_45b306365c3d",
  mpIconUrl:
    "https://img13.360buyimg.com/imagetools/jfs/t1/115966/32/35131/2698/6433bc46Fbedb0e90/7bd7f73e0dc8fbd3.png",
  mpPath: "/pages/index/index",
  mpType: "0",

  keyparam: {
    keyTitle: "京口令京东app内标题",
    keyContent:
      "京口令分享文字内容。\n可以换行。",
    keyImg:
      "https://img11.360buyimg.com/imagetools/jfs/t1/105241/11/38687/5228/6433bc46F0d23c83d/733866745e53690e.png",
    url: "https://m.jd.com",
    keyEndTime: "1757001600000",
    keyId: "5",
    sourceCode: "jdjssdkexample",
    keyChannel:
      "Wxfriends,Wxmoments,QQfriends,QQzone,Sinaweibo"
  },

  weiXinContentParam: {
    titleTag: "titleTag",
    price: "price",
    secKill: "secKill",
    ranking: "ranking",
    rating: "rating"
  },

  isNewWeixinShareUI: "1",

  callback: function(result) {
    console.log("callback", result);
  },
  clickcallback: function(result) {
    console.log(
      "clickcallback",
      result
    );
  },

  shareSource: {
    source: "分享来源",
    scene: "分享场景"
  },
  hidePlus: "1",
  cmChannel: [
    {
      iconId: "icon0",
      iconUrl:
        "https://img11.360buyimg.com/imagetools/jfs/t1/18327/40/20852/2063/669617ccF1ce5d4a0/6d44511071834033.png",
      iconName: "渠道名称",
      jumpUrl: "https://m.jd.com",
      source: "source0"
    },
    {
      iconId: "icon1",
      iconUrl:
        "https://img12.360buyimg.com/imagetools/jfs/t1/18828/9/21462/3122/669617ccFffefd9a5/57186d3fd814c998.png",
      iconName:
        "超过4个字会显示省略号",
      jumpUrl:
        "openApp.jdMobile://virtual?params=%7B%22category%22%3A%22jump%22%2C%22des%22%3A%22cartB%22%7D",
      source: "source1"
    }
  ]
}

```

## 要求
1. 组件库使用"vant": "^4.6.8" （已安装）
2. 使用vue3 compositionAPI typescript
3. 按照已知所有sdk进行合理的分组，合理的交互，进行分享配置组装