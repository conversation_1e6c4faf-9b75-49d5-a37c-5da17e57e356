import { createApp } from 'vue';
import root from './App.vue';
import { initRem } from '@/utils/client';
import 'vant/lib/index.css';

// 导入需要的 Vant 组件
import {
  NavBar,
  CellGroup,
  Cell,
  Collapse,
  CollapseItem,
  Button,
  Popup,
  Form,
  Field,
  Picker,
  Divider,
  Toast,
  Dialog,
  Notify,
  Tabbar,
  TabbarItem,
  Tabs,
  Tab,
  CheckboxGroup,
  Checkbox,
  Switch,
  Tag
} from 'vant';

document.title = '分享调试工具';

new window.VConsole();

initRem();
const app = createApp(root);

// 注册 Vant 组件
app.use(NavBar);
app.use(CellGroup);
app.use(Cell);
app.use(Collapse);
app.use(CollapseItem);
app.use(Button);
app.use(Popup);
app.use(Form);
app.use(Field);
app.use(Picker);
app.use(Divider);
app.use(Toast);
app.use(Dialog);
app.use(Notify);
app.use(Tabbar);
app.use(TabbarItem);
app.use(Tabs);
app.use(Tab);
app.use(CheckboxGroup);
app.use(Checkbox);
app.use(Switch);
app.use(Tag);

app.mount('#app');
