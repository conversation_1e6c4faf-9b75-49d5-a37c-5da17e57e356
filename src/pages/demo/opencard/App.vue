<template>
  <textarea v-model="testOpenCard" rows="4" style="width: 100%" placeholder="请输入开卡链接" class="multi-line-textarea"></textarea>
  <div class="test-btn" @click="test">测试半屏开卡</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import openCard from '@/utils/openCard';

const testOpenCard = ref('https://shop.m.jd.com/member/card?venderId=1000002836&shopId=1000002836&channel=401');

const test = () => {
  if (!testOpenCard.value) return;
  openCard(testOpenCard.value, {});
};
</script>

<style lang="scss" scoped>
.test-btn {
  width: 100%;
  background-color: #007a54;
  color: #fff;
  padding: 0.1rem 0;
  text-align: center;
}
</style>
