<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv">
      <div class="header-content" :style="limitOrder === 0 ? {marginBottom:'6.62rem'} : {}" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>
        <div>
          <div
            class="header-btn"
            :style="furnishStyles.headerBtn.value"
            v-for="(btn, index) in btnList"
            :key="index"
            @click="btn.event"
          >
            {{btn.name}}
          </div>
          <div class="header-btn" v-if="limitOrder === 1" :style="furnishStyles.headerBtn.value" @click="showToast('活动预览，仅供查看')">活动商品</div>
        </div>
      </div>
    </div>
    <div class="hotZoneBox">
      <img class="hotZone" :src="furnish.hotZoneBg" alt="">
      <div class="hotBtn" v-for="(item, index) in showData" :key="index" :style="item.style" @click="ShowToast"></div>
      <div class="remainBox">奖品剩余{{prizeRemains}}份</div>
    </div>
    <div class="bottomBox">
      <div class="bottomRule" :style="furnishStyles.shortRuleBg.value"></div>
    </div>
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!--    活动商品弹窗-->
    <VanPopup teleport="body" v-model:show="showGoods">
      <GoodsPopup :data="orderSkuListPreview" :orderSkuList="orderSkuList" @close="showGoods = false"></GoodsPopup>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, reactive } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';
import GoodsPopup from '../components/GoodsPopup.vue';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);

const isLoadingFinish = ref(false);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
}
const prizeList = ref<Prize>(defaultStateList);

type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuListPreview = ref<Sku[]>([]); // 曝光商品
const skuList = ref<Sku[]>([]); // 曝光商品
const orderSkuList = ref<Sku[]>([]);
const orderSkuListPreview = ref<Sku[]>([]);

const nextStateAmount = ref(0);
const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const showAward = ref(false);
const showOrderRecord = ref(false);
const showGoods = ref(false);
const limitOrder = ref(0);
// 奖品剩余份数
const prizeRemains = ref(0);

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};
const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => { showRule.value = true; } },
  {
    name: '我的奖品',
    event: () => { showMyPrize.value = true; },
  },
];

const isAllOrderSku = ref(1);
const isExposure = ref(1);

const activityGiftRecords = reactive([
  {
    userImg: '',
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: '',
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: '',
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: '',
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: '',
    nickName: '用户***',
    giftName: '20积分',
  },
]);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  showOrderRecord.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

const showData = ref([]);

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  console.log('furnish', furnish);
  // 热区部分样式赋值
  showData.value = furnish.hotZoneList;
  showData.value.forEach((item: any) => {
    const style:any = {};
    style.width = `${((item.width * 2) / 100) * 0.9186}rem`;
    style.height = `${((item.height * 2) / 100) * 0.9186}rem`;
    style.position = 'absolute';
    style.top = `${((item.top * 2) / 100) * 0.9186}rem`;
    style.left = `${((item.left * 2) / 100) * 0.9186}rem`;
    item.style = style;
  });
  console.log(furnish.hotZoneList);
  console.log(showData.value);
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  const list = data.prizeList[0];
  if (list.prizeType !== 0) {
    prizeList.value = list;
  }
  // else {
  //   prizeList.value = defaultStateList;
  // }
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuListPreview) {
    skuList.value = data.skuList;
    skuListPreview.value = data.skuListPreview;
  }
  if (data.orderSkuListPreview) {
    orderSkuListPreview.value = data.orderSkuListPreview;
    orderSkuList.value = data.orderSkuList;
  }
  if (data.limitOrder === 0) {
    limitOrder.value = 0;
  }
  if (data.limitOrder === 1) {
    limitOrder.value = 1;
  }
  ruleTest.value = data.rules;
  isAllOrderSku.value = data.isAllOrderSku;
  isExposure.value = data.isExposure;
  prizeRemains.value = data.prizeList[0].sendTotalCount;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
});
// // 点击边框监听
// registerHandler('border', (data: any) => {
//   showSelect.value = data;
// });

onMounted(() => {
  if (activityData) {
    const data = activityData;
    endTime.value = dayjs(data.endTime).valueOf();
    const list = data.prizeList[0];
    if (list.prizeType !== 0) {
      prizeList.value = list;
    } else {
      prizeList.value = defaultStateList;
    }
    startTime.value = new Date(data.startTime).getTime();
    if (startTime.value > new Date().getTime()) {
      isStart.value = false;
    }
    if (startTime.value < new Date().getTime()) {
      isStart.value = true;
    }
    endTime.value = new Date(data.endTime).getTime();
    // if (data.skuList) {
    //   skuList.value = data.skuList;
    //   skuListPreview.value = data.skuListPreview;
    // }
    // if (data.orderSkuListPreview) {
    //   orderSkuList.value = data.orderSkuList;
    //   orderSkuListPreview.value = data.orderSkuListPreview;
    // }
    ruleTest.value = data.rules;
    // isAllOrderSku.value = data.isAllOrderSku;
    // isExposure.value = data.isExposure;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});
const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style lang="scss" scoped>
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}
.prize-select-box{
  padding-bottom: 0.6rem;
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0;
    margin-bottom: 6rem;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.36rem;
    height: 0.52rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-size: 100%;
  }
}

.hotZoneBox{
  width: 6.89rem;
  margin: 0 auto;
  position: relative;
}
.hotZone{
  width: 100%;
}

//.hotBtn {
//  background-color: #000;
//}

.remainBox {
  position: absolute;
  bottom: 0.14rem;
  left: 4.4rem;
  font-size: 0.18rem;
}

.bottomBox{
 padding: 0.1rem 0 0.5rem;
}

.bottomRule {
  width: 7.09rem;
  height: 9.08rem;
  margin: 0.2rem auto;
  background-size: 100%;
}

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
