import { computed, reactive } from 'vue';

export const furnish = reactive({
// 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 按钮
  btnColor: '',
  btnBg: '',
  hotZoneBg: '',
  hotZoneList: [],
  shortRuleBg: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundImage: furnish.btnBg ? `url(${furnish.btnBg})` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const shortRuleBg = computed(() => ({
  backgroundImage: furnish.shortRuleBg ? `url(${furnish.shortRuleBg})` : '',
}));

const hotZoneBg = computed(() => ({
  backgroundImage: furnish.hotZoneBg ? `url(${furnish.hotZoneBg})` : '',
}));

export default {
  pageBg,
  shopNameColor,
  headerBtn,
  shortRuleBg,
  hotZoneBg,
};
