<template>
  <div class="rule-bk" :style="furnishStyles.ruleDialogBg.value">
    <div class="rule">
      <div v-html="rule"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import furnishStyles, { furnish } from '../ts/furnishStyles';

const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};

</script>

<style scoped lang="scss">
.rule-bk {
  // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/253888/13/13815/127662/67889e90F8e353d55/8d84c15ec953443f.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.48rem;
  height: 8rem;
  position: relative;
}
.rule {
  height: 6.2rem;
  width: 6.4rem;
  padding: 0 0.3rem;
  font-size: 0.27rem;
  color: #f13033;
  font-weight: 600;
  white-space: pre-wrap;
  word-break: break-all;
  position: absolute;
  top: 1.2rem;
  left: 50%;
  transform: translateX(-50%);
  div {
    height: 100%;
    overflow-y: scroll;
  }
}
.close {
  width: 0.9rem;
  height: 0.9rem;
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  bottom: 0.3rem;
  //background: #2d8cf0;
}
</style>
