import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  actBg: '',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/264732/22/12412/103367/67887d8fFb23c0be0/163a3469880894c9.png',
  actBgColor: '#8c1717',
  ruleBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/254339/26/13774/2820/67887d90F15452f4d/b6827c2b38ef5aa5.png',
  myOrderBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/254123/16/13075/2678/67887d8eF47c41021/6b47af7837f957f8.png',
  myPrizeBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/263014/35/12536/2991/67887d8eF8c8dfcf4/b6510db93e30f650.png',
  goodsBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/266714/35/13359/2903/678b5f27F696e359b/00e5acd3e44a8d13.png',
  prizeAreaBg: '//img10.360buyimg.com/imgzone/jfs/t1/254205/5/13550/116898/6788ae63F27c80679/0560e5911784f88f.png',
  countDownBg: '//img10.360buyimg.com/imgzone/jfs/t1/263946/34/12512/2951/67887d90F0f3f37c1/33e9d31b7cd81e36.png',
  toBuyBtn: '//img10.360buyimg.com/imgzone/jfs/t1/267468/40/12506/3817/67887d8cFd372ec9e/6eb44fc02b15a8dc.png',
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/265182/19/12508/8701/67887d91F856f8d46/137f8a698657b26a.png',
  showGoodsBg: '//img10.360buyimg.com/imgzone/jfs/t1/265087/34/12443/87588/67887d8dF0243c61e/215b0cb07ef36af6.png',
  ruleDialogBg: '//img10.360buyimg.com/imgzone/jfs/t1/253136/33/13301/127662/67887db3F0ba8f923/658c3099cd05e662.png',
  myPrizeDialogBg: '//img10.360buyimg.com/imgzone/jfs/t1/255439/27/13627/126502/67887db6F4e8742b9/7174d747bccfd98e.png',
  myOrderDialogBg: '//img10.360buyimg.com/imgzone/jfs/t1/254128/1/11031/129606/67887db5Fb37835e6/07276f81613f6926.png',
  addressDialog: '//img10.360buyimg.com/imgzone/jfs/t1/115939/22/47341/15021/67887db4F80c82d5a/e4a85b974352359f.png',
  addConfirmBtn: '//img10.360buyimg.com/imgzone/jfs/t1/254360/13/13722/8408/67888531F258a5e61/a42827b74c132738.png',
  ladderWarningBg: '//img10.360buyimg.com/imgzone/jfs/t1/237520/28/10203/76617/6593bcc3F7e1601b9/359d2b437b1572a4.png',
  confirmationBg: '//img10.360buyimg.com/imgzone/jfs/t1/255918/22/13258/129464/67887db4F094d18e3/bf52df75aa8ccfc3.png',
  successDialog: '//img10.360buyimg.com/imgzone/jfs/t1/261176/23/12539/127989/67887db3F93144c7f/529ce56fc854072e.png',
  goodsDialogBg: '//img10.360buyimg.com/imgzone/jfs/t1/254699/20/14121/121321/678b6358F3d56627f/134ba7605bbe7f93.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/229813/25/10578/76959/6593bcc6F530ebe60/5087fb2b6c43ec0f.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/232663/18/9044/12308/6593bcc7F67021cf8/146508ec836842d0.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/230477/32/10477/88198/6593bcc6F56ee1368/eb50a9cbe792fc66.png',
  disableShopName: 0,
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  // 设置页面title
  document.title = activityData?.activityName || '下单满罐有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  // app.provide('_decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
