<template>
  <div class="title">
    <img alt="" data-v-705393a4="" src="https://img10.360buyimg.com/imgzone/jfs/t1/99905/3/40427/1341/64c9b227Ffca6ed5f/6e1393c7388d19ac.png" class="close" @click="close" />
  </div>
  <div class="rule-bk">
    <div class="content">
      <div class="form">
        <VanField v-model="phone" required label="电话：" maxlength="11" type="number"></VanField>
      </div>
      <p class="tip-title">重要提醒:</p>
      <div class="tip">{{ planDesc }}</div>
      <div class="submit" @click="checkForm"></div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="confirmPopup">
    <div class="affiem-inner" @click.stop>
      <div class="affiem-text">{{ phone }}</div>
      <div class="affiem-hint">请再次确定号码填写无误</div>
      <div class="affiem-word">
        <div class="affiem-balck">重要提示：</div>
        <p>① 充值成功后 无法退换；</p>
        <p>② 切勿写错手机号，如充错，责任自行承担；</p>
        <p>③ 点击【确认提交】后，权益领取手机号会无法修改，请 确认无误后再点击确认。</p>
      </div>
      <div class="flex">
        <div class="affirm-cancel-btn m-r-37" @click="confirmPopup = false"></div>
        <div class="affiem-btn" @click="submit"></div>
      </div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { ref } from 'vue';
import { httpRequest } from '@/utils/service';

const props = defineProps({
  userPrizeId: {
    type: String,
    required: true,
  },
  planDesc: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const phone = ref('');

// 二次确认弹窗
const confirmPopup = ref(false);

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/80020/fuLuReceive', {
      userPrizeId: props.userPrizeId,
      phone: phone.value,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!phone.value) {
    showToast('请输入电话');
  } else if (!checkPhone.test(phone.value)) {
    showToast('请输入正确的电话');
  } else {
    // submit();
    confirmPopup.value = true;
  }
};
</script>

<style scoped lang="scss">
.title {
  position: relative;
  display: flex;
  width: 7.5rem;
  align-items: center;
  justify-content: center;
  height: 1rem;
  font-size: 0.4rem;
  font-weight: bold;
  color: #dc84aa;
  .close {
    width: 0.5rem;
    height: 0.5rem;
  }
}
.rule-bk {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/106865/28/43607/239452/64d98b8dFd2e38c74/11c7531ff57eee89.png) no-repeat;
  background-size: 100%;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;
  height: 6.4rem;
  padding-top: 1.6rem;

  .title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.4rem;
    font-weight: bold;
    color: #dc84aa;

    .leftLineDiv {
      width: 0.5rem;
      height: 0.04rem;
      background-color: #dc84aa;
      margin-right: 0.2rem;
    }

    .rightLineDiv {
      width: 0.5rem;
      height: 0.04rem;
      background-color: #dc84aa;
      margin-left: 0.2rem;
    }
  }

  .close {
    position: absolute;
    top: -0.9rem;
    right: 0.26rem;
    width: 0.3rem;
    opacity: 0;
  }

  .content {
    border: 0.3rem solid transparent;
    font-size: 0.24rem;
    color: #333333;
    margin-top: 0.6rem;
    white-space: pre-wrap;

    .form {
      .van-cell {
        height: 0.87rem;
        border-radius: 0.08rem;
        margin-bottom: 0.15rem;
        box-shadow: -0.007rem 0.019rem 0.098rem 0.002rem rgba(186, 170, 116, 0.25);
        align-items: center;

        &::after {
          display: none;
        }
      }
    }

    .tip {
      font-size: 0.18rem;
      color: #b3b3b3;
      height: 1.2rem;
      overflow-y: scroll;
      margin-top: 0.25rem;
    }

    .submit {
      width: 2.86rem;
      height: 0.71rem;
      margin: 0.2rem auto 0;
      text-align: center;
      font-size: 0.24rem;
      line-height: 0.71rem;
      color: #fff;
      background: url(https://img10.360buyimg.com/imgzone/jfs/t1/185090/23/36109/6962/64d98b8cFa2d445b3/2eb5395fd48f1b84.png) no-repeat;
      background-size: 100%;
    }
  }
}

// 二次确认弹窗
.affirm-box {
  z-index: 300;
  display: flex;
  align-items: center;
  justify-content: center;
}
.affiem-inner {
  //background-color: #fff;
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/190504/6/36247/110813/64d98bf2F193ba39f/8a19a8664b99c937.png);
  background-repeat: no-repeat;
  background-size: 100%;
  border-radius: 0.2rem;
  width: 5.49rem;
  height: 6.09rem;
  min-height: 2rem;
  padding: 0.4rem 0.2rem;
}
.affiem-text {
  margin-top: 0.09rem;
  font-size: 0.3rem;
  text-align: center;
  line-height: 0.54rem;
  color: #262626;
}
.affiem-hint {
  color: #dc84aa;
  font-weight: 400;
  font-size: 0.26rem;
  text-align: center;
  line-height: 0.54rem;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.22rem;
}
.affirm-cancel-btn {
  width: 2.23rem;
  border-radius: 0.3rem;
  height: 0.6rem;
  line-height: 0.6rem;
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/124972/5/36059/4933/64d98c68F100674ff/cb5f466f95be4219.png);
  background-repeat: no-repeat;
  background-size: 100%;
  text-align: center;
  font-size: 0.24rem;
  box-sizing: border-box;
}
.affiem-btn {
  width: 2.23rem;
  border-radius: 0.3rem;
  height: 0.6rem;
  line-height: 0.6rem;
  color: #fff;
  text-align: center;
  font-size: 0.24rem;
  box-sizing: border-box;
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/135495/7/39481/4437/64d98c68Fcf1d22d9/15ea605d49ec2581.png);
  background-repeat: no-repeat;
  background-size: 100%;
}
.m-r-37 {
  margin-right: 0.37rem;
}
.affiem-word {
  color: #8c8c8c;
  font-size: 0.2rem;
  margin-top: 0.4rem;
  padding: 0 0.2rem;
  line-height: 0.4rem;
}
.affiem-balck {
  color: #262626;
  line-height: 0.43rem;
}
</style>
