<template>
  <div class="rule-bk">
    <div class="title"></div>
    <div class="content" v-html="rule"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 6.1rem;
  height: 9rem;
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/119781/6/41717/148503/64d1ba6bF93531c9a/f206b732cc20f671.png) no-repeat;
  background-size: 100%;

  .title {
    position: relative;
    height: 1.14rem;
  }

  .content {
    padding: 0 0.7rem;
    height: 6.3rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    word-break: break-all;
  }
  .close {
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/99905/3/40427/1341/64c9b227Ffca6ed5f/6e1393c7388d19ac.png) no-repeat;
    background-size: 100%;
    width: 0.53rem;
    height: 0.53rem;
    margin: 0.4rem auto 0 auto;
  }
}
</style>
