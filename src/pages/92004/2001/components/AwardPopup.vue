<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <img :src="prize.showImg" alt="" class="prize-img" />
    <p class="p1">恭喜你 中奖了</p>
    <p class="prize-name">{{ prize.prizeName }}</p>
    <div class="btn" @click="saveAddress" v-if="prize.prizeType === 3">填写地址</div>
    <div class="btn" @click="gotoShopPage(baseInfo.shopId)" v-else-if="prize.prizeType === 11">去领取</div>
    <div class="close" @click="close"></div>
  </div>
  <div class="thanks-join" v-else>
    <div class="close" @click="close"></div>
    <div class="btn" @click="gotoShopPage(baseInfo.shopId)"></div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { gotoShopPage, gotoSkuPage, exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  showImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const saveAddress = () => {
  emits('saveAddress', props.prize.result.result, props.prize.activityPrizeId, props.prize.userPrizeId);
};

const showCardNum = () => {
  emits('showCardNum', { ...props.prize.result, showImg: props.prize.showImg, prizeName: props.prize.prizeName });
};

const savePhone = () => {
  emits('savePhone', props.prize.userPrizeId, props.prize.result.result.planDesc);
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/281597/13/18163/2423/67f7af8eF1a1b7ee6/03411ae8226fa226.png) no-repeat;
  background-size: 100%;
  width: 5.5rem;
  height: 6.5rem;
  padding-top: 1.2rem;
  .prize-img {
    height: 2rem;
    width: 2rem;
    margin: 0 auto;
  }
  .p1 {
    font-size: 0.34rem;
    line-height: 0.4rem;
    text-align: center;
    margin-top: 0.2rem;
  }
  .prize-name {
    font-size: 0.2rem;
    line-height: 0.2rem;
    text-align: center;
    margin-top: 0.2rem;
    opacity: 0.5;
  }
  .btn {
    margin: 0.3rem auto 0;
    width: 1.5rem;
    height: 0.4rem;
    background-color: #000;
    color: #fff;
    text-align: center;
    line-height: 0.4rem;
    font-size: 0.18rem;
  }
}
.thanks-join {
  width: 5.5rem;
  height: 6.5rem;
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/279779/11/20026/27451/67fdb6ebF4cac71a4/c90d5c1d248dabe1.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 4.4rem;
  .btn {
    display: block;
    margin: 0 auto;
    width: 4rem;
    height: 0.76rem;
  }
}
.close {
  width: 0.3rem;
  height: 0.3rem;
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
}
</style>
