import { computed, reactive } from 'vue';
import { prizePlateArray } from '@/utils/prizePlateArray';

export const prizeInfo = reactive([
  {
    // id: '',
    // index: 1,
    // prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png',
    // prizeName: 'XX积分',
    // shareNum: 0,
    // sendTotalCount: 0,
    // prizeType: 4,
    // img: '',
    // num: 0,
    // peopleNum: 0,
    // status: 0,
    prizeName: 'XX积分',
    prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png',
    prizeType: 4,
    sendTotalCount: 0,
    type: 0,
    peopleNum: 0,
    rank: 0,
    img: '',
    num: 0,
    status: 1,
    userImg: '',
  },
]);

export const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};
export const furnish = reactive({
  actBg: '',
  actBgColor: '',
  ruleBtn: '',
  myPrizeBtn: '',
  rankPrize: '',
  // 排行榜背景
  rankBg: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
});

export const rankBg = computed(() => ({
  backgroundImage: furnish.rankBg ? `url("${furnish.rankBg}")` : '',
}));

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  // backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));
export default {
  pageBg,
  rankBg,
};
