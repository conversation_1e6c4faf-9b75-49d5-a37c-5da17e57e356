<template>
  <div class="kv">
    <img class="head-btn rule-btn" src="//img10.360buyimg.com/imgzone/jfs/t1/330454/21/17134/2710/68d52fb3F5d018f9a/f46cf5884ef7e62e.png" alt="" @click="showPopup.showRule = true" />
    <img class="head-btn prize-btn" src="//img10.360buyimg.com/imgzone/jfs/t1/328018/33/23949/2844/68d52fb3F753ebf01/3abb8116d156e344.png" alt="" @click="showPopup.showMyPrize = true" />
    <div class="prize-list-sku">
      <div class="swiper-container" ref="swiperRefSku">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="item in prizeSkuList" :key="item?.id">
            <div class="prize-card" @click="gotoSkuPage(item.skuId)">
              <img class="prize-img" :src="item.imgUrl" alt="" />
              <div class="prize-name">{{ item.skuName }}</div>
              <div class="prize-title">{{ item.title }}</div>
              <img class="draw-btn" src="//img10.360buyimg.com/imgzone/jfs/t1/330700/4/17207/2797/68d54415F0e8ee6cc/37901d653886e766.png" alt="" @click="gotoSkuPage(item.skuId)" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="prize-list">
      <div class="swiper-container" ref="swiperRef">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="item in prizeList.filter((it) => it.poolId === 1)[0]?.prizes" :key="item?.id">
            <div class="prize-card">
              <img class="prize-img" :src="item.rightsImg1" alt="" />
              <div class="prize-name">{{ item.rightsName }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="remaining-points" @click="showPopup.showMyPoints = true">
        剩余积分：<span>{{ actInfo.availableScore }}</span>
      </div>
      <img class="draw-btn1" src="//img10.360buyimg.com/imgzone/jfs/t1/346970/40/7439/2888/68d540e0F4fc6c6ea/d865a0436a74ab82.png" alt="" @click="draw('1')" />
      <div class="tips">每消耗{{ actInfo.pool1Score }}积分，可参与1次抽奖</div>
    </div>
    <img class="img-draw" src="//img10.360buyimg.com/imgzone/jfs/t1/347667/8/7318/90302/68d5450bF5fff9c7d/e4d37f412dc18e6f.png" alt="" />
    <!-- <div class="draw-bg" v-if="actInfo.activityScore >= actInfo.pool2BlockScore">
      <img class="draw-btn2" src="//img10.360buyimg.com/imgzone/jfs/t1/314195/8/4718/7253/6836bdeaF31716690/1ad81fcf29a08ed4.png" alt="" @click="draw('2')" />
    </div>
    <div class="draw-bg draw-bg-false" v-else>
      <div class="text">
        当前实时总积分：<span>{{ actInfo.activityScore }}</span>
      </div>
      <div class="progress-container">
        <span class="label">0</span>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: fillPercent + '%' }"></div>
        </div>
        <span class="label">{{ actInfo.pool2BlockScore }}</span>
      </div>
    </div> -->
    <div class="task-list">
      <div class="task-info" v-for="(item, index) in actInfo.taskList" :key="index" :style="{ backgroundImage: `url(${JSON.parse(item.taskDescription).imgUrl})` }">
        <div class="tips">{{ JSON.parse(item.taskDescription).title1 }}</div>
        <div class="btn" v-if="item.status === 1" @click="doTask(item)">{{ JSON.parse(item.taskDescription).actText }}</div>
        <div class="btn gray" v-else @click="doTask(item)">{{ item.status === 0 ? JSON.parse(item.taskDescription).actText : JSON.parse(item.taskDescription).garyText }}</div>
      </div>
    </div>
  </div>
  <!-- 活动规则 -->
  <RulePopup :showRule="showPopup.showRule" @close="showPopup.showRule = false" :rule="rule" />
  <!-- 我的奖品 -->
  <MyPrizePopup :showPrize="showPopup.showMyPrize" @close="showPopup.showMyPrize = false" @fillAddress="fillAddress" />
  <!-- 我的积分 -->
  <MyPoints :showMyPoints="showPopup.showMyPoints" @close="showPopup.showMyPoints = false" />
  <!-- 浏览商品 -->
  <BrowseProducts
    :showBrowseProducts="showPopup.showBrowseProducts"
    @close="
      showPopup.showBrowseProducts = false;
      getMainActivity();
    " />
  <!-- 加购商品   -->
  <PurchaseMore
    :showPurchaseMore="showPopup.showPurchaseMore"
    @close="
      showPopup.showPurchaseMore = false;
      getMainActivity();
    " />
  <!-- 下单 -->
  <PlaceAnOrder :showPlaceAnOrder="showPopup.showPlaceAnOrder" @close="showPopup.showPlaceAnOrder = false" />
  <!-- 中奖弹窗 -->
  <WinPrize :showWinPrize="showPopup.showWinPrize" @close="showPopup.showWinPrize = false" :drawInfo="drawInfo" @showAddress="showAddress" />
  <!-- 未中奖弹窗 -->
  <FailWin :showFailWin="showPopup.showFailWin" @close="showPopup.showFailWin = false" />
  <!-- 地址弹窗 -->
  <AddressPopup :showAddress="showPopup.showAddress" @close="showPopup.showAddress = false" :id="addressId" :addressInfo="addressInfo" />
</template>
<script setup lang="ts">
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import { computed, inject, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import RulePopup from './components/RulePopup.vue';
import MyPrizePopup from './components/MyPrizePopup.vue';
import MyPoints from './components/MyPoints.vue';
import BrowseProducts from './components/BrowseProducts.vue';
import PurchaseMore from './components/PurchaseMore.vue';
import PlaceAnOrder from './components/PlaceAnOrder.vue';
import WinPrize from './components/WinPrize.vue';
import FailWin from './components/FailWin.vue';
import AddressPopup from './components/AddressPopup.vue';
import { ActivityInfo, LotteryPrizeList } from './script/type';
import { mainActivity, lotteryPrizeList, getRule, followShop, sign, toLive, drawGift } from './script/ajax';
import { callShare } from '@/utils/platforms/share';
import { showToast } from 'vant';
import { title } from 'process';
import { gotoSkuPage } from '@/utils/platforms/jump';

const pathParams: any = inject('pathParams');
const helpUuid = ref('');
const rule = ref('');
const actInfo = ref<ActivityInfo>({} as ActivityInfo);
const prizeList = ref<LotteryPrizeList[]>([] as LotteryPrizeList[]);
const drawInfo = ref<any>({});
const addressId = ref('');
const addressInfo = ref({});
Swiper.use([Autoplay]);
const showPopup = reactive({
  showRule: false,
  showMyPrize: false,
  showMyPoints: false,
  showBrowseProducts: false,
  showPurchaseMore: false,
  showPlaceAnOrder: false,
  showWinPrize: false,
  showFailWin: false,
  showAddress: false,
});
const swiperRef = ref(null);
const swiperRefSku = ref(null);
const prizeSkuList = [
  {
    imgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/240957/13/30437/49930/68d534e4Febee7a99/3235ff7488cc7b45.jpg',
    title: '享0元安装&升级8年保修金卡',
    skuName: '万和安睡洗V9X Ultra',
    skuId: '100280488668',
  },
  {
    imgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/299501/36/15021/19312/68d666f9F9769d26c/55a3c79c3c02c5db.jpg',
    title: '享0元安装&升级8年保修金卡',
    skuName: '万和安睡洗V9L Ultra',
    skuId: '100205030906',
  },
  {
    imgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/341766/29/7319/46377/68d534e2Fc388fe18/004c629fc4187407.jpg',
    title: '免安装人工费&升级8年保修金卡',
    skuName: '万和A3E·行业至短扁桶',
    skuId: '100276536282',
  },
  {
    imgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/242405/13/30145/29585/68d666f8F097f73d3/ee398b6771155bad.jpg',
    title: '免安装人工费&升级8年保修金卡',
    skuName: '万和R6pro·鲜活好水',
    skuId: '100284852796',
  },
  {
    imgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/326658/14/22891/55522/68d534e3F61705d72/416099212b427fbb.jpg',
    title: '白条三期免息',
    skuName: '万和小双翼V6L Pro搭B9猛火灶',
    skuId: '100202267305',
  },
  {
    imgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/330315/7/17536/29627/68d666f9F31458e2a/2e3e67ddb02f4c14.jpg',
    title: '白条三期免息',
    skuName: '万和无印V9L Pro',
    skuId: '100273434984',
  },
];
// 主接口数据
const getMainActivity = async () => {
  helpUuid.value = pathParams.shareUuid;
  actInfo.value = await mainActivity(helpUuid.value);
  if (actInfo.value.inviteStatus === 1) {
    showToast('助力成功');
  } else if (actInfo.value.inviteStatus === 2) {
    showToast('已助力过当前用户');
  } else if (actInfo.value.inviteStatus === 3) {
    showToast('您已助力过其他人');
  } else if (actInfo.value.inviteStatus === 4) {
    showToast('助力人数已达上限');
  }
};
getMainActivity();
// 奖品列表
const getPrizeList = async () => {
  prizeList.value = await lotteryPrizeList();
};
// 分享
const handleShare = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
    shareUrl: `${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?shareUuid=${actInfo.value.uuid}&debug=1`,
    afterShare: () => {},
  });
};
// 抽奖
const draw = async (poolId: string) => {
  if (poolId === '1') {
    if (actInfo.value.availableScore < actInfo.value.pool1Score) {
      showToast('积分不足');
      return;
    }
  } else if (poolId === '2') {
    if (actInfo.value.availableScore < actInfo.value.pool2Score) {
      showToast('积分不足');
      return;
    }
  }
  const res = await drawGift(poolId);
  if (res.status) {
    if (res.status === 1) {
      showPopup.showWinPrize = true;
      drawInfo.value = res;
      getMainActivity();
    }
  } else if (res.status === 0) {
    showPopup.showFailWin = true;
    getMainActivity();
  }
};
const showAddress = () => {
  showPopup.showAddress = true;
  showPopup.showWinPrize = false;
  addressId.value = drawInfo.value.userPrizeId;
};
const fillAddress = (item: any) => {
  showPopup.showAddress = true;
  showPopup.showMyPrize = false;
  addressId.value = item.id;
  addressInfo.value = item;
};
// 做任务
const doTask = async (item: any) => {
  switch (item.taskType) {
    case 1:
      await followShop();
      getMainActivity();
      setTimeout(() => {
        showToast('关注成功');
      }, 1000);
      break;
    case 2:
      await sign();
      getMainActivity();
      setTimeout(() => {
        showToast('签到成功');
      }, 1000);
      break;
    case 3:
      showPopup.showBrowseProducts = true;
      break;
    case 4:
      showPopup.showPurchaseMore = true;
      break;
    case 5:
      window.location.href = item.taskLinkUrl; // 直播间id
      await toLive(item.id);
      getMainActivity();
      break;
    case 8:
      window.location.href = item.taskLinkUrl; // 直播间id
      await toLive(item.id);
      getMainActivity();
      break;
    case 6:
      showPopup.showPlaceAnOrder = true;
      break;
    case 7:
      handleShare();
      getMainActivity();
      break;
    default:
      break;
  }
};
const fillPercent = computed(() => Math.min(100, Math.max(0, (actInfo.value.activityScore / actInfo.value.pool2BlockScore) * 100)));
onMounted(async () => {
  getPrizeList();

  rule.value = await getRule();
  nextTick(() => {
    new Swiper(swiperRef.value, {
      slidesPerView: 2.5, // 同时显示的slide数量
      observer: true,
      observeParents: true,
      autoplay: {
        delay: 2000,
      },
    });
    new Swiper(swiperRefSku.value, {
      spaceBetween: 10,
      slidesPerView: 1, // 同时显示的slide数量
      observer: true,
      observeParents: true,
      autoplay: {
        delay: 2000,
      },
    });
  });
});
</script>
<style lang="scss" scoped>
.kv {
  width: 100%;
  height: 41rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/332465/30/17365/137757/68d52eddF61863197/b3bf8702a5700d37.jpg') no-repeat;
  background-size: 100%;
  position: relative;
  padding-top: 9.6rem;
  .head-btn {
    width: 1.4rem;
    position: absolute;
    right: 0;
  }
  .rule-btn {
    top: 3rem;
  }
  .prize-btn {
    top: 3.7rem;
  }
  .prize-list-sku {
    width: 6.9rem;
    height: 8.5rem;
    margin: 0 auto;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/243030/38/28896/63002/68d53033Fe0352055/c59c965471dde6b1.png') no-repeat;
    // padding: 1.2rem 0.5rem 0;
    padding-top: 1.2rem;
    background-size: 100%;
    .swiper-container {
      width: 6rem;
      height: 6.55rem;
      margin: 0 auto;
      overflow: hidden;
      // background: url('//img10.360buyimg.com/imgzone/jfs/t1/240267/26/30289/3464/68d53105F6eafb261/17759a3a776b2a8a.png') no-repeat;
      // background-size: 100%;
      // margin: 0 auto;
      .swiper-slide {
        // margin-right: 0.2rem;
        .prize-card {
          width: 6rem;
          height: 6.55rem;
          background: url('//img10.360buyimg.com/imgzone/jfs/t1/240267/26/30289/3464/68d53105F6eafb261/17759a3a776b2a8a.png') no-repeat;
          background-size: 100%;
          .prize-img {
            width: 99%;
            object-fit: contain;
            border-radius: 0.1rem;
            margin: 0 auto;
          }
          .prize-name {
            width: 90%;
            font-size: 0.32rem;
            color: #000;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 0.2rem auto 0;
          }
          .prize-title {
            width: 90%;
            font-size: 0.24rem;
            color: #000;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 0.1rem auto 0;
          }
        }
      }
    }
    .draw-btn {
      width: 1.86rem;
      margin: 0.2rem auto 0;
    }
  }
  .prize-list {
    width: 6.9rem;
    height: 6.5rem;
    margin: 0.5rem auto 0;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/339353/34/14648/50901/68d53f10F3c4bd857/b0c2034bd46f85a0.png') no-repeat;
    // padding: 1.2rem 0.5rem 0;
    padding-top: 1.2rem;
    background-size: 100%;
    overflow: hidden;
    position: relative;
    .swiper-container {
      width: 6rem;
      height: 2.55rem;
      margin: 0 auto;
      overflow: hidden;
      .swiper-slide {
        // margin-right: 0.2rem;
        .prize-card {
          width: 2.3rem;
          height: 2.9rem;
          background: url('//img10.360buyimg.com/imgzone/jfs/t1/337927/40/10748/2202/68d53fbcF2689e5d3/088ffba678c18851.png') no-repeat;
          background-size: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: start;
          padding-top: 0.25rem;
          .prize-img {
            width: auto;
            max-height: 1.6rem;
            margin: 0 auto;
            object-fit: contain;
          }
          .prize-name {
            width: 90%;
            font-size: 0.22rem;
            margin-top: 0.15rem;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
    .tips {
      text-align: center;
      font-size: 0.24rem;
      color: #000;
      margin: 0.2rem auto;
    }
    .remaining-points {
      width: 1.8rem;
      height: 0.68rem;
      line-height: 0.6rem;
      text-align: center;
      position: absolute;
      top: 3.8rem;
      left: 1rem;
      color: #fff;
      font-size: 0.2rem;
      background: url('//img10.360buyimg.com/imgzone/jfs/t1/340047/14/14676/1823/68d541edFa00c41e4/47f5899281d533e9.png') no-repeat;
      background-size: 100%;
    }
    .draw-btn1 {
      width: 2.84rem;
      margin: 0.8rem auto 0;
    }
  }
  .img-draw {
    width: 6.9rem;
    margin: 0.2rem auto 0;
  }
  .draw-bg {
    width: 7rem;
    height: 5.3rem;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/288453/19/11119/152840/68391933F309f770a/a6b7547ee5293f85.png') no-repeat;
    background-size: 100%;
    margin: 4.5rem auto 0;
  }
  .draw-bg-false {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/300535/6/10262/124029/68391933Fd87c6726/0b421cf9c502242e.png') no-repeat;
    background-size: 100%;
    padding-top: 2rem;
    .text {
      width: 100%;
      font-size: 0.24rem;
      text-align: center;
      span {
        font-size: 0.3rem;
        color: #f75658;
      }
    }
    .progress-container {
      width: 96%;
      display: flex;
      align-items: center;
      gap: 10px;
      margin: 0.2rem -0.08rem;
    }

    .label {
      font-size: 14px;
      width: 50px;
      text-align: center;
    }

    .progress-bar {
      position: relative;
      flex: 1;
      height: 24px;
      background-color: #3a8426; // 深绿色背景
      border-radius: 12px;
      overflow: hidden;
      border: 2px solid #226c18;
    }

    .progress-fill {
      height: 100%;
      background: repeating-linear-gradient(45deg, #ffeb3b, #ffeb3b 10px, #fdd835 10px, #fdd835 20px);
      transition: width 0.3s ease;
      border-radius: 12px 0 0 12px;
    }
  }
  .draw-btn2 {
    width: 2.84rem;
    position: absolute;
    top: 22.7rem;
    right: 0.98rem;
  }
  .task-list {
    width: 7rem;
    margin: 0.5rem auto;
    .task-info {
      width: 100%;
      height: 1.2rem;
      background-size: 100%;
      margin-bottom: 0.15rem;
      position: relative;
      .tips {
        position: absolute;
        top: 0.5rem;
        left: 1.2rem;
        width: 3.8rem;
        font-size: 0.18rem;
        color: #434343;
        text-align: left;
      }
      .btn {
        width: 1.63rem;
        height: 0.62rem;
        padding-top: 0.1rem;
        background: url('//img10.360buyimg.com/imgzone/jfs/t1/350996/2/6761/3711/68d546f9F95b1cbc2/f884d4a607d8e11d.png') no-repeat;
        background-size: 100%;
        position: absolute;
        top: 0.3rem;
        right: 0.1rem;
        display: flex;
        justify-content: center;
        font-size: 0.24rem;
        color: #fff;
      }
    }
  }
}
</style>
<style>
::-webkit-scrollbar {
  display: none;
}
.gray {
  filter: grayscale(1);
}
</style>
