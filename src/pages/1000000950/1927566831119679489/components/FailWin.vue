<template>
  <VanPopup v-model:show="isShow">
    <div class="popup-bg">
      <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/340812/38/14805/4443/68d60b02F7175ba2b/4085b72e3e9ccec1.png" alt="" @click="close" />
      <img class="close" src="//img10.360buyimg.com/imgzone/jfs/t1/288245/31/9579/1772/6836db46Fb2c9f682/860212cf7162771c.png" @click="close" />
    </div>
  </VanPopup>
</template>
<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  showFailWin: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['close']);
const close = () => {
  emit('close');
};
const isShow = computed(() => props.showFailWin);
</script>
<style lang="scss" scoped>
.popup-bg {
  width: 6.5rem;
  height: 8.5rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/341525/34/7594/53716/68d60bb1F30113833/78e05675083bb783.png') no-repeat;
  background-size: 100%;
  padding-top: 1.4rem;
  margin-top: 0.5rem;
  .close {
    width: 0.6rem;
    height: 0.6rem;
    position: absolute;
    top: 0.1rem;
    right: 0.2rem;
  }

  .btn {
    position: absolute;
    bottom: 0.8rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2.5rem;
  }
}
</style>
