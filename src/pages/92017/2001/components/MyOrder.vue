<template>
  <div class='box'>
    <div class='content'>
      <div v-if='activityInfo.userOrderList.length>0'>
        <div class='order-view' v-for='(item, index) in activityInfo.userOrderList' :key='index'>
          <div class='order-id'>订单号：{{ item.orderId }}</div>
          <div class='order-time'>完成时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          <div class='order-status'>{{ item.orderStatus ?? '' }}</div>
        </div>
      </div>

      <div class='no-data' v-else>暂无我的订单数据</div>

    </div>
    <div class='close' @click='closePopup()'></div>
  </div>
</template>

<script lang='ts' setup>
import dayjs from 'dayjs';
import { closePopup, openPopup } from '../ts/popup';
import { inject, reactive, ref, onMounted, PropType } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { gotoSkuPage } from '@/utils/platforms/jump';
import Clipboard from 'clipboard';
import { IActivityInfo } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;

const props = defineProps({ activityInfo: Object as PropType<IActivityInfo> });

</script>

<style scoped lang='scss'>
.box {
  width: 6rem;
  height: 8.41rem;
  position: relative;
  padding: 2.5rem 0 0;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/331738/21/2338/18600/68a7d173F0704f768/f737c8d02fbfc4fe.png");
    repeat: no-repeat;
    size: contain;
  };

  .content {
    padding: 0 .1rem;
    border: 0.1rem solid transparent;
    height: 4.8rem;
    overflow-y: auto;
    white-space: pre-wrap;

    .order-view {
      padding-bottom: .2rem;
      border-bottom: 1px solid #cecece;
      margin-bottom: .2rem;
      position: relative;

      .order-id {
        font-size: .32rem;
        font-weight: bold;
      }

      .order-time {
        font-size: .24rem;
        color: #999999;
      }

      .order-status {
        position: absolute;
        right: .3rem;
        top: 50%;
        transform: translateY(-50%);
        font-size: .24rem;
      }
    }

    .no-data {
      text-align: center;
      line-height: 4rem;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
  }
}

</style>
