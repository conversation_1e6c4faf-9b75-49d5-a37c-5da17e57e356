<template>
  <div class='box'>
    <div class='fill-view'>
      <label for='fillInput'>电话 :</label>
      <input class='fill-input' id='fillInput' v-model='phone' type='tel' maxlength='11' placeholder='请输入电话号码'>
    </div>

    <div class='equity-rule-view'>
      1、充值账号：用户填写的【充值手机号】如果已经在待领取权益平台注册账号，则用户所获权益会直接充值到该用户账号；如果用户并未在对应权益平台注册账号，则平台会以填入手机号自动注册账号，并将密码以短信形式发送给该手机号。如未收到短信，请使用手机号找回密码；
      <br/>2、到账时间：1-10分钟；
      <br/>3、权益查看说明：权益详细信息，请登录对应账号查看；
      <br/>4、特殊说明：
      <br/>① 充值成功后无法退换；
      <br/>② 切勿写错手机号，如充错，责任自行承担；
      <br/>③ 点击【确认并提交】后，权益领取手机号会无法修改，请确认无误后再点击确认；
    </div>

    <div class='close' @click='closePopup()'></div>
    <div class='handle-btn' @click='submitTel'></div>
  </div>
</template>

<script lang='ts' setup>
import { inject, PropType, ref, defineProps } from 'vue';
import { closePopup } from '../ts/popup';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoSkuPage } from '@/utils/platforms/jump';
import { IActivityInfo } from '../ts/type';
import { showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({ drawSuccessPrize: Object as PropType<any> });

const phone = ref();
const submitTel = async () => {
  const regex = /^1[3-9]\d{9}$/;
  if (!phone.value) {
    showToast('请输入电话号码');
    return;
  }
  if (!regex.test(phone.value)) {
    showToast('电话号码格式不正确');
    return;
  }

  try {
    // 获取奖品
    const { data, code } = await httpRequest.post('/92017/fuLuReceive', { phone: phone.value, userPrizeId: props?.drawSuccessPrize?.userPrizeId });
    if (code === 200) {
      showToast('领奖成功');
    }
    closePopup();
    // if(){}
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
    setTimeout(() => {
      closePopup();
    }, 500);
  }
};
</script>

<style scoped lang='scss'>
.box {
  width: 6rem;
  height: 8.41rem;
  position: relative;
  padding: 2.4rem .2rem .3rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/295318/20/15114/15984/68a2b0f4F02118cd0/7c44bd5ffcaf8ab7.png");
    repeat: no-repeat;
    size: contain;
  };

  .fill-view {
    width: 5rem;
    height: .7rem;
    line-height: .7rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: .1rem;
    background-color: #f2f2f2;
    margin: 0 auto;
    padding: 0 .2rem;

    label {
      width: 1.2rem;
      font-size: .26rem;
      font-weight: bold;
      line-height: .6rem;
    }

    .fill-input {
      width: 3.5rem;
      font-size: .27rem;
      letter-spacing: .01rem;
      font-weight: bold;
      height: .6rem;
      line-height: .6rem;
      background-color: #f2f2f2;
      border: transparent !important;
    }
  }

  .equity-rule-view {
    font-size: .22rem;
    width: 5.5rem;
    margin: .2rem auto;
    padding: 0 .2rem;
    white-space: pre-wrap;
    height: 3.5rem;
    overflow-y: auto;
  }

  .submit-btn {
    width: 3rem;
    height: .5rem;
    line-height: .5rem;
    border-radius: .2rem;
    text-align: center;
    color: #ffffff;
    background-color: #d51a4e;
    position: absolute;
    bottom: 4.2rem;
    left: 50%;
    transform: translateX(-50%);
  }
}

</style>
