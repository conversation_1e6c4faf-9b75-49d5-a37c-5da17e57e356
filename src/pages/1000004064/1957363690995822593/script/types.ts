export interface ScoreInfo {
  dayLimit: boolean;
  total: number;
  used: number;
}
export interface PrizeItem {
  id: number | null;
  prizeImg: string;
  prizeName: string;
  prizeType: number;
  sortId: number;
}

export interface TaskItem {
  img: string;
  btnImg: string;
  status: boolean;
  /**
   * 任务类型
   * 13-开卡
   * 7-加购
   * 8-下单
   * 25-上传照片
   *  */
  taskType: number;
  taskTimes: number;
  taskId: string;
  taskContent: string;
}
export interface DrawInfo {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  areaType: number;
  skuId: string;
  userPrizeId: string;
}
export interface ActInfo {
  orderFlag: boolean;
  canDrawChance: number;
  taskResponses: TaskItem[];
}
export interface SKUItem {
  skuId: number;
  skuName: string;
  jdPrice: string;
  skuMainPicture: string;
  skuUrl: string;
}
