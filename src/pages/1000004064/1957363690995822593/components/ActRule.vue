<template>
  <Popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false" @closed="handleClose" @opened="openedPopup">
    <div class="box">
      <div class="content">
        <div class="ruleText" v-html="ruleText"></div>
      </div>
      <div class="close-icon" @click="handleClose"></div>
    </div>
  </Popup>
</template>
<script setup lang="ts">
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import { Popup } from 'vant';
import { getRules } from '../script/ajax';

const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog']);
const handleClose = () => {
  emits('closeDialog');
};
const ruleText = ref('');
const openedPopup = async () => {
  ruleText.value = await getRules();
};
</script>
<style scoped lang="scss">
.dialog-bg {
  padding-bottom: 0.2rem;
  position: relative;
}
.box {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/329237/20/299/16955/68a3f2dbFea3e01ea/1c00fa40b3a837bd.png) no-repeat;
  background-size: contain;
  width: 6.5rem;
  height: 8rem;
  padding: 1.5rem 0.651rem;
  box-sizing: border-box;
  position: relative;
  border-radius: 0.13rem;
  .ruleText {
    color: #306acf;
    font-size: 0.24rem;
    word-break: break-all;
    margin-top: 0.3rem;
    max-height: 4.7rem;
    overflow-y: auto;
  }
}
.close-icon {
  position: absolute;
  bottom: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  font-weight: bold;
  width: 1rem;
  height: 1rem;
}
</style>
