import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin, { EventTrackParams } from '@/plugins/EventTracking';
import '@/style';
import thresholdPlugin from '@/plugins/ThresholdChecker';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // thresholdPopup: JudgmentConditions,
  // backActRefresh: true,
  urlPattern: '/custom/:activityType/:templateCode',
};

const _decoData = {
  // pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/275084/6/5467/225938/67d9482fF846c055f/a0e2d7639a540437.png',
  // actBg: '',
  // actBgColor: '#e8e3da',
  // ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/228105/27/33270/2446/67b6c86aFfd65e0a7/3cb2aed142c88a15.png',
  // myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/259477/23/22154/2504/67b6c86bFe2fb5b0e/e0dd5dd9ed8969ad.png',
  // orderBtn: '//img10.360buyimg.com/imgzone/jfs/t1/258068/14/22310/2339/67b6c867F71ff5bb6/8f9f40158a7be60f.png',
  // userInfoBg: '//img10.360buyimg.com/imgzone/jfs/t1/252918/37/22664/42952/67b6c869F855546ad/54e323eb5bf6c8d2.png',
  // userInfoColor: '#97653c',
  // thresholdBg: '//img10.360buyimg.com/imgzone/jfs/t1/271852/11/5473/45669/67d94818F89985d13/7945070671e2045c.png',
  // stepBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/279233/11/5462/2171/67d94818F86c6f7ba/04bab3c9f0698b51.png',
  // stepBtnSelectBg: '//img10.360buyimg.com/imgzone/jfs/t1/282655/2/4871/2174/67d94818Fe9be6c1c/aae3083330883627.png',
  // stepTextColor: '#e0362a',
  // stepTextColorShadow: '#fff',
  // prizeListBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/271740/20/5442/56239/67d94818F087c570e/2a6eeb4f997dce30.png',
  // prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/231457/4/37577/7511/67b6fe56F667a4414/1cbdbb15d8655b25.png',
  // exchangeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/264505/37/22187/4140/67b6c869F1a12ff97/997d4d0411856a73.png',
  // skuListBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/80196/10/27770/128910/66dff8c1Fd8dd7d8f/666f84f37a8a83cf.png',
  // skuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/97264/18/48609/13072/66e12e18Fd2700ee4/5cc9c135ed83bb7d.png',
  // skuTitleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/243580/22/16339/2665/66dff8c2Fe83e05c4/8f5a41ccc7212189.png',
  // skuTitleColor: '#f8f1e0',
  // goSkuBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/156269/19/45386/5947/66dff8c3F6a3f18a8/dc58e86af778aa79.png',
  // cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  // h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  // mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',

  pageBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/274096/16/26360/289327/680b3db5Fe139f01c/1ae71f85911387af.png',
  // actBg: '', // 主页背景图
  actBgColor: '#05913d', // 主页背景色
  ruleBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/273947/39/26749/5433/680b3db6F446bd759/d0dcdc5e846e9308.png', // 活动规则按钮
  orderRecordBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/273290/21/25979/5293/680b3db7F7eaee408/11a8db76b52b9be6.png', // 订单记录按钮
  // stepBtnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/279329/35/8244/9492/67e106e0Fba8a4ae5/5448de550cb8e245.png', // 门槛按钮
  // stepBtnSelectBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/281358/29/8517/11485/67e106e0Fa82fd4c7/a08c36c277f76846.png', // 门槛按钮选中
  // prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/259850/19/21830/135972/67b6c86bFe24ddd6d/73efdd3021a0e2a8.png', // 奖品背景图
  // prizeBorder: '//img10.360buyimg.com/imgzone/jfs/t1/260947/26/22002/3870/67b6c869F9d2e191b/5bd1dd8e75f0a2e7.png',
  actRecordBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/286093/16/11/5418/680b3db6Ff693112d/a97f41a3cf58943f.png', // 活动记录按钮
  drawRecordBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/280843/39/29557/5494/681ad9d7F9a28cfab/be70cdf61e1a2311.pngg', // 活动记录按钮
  skuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/271529/10/26520/9475/680b3db2Ff2409e2f/1c11b5259992c81c.png', // 商品背景图
  skuCheckedBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/282847/34/26020/1260/680b3db5F872e8fce/073e8939a50f64c1.png', // 商品选中图
  skuUnCheckedBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/274745/37/27586/1108/680b3db6Fb9b61349/16fafe33f10ee071.png', // 商品未选中图
  skuNameColor: '#000', // 商品标题颜色
  alertColor: '#fff', // 提醒文字颜色
  step1Title: 'https://img10.360buyimg.com/imgzone/jfs/t1/276235/9/26389/41099/680b3db8F259189e1/444e7b0915091c04.png',
  step2Title: 'https://img10.360buyimg.com/imgzone/jfs/t1/284939/40/26731/42376/680b3db8F3636775d/d317558e17cb2a10.png',
  step3Title: 'https://img10.360buyimg.com/imgzone/jfs/t1/281369/7/26095/41342/680b3db7Fb6e33006/35458e21ceb07443.png',
  step4Title: 'https://img10.360buyimg.com/imgzone/jfs/t1/276865/36/27827/42908/680b3db7F6d92750b/7c141a10e019831e.png',
  step1Btn: 'https://img10.360buyimg.com/imgzone/jfs/t1/277241/21/27862/21050/680b3db9F5c35f533/d90b2b89910e22e9.png',
  step2Btn: 'https://img10.360buyimg.com/imgzone/jfs/t1/279378/40/26766/20939/680b3db9F8fb379a7/ed91c3bd9e195e8f.png',
  step3Btn: 'https://img10.360buyimg.com/imgzone/jfs/t1/276613/21/27738/23508/680b3db9Fa4cec806/e029ee226ac916cd.png',
  step4Btn: 'https://img10.360buyimg.com/imgzone/jfs/t1/275075/28/27466/20905/680b3db9Ff01f991c/cc894866fb8f9fad.png',
  checkedTabBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/272414/38/27155/9954/680b3db5F4c1e235b/91ddb33e5e92b9bd.png',
  unCheckedTabBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/282025/9/26932/2695/680b3db6F478299b7/47be985b16bb257d.png',
  checkedTabColor: '#d8231c',
  UNcheckedTabColor: '#ffffb9',
  // goSkuBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/156269/19/45386/5947/66dff8c3F6a3f18a8/dc58e86af778aa79.png', // 去商品按钮
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/279026/32/9061/41157/67e1284dF767f99ad/ecf68271ad90ef6d.jpg',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/279142/30/9023/5263/67e1284eF8311ac7b/713b3a7cfbacc716.jpg',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/274190/1/9056/48480/67e1284eF7dfff0a8/0aa0802faa26ce5c.jpg',
};

init(config).then(({ baseInfo, pathParams, decoData, userInfo }) => {
  // 设置页面title
  console.log(baseInfo, 'baseInfo基础信息');
  document.title = baseInfo?.activityName || '满额阶梯礼';
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', decoData);
  app.provide('pathParams', pathParams);
  app.provide('userInfo', userInfo);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.use(thresholdPlugin);
  app.mount('#app');
});
