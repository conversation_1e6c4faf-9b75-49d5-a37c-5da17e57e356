<template>
  <van-swipe class="mySwiper" ref="mySwipe" :width='screenWidth' :loop="false" indicator-color="white" v-if="swiperArr.length>0">
    <van-swipe-item v-for="(item, index) in swiperArr" :key="index">
      <div class="sku-card" :style="furnishStyles.skuBg.value">
        <div v-if='stepInd==2' class="sku-count-down"><div style='word-break: keep-all;'>下单倒计时：</div><van-count-down style='color: red;font-size: .22rem;word-break: keep-all;margin-top: -.05rem' :time="item.countDownTime" format="DD天HH时mm分" :auto-start="true" :millisecond="true" /></div>
        <img class="sku-img" :src="item.prizeImg || '//img10.360buyimg.com/imgzone/jfs/t1/285021/21/27483/75599/680f2f59F51b1592f/1fdf3194174579e0.png'" alt="" />
        <p class="sku-name" :style="{ color: furnish.skuNameColor}">{{ item.prizeName }}</p>
        <img v-if='stepInd==0' class="sku-checked" @click="() => {emits('skuChecked', index)}" :src="item.isChecked ? furnish.skuCheckedBg : furnish.skuUnCheckedBg" alt=''>
      </div>
    </van-swipe-item>
    <div style='width: 5rem'></div>
  </van-swipe>
  <p v-else class="winner-null">暂无{{stepInd == 0 ? '加购商品' : stepInd == 1  ? '加购抽奖商品' : stepInd == 2 ? '下单商品' : '下单抽奖商品'}}哦~</p>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';

const props = defineProps(['swiperArr', 'stepInd', 'currentSeries']);
const emits = defineEmits(['skuChecked']);

const mySwipe = ref();

console.log('获取屏幕可用宽度', window.screen.availWidth);
const screenWidth = window.screen.availWidth/2.1;

watch(()=>props.currentSeries, () => {
  mySwipe.value && mySwipe.value.swipeTo(0);
});
</script>

<style>
.van-swipe__indicator{
  width: .14rem !important;
  height: .1rem !important;
  background-color: #026c23 !important;
  border-radius: 5px !important;
}
.van-swipe__indicator--active{
  //width: .4rem !important;
  //height: .1rem !important;
  background-color: #ffeab4 !important;
  //border-radius: 5px !important;
}
</style>
<style scoped lang="scss">
.mySwiper {
  position: relative;
  width: 100%;
  height: 3.9rem;
  .sku-card{
    width: 2.94rem;
    height: 3.24rem;
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .sku-count-down{
      display: flex;
      position: absolute;
      top: .05rem;
      left: .1rem;
      font-size: 0.20rem;
    }
    .sku-img {
      position: absolute;
      bottom: .7rem;
      left: 12%;
      width: 75%;
      max-height: 90%;
      margin: 0 auto;
    }

    .sku-name {
      width: 90%;
      font-size: 0.26rem;
      font-weight: 600;
      text-align: center;
      position: absolute;
      left: .1rem;
      bottom: 0.1rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .sku-checked {
      width: 0.4rem;
      height: 0.4rem;
      position: absolute;
      right: 0.2rem;
      bottom: 0.35rem;
    }
  }
}

.myselfSwiper{
  display: flex;
  .item{
    width: 2.94rem;
    margin-right: .2rem;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #fff;
}
</style>
