<template>
  <div class="fail-pop-all">
    <div v-if="failType === 0" class="fail-pop0">
      <div class="btnDiv" @click="closeClick()">我知道了</div>
      <div class="closeDiv" @click="closeClick()">关闭</div>
    </div>
    <div v-if="failType === 1" class="fail-pop1">
      <div class="btnDiv" @click="closeClick()">我知道了</div>
      <div class="closeDiv" @click="closeClick()">关闭</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  failType: {
    type: Number,
    default: 0,
  },
});
const emits = defineEmits(["close"]);
const closeClick = () => {
  emits("close");
};
</script>
<style lang="scss" scoped>
.fail-pop-all {
  .fail-pop0 {
    width: 5.3rem;
    height: 7.45rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/316414/13/8641/16911/68491c33Fe9aa21b1/ce03cbfb4b2cd08e.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .btnDiv {
      width: 2.03rem;
      height: 0.65rem;
      // background-color: red;
      font-size: 0;
      position: absolute;
      bottom: 2.12rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .closeDiv {
      width: 0.6rem;
      height: 0.6rem;
      // background-color: red;
      font-size: 0;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .fail-pop1 {
    width: 5.3rem;
    height: 7.45rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/314139/39/8586/14492/684926faFdd7ae5d8/180cbd5a85175afd.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .btnDiv {
      width: 2.03rem;
      height: 0.65rem;
      // background-color: red;
      font-size: 0;
      position: absolute;
      bottom: 2.12rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .closeDiv {
      width: 0.6rem;
      height: 0.6rem;
      // background-color: red;
      font-size: 0;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style>
