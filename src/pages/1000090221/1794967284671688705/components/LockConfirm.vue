<template>
  <div class="confirm-bg">
    <van-radio-group v-model="checked" @change='handleCheck'>
      <van-radio name="6" checked-color="#ee0a24" :disabled='props.gift1Stock===0'>
        <img :src="decoData.gitft1" class='gift'>
      </van-radio>
      <van-radio name="12" checked-color="#ee0a24" :disabled='props.gift2Stock===0'>
        <img :src="decoData.gitft2" class='gift'>
      </van-radio>
    </van-radio-group>
    <div class='confirm-btn' @click='handleConfirm'></div>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref, defineProps, inject } from 'vue';

const props = defineProps(['gift1Stock', 'gift2Stock']);
const decoData = inject('decoData') as any;
const emit = defineEmits(['handleConfirm']);

const checked = ref('');
const handleCheck = (val:string) => {
  console.log(val);
  checked.value = val;
  // emit('handleConfirm', Number(val));
};
const handleConfirm = () => {
  if (checked.value === '') {
    showToast('请选择要锁定的权益');
  } else {
    emit('handleConfirm', Number(checked.value));
  }
};
</script>

<style scoped lang="scss">
.confirm-bg {
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/179017/11/46945/18298/66557995F66ecfbdb/98a867aa1aa55f21.png') no-repeat;
  background-size: 100%;
  width: 5.8rem;
  height: 7.16rem;
  padding-top: 1.9rem;
  padding-bottom: 0.3rem;
  position: relative;
  .van-radio-group{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .gift{
    width: 3.2rem;
  }
  .confirm-btn{
    width: 1.7rem;
    height: .5rem;
    //background: #3cc51f;
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    bottom: .5rem;
  }
}
</style>
