<template>
  <div class="card-bk">
    <div class="content">
      <img :src="detail.showImg" alt="" class="card-img" />
      <div class="prize-name">{{ detail.prizeName }}</div>
      <div class="item" v-show="detail.cardNumber">
        <div class="label">卡号：</div>
        <div class="text">{{ detail.cardNumber }}</div>
        <div class="copy-btn" :copy-text="detail.cardNumber">复制</div>
      </div>
      <div class="item" v-show="detail.cardPassword">
        <div class="label">卡密：</div>
        <div class="text">{{ detail.cardPassword }}</div>
        <div class="copy-btn" :copy-text="detail.cardPassword">复制</div>
      </div>
      <div class="tip">礼品卡说明：</div>
      <div class="tip" :class="{ 'tip-small': detail.cardNumber && detail.cardPassword }">{{ detail.cardDesc }}</div>
    </div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import { CardType } from '../ts/type';
import Clipboard from 'clipboard';
import { showToast } from 'vant';

const props = defineProps({
  detail: {
    type: Object as PropType<CardType>,
    required: true,
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>

<style scoped lang="scss">
.card-bk {
  position: relative;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/102242/13/45867/96404/65019afcF271c27b4/6a6fbddfbb31c2bd.png) no-repeat;
  background-size: 100%;
  width: 6rem;
  height: 7.89rem;
  padding: 1.5rem 0.2rem 0.2rem;
  .content {
    padding: 0 0.35rem;
    .card-img {
      margin: 0 auto;
      width: 5rem;
      height: 1.8rem;
      object-fit: contain;
    }
    .prize-name {
      color: #000;
      width: 100%;
      text-align: center;
      font-weight: bolder;
      margin-top: 0.2rem;
      margin-bottom: 0.2rem;
      font-size: 0.3rem;
    }
    .item {
      display: flex;
      align-items: center;
      font-size: 0.3rem;
      margin-bottom: 0.2rem;
      .label {
        color: #000;
        width: 1rem;
      }
      .text {
        flex: 1;
        background-color: #fff;
        color: #000;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 0.1rem;
        line-height: 0.45rem;
        border-radius: 0.05rem 0 0 0.05rem;
      }
      .copy-btn {
        width: 1rem;
        line-height: 0.45rem;
        text-align: center;
        color: #fff;
        font-size: 0.24rem;
        border-radius: 0 0.05rem 0.05rem 0;
        background-color: #ff5f00;
      }
    }
    .tip {
      font-size: 0.2rem;
      color: #000;
      white-space: pre-line;
      max-height: 1.75rem;
      overflow-y: scroll;
    }
    .tip-small {
      max-height: 1.1rem;
    }
  }
  .close {
    height: 0.6rem;
    width: 0.6rem;
    position: absolute;
    bottom: 0;
    left: 2.7rem;
  }
}
</style>
