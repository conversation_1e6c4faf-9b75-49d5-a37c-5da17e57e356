export interface Task {
  id: number;
  taskType: number;
  optWay: number;
  buyType: number;
  perOperateCount: number;
  perLotteryCount: number;
  lotteryCount: number;
  taskFinishCount: number;
  limit: number;
}
export interface FormType {
  realName: string;
  mobile: string;
  province: string;
  city: string;
  county: string;
  address: string;
}
export interface CardType {
  cardDesc: string;
  cardNumber: string;
  cardPassword: string;
  id: number;
  prizeName: string;
  showImg: string;
}
