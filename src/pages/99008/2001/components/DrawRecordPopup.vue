<template>
  <CommonDrawer title="抽奖记录" @close="emits('close')">
    <div class="h-[40vh] px-2 pb-2 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="text-gray-400 text-center">
        <div>下单状态为已完成时，才可以领取对应奖励。</div>
        <div>若取消中奖订单，则同时视为自动放弃该奖品。</div>
      </div>
      <div v-if="drawList.length">
        <div class="grid grid-cols-3 mt-3 bg-white p-2 rounded" v-for="(order, index) in drawList" :key="index">
          <div class="flex col-span-2">
            <div>抽奖时间：</div>
            <div>{{ order.createTime ? dayjs(order.createTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}</div>
          </div>
          <div class="flex">
            <div>状态：</div>
            <div >{{ order.missReason }}</div>
          </div>
         <template v-if="['中奖','订单已取消'].includes(order.missReason)">
           <div class="flex col-span-2">
             <div>订单编号：</div>
             <div>{{ order.orderId }}</div>
           </div>

           <div class="flex  items-center">
             <div>奖品图片：</div>
             <img alt="" style="width:.5rem" :src="order.prizeImg">
           </div>
           <div class="flex col-span-2">
             <div>奖品名称：</div>
             <div>{{ order.prizeName }}</div>
           </div>
           <div class="flex ">
             <div>奖品类型：</div>
             <div>{{ order.prizeType }}</div>
           </div>
         </template>
        </div>
      </div>
      <div v-else class="text-gray-400 text-sm h-[80%] flex justify-center items-center">暂无抽奖记录哦～</div>
    </div>
  </CommonDrawer>
</template>

<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';

import { ref } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';

interface Draw {
  orderId: string;
  missReason: string;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  prizeType: string;
}

const drawList = ref<Draw[]>([]);
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const getRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/99008/drawRecord');
    drawList.value = data;
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};

!isPreview && getRecord();
</script>
