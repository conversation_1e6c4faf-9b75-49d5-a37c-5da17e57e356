import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  actBg: '',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/248952/24/27457/209079/67501037Fc4598552/7aa7b84769f71267.png',
  actBgColor: '#f3d799',
  ruleBtn: '',
  myPrizeBtn: '',
  shopNameColor: '',
  prizeContentBg: '',
  prizeBg: '',
  prizeNameColor: '',
  drawsNum: '',
  drawBtn: '',
  winnersBg: '',

  unregisteredUserHotZoneSetting: {
    bg: '//img10.360buyimg.com/imgzone/jfs/t1/177429/29/54919/150739/67501abdFc737c0d0/6ebb044b7aef179c.png',
    hotZoneList: [],
  },
  unregisteredUserPointColor: '#757575',

  registeredUserHotZoneSetting: {
    bg: '//img10.360buyimg.com/imgzone/jfs/t1/245739/3/26697/155454/67501cecF3859c01d/025600ea5032b9ac.png',
    hotZoneList: [],
  },
  registeredUserPointColor: '#757575',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '直播间入会抽奖';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', _decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
