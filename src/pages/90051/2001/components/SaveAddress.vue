<template>
  <div class="popup-bk">
    <div>
      <div class="row">
        <div>收货人：</div>
        <input type="text" v-model="form.receiver" placeholder="请输入姓名" maxlength="10" />
      </div>
      <div class="row">
        <div>手机号：</div>
        <input type="text" v-model="form.phone" placeholder="请填写手机号" oninput="value=value.replace(/[^\d]/g,'')" maxlength="11" />
      </div>
      <div class="row">
        <div>省市区：</div>
        <input type="text" v-model="addressCode" readonly="true" placeholder="选择省/市/区" @click="addressSelects = true" />
      </div>
      <div class="row">
        <div>详细地址：</div>
        <input type="text" v-model="form.address" placeholder="请填写地址" maxlength="30" />
      </div>
      <div class="row">
        <div>邮政编码：</div>
        <input v-model="form.postalCode" oninput="value=value.replace(/[^\d]/g,'')" maxlength="6" placeholder="请输入邮政编码">
      </div>
      <div class="button" @click="checkForm"></div>
    </div>
    <!--地址选择-->
    <Popup v-model:show="addressSelects" teleport="#app" position="bottom">
      <Area title="请输入详细地址" :area-list="areaList" @confirm="confirmAddress" @cancel="onCancel" />
    </Popup>
  </div>
</template>

<script lang="ts" setup>
import { inject, defineProps, defineEmits, reactive, ref, watch, onMounted } from 'vue';
import { Toast, Area, Popup } from 'vant';
import { areaList } from '@vant/area-data';
import { BaseInfo } from '@/types/BaseInfo';
import { cjwxRequest } from '@/utils/service';
import { checkAddress } from '../universal';

// 数据
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

// 获取基本信息
const { activityId, actName, pin, venderId } = baseInfo;

const emits = defineEmits(['closePopup']);

const props = defineProps({
  giftId: {
    type: Number,
    default: 0,
  },
});

const form = reactive({
  venderId,
  pin,
  activityId,
  actType: '13',
  prizeName: '',
  receiver: '',
  phone: '',
  province: '',
  giftRecordId: props.giftId,
  city: '',
  district: '', // 区
  postalCode: '',
  county: '',
  address: '',
});
const addressCode = ref('');

const addressSelects = ref(false);
// 关闭三联地址框
const onCancel = () => {
  addressSelects.value = false;
};
// 确认三联地址信息
const confirmAddress = (addressItemList: any[]) => {
  form.province = addressItemList[0].name;
  form.city = addressItemList[1].name;
  form.district = addressItemList[2].name;
  addressCode.value = addressItemList.map((item) => item.name).join('/');
  addressSelects.value = false;
};

const submit = async () => {
  try {
    Toast.loading({
      message: '提交中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await cjwxRequest.post('/biostime/purchase/updateAddress', form);
    Toast.clear();
    if (res.success) {
      Toast.success('保存成功');
      setTimeout(() => {
        emits('closePopup');
      });
    } else {
      Toast.fail(res.msg);
    }
  } catch (error) {
    Toast.clear();
    if (error && error.msg) {
      Toast.fail(error.msg);
    }
  }
};

const checkForm = () => {
  // 特殊字符
  const spcWord = /[^\u4e00-\u9fa5a-zA-Z\d,.，。（）() - - ! ！? ？]+/;
  // 空格
  const whitespace = /\s+/g;
  // 表情符号
  const faceWord = /[\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/;
  // 电话号校验
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;

  if (!form.receiver) {
    Toast.fail('请输入姓名！');
  } else if (spcWord.test(form.receiver) || whitespace.test(form.receiver) || faceWord.test(form.receiver)) {
    Toast.fail('请输入正确的姓名！');
  } else if (!form.phone) {
    Toast.fail('请输入电话！');
  } else if (!checkPhone.test(form.phone)) {
    Toast.fail('请输入正确的电话号！');
  } else if (!form.province) {
    Toast.fail('请选择地区！');
  } else if (!form.address) {
    Toast.fail('请输入收货地址！');
  } else if (!form.address.trim()) {
    Toast.fail('请输入正确收货地址！');
  } else if (faceWord.test(form.address)) {
    Toast.fail('收货地址请不要填写表情符号！');
  } else if (!form.postalCode || !form.postalCode.toString().trim()) {
    Toast.fail('请输入邮编！');
  } else {
    submit();
  }
};

onMounted(() => {
  checkAddress(activityId, pin, props.giftId).then((res:any) => {
    Object.assign(form, res.data);
    if (res.data.province) {
      addressCode.value = `${res.data.province}/${res.data.city}/${res.data.district}`;
    }
  }).catch((res:any) => {
    console.log('错误:', res);
  });
});
</script>

<style scoped lang="scss">
.popup-bk {
  position: relative;
  width: 6.42rem;
  height: 6.53rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/23237/26/20207/44459/652941aeF5df63118/e137e70f34e1578f.png') no-repeat;
  background-size: 100%;
  padding: 1.85rem 0.58rem 1.3rem;
  position: relative;
  .button {
    background: url('https://img10.360buyimg.com/imgzone/jfs/t1/102517/4/25293/7297/65239590F5f01545a/a4598d54a2493ef4.png') no-repeat left/100%;
    height: 0.81rem;
    width: 3.69rem;
    position: absolute;
    bottom:.2rem;
    left: calc(50% - 1.845rem);
  }

}
.row {
  display: flex;
  align-items: center;
  height: .55rem;
  border: .02rem solid #e20b0a;
  border-radius: .1rem;
  padding: 0 .2rem;
  margin-bottom: .15rem;
  div{
    color: #e20b0a;
    font-size: .3rem;
    font-weight: bold;
  }
  input {
    border: none;
    background: none;
    font-size: 0.26rem;
    color: #e72c1c;
    width: 3rem;
  }

  ::-webkit-input-placeholder {
    /* WebKit browsers，webkit内核浏览器 */
    color: #fe4d36;
    font-size: .3rem;
  }
  :-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #fe4d36;
    font-size: .3rem;

  }
  ::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #fe4d36;
    font-size: .3rem;

  }
  :-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #fe4d36;
    font-size: .3rem;

  }
}

</style>
