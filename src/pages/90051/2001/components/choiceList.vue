<template>
  <div class="choice-list-bg"
       :style="{ backgroundImage: `url(${backgroundUrl[giftType]})` }">
    <div @click="changeList(index)"
         :key="index"
         v-for="(item,index) in new Array(3)"/>
  </div>
  <!--    <div></div>-->
  <!--    <div></div>-->
  <!--  </div>-->
</template>
<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

const emits = defineEmits(['changeList']);

const props = defineProps({
  giftType: { // 选择类型：0 派星，1：贝塔星，2天呵
    type: Number,
    default: 0,
  },
});
const backgroundUrl = [
  '//img10.360buyimg.com/imgzone/jfs/t1/109961/23/30790/43351/664578aaF8cc14548/41fe34ce0a788f5d.jpg',
  '//img10.360buyimg.com/imgzone/jfs/t1/239667/1/8873/43685/664578a9Fece013f7/370c5928df7d9a87.jpg',
  '//img10.360buyimg.com/imgzone/jfs/t1/172505/17/46469/43829/664578aaF9869cf1e/909e4a35d2b9f491.jpg',
];

function changeList(index: number) {
  let tempIndex = index;
  if (index === 1) { // 后改的，由于设计跟我原来的顺序不同，所以这么写
    tempIndex = 2;
  } else if (index === 2) {
    tempIndex = 1;
  }
  emits('changeList', tempIndex);
}
</script>
<style scoped lang="scss">
.choice-list-bg {
  width: 7.2rem;
  height: .94rem;
  background-size: 100%;
  background-repeat: no-repeat;
  margin: auto;
  display: flex;

  div {
    width: 33%;
    height: 100%;
  }
}
</style>
