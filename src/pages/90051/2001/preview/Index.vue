<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv ">
      <img :src="furnish.kv ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/295346/21/18646/134386/6894191eF2d273f93/31c432a4834b846c.png'" alt="" class="kv-img"/>
      <img :src="furnish.introduce ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/319006/38/21591/92862/6894191dF57a18ccf/294ad848e3634d54.png'" alt="" class="kv-img"/>
    </div>
    <div class="step-one">
      <img class="step-one-title" :src="furnish.step1Title ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/299513/5/21967/39348/6894191fF771b4380/1e1e5c15d49bf28e.png'"></img>
      <div class="step-one-content" :style="furnishStyles.stepOneBg.value">
        <div class="sku-item" v-for="(item,index) in skuList" :style="{backgroundImage: `url(${item?.showSkuImage})`}" :key="index" @click="ShowToast">
          <img :src="imgObj[0]" class="sku-btn"></img>
        </div>
      </div>
    </div>
    <div class="step-two">
      <img class="step-two-title" :src="furnish.step2Title ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/323466/38/1968/51861/68941921F52051eca/fc9688b9b11a52a9.png'"></img>
      <div class="step-two-content" :style="furnishStyles.stepTwoBg.value">
        <div class="sku-item" v-for="(item,index) in stepTwoSkuList" :style="{backgroundImage: `url(${item?.showSkuImage})`}" :key="index" @click="ShowToast">
        </div>
      </div>
    </div>
    <HotZone :width="7.5" :data="furnish.hotZoneSetting" @hotClick="ShowToast" reportKey="" />
    <img style="width: 7.5rem;" :src="furnish.ruleBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/325755/15/1939/273000/68947fc4Fa1f17202/9942c999ee1466dd.png'"></img>
    
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, nextTick } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList } from '../ts/default';
// import RulePopup from '../components/RulePopup.vue';
// import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';
import html2canvas from 'html2canvas';
import HotZone from '../components/HotZone.vue';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const total = ref(0);

const imgObj = {
  0: 'https://img10.360buyimg.com/imgzone/jfs/t1/96672/23/46959/4865/6520d3fdFc05d353d/d6406a1106d55b69.png', // 立即验证
  4: 'https://img10.360buyimg.com/imgzone/jfs/t1/223862/30/31620/5943/65275b86F8980e541/3c85cd77737f6e9c.png', // 立即查看
  5: 'https://img10.360buyimg.com/imgzone/jfs/t1/183151/36/40149/10234/65275b85F16f20b3e/7fc6d4708c8af972.png', // 不符合条件
  6: 'https://img10.360buyimg.com/imgzone/jfs/t1/223342/6/30134/6661/65275b86Fe17b99f8/964a50a215b82bf2.png', // 已领取

  1: 'https://img10.360buyimg.com/imgzone/jfs/t1/69334/34/25853/5985/65275b86F3fc71308/40e5c31edcf358dd.png', // 已参与
  3: 'https://img10.360buyimg.com/imgzone/jfs/t1/130302/1/41685/10642/65ea94b8F3f83261f/c7d74d123329e0cf.png', // 仅限新客
  7: 'https://img10.360buyimg.com/imgzone/jfs/t1/95540/20/33423/12323/6526363fF69c3da9f/b530ce7a1b96075f.png', // 立即下单
};

const isLoadingFinish = ref(false);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
}

const couponPrizeList = ref<Prize>([defaultStateList]);
const prizeList = ref<Prize>([defaultStateList]);
const multiplePrizeList = ref<Prize>([defaultStateList, defaultStateList]);

type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuList = ref<Sku[]>([
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png',
  },
]);

const stepTwoSkuList = ref<Sku[]>([{
  showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/190752/39/39426/25050/6649b3d4Fbb0e1c5f/a43808ebcf8ccba0.png'
},{
  showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/210256/22/41382/28091/66471039F2ab1a09c/9e9d03bbc93266af.png'
},{
  showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/243309/36/8786/29161/66471039Fa5339dc3/b1f8e6a2a3f7e0f6.png'
}]);

const orderSkuList = ref<Sku[]>([]);
const orderSkuListPreview = ref<Sku[]>([]);
const nextStateAmount = ref(0);
const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const showGoods = ref(false);

const showRulePop = () => {
  showRule.value = true;
};

const showMyPrizePop = () => {
  showMyPrize.value = true;
};

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

// const toSaveAddress = (id: string) => {
//   addressId.value = id;
//   showSaveAddress.value = true;
// };

// const orderSkuisExposure = ref(1);
const isExposure = ref(1);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;
    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);
    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;
    const blob = dataURLToBlob(croppedBase64);
    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  const list1 = data.couponPrizeList;
  const list2 = data.prizeList;
  const list3 = data.multiplePrizeList;
  if (list1.prizeType !== 0) {
    couponPrizeList.value = list1;
  }
  if (list2.prizeType !== 0) {
    prizeList.value = list2;
  }
  if (list3.prizeType !== 0) {
    multiplePrizeList.value = list3;
  }
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuList) {
    skuList.value = data.skuList;
  }
  if (data.stepTwoSkuList) {
    stepTwoSkuList.value = data.stepTwoSkuList;
  }
  if (data.orderSkuListPreview) {
    orderSkuListPreview.value = data.orderSkuListPreview;
  }
  ruleTest.value = data.rules;
  // orderSkuisExposure.value = data.orderSkuisExposure;
  isExposure.value = data.isExposure;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    const list1 = activityData.couponPrizeList;
    const list2 = activityData.prizeList;
    const list3 = activityData.multiplePrizeList;
    if (list1.prizeType !== 0) {
      couponPrizeList.value = list1;
    }
    if (list2.prizeType !== 0) {
      prizeList.value = list2;
    }
    if (list3.prizeType !== 0) {
      multiplePrizeList.value = list3;
    }
    ruleTest.value = activityData.rules;
    orderSkuListPreview.value = activityData.orderSkuListPreview;
    shopName.value = activityData.shopName;
    skuList.value = activityData.skuList;
    stepTwoSkuList.value = activityData.stepTwoSkuList;
    // orderSkuisExposure.value = activityData.orderSkuisExposure;
    isExposure.value = activityData.isExposure;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style lang="scss" scoped>

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  //margin-bottom: 9rem;

  .kv-img {
    width: 100%;
  }
}

.step-one {
  position: relative;
  box-sizing: border-box;
  padding-top: 0.26rem;
  height: 5.27rem;
  .step-one-title {
    width: 100%;
    margin-bottom: 0.26rem;
  }
  .step-one-content {
    width: 7.20rem;
    height: 4.03rem;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    box-sizing: border-box;
    margin-left: 0.15rem;
    display: flex;
    align-items: center;
    overflow-x: scroll;
    .sku-item {
      position: relative;
      flex: none;
      width: 2.36rem;
      height: 100%;
      background-size: contain;
      background-position: top;
      background-repeat: no-repeat;
      margin: 0.03rem;
      margin-right: 0.15rem;
    }
    .sku-btn {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 2.1rem;
      height: 0.52rem;
      transform: translateX(-50%);
    }
  }
}

.step-two {
  position: relative;
  box-sizing: border-box;
  padding-top: 0.26rem;
  height: 4.92rem;
  .step-two-title {
    width: 100%;
    margin-bottom: 0.26rem;
  }
  .step-two-content {
    width: 7.20rem;
    height: 3.53rem;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    box-sizing: border-box;
    margin-left: 0.15rem;
    padding-left: 0.5rem;
    display: flex;
    overflow-x: scroll;
    .sku-item {
      flex: none;
      width: 2.2rem;
      height: 100%;
      margin-right: 0.15rem;
      background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    }
  }
}

.hotZoneBox{
  width: 7.21rem;
  margin: 0 auto;
  position: relative;
}
.hotZone{
  width: 100%;
  margin: 0 0 0.2rem 0;
}

//.hotBtn {
//  background-color: #000;
//}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
