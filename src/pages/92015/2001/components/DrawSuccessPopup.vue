<template>
  <div class='box' :class="{'box-shiwu':drawSuccessPrize.prizeType===3,'box-coupon':drawSuccessPrize.prizeType===1}">
    <div class='know-btn'>
      <div class='item-btn' @click='closePopup()'></div>
      <div class='item-btn' @click='handleBtn()'></div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { inject, PropType } from 'vue';
import { closePopup } from '../ts/popup';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { IActivityInfo } from '../ts/type';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({ drawSuccessPrize: Object as PropType<any> });
const emits = defineEmits(['fillAddress']);

const handleBtn = () => {
  const { prizeType, couponSkuId, userPrizeId } = props.drawSuccessPrize;
  if (prizeType === 1) {
    if (couponSkuId) {
      gotoSkuPage(couponSkuId);
    } else {
      gotoShopPage(baseInfo.shopId);
    }
  } else if (prizeType === 3) {
    emits('fillAddress', { userPrizeId });
  }
};

</script>

<style scoped lang='scss'>
.box-shiwu {
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/287464/6/17849/126212/6862221dFd8a20118/236add938f06c08b.png") !important;
}

.box-coupon {
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/315702/15/12566/95564/685cec2fFa2ea7224/bf6d10c82d4675a8.png") !important;
}

.box {
  width: 5.92rem;
  height: 5.77rem;
  position: relative;
  padding: 1rem .2rem .3rem;
  background: {
    repeat: no-repeat;
    size: contain;
  };

  .sure-title {
    width: 100%;
    font-size: .38rem;
    text-align: center;
    color: #ae0002;
    font-weight: 900;
  }

  .sure-tip {
    width: 100%;
    text-align: center;
    font-size: .3rem;
    color: #4f4f4f;
    margin-top: .3rem;
    font-weight: bold;
  }

  .sure-notice {
    width: 100%;
    text-align: center;
    padding: 0 .3rem;
    font-size: 0.22rem;
    color: #6a6a6a;
    margin-top: 0.6rem;
  }

  .know-btn {
    width: 5.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    padding: 0 0.2rem;
    justify-content: space-between;
    position: absolute;
    bottom: 0.1rem;
    left: 50%;
    transform: translateX(-50%);

    .item-btn {
      width: 2.5rem;
      height: 100%;
    }
  }

}

</style>
