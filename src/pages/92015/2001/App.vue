<template>
  <div class='bg' :style='{backgroundImage:`url(${decoData.pageBg})`,backgroundColor:decoData.actBgColor}'>
    <!--     活动未开启点击遮罩层 -->
    <!--    <div class='no-start-mask' @click.stop="showToast('活动未开始')" v-if='baseInfo?.thresholdResponseList[0]?.thresholdCode===1'></div>-->

    <div class='count-down-time'>
      <template v-if='baseInfo?.thresholdResponseList[0]?.thresholdCode === 1'>
        <span class='title'>活动未开始</span>
      </template>
      <template v-else-if='baseInfo?.thresholdResponseList[0]?.thresholdCode === 2'>
        <span class='title'>活动已结束</span>
      </template>
      <template v-else>
        <span class='title'>距活动结束:</span>
        <van-count-down :time='baseInfo.endTime - new Date().getTime()' format='DD:HH:mm:ss'>
          <template #default='timeData'>
            <div class='contentSpan'>
              <div class='acblockStyleStyle'>{{ timeData.days }}</div>
              <span>天</span>
              <div class='acblockStyleStyle'>{{ timeData.hours }}</div>
              <span>时</span>
              <div class='acblockStyleStyle'>{{ timeData.minutes }}</div>
              <span>分</span>
              <div class='acblockStyleStyle'>{{ timeData.seconds }}</div>
              <span>秒</span>
            </div>
          </template>
        </van-count-down>
      </template>
    </div>

    <div class='header-btn-box'>
      <div class='header-btn' v-for='(btn, index) in btnList' :key='index' @click='btn.event'></div>
    </div>

    <div class='show-number-group'>
      <div class='cumulative-number show-number-item'>{{ activityInfo.type === 1 ? '累计已消费金额' : '累计已获得罐数' }}:<br />{{ activityInfo.totalCanNum }}</div>
      <div class='remainder-number show-number-item'>{{ activityInfo.type === 1 ? '您剩余可消耗金额' : '您剩余可兑换罐数' }}:<br />{{ activityInfo.remainingCanNum }}</div>
    </div>

    <div class='act-strategy'>
      <img :src='decoData.actStrategy' alt=''>
    </div>

    <div class='exchange-module' :class="{'exchange-module-money':activityInfo.type===1}">
      <div class='tab-view' :style="{'justify-content': activityInfo.seriesList?.length>=3?'flex-start':'space-evenly'}">
        <div class='tab-item' :class='{activeTab:index===currentTab}' v-for='(item,index) in activityInfo.seriesList' :key='index' @click='changeTab(item,index)'>{{ item.seriesName }}</div>
      </div>
      <div class='swiper-container'>
        <div class='swiper-wrapper'>
          <div class='swiper-slide' v-for='(item, index) in currentSerise.prizeList' :key='index'>
            <div class='prize-view'>
              <img class='prize-img' :src='item.prizeImg' alt=''>
              <div class='prize-info'>
                <div class='prize-levle'>{{ activityInfo.type === 0 ? `【${currentSerise.seriesThreshold}罐可兑礼品】` : `【消费满${currentSerise.seriesThreshold}元可兑礼品】` }}</div>
                <div class='prize-name'>{{ item.prizeName }}</div>
                <div class='prize-stock'>剩余库存：{{ item.remainingCount }}</div>
              </div>
              <div class='draw-btn' :class='{noStock:item.status!==5,gray:item.status===4||item.status===1}' @click='maskSureDrawPrize(item)'>{{ getDrawBtnName(item.status) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class='sku-module' v-if='activityInfo.skuList?.length>0'>
      <div class='sku-list' :style="{height:activityInfo.skuList?.length>=5?'11.7rem':''}">
        <div class='sku-item' v-for='(item,index) in activityInfo.skuList'>
          <img :src='item.skuMainPicture' class='sku-image' alt=''>
          <div class='two-line-omit sku-name'>{{ item.skuName }}</div>
          <img @click='gotoSkuPage(item.skuId)' style='width: 1.72rem' src='//img10.360buyimg.com/imgzone/jfs/t1/300934/40/14701/5039/6858c46cFca439df4/dc490c2e2b3c608c.png' alt=''>
        </div>
      </div>
    </div>

    <div class='banner-module' v-if='decoData.isShowBanner'>
      <div class='swiper-banner'>
        <div class='swiper-wrapper'>
          <div class='swiper-slide' v-for='(item, index) in decoData.bannerList' :key='index'>
            <div class='' @click='goBannerHref(item)'>
              <img :src='item.bannerImage' class='banner-image' alt=''>
            </div>
          </div>
        </div>
      </div>
    </div>

    <VanPopup v-model:show='popupShow' :close-on-click-overlay='false'>
      <!-- 规则弹窗 -->
      <RulePopup :rule='activityInfo.rule' v-if="popupName === 'rulePopup'"></RulePopup>
      <!-- 我的奖品弹窗 -->
      <MyOrder v-if="popupName === 'myOrderPopup'" :activityInfo='activityInfo'></MyOrder>
      <!-- 我的奖品弹窗 -->
      <MyPrize v-if="popupName === 'myPrizePopup'" :activityInfo='activityInfo' @fillAddress='fillAddress'></MyPrize>
      <!-- 领奖成功弹窗 -->
      <DrawSuccessPopup v-if="popupName === 'drawSuccessPopup'" :drawSuccessPrize='drawSuccessPrize' @fillAddress='fillAddress'></DrawSuccessPopup>
      <!-- 领奖失败弹窗 -->
      <DrawFailPopup v-if="popupName === 'drawFailPopup'"></DrawFailPopup>
      <!-- 非会员弹窗 -->
      <NoMemberPopup v-if="popupName === 'noMemberPopup'"></NoMemberPopup>
      <!-- 填写地址弹窗 -->
      <AddressPopup v-if="popupName === 'addressPopup'" :userPrizeId='userPrizeId' :addressInfo='addressInfo'></AddressPopup>
      <!-- 确认兑换弹窗 -->
      <MakeSurePopup v-if="popupName === 'makeSurePopup'" @sureDrawPrize='receiveGift' :activityInfo='activityInfo' :currentSerise='currentSerise' :currentDrawPrize='currentDrawPrize'></MakeSurePopup>

    </VanPopup>

  </div>
</template>

<script setup lang='ts'>
import { ref, inject, reactive, onMounted, nextTick } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { IActivityInfo } from './ts/type';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import 'swiper/swiper-bundle.css'; // 引入Swiper样式

import { popupShow, popupName, openPopup, closePopup } from './ts/popup';
import RulePopup from './components/RulePopup.vue';
import MyPrize from './components/MyPrize.vue';
import MyOrder from './components/MyOrder.vue';
import AddressPopup from './components/AddressPopup.vue';
import DrawFailPopup from './components/DrawFailPopup.vue';
import MakeSurePopup from './components/MakeSurePopup.vue';
import NoMemberPopup from './components/NoMemberPopup.vue';
import DrawSuccessPopup from './components/DrawSuccessPopup.vue';
import Swiper, { Autoplay } from 'swiper';

const baseInfo = inject('baseInfo') as BaseInfo;
const decoData = inject('decoData') as any;
const pathParams = inject('pathParams') as any;
const activityInfo = reactive({}) as IActivityInfo; // 活动相关数据

const mySwiper = ref();
const bannerSwiper = ref();
const userPrizeId = ref('');
const currentTab = ref(0);
const currentSerise = ref({}); // 当前选择的阶梯系列

const currentDrawPrize = ref({});
const drawSuccessPrize = ref({});
const addressInfo = ref({});
Swiper.use([Autoplay]); // 使用Swiper的扩展模块

const getDrawBtnName = (status: number) => {
  switch (status) {
    case 1:
      return '已兑换';
    case 4:
      return '奖品已兑完';
    default:
      return '立即兑换';
  }
};

const changeTab = (item: any, index: number) => {
  initSwiper();
  currentTab.value = index;
  currentSerise.value = item as any;
};

const goBannerHref = (banner: any) => {
  if (banner.isShowJump && banner.bannerLink) {
    window.location.href = banner.bannerLink;
  }
};

// 获取活动规则
const getRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    Object.assign(activityInfo, { rule: data });
    openPopup('rulePopup');
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取曝光商品
const getSkuListPage = async () => {
  try {
    const { data } = await httpRequest.post('/92015/getSkuListPage', { pageNum: 1, pageSize: 100 });
    Object.assign(activityInfo, { skuList: data.records });
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取资产信息
const getSeriesPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/92015/getSeriesPrizes');
    currentSerise.value = data[0];
    currentTab.value = 0;
    Object.assign(activityInfo, { seriesList: data });
    initSwiper();
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取活动信息
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/92015/activity');
    Object.assign(activityInfo, data);
    await getSeriesPrizes();
    await getSkuListPage();
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取用户订单
const getUserOrders = async () => {
  try {
    const { data } = await httpRequest.post('/92015/getUserOrders');
    Object.assign(activityInfo, { userOrderList: data });
    openPopup('myOrderPopup');
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取用户奖品
const getUserPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/92015/getUserPrizes');
    Object.assign(activityInfo, { userPrizeList: data });
    openPopup('myPrizePopup');
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

const fillAddress = (info: any) => {
  addressInfo.value = info;
  userPrizeId.value = info.userPrizeId;
  console.log('fillAddress', fillAddress, '===============');
  openPopup('addressPopup');
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      getRule();
    },
  },
  {
    name: '我的订单',
    event: () => {
      getUserOrders();
    },
  },
  {
    name: '兑换记录',
    event: () => {
      getUserPrizes();
    },
  },
];

const receiveGift = async (prizeId: string) => {
  try {
    // const data = {
    //   'activityPrizeId': '1938058354951892993',
    //   'couponSkuId': null,
    //   'exchangeImg': null,
    //   'prizeImg': 'http://img14.360buyimg.com/n1/s200x200_jfs/t1/315672/22/2542/179380/682c3110Fa371e162/f73fa18ccfb40265.jpg',
    //   'prizeName': '澳优（Ausnutr',
    //   'prizeType': 3,
    //   'result': {
    //     'result': 'o250626180611506949',
    //   },
    //   'sortId': 1,
    //   'status': 1,
    //   'userPrizeId': '1938176961093406722',
    // };
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/92015/receivePrize', { prizeId });
    closeToast();
    if (data.status === 0) { // 如果兑换失败
      showToast('兑换失败，请稍后再试');
      closePopup();
    }
    if (data.status === 1) { // 如果兑换成功
      drawSuccessPrize.value = data;
      openPopup('drawSuccessPopup');
    }
    await getActivityInfo();
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

const maskSureDrawPrize = (gift: any) => {
  if (baseInfo?.thresholdResponseList[0]?.thresholdCode === 1) {
    showToast('活动未开始');
    return;
  }
  if (baseInfo?.thresholdResponseList[0]?.thresholdCode === 2) {
    showToast('活动已结束');
    return;
  }
  const status = gift.status;
  switch (status) {
    case 1:
      showToast('该奖品已达到领取上限');
      return;
    case 2:
      showToast('该阶梯已达到领取上限');
      return;
    case 3:
      showToast(`剩余可兑${activityInfo.type === 0 ? '罐数' : '金额'}不足`);
      return;
    case 4:
      showToast('很抱歉，该奖品库存不足');
      return;
  }
  if (status === 5) {
    currentDrawPrize.value = gift;
    console.log(gift);
    openPopup('makeSurePopup');
  }
};

const initSwiper = () => {
  nextTick(() => {
    if (mySwiper.value) {
      mySwiper.value?.destroy();
    }
    mySwiper.value = new Swiper('.swiper-container', {
      slidesPerView: 1.2,
      centeredSlides: true,   // 居中显示当前幻灯片
      spaceBetween: 10,
    });
  });

  nextTick(() => {
    if (bannerSwiper.value) {
      bannerSwiper.value?.destroy();
    }
    bannerSwiper.value = new Swiper('.swiper-banner', {
      loop: decoData.bannerList.length > 1,
      autoplay: {
        delay: 2000,
        disableOnInteraction: false,
      },
      centeredSlides: true,   // 居中显示当前幻灯片
    });
  });
};

onMounted(() => {
  if (!baseInfo.levelQualify) {
    openPopup('noMemberPopup');
  }
  getActivityInfo();
});

</script>

<style scoped lang='scss'>

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
