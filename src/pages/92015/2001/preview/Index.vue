<template>
  <div class='bg' :style='{backgroundImage:`url(${furnish.pageBg})`,backgroundColor:furnish.actBgColor}'>
    <div class='count-down-time'>
      <span class='title'>距活动结束:</span>
      <van-count-down format='DD:HH:mm:ss'>
        <template #default='timeData'>
          <div class='contentSpan'>
            <div class='acblockStyleStyle'>XX</div>
            <span>天</span>
            <div class='acblockStyleStyle'>XX</div>
            <span>时</span>
            <div class='acblockStyleStyle'>XX</div>
            <span>分</span>
            <div class='acblockStyleStyle'>XX</div>
            <span>秒</span>
          </div>
        </template>
      </van-count-down>
    </div>

    <div class='header-btn-box'>
      <div class='header-btn' v-for='(btn, index) in btnList' :key='index' @click='btn.event'></div>
    </div>

    <div class='show-number-group'>
      <div class='cumulative-number show-number-item'>{{ activityInfo.type === 1 ? '累计已消费金额' : '累计已获得罐数' }}:<br />0</div>
      <div class='remainder-number show-number-item'>{{ activityInfo.type === 1 ? '您剩余可消耗金额' : '您剩余可兑换罐数' }}:<br />0</div>
    </div>

    <div class='act-strategy'>
      <img :src='furnish.actStrategy' alt=''>
    </div>

    <div class='exchange-module' :class="{'exchange-module-money':activityInfo.type===1}">
      <div class='tab-view' :style="{'justify-content': activityInfo.seriesList?.length>=3?'flex-start':'space-evenly'}">
        <div class='tab-item' :class='{activeTab:index===currentTab}' v-for='(item,index) in activityInfo?.seriesList' :key='index' @click='changeTab(item,index)'>{{ item.stepName }}</div>
      </div>
      <div class='swiper-container'>
        <div class='swiper-wrapper'>
          <div class='swiper-slide' v-for='(item, index) in currentSerise?.prizeList' :key='index'>
            <div class='prize-view'>
              <img class='prize-img' :src='item.prizeImg' alt=''>
              <div class='prize-info'>
                <div class='prize-levle'>{{ activityInfo.type === 0 ? `【${currentSerise.stepNum}罐可兑礼品】` : `【消费满${currentSerise.stepNum}元可兑礼品】` }}</div>
                <div class='prize-name'>{{ item.prizeName }}</div>
                <div class='prize-stock'>剩余库存：{{ item.sendTotalCount }}</div>
              </div>
              <div class='draw-btn' @click='clickShowTosat()'>立即兑换</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class='sku-module' v-if='activityInfo.isExposure===1'>
      <div class='sku-list' :style="{height:activityInfo.exposureSkuList?.length>=5?'11.7rem':''}">
        <div class='sku-item' v-for='(item,index) in activityInfo.exposureSkuList'>
          <img :src='item.skuMainPicture' class='sku-image' alt=''>
          <div class='two-line-omit sku-name'>{{ item.skuName }}</div>
          <img @click='clickShowTosat()' style='width: 1.72rem' src='//img10.360buyimg.com/imgzone/jfs/t1/300934/40/14701/5039/6858c46cFca439df4/dc490c2e2b3c608c.png' alt=''>
        </div>
      </div>
    </div>

    <div class='banner-module' v-show='furnish.isShowBanner'>
      <div class='swiper-banner'>
        <div class='swiper-wrapper'>
          <div class='swiper-slide' v-for='(item, index) in furnish?.bannerList' :key='index'>
            <div class='' @click='clickShowTosat()'>
              <img :src='item.bannerImage' class='banner-image' alt=''>
            </div>
          </div>
        </div>
      </div>
    </div>

    <VanPopup teleport='body' v-model:show='showRule' :close-on-click-overlay='false'>
      <div class='rule-box'>
        <div class='content' v-html='activityInfo.rules'></div>
        <div class='close' @click='showRule=false'></div>
      </div>
    </VanPopup>
    <VanPopup teleport='body' v-model:show='showOrder' :close-on-click-overlay='false'>
      <div class='order-box'>
        <div class='content'>
          <ul>
            <li style='width: 60%'>订单号</li>
            <li v-if='activityInfo.type===0'>获得罐数</li>
            <li v-else>获得金额</li>
          </ul>

          <div class='no-data'>您还没有符合条件的订单哦~</div>
        </div>
        <div class='close' @click='showOrder=false'></div>
      </div>
    </VanPopup>
    <VanPopup teleport='body' v-model:show='showMyPrize' :close-on-click-overlay='false'>
      <div class='myPrize-box'>
        <div class='content'>
          <ul>
            <li>礼品名称</li>
            <li>兑换时间</li>
            <li v-if='activityInfo.type===0'>消耗罐数</li>
            <li v-else>消耗金额</li>
            <li>状态</li>
          </ul>
          <div class='no-data'>您还没有兑换记录~</div>
        </div>
        <div class='close' @click='showMyPrize=false'></div>
      </div>
    </VanPopup>
  </div>
</template>

<script setup lang='ts'>
import { ref, onMounted, onUnmounted, inject, nextTick } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import '../style/index.scss';
import { showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper-bundle.css'; // 引入Swiper样式

const { registerHandler } = usePostMessage();
const activityData = inject('activityData') as any;
const isLoadingFinish = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const shopName = ref('xxx旗舰店');
const showRule = ref(false);
const showOrder = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const mySwiper = ref<Swiper>();
const bannerSwiper = ref();
// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
Swiper.use([Autoplay]); // 使用Swiper的扩展模块

const clickShowTosat = () => {
  showToast('活动预览，仅供查看');
};

const activityInfo = ref({});
const currentTab = ref(0);
const currentSerise = ref({}); // 当前选择的阶梯系列
const setDataInfo = (data: any) => { // 数据赋值
  activityInfo.value = data;
  if (data.seriesList?.length > 0) {
    currentTab.value = 0;
    currentSerise.value = data.seriesList[0];
    nextTick(() => {
      if (mySwiper.value) {
        mySwiper.value?.destroy();
      }
      mySwiper.value = new Swiper('.swiper-container', {
        slidesPerView: 1.2,
        centeredSlides: true,   // 居中显示当前幻灯片
        spaceBetween: 10,
      });
    });
  }
  if (data.rules) {
    ruleTest.value = data.rules;
  }
};

const changeTab = (item: any, index: number) => {
  currentTab.value = index;
  currentSerise.value = item as any;
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      showRule.value = true;
    },
  },
  {
    name: '我的订单',
    event: () => {
      showOrder.value = true;
    },
  },
  {
    name: '我的奖品',
    event: () => {
      showMyPrize.value = true;
    },
  },
];

const initBannerSwiper = () => {
  if (bannerSwiper?.value) {
    bannerSwiper?.value?.destroy();
  }
  nextTick(() => {
    bannerSwiper.value = new Swiper('.swiper-banner', {
      // observer: true,       // 启用 Swiper 自身变化观察
      // observeParents: true,
      loop: furnish?.bannerList?.length > 1,
      autoplay: {
        delay: 2000,
      },
    });
  });
};
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });

    initBannerSwiper();
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    setDataInfo(res.data.data);
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'task') {
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
// 截图监听
registerHandler('screen', () => {
  createImg();
});
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  if (activityData) {
    setDataInfo(activityData);
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    initBannerSwiper();
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang='scss'>
img[src=""], img:not([src]) {
  opacity: 0;
}

.rule-box {
  width: 6.73rem;
  height: 11.12rem;
  position: relative;
  padding-top: 1rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/292559/39/17613/11169/685cec30F9de58146/9083e8a04598fb59.png");
    repeat: no-repeat;
    size: contain;
  };

  .content {
    height: 8.7rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
  }
}

.order-box {
  width: 6.72rem;
  height: 8.64rem;
  position: relative;
  padding-top: 1.4rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/297187/16/16384/11500/685cec2dF4b35ddb4/37389c406b0c0b9a.png");
    repeat: no-repeat;
    size: contain;
  };

  .content {
    height: 5.5rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    ul {
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      margin-bottom: .15rem;
      line-height: .5rem;

      li {
        font-size: .28rem;
        color: #4f4f4f;
        font-weight: bold;
        text-align: center;
        width: 30%;
      }
    }
  }
}

.myPrize-box {
  width: 6.72rem;
  height: 8.64rem;
  position: relative;
  padding-top: 1.4rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/294300/9/7861/11309/685cec2cF13f76d76/2d814514f15fe91f.png");
    repeat: no-repeat;
    size: contain;
  };

  .content {
    height: 5.5rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    ul {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: .15rem;
      line-height: .5rem;

      li {
        font-size: .22rem;
        color: #4f4f4f;
        font-weight: bold;
        text-align: center;
        width: 25%;
      }
    }
  }
}

.no-data {
  text-align: center;
  line-height: 4.5rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}

.close {
  width: .8rem;
  height: .8rem;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
