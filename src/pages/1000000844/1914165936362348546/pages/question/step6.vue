<template>
    <div class="main-view">
        <div class="qustion-slise-box" :style="`background-color: ${PAGE_CONFIG.questionSlideBg};`">
            <div class="qustion-slise-item" :style="`background-color: ${PAGE_CONFIG.quuetionSlideColor};width: 100%;`"></div>
        </div>
        <div class="qustion-step-text">{{ `(6/6)` }}</div>
        <canvas id="step1Title" style="width: 7.5rem;height: 1.4rem;"></canvas>
        <div class="qustion-no-btn">
          <input type="text" v-model="petWeight" placeholder="请输入体重数值，例如：8" @input="onInputWeight" maxlength="7" />
        </div>
        <img class="qustion-step-tips" style="height: 3.06rem;" :src="require(`../../asset/question/tips2.png`)"/>
        <img class="common-dog" :src="require(`../../asset/question/questionDog.png`)"/>
        <div class="photo-bottom">
          <img class="item-btn" style="width: 2.16rem;height: 1.04rem;" :src="require('../../asset/question/before.png')" @click="router.back();"/>
          <img class="item-btn" style="width: 2.16rem;height: 1.04rem;" v-if="!petWeight" :src="require('../../asset/question/next-no.png')"/>
          <img class="item-btn" style="width: 4.16rem;height: 1.04rem;" v-else :src="require('../../asset/question/to-result.png')" @click="nextStep"/>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted, reactive } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import { saveQaInfo } from '../../config/api';
import { emit } from 'process';
import { lzReportClick } from '@/pages/MARS/lzReport';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant/es';

const route = useRoute();
const router = useRouter();
const store = useStore<RootState>();
const PetInfo = computed(() => store.state.createPetForm);

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');

const petWeight = ref(store.state.petWeight);
const addTitle = () => {
  const canvas = document.getElementById('step1Title') as HTMLCanvasElement;
  const ctx = canvas.getContext('2d')!;
  const posterWidth = 750; // 7.5rem 转换为像素
  const posterHeight = 140; // 示例高度
  canvas.width = posterWidth;
  canvas.height = posterHeight;
  ctx.fillStyle = '#fff';
  ctx.fillRect(0, 0, posterWidth, posterHeight);
  ctx.font = 'bold 40px Arial';
  ctx.fillStyle = '#000';
  ctx.textAlign = 'center';
  ctx.fillText(`目前，${PetInfo.value.petNick}的体重为多少kg？`, posterWidth / 2, 45);
};

const changeNum = (value: any) => {
  // 去掉除除数字及小数点以外的字符
  let val = value.replace(/[^\d.]/g, '');
  const index = value.indexOf('.');
  const includNumber = /\d./g.test(val);
  // console.log(index, includNumber);
  if (index > -1 && includNumber) {
    val = val.replace(/\./g, '');
    val = val.split('');
    val.splice(index, 0, '.');
    val = val.join('');
    if (index === 0) {
      val = `0${val}`;
    }
    if (val.length - index > 3) {
      val = val.substring(0, index + 3);
    }
  }
  if (index === 0 && !includNumber) {
    val = `0${val}`;
  }
  console.log('****', val, '****');
  return val;
};
const onInputWeight = (event: any) => {
  const value = event?.target.value;
  // 限制输入的长度为 7 位 从天猫拿来的限制规则
  if (value.length > 7) {
    event.target.value = value.slice(0, 7);
  }
  const num = Number(event?.target.value);
  petWeight.value = changeNum(event.target.value);
};
const nextStep = () => {
  console.log(123);
  store.commit('setPetWeight', petWeight.value);
  lzReportClick('qa-petWeight');
  window.localStorage.setItem('1000000844-petNick', store.state.createPetForm.petNick);
  router.push('/loadding');
};

onMounted(() => {
  console.log('step1');
  addTitle();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss">
.swiper-box {
  width: 7.5rem;
  height: auto;
  background: #fff;
  overflow: hidden;
  position: relative;
}
</style>
