<template>
    <VanPopup teleport="body" v-model:show="show" position="center" :close-on-click-overlay="true">
        <div>
            <div class="common-text-popup">
                <div class="title">免责声明</div>
                <div style="height: 8.25rem;overflow-y: scroll;">
                    <div style="margin-bottom: 0.6rem;">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;汪汪爱牙宝是一款使用人工智能技术的信息工具，它并非用于诊断狗狗的牙科疾病或其他任何疾病或健康状况--此目的只有由兽医通过全面的兽医检查才能达到。
                    </div>
                    <div style="margin-bottom: 0.6rem;">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;此外，通过使用汪汪爱牙宝，您接受:(i)使用汪汪爱牙宝受到玛氏<text class="link-text" @click="openPrivacyLinkLink">隐私声明</text>和<text class="link-text" @click="openTermsOfUse">使用条款</text>的约束;(ii)您通过汪汪爱牙宝提交的图像可能会被玛氏用于未来的模型训练和其他研究目的;(iii)汪汪爱牙宝的结果不能替代兽医的口腔检查，其仅用于帮助支持您与兽医的后续交流。
                    </div>
                    <div>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;点击“我已知晓”按钮，即可查看汪汪爱牙宝结果。
                    </div>
                    <div class="bottom-btn" :style="`border-color: #d96c06;color: #d96c06`" @click="closePopup()">我已知晓</div>
                </div>
            </div>
        </div>
    </VanPopup>
</template>
<script lang="ts" setup>
import { FLAGS } from 'html2canvas/dist/types/dom/element-container';
import { emit } from 'process';
import { text } from 'stream/consumers';
import { defineProps, computed, ref, inject } from 'vue';

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
  },
});

const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const openPrivacyLinkLink = () => {
  window.open(PAGE_CONFIG.privacyLink);
};
const openTermsOfUse = () => {
  window.open(PAGE_CONFIG.termsOfUse);
};

const show = computed(() => props.isShow);
const emits = defineEmits(['closePopup']);
const closePopup = () => {
  emits('closePopup');
};

</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
<style lang="scss" scoped>
.bottom-btn {
    width: 3.8rem;
    margin: auto;
    height: 1rem;
    border-radius: 0.5rem;
    box-sizing: border-box;
    border-width: 0.03rem;
    border-style: solid;
    text-align: center;
    font-size: 0.48rem;
    line-height: 1rem;
}
</style>
