/* eslint-disable */
export interface RootState {
  toothImg: string;
  checkId: string;
  createPetForm: any;
  petInfo: any;
  qustionRes: any;
  aiRes: any;

}

const state: RootState = {
  toothImg: '',
  checkId: '',
  createPetForm: {
    otherPin: '',
    petAvatar: 'https://img.alicdn.com/imgextra/i2/155168396/O1CN01FWskFX2BtQJY3wO2Z_!!155168396.jpg',
    petBirth: '', // 宠物生日
    petBreed: '', // 宠物品种
    petBreedId: 1, // 宠物品种id
    petDefault: true, // 是否默认展示（false：否；true：是）
    petGender: 0, // 宠物性别（0：未知；1：公；2：母）
    petImmunity: true, // 是否免疫（false：否；true：是）
    petNeutered: 0, // 是否绝育（0：否；1：是；2：未知）
    petNick: '',
    petPrescription: true, // 是否使用过处方粮（false：否；true：是）
    petType: 1, // 2 表示猫，1 表示狗
    petWeight: 0, // 宠物体重
  },
  petInfo: {
    petId: null,
    petAvatar: '',
    petNick: '',
    petType: 0,
    petBreed: '',
    petGender: 0,
  },
  qustionRes: {},
  aiRes: {},
};
export default state;
