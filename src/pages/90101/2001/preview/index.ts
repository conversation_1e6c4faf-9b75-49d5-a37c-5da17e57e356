import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

// const _decoData = {
//   actBg: '',
//   pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/2062/15/24704/271830/66cd710dF0850932f/b2c5ac94ca95ac9a.png',
//   actBgColor: '#1c183d',
//   ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/244229/22/17079/5275/66cd710cFa22072ed/9e6baa78a2f4d7f4.png',
//   myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/5983/18/25206/5085/66cd710dF9554f29d/65728de3dea95f22.png',
//   shopNameColor: '#111111',
//   prizeContentBg: '//img10.360buyimg.com/imgzone/jfs/t1/237848/24/24061/1020/66cd710dFb3c9e080/64d561332b28702d.png',
//   prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/57117/24/26328/10801/66cd7dadFecfbc19f/a9e040751ea719ac.png',
//   prizeNameColor: '#ffffff',
//   drawsNum: '#ffffff',
//   drawBtn: '//img10.360buyimg.com/imgzone/jfs/t1/237050/2/25010/5640/66cd710dF3ebc3ede/cbfbbd94784a3578.png',
//   winnersBg: '//img10.360buyimg.com/imgzone/jfs/t1/69572/18/28026/2615/66cd710dF7fb64e69/13eec43bbb8239ab.png',

//   ruleBk: '//img10.360buyimg.com/imgzone/jfs/t1/88578/3/50938/76657/670496ceFd5b3bd00/6c9f3eb864c733cd.png',
//   ruleTextColor: '#ffffff',
//   myPrizeBk: '//img10.360buyimg.com/imgzone/jfs/t1/149483/5/44230/77729/670496cdF037e5b51/b4d558455a79419d.png',
//   myPrizeTextColor: '#fbdeb5',
//   awardBk: '//img10.360buyimg.com/imgzone/jfs/t1/134985/10/47322/77122/670496ccF32eabf8d/d0261e1cfb0842e6.png',
//   notAwardBk: '//img10.360buyimg.com/imgzone/jfs/t1/188078/40/50495/89350/670496cdF80026eb4/cac9b9a7abe329ad.png',
//   saveAddressBtn: '//img10.360buyimg.com/imgzone/jfs/t1/193678/8/47775/7670/670496ceFb2d14ef8/82bcc7f9adc9af21.png',
//   confirmBtn: '//img10.360buyimg.com/imgzone/jfs/t1/196005/12/48157/6941/670496ccF18162b62/3482d87f8bd2e3e6.png',
//   saveAddressBk: '//img10.360buyimg.com/imgzone/jfs/t1/182286/25/49623/62964/670496cfF2183bd61/8413479c205631e9.png',
//   saveAddressColor: '#13994f',
//   copyCardBk: '//img10.360buyimg.com/imgzone/jfs/t1/178067/10/49074/82564/670496cdFa6bfb867/8bc6eee1ff658be6.png',
//   copyCardTitle: '#ffdb6e',
//   copyCardText: '#fff',
//   noOrderTextColor: '#ffffff',
//   noOrderBk: '//img10.360buyimg.com/imgzone/jfs/t1/99180/4/53534/65533/670496cdF92e88316/bdb17f6e99f01501.png',
//   joinBk: '//img10.360buyimg.com/imgzone/jfs/t1/243692/26/12923/72780/670496ceF2b8b805a/746853682b5dee90.png',

//   tipColor: '#ffffff',
//   tipImportColor: '#ffd800',
//   h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/210614/13/34860/50205/64804bb8F190cfb33/1caec4458757db02.jpg',
//   cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/210614/13/34860/50205/64804bb8F190cfb33/1caec4458757db02.jpg',
//   mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/210614/13/34860/50205/64804bb8F190cfb33/1caec4458757db02.jpg',
// };

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '下单抽奖';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
