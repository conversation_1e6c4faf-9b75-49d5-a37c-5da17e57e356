import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';

import '@/style';
import 'animate.css';
import './common.scss';
import { initConfigData } from './common';

initRem();

const app = createApp(index);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
};
init(config).then(async ({ baseInfo, pathParams }) => {
  // 设置页面title
  document.title = baseInfo?.activityName;
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  await initConfigData();
  app.mount('#app');
});

// app.mount('#app');
