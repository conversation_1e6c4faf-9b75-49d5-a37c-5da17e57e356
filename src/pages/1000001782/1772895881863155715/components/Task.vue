<template>
  <div class="taskAllClass" :style="{ backgroundImage: `url(${imageJson.taskBgImage}})` }">
    <div class="taskListClass" :key="timeKey">
      <div class="taskItemClass" v-for="(item, index) in taskList" :key="index">
        <div class="taskLeftClass">
          <div class="taskIconClass">
            <img :src="taskIcon[item.taskType]" alt="" />
          </div>
          <div class="taskContentClass">
            <div class="taskTitleClass" v-if="item.taskType === 8 || item.taskType === 13">
              {{ taskType[item.taskType] }}(无上限)
              <div class="taskRedBagClass"></div>
            </div>
            <div class="taskTitleClass" v-else>
              {{ taskType[item.taskType] }}({{ item.dailyLimit ? item.taskFinishCount + '/' : null }}{{ item.dailyLimit ? item.dailyLimit : '无上限' }})
              <div class="taskRedBagClass"></div>
            </div>
            <div class="taskInsClass">
              {{ taskPrompt[item.taskType] }}得<span>{{ item.perLotteryCount }}</span
              >悦享值并有机会获得红包
            </div>
          </div>
        </div>
        <div v-if="item.taskType === 8 || item.taskType === 13">
          <div class="taskRightBtnClass" @click="doTask(item)" :style="{ backgroundImage: `url(${imageJson.taskBtnImage}})` }">{{ taskBtnText[item.taskType] }}</div>
        </div>
        <div v-else>
          <div v-if="!item.dailyLimit || (item.dailyLimit && item.taskFinishCount < item.dailyLimit)" class="taskRightBtnClass" @click="doTask(item)" :style="{ backgroundImage: `url(${imageJson.taskBtnImage}})` }">{{ taskBtnText[item.taskType] }}</div>
          <div v-else-if="item.dailyLimit && item.taskFinishCount >= item.dailyLimit" class="taskRightBtnGrayClass" :style="{ backgroundImage: `url(${imageJson.taskBtnImage}})` }">今日已完成</div>
        </div>
      </div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="showTaskSuccessPop" position="center">
    <TaskSuccessPop :redBagCount="redBagCount"></TaskSuccessPop>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref, reactive, onMounted, onUnmounted, inject } from 'vue';
import TaskSuccessPop from './TaskSuccesspop.vue';
import { configData, getConfigData } from '../common';
import { callShare } from '@/utils/platforms/share';
import { httpRequest } from '@/utils/service';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;

interface Task {
  dailyLimit: number;
  skuId: number;
  shopUrl: number;
  taskFinishCount: number;
  taskType: number;
  perLotteryCount: number;
  shopId: string;
  liveUrl: string;
}
const pathParams = inject('pathParams') as any;
const redBagCount = ref(1);
const timeKey = ref(dayjs().valueOf());
const taskType = {
  2: '关注店铺',
  3: '逛逛精选新品',
  4: '逛逛超级品牌日会场',
  7: '加购品牌新品',
  8: '购买商品',
  13: '邀请好友入会',
  14: '每日签到',
};
const taskIcon = {
  2: '//img10.360buyimg.com/imgzone/jfs/t1/50117/34/24388/2126/6698763aFd19e9e8c/55f6b3e9b4770939.png',
  3: '//img10.360buyimg.com/imgzone/jfs/t1/244991/17/14911/2289/6698763aFe3cf1b12/4d95039e361da4e1.png',
  4: '//img10.360buyimg.com/imgzone/jfs/t1/226813/31/23189/2330/6698763aFd41e6926/7f0fb33e99c18e02.png',
  7: '//img10.360buyimg.com/imgzone/jfs/t1/183416/30/47789/2752/66987639F240fcefc/334bc785b8d5081b.png',
  8: '//img10.360buyimg.com/imgzone/jfs/t1/244722/2/14999/2000/6698763bF328cd299/13e140844329ed0b.png',
  13: '//img10.360buyimg.com/imgzone/jfs/t1/231975/13/24008/2063/66987637F69d098df/7d1417c5ada5f4e5.png',
  14: '//img10.360buyimg.com/imgzone/jfs/t1/242326/21/15188/2122/66987639F26cd98d1/a1a299fa1a7d3d47.png',
};
const taskPrompt = {
  2: '关注店铺',
  8: '购买任意商品获',
  4: '浏览会场15s',
  3: '浏览海尔系精选新品5s',
  7: '加购海尔系品牌新品',
  13: '邀请一位好友加入卡萨帝会员',
  14: '每日活动签到',
};
const taskBtnText = {
  2: '去关注',
  3: '去逛逛',
  4: '去逛逛',
  7: '去加购',
  8: '去购买',
  13: '去邀请',
  14: '去签到',
};
const moduleName = 'getTask';
const taskList = reactive([] as Task[]);
taskList.splice(0);
taskList.push(...configData.value[moduleName]);
console.log(taskList, 'taskList=========');
const props = defineProps({
  isStart: {
    type: Boolean,
    default: false,
    required: true,
  },
  acIsEnd: {
    type: Boolean,
    default: false,
    required: true,
  },
});
const showTaskSuccessPop = ref(false);
const imageJson = ref({
  taskBgImage: '//img10.360buyimg.com/imgzone/jfs/t1/36169/3/21781/93387/66987639Fcb98d8a7/f544a25789e08b3f.png',
  taskBtnImage: '//img10.360buyimg.com/imgzone/jfs/t1/66798/17/25277/863/66987638F9f82c1fb/5a87f7ede1088592.png',
});
const reportTask = async (taskType: number, skuId: number, shopId: string) => {
  redBagCount.value = 1;
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const apis = {
      2: '/dzKSD/browseShop',
      3: '/dzKSD/browseSku',
      4: '/dzKSD/browseLive',
      7: '/dzKSD/addSku',
      8: '/dzKSD/orderSku',
      13: '',
      14: '/dzKSD/singDay',
    };
    const res = await httpRequest.post(apis[taskType], { skuId, shopId });
    await getConfigData('getLoveNum');
    await getConfigData('getTask');
    taskList.splice(0);
    taskList.push(...configData.value[moduleName]);
    timeKey.value = dayjs().valueOf();
    if (res.code === 200) {
      showTaskSuccessPop.value = true;
      const aa = setTimeout(() => {
        showTaskSuccessPop.value = false;
        clearTimeout(aa);
      }, 2000);
      closeToast();
    } else {
      showToast(res.msg);
      closeToast();
    }
  } catch (error: any) {
    console.error(error);
    await getConfigData('getLoveNum');
    await getConfigData('getTask');
    taskList.splice(0);
    taskList.push(...configData.value[moduleName]);
    timeKey.value = dayjs().valueOf();
    if (error.message === '奖品已领完！' || error.message === '红包数量不足') {
      showToast('您与红包擦肩而过了 再接再厉吧!');
    } else {
      closeToast();
      showToast(error.message);
    }
  }
};

// 关注店铺
const fellowShop = async (shopId: string) => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    await httpRequest.post('/dzKSD/followShop', {
      shopId,
    });
    closeToast();
    return true;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
    return false;
  }
};

const doTask = async (taskData: any) => {
  if (!props.acIsEnd && !props.isStart) {
    showToast('活动尚未开始');
    return;
  }
  if (props.acIsEnd) {
    showToast('活动已结束');
    return;
  }
  if (taskData.taskType === 2) {
    console.log('浏览并关注店铺');
    const fellowType = await fellowShop(taskData.shopId);
    // 关注成功直接领取奖励
    if (fellowType) {
      await reportTask(taskData.taskType, 0, '0');
    }
    // 浏览并关注店铺
    // const oldtime = dayjs().valueOf();
    // window.taskCallback = () => {
    //   console.log('cccccccccccccc');
    // };
    // localStorage.setItem('doTask', JSON.stringify({ taskType: taskData.taskType, time: oldtime }));
    // gotoShopPage(taskData.shopId);
    // window.location.href = taskData.shopUrl;
  } else if (taskData.taskType === 3) {
    console.log('逛逛精选新品');
    const oldtime = dayjs().valueOf();
    window.taskCallback = () => {
      console.log('cccccccccccccc');
    };
    localStorage.setItem('doTask', JSON.stringify({ taskType: taskData.taskType, skuId: taskData.skuId, time: oldtime }));
    gotoSkuPage(taskData.skuId);
  } else if (taskData.taskType === 4) {
    console.log('逛逛超级品牌日会场');
    const oldtime = dayjs().valueOf();
    window.taskCallback = () => {
      console.log('cccccccccccccc');
    };
    localStorage.setItem('doTask', JSON.stringify({ taskType: taskData.taskType, time: oldtime }));
    window.location.href = taskData.liveUrl;
  } else if (taskData.taskType === 7) {
    console.log('加购品牌新品', taskData.skuId);
    await reportTask(taskData.taskType, taskData.skuId, taskData.addSkuShopId);
    const aab = setTimeout(() => {
      gotoSkuPage(taskData.skuId);
      clearTimeout(aab);
    }, 2000);
  } else if (taskData.taskType === 8) {
    console.log('购买商品跳转卡萨帝自营店铺');
    // gotoShopPage('1000004489');
    window.location.href = 'https://pro.m.jd.com/mall/active/3JxbxYMSwMmUBwABoZxiUXJe6RR2/index.html';
  } else if (taskData.taskType === 13) {
    // 邀请好友
    console.log('邀请好友');
    const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
    callShare({
      title: shareConfig.shareTitle,
      content: shareConfig.shareContent,
      imageUrl: shareConfig.shareImage,
    });
  } else if (taskData.taskType === 14) {
    console.log('每日签到');
    await reportTask(taskData.taskType, 0, '0');
  }
};
const helpCount = ref(0); // 助力成功发红包个数
// 调用购买商品任务接口
const buyTaskCheck = async () => {
  redBagCount.value = 1;
  try {
    const res = await httpRequest.post('/dzKSD/orderSku');
    console.log(res, '购买任务');
    if (res.data > 0 || helpCount.value > 0) {
      redBagCount.value = res.data + helpCount.value;
      showTaskSuccessPop.value = true;
      const aa = setTimeout(() => {
        showTaskSuccessPop.value = false;
        clearTimeout(aa);
      }, 2000);
      await getConfigData('getLoveNum');
      await getConfigData('getTask');
      taskList.splice(0);
      taskList.push(...configData.value[moduleName]);
      timeKey.value = dayjs().valueOf();
    }
  } catch (error: any) {
    console.log(error, 'err');
    await getConfigData('getLoveNum');
    await getConfigData('getTask');
    taskList.splice(0);
    taskList.push(...configData.value[moduleName]);
    timeKey.value = dayjs().valueOf();
    if (helpCount.value > 0) {
      redBagCount.value = helpCount.value;
      showTaskSuccessPop.value = true;
      const aa = setTimeout(() => {
        showTaskSuccessPop.value = false;
        clearTimeout(aa);
      }, 2000);
    } else if (helpCount.value <= 0) {
      if (error.message === '奖品已领完！' || error.message === '红包数量不足') {
        showToast('您与红包擦肩而过了 再接再厉吧!');
      } else if (!baseInfo.thresholdResponseList.length) {
        showToast(error.message);
      }
    }
  }
};
// 助力任务
const helpTask = async () => {
  helpCount.value = 0;
  console.log('助力任务');
  try {
    const res = await httpRequest.post('/dzKSD/getHBNum');
    console.log(res, '助力任务');
    helpCount.value = res.data;
    await buyTaskCheck();
  } catch (error: any) {
    console.log(error, 'err');
    helpCount.value = 0;
    await buyTaskCheck();
    if (error.message === '奖品已领完！' || error.message === '红包数量不足') {
      showToast('您与红包擦肩而过了 再接再厉吧!');
    } else {
      showToast(error.message);
    }
  }
};
// 检查任务状态
const initTask = async () => {
  const task = window.localStorage.getItem('doTask');
  console.log(task, '检查任务状态');
  if (!task) return;
  window.localStorage.removeItem('doTask');
  const newItem = JSON.parse(task);
  const newTime: number = dayjs().valueOf(); // 当前时间戳
  const oldtime: number = newItem.time; // 做任务的时间
  const { taskType, skuId } = newItem;
  const num = ref(0); // 需要做任务满足时长
  if (taskType === 2) {
    num.value = 15000;
  } else if (taskType === 4) {
    num.value = 15000;
  } else if (taskType === 3) {
    num.value = 5000;
  }
  const downTime = newTime - oldtime >= num.value;
  console.log(downTime, '检查任务状态1111');
  if (downTime) {
    reportTask(taskType, skuId, '0');
  } else {
    await getConfigData('getTask');
    showToast('浏览时间不足 不能获得奖励哦~');
  }
};
const handleVisiable = async (e: any) => {
  // 如果缓存中存在flag再执行判断visibilityState
  switch (e.target.visibilityState) {
    case 'visible':
      console.log('visible');
      if (window.taskCallback) {
        window.taskCallback = null;
        if (!pathParams.shareId) {
          await helpTask();
          await initTask();
        }
      }
      break;
    default:
      console.log('default');
      break;
  }
};
onMounted(() => {
  console.log('onMounted');
  document.addEventListener('visibilitychange', handleVisiable);
});
onUnmounted(() => {
  console.log('destroyed');
  document.removeEventListener('visibilitychange', handleVisiable);
});
if (!pathParams.shareId) {
  helpTask();
}
</script>

<style lang="scss" scoped>
.taskAllClass {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/227947/22/15676/7162/6602899eFaecf96b6/8f46e574d972bf49.png');
  background-repeat: no-repeat;
  background-size: 100%;
  width: 6.85rem;
  height: 11.13rem;
  margin-top: 0.28rem;
  padding-top: 1rem;
  margin-left: calc(50% - 6.85rem / 2);
  .taskListClass {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .taskItemClass {
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/50872/21/24882/7374/66987638F6282308a/ba6e18bcb2adffd3.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 6.09rem;
      height: 1.25rem;
      display: flex;
      justify-content: space-between;
      padding: 0.16rem;
      align-items: center;
      margin-bottom: 0.14rem;

      .taskLeftClass {
        display: flex;
        align-items: center;

        .taskIconClass {
          width: 0.98rem;
          height: 0.98rem;

          img {
            width: auto;
            height: 100%;
          }
        }

        .taskContentClass {
          display: flex;
          flex-direction: column;
          margin-left: 0.06rem;

          .taskTitleClass {
            display: flex;
            font-size: 0.25rem;
            color: #000000;
            align-items: center;

            .taskRedBagClass {
              background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/159268/37/43849/1428/6602899fF440e73cd/06c46320efdf75ee.png');
              background-repeat: no-repeat;
              background-size: 100%;
              width: 0.25rem;
              height: 0.3rem;
              margin-left: 0.07rem;
            }
          }

          .taskInsClass {
            font-size: 0.17rem;
            color: #623c1d;
            span {
              color: #e02c1b;
            }
          }
        }
      }
      .taskRightBtnClass {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/207942/1/39253/958/6602899dFf55e6bb3/573985df888b25e7.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 1.35rem;
        height: 0.53rem;
        color: #f1d0b0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.27rem;
      }

      .taskRightBtnGrayClass {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/207942/1/39253/958/6602899dFf55e6bb3/573985df888b25e7.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 1.35rem;
        height: 0.53rem;
        color: #f1d0b0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.22rem;
        filter: grayscale(1);
      }
    }
  }
}
</style>
