<template>
  <div class="bg">
    <div v-if="props.drawResultData.status === 1" class="contentClass">
      <div class="content">
        <div class="titleClass">中奖啦</div>
        <div class="prizeImageClass5" v-if="props.drawResultData.prizeType !== 3">
          <div class="prizeImageClass">
            <img :src="drawResultData.prizeImg" alt="" />
          </div>
          <div class="redBagInsClass">
            <div>抽中{{ drawResultData.prizeName }}</div>
            <div class="lookClass">可到京东APP-我的-我的钱包中查看</div>
          </div>
        </div>
        <div v-else-if="props.drawResultData.prizeType === 3">
          <div class="prize3Class">
            <div class="prizeImage3">
              <img :src="drawResultData.prizeImg" alt="" />
              <div class="prizeNameClass">{{ drawResultData.prizeName }}</div>
            </div>
            <div v-if="!isAddress" class="addressClass" @click="addressClick()"></div>
            <div v-else class="addressGrayClass"></div>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
<!--      <div class="noDrawCouponClass" v-if="showCoupon">-->
<!--        <div class="btnClass" @click="getCoupon" v-click-track="'lqyhq'"></div>-->
<!--      </div>-->
<!--      v-else-->
      <div class="noDrawClass">
        <div class="btnClass" @click="goOnClick()"></div>
      </div>
    </div>
    <div class="closeClass" @click="closeClickPop"></div>
  </div>
  <VanPopup teleport="body" v-model:show="showAddressPop" position="center">
    <AddressPop :activityPrizeId="drawResultData.activityPrizeId" :addressId="drawResultData.result.result" :addressData="addressData" @closePop="closeAddress"></AddressPop>
  </VanPopup>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import AddressPop from './AddressPop.vue';
import dayjs from 'dayjs';

const props = defineProps({
  drawResultData: {
    type: Object,
    required: true,
  },
});
const isAddress = ref(false); // 是否已经填写地址
const showAddressPop = ref(false);
const emits = defineEmits(['closePop']);
const addressData = ref(null);

const closeClickPop = () => {
  emits('closePop');
};
// 填写地址
const addressClick = () => {
  console.log('填写地址');
  showAddressPop.value = true;
};
// 继续参与活动
const goOnClick = () => {
  console.log('继续参与活动');
  emits('closePop');
};
// 关闭填写地址弹窗
const closeAddress = (isSubmit: boolean) => {
  showAddressPop.value = false;
  if (isSubmit) {
    isAddress.value = true;
  } else {
    isAddress.value = false;
  }
};

// 未中奖展示优惠券
const showCoupon = ref(false);

const getCoupon = () => {
  window.location.href = 'https://coupon.m.jd.com/coupons/show.action?key=c0mac0s6o4aa43b3b638329d62dac0a9&roleId=145805905&to=https://pro.m.jd.com/mall/active/42dkvNCeXWWXobi2R3ZPbaawwjVY/index.html';
};

const init = () => {
  // 未中奖展示优惠券, 未中奖次数小于等于3次展示
  if (props.drawResultData.status !== 1) {
    const now = dayjs().format('YYYYMMDD');
    // 未中奖次数
    let noDrawNum = 1;
    if (localStorage.getItem('HairNoDrawNum')) {
      const HairNoDrawNum = JSON.parse(localStorage.getItem('HairNoDrawNum') || '');
      if (HairNoDrawNum.date === now) {
        noDrawNum += HairNoDrawNum.num;
      }
    }
    if (noDrawNum <= 3) {
      showCoupon.value = true;
    }
    localStorage.setItem('HairNoDrawNum', JSON.stringify({ date: now, num: noDrawNum }));
  }
};
init();
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style lang="scss" scoped>
.bg {
  display: flex;
  flex-direction: column;
  align-items: center;
  .noDrawClass {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/198270/34/40369/59535/6698b259F39b7ea6d/e0d288309515b5fc.png) no-repeat;
    background-size: 100%;
    width: 6.45rem;
    height: 5.91rem;
    position: relative;
    display: flex;
    justify-content: center;
    .btnClass {
      width: 4.50rem;
      height: 0.84rem;
      position: absolute;
      bottom: 0.7rem;
    }
  }
  .noDrawCouponClass {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/229664/28/21729/57092/6698b308F3993937b/d6ea37e3ccc6edfb.png') no-repeat;
    background-size: 100%;
    width: 6.45rem;
    height: 5.91rem;
    position: relative;
    display: flex;
    justify-content: center;
    .btnClass {
      width: 3.37rem;
      height: 0.84rem;
      position: absolute;
      bottom: 0.3rem;
    }
  }
  .contentClass {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/36023/1/21444/11335/6698b38cF62b59c53/2d2fd01b6df6a8ce.png) no-repeat;
    background-size: 100%;
    width: 6.45rem;
    height: 5.91rem;
    padding-top: 0.88rem;
    display: flex;
    justify-content: center;
    .content {
      // width: 5.50rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      .titleClass {
        font-size: 0.38rem;
        color: #dab695;
      }
      .prizeImageClass5 {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .prizeImageClass {
        margin-top: 0.42rem;
        img {
          // width: 100%;
          height: 1.68rem;
        }
      }
      .redBagInsClass {
        margin-top: 0.68rem;
        font-size: 0.41rem;
        color: #dab695;
        text-align: center;
        .lookClass {
          font-size: 0.22rem;
          color: #dab695;
          margin-top:0.23rem;
        }
      }
      .prize3Class {
        display: flex;
        flex-direction: column;
        align-items: center;
        .prizeImage3 {
          width: 2.87rem;
          height: 2.83rem;
          background: #fff;
          border-radius: 0.08rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          padding: 0 0 0.22rem 0;
          margin-top: 0.1rem;
          img {
            height: 2rem;
          }
          .prizeNameClass {
            font-size: 0.32rem;
            color: #000000;
          }
        }
        .addressClass {
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/246449/9/15067/13439/6698b42aFba02943f/6ac50fbc7ac5a15a.png');
          background-repeat: no-repeat;
          background-size: 100%;
          width: 4.11rem;
          height: 0.83rem;
          margin-top: 0.28rem;
        }
        .addressGrayClass {
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/227327/27/24367/10753/6698b42aF17cf3626/4688e6c9fa9490bf.png');
          background-repeat: no-repeat;
          background-size: 100%;
          width: 4.11rem;
          height: 0.83rem;
          margin-top: 0.28rem;
          filter: grayscale(1);
        }
      }
    }
  }
  .closeClass {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/22151/22/20356/1774/66988ba1Fba0d4cc9/a2893f03a1e25920.png) no-repeat;
    background-size: 100%;
    width: 0.6rem;
    height: 0.6rem;
    margin-top: 0.32rem;
  }
}
</style>
