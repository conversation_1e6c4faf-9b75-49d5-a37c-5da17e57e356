<template>
    <div class="bg">
        <div class="contentClass">
            <div class="titleClass">
                   <div class="detailDiv">红包明细</div>
                   <div class="dateDiv">发放时间</div>
                </div>
            <div class="content" v-if="taskRedBagList.length > 0">
              <div class="itemClass" v-for="(item, index) in taskRedBagList" :key="index">
                <div class="detailValueClass">{{ taskType[item.taskType] }}<span>+0.1元</span></div>
                <div class="dateValueClass">{{ dayjs(item.time).format('YYYY-MM-DD') }}</div>
              </div>
            </div>
            <div v-else class="noPrizeClass">暂无数据</div>
        </div>
        <div class="closeClass" @click="closeClickPop"></div>
    </div>
</template>
<script lang="ts" setup>
import { showLoadingToast, closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { reactive } from 'vue';
import dayjs from 'dayjs';

interface RedBag {
  taskType: number;
  time: number;
}
const taskType = {
  2: '关注店铺红包',
  8: '购买商品红包',
  4: '逛逛超级品牌日会场红包',
  3: '逛逛精选新品红包',
  7: '加购品牌新品红包',
  13: '邀请好友入会红包',
  14: '每日签到任务红包',
};
const taskRedBagList = reactive([] as RedBag[]);
const emits = defineEmits(['closePop']);
const closeClickPop = () => {
  emits('closePop');
};
// 初始化数据
const initData = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/dzKSD/getHB');
    console.log(res, '任务红包记录');
    taskRedBagList.splice(0);
    taskRedBagList.push(...res.data);
    closeToast();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};
initData();
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style lang="scss" scoped>
.bg{
    display:flex;
    flex-direction: column;
    align-items: center;
   .contentClass{
     background: url(//img10.360buyimg.com/imgzone/jfs/t1/64668/24/26155/62077/6698ad2eF8b26cb10/b375a7ed86f53c23.png) no-repeat;
     background-size: 100%;
     width: 6.45rem;
     height: 6.92rem;
     padding-top: 0.8rem;
     display: flex;
     flex-direction: column;
     .titleClass{
        height: 0.60rem;
        line-height: 0.6rem;
        display: flex;
        align-items: center;
        color: #dab695;
        font-size: 0.26rem;
        margin-bottom: 0.2rem;
        .detailDiv{
            height: 100%;
            display: flex;
            align-items: center;
            // justify-content: center;
            margin-left: 0.72rem;
            flex: 2;
            }
        .dateDiv{
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1.5;
        }
    }
     .content{
        width: 100%;
        max-height: 4.68rem;
        overflow-y: scroll;
        color: #dab695;
        font-size: 0.22rem;
        .itemClass{
            display: flex;
            margin-bottom:0.08rem;
            .detailValueClass{
                display: flex;
                align-items: center;
                margin-left: 0.72rem;
                flex: 2;
                span{
                    color:#e04237;
                }
            }
            .dateValueClass{
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1.5;
            }
        }
     }
     .noPrizeClass{
      display: flex;
      width: 100%;
      justify-content: center;
      margin-top: 2rem;
      color: #dab695;
      font-size: 0.22rem;
     }
   }
   .closeClass{
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/22151/22/20356/1774/66988ba1Fba0d4cc9/a2893f03a1e25920.png) no-repeat;
    background-size: 100%;
    width: 0.6rem;
    height: 0.6rem;
    margin-top: 0.4rem;
   }
}
</style>
