<template>
    <div class="bg">
        <div v-if="helpType === 1" class="helpJoinVipClass">
            <div class="btnClass" @click="joinClick1()"></div>
        </div>
        <div v-else class="joinVipClass">
            <div class="btnClass" @click="joinClick()"></div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { inject } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  helpType: {
    type: Number,
    required: true,
  },
});
const emits = defineEmits(['closePop']);

// 加入会员
const joinClick = () => {
  console.log('加入会员');
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(window.location.href)}`;
};

const joinClick1 = () => {
  const hrefAll = `${window.location.href}&isHelp=true`;
  console.log(hrefAll, '助力者加入会员');
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(hrefAll)}`;
};
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style lang="scss" scoped>
.bg{
    display:flex;
    flex-direction: column;
    align-items: center;
    .helpJoinVipClass{
     background: url(//img10.360buyimg.com/imgzone/jfs/t1/230968/18/28759/40488/674d1765F3cffe225/9c7c68088b3bc583.png) no-repeat;
     background-size: 100%;
     width: 6.34rem;
     height: 7rem;
     display: flex;
     justify-content: center;
     position:relative;
     .btnClass{
        width: 3.82rem;
        height: 0.84rem;
        position: absolute;
        bottom:1.3rem;
     }
   }
   .joinVipClass{
     background: url(//img10.360buyimg.com/imgzone/jfs/t1/179238/5/55012/36754/674d16cbF8d9a6e7d/723ca2586d77b6a2.png) no-repeat;
     background-size: 100%;
     width: 6.34rem;
     height: 7rem;
     display: flex;
     justify-content: center;
     position:relative;
     .btnClass{
        width: 3.82rem;
        height: 0.84rem;
        position: absolute;
        bottom: 1.3rem;
     }
   }
   .closeClass{
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/22151/22/20356/1774/66988ba1Fba0d4cc9/a2893f03a1e25920.png) no-repeat;
    background-size: 100%;
    width: 0.6rem;
    height: 0.6rem;
    margin-top: 0.4rem;
   }
}
</style>
