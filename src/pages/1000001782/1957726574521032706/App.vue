<template>
  <div class="container">
    <!-- 头部区域 -->
    <div class="header">
      <div class="header-title">选海尔不再纠结，一键对比更省心</div>
      <div class="header-tip">立即开启智慧对比</div>
      <img class="header-btn" src="//img10.360buyimg.com/imgzone/jfs/t1/340528/27/2809/2061/68afcd44F493a94e0/d0b96585ccbf8b0f.png" alt="" @click="showOperationGuidePopup = true" />
    </div>
    <div class="category-selector-container" v-for="(params, indexParams) in filteredParamsList" :key="params.id">
      <div class="label special font-bold" v-if="indexParams === 1"></div>
      <div class="label font-bold" v-else>{{ params.parametersName }}</div>

      <!-- 产品分类级联选择器 -->
      <div class="category-selector" v-if="indexParams === 0">
        <div class="category-selector-item">
          <div class="category-selector-item-label font-27 font-bold">品类</div>
          <van-field v-model="fieldValue" is-link readonly @click="show = true" />
          <van-popup v-model:show="show" round position="bottom">
            <van-cascader v-model="selectedCategory" title="请选择品类" :options="categoryList" :field-names="{ text: 'categoryName', value: 'id', children: 'childList' }" @close="show = false" @finish="onFinish" />
          </van-popup>
        </div>
        <div class="title font-27 font-bold">基本信息</div>
        <div class="specifications-container">
          <!-- 固定区域 -->
          <div class="pinned-area">
            <div class="column-wrapper" v-for="(item, index) in pinnedItems" :key="'pinned-' + index">
              <div class="specifications pinned">
                <div class="specifications-group">
                  <div class="specifications-item" v-for="(it, indexs) in item.specificationsList" :key="indexs">{{ it }}</div>
                </div>
                <div class="sku-name font-27 font-bold">{{ item.skuName }}</div>
                <img class="sku-image" :src="item.imageUrl" alt="" @click="previewImage(item.imageUrl)" />
                <div class="sku-actions">
                  <img class="action-icon" :src="getPinIcon(true)" @click="unpinItem(index)" alt="取消固定" />
                  <img class="action-icon" :src="getDeleteIcon(canDelete)" @click="handleDeleteClick(index, true)" alt="删除" />
                </div>
                <div class="buy-btn" @click="gotoSkuPage(item.skuId)">立即购买</div>
              </div>
            </div>
          </div>

          <!-- 滚动区域 -->
          <div class="scrollable-area">
            <div class="column-wrapper" v-for="(item, index) in scrollableItems" :key="'scroll-' + index">
              <div class="specifications">
                <div class="specifications-group">
                  <div class="specifications-item" v-for="(it, indexs) in item.specificationsList" :key="indexs">{{ it }}</div>
                </div>
                <div class="sku-name font-27 font-bold">{{ item.skuName }}</div>
                <img class="sku-image" :src="item.imageUrl" ref="skuImageRef" alt="" @click="previewImage(item.imageUrl)" />
                <div class="sku-actions">
                  <img class="action-icon" :src="getPinIcon(false)" @click="pinToLeft(index)" alt="钉在左侧" />
                  <img class="action-icon" :src="getDeleteIcon(canDelete)" @click="handleDeleteClick(index, false)" alt="删除" />
                </div>
                <div class="buy-btn" @click="gotoSkuPage(item.skuId)">立即购买</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="category-selector control-panel" v-else-if="indexParams === 1" style="border-bottom: 1px solid #c5c6ca">
        <div class="control-switches font-27">
          <div class="switch-item">
            <van-switch v-model="hideSameItems" @change="handleHideSameItems" />
            <span class="switch-label">隐藏相同项</span>
          </div>
          <div class="switch-item">
            <van-switch v-model="highlightDifferences" @change="handleHighlightDifferences" />
            <span class="switch-label">高亮差异参数</span>
          </div>
        </div>
      </div>
      <div class="category-selector" :class="{ 'highlight-row': shouldHighlight(params.id) }" style="font-size: 0.22rem" v-else>
        <div class="specifications-container h-100">
          <!-- 固定区域 -->
          <div class="pinned-area">
            <div class="column-wrapper" v-for="(item, index) in pinnedItems" :key="'pinned-' + index">
              <div class="specifications pinned line-center" :class="{ 'highlight-cell': shouldHighlight(params.id) }" v-html="getParameterValue(item, params.id)"></div>
            </div>
          </div>
          <!-- 滚动区域 -->
          <div class="scrollable-area">
            <div class="column-wrapper" v-for="(item, index) in scrollableItems" :key="'scroll-' + index">
              <div class="specifications line-center" :class="{ 'highlight-cell': shouldHighlight(params.id) }" v-html="getParameterValue(item, params.id).replaceAll('\n', '<br>')"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="operation-switch"></div> -->
    <van-floating-bubble v-model:offset="offset" class="add-btn" axis="xy" icon="chat" magnetic="x" :gap="0" @click="handleAddButtonClick">
      <van-image src="//img10.360buyimg.com/imgzone/jfs/t1/330666/16/4061/4596/68ac2adaF031c6d3b/d7ea7be444a1a142.png" alt="" @click="handleAddButtonClick" />
    </van-floating-bubble>
  </div>
  <ParamsSelect :showPopup="showParamSelectPopup" @closePopup="showParamSelectPopup = false" @compareProducts="handleCompareProducts" :categoryId="skuParams.categoryId" :currentDataCount="currentDataCount" />
  <OperationGuide :showPopup="showOperationGuidePopup" @closePopup="showOperationGuidePopup = false" :rule="rule" />
</template>

<script setup lang="ts">
import { ref, inject, onMounted, nextTick, computed } from 'vue';
import type { Ref } from 'vue';
import { getCategoryList, getSkuInfoList, getSkuDetailList, getSkuDetailParameterList, getActivityRules, compareEvent } from './ajax/ajax';
import { gotoSkuPage } from '@/utils/platforms/jump';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { showToast, showImagePreview, FloatingBubble } from 'vant';
import 'vant/es/toast/style';
import 'vant/es/dialog/style';
import 'vant/es/notify/style';
import 'vant/es/image-preview/style';
import ParamsSelect from './compoents/ParamsSelect.vue';
import OperationGuide from './compoents/OperationGuide.vue';

// 类型定义
interface CategoryItem {
  id: string;
  categoryName: string;
  hasChild: boolean;
  childList?: CategoryItem[];
}

interface SkuItem {
  skuId: string;
  skuName: string;
  imageUrl: string;
  specificationsList: string[];
  parameterValueList: ParameterValue[];
}

interface ParameterValue {
  id: string;
  parametersValue: string;
}

interface ParamsItem {
  id: string;
  parametersName: string;
}

interface SkuParams {
  categoryId: string;
  skuInfoIds: string[];
}

interface ScrollListener {
  area: Element;
  listener: EventListener;
}

const pathParams = inject<any>('pathParams');

const categoryList: Ref<CategoryItem[]> = ref([]);
const selectedCategory: Ref<string> = ref('');
const fieldValue: Ref<string> = ref('冰箱');
const show: Ref<boolean> = ref(false);
const showParamSelectPopup: Ref<boolean> = ref(false);
const showOperationGuidePopup: Ref<boolean> = ref(false);
const skuInfoList: Ref<SkuItem[]> = ref([]);
const pinnedItems: Ref<SkuItem[]> = ref([]); // 固定在左侧的数据
const scrollableItems: Ref<SkuItem[]> = ref([]); // 可滚动的数据
const paramsList: Ref<ParamsItem[]> = ref([]); // 参数数据
const rule = ref<any>(); //操作指南
const offset = ref({ y: 500 });
const skuIds = ref();
const skuParams: Ref<SkuParams> = ref({
  categoryId: pathParams.categoryId ?? '1957618049676591106',
  skuInfoIds: [],
});
const skuImageRef = ref();
// 控制按钮状态
const hideSameItems = ref(false); // 隐藏相同项状态
const highlightDifferences = ref(false); // 高亮差异状态

// 获取类目数据
const getCategory = async (): Promise<void> => {
  const res: CategoryItem[] | null = await getCategoryList();
  if (res) {
    // 根据hasChild字段判断是否有下一级
    const processCategories = (categories: CategoryItem[]): CategoryItem[] => {
      return categories.map((category: CategoryItem) => {
        const processedCategory = { ...category };
        // 如果hasChild为false或不存在，则移除childList
        if (!category.hasChild) {
          delete processedCategory.childList;
        } else if (category.childList && Array.isArray(category.childList)) {
          // 递归处理子分类
          processedCategory.childList = processCategories(category.childList);
        }
        return processedCategory;
      });
    };

    let processedCategories = processCategories(res);

    // 如果pathParams.categoryId存在，过滤categoryList只显示对应的品类
    if (pathParams.categoryId) {
      const filterCategoriesByPath = (categories: CategoryItem[], targetId: string): CategoryItem[] => {
        const result: CategoryItem[] = [];

        for (const category of categories) {
          if (category.id === targetId) {
            // 找到目标品类，直接返回
            result.push(category);
          } else if (category.childList && Array.isArray(category.childList)) {
            // 递归查找子分类
            const filteredChildren = filterCategoriesByPath(category.childList, targetId);
            if (filteredChildren.length > 0) {
              // 如果子分类中找到了目标，保留当前分类并更新子分类
              result.push({
                ...category,
                childList: filteredChildren,
              });
            }
          }
        }

        return result;
      };

      processedCategories = filterCategoriesByPath(processedCategories, pathParams.categoryId);

      // 设置默认选中的品类和显示值
      const findCategoryName = (categories: CategoryItem[], targetId: string): string => {
        for (const category of categories) {
          if (category.id === targetId) {
            return category.categoryName;
          }
          if (category.childList && Array.isArray(category.childList)) {
            const childName = findCategoryName(category.childList, targetId);
            if (childName) {
              return childName;
            }
          }
        }
        return '';
      };

      const categoryName = findCategoryName(res, pathParams.categoryId);

      if (categoryName) {
        selectedCategory.value = pathParams.categoryId;
        fieldValue.value = categoryName;
      }

      // 检查当前选中的类目是否有二级类目，如果有则默认选中第一个二级类目
      const findCategoryById = (categories: CategoryItem[], targetId: string): CategoryItem | null => {
        for (const category of categories) {
          if (category.id === targetId) {
            return category;
          }
          if (category.childList && Array.isArray(category.childList)) {
            const found = findCategoryById(category.childList, targetId);
            if (found) {
              return found;
            }
          }
        }
        return null;
      };
      const currentCategory = findCategoryById(res, pathParams.categoryId);
      if (currentCategory) {
        if (currentCategory.childList && currentCategory.childList.length > 0) {
          // 如果当前类目有二级类目，默认选中第一个二级类目
          const firstSubCategory = currentCategory.childList[0];
          skuParams.value.categoryId = firstSubCategory.id;
          selectedCategory.value = `${currentCategory.categoryName}/${firstSubCategory.categoryName}`;
          fieldValue.value = `${currentCategory.categoryName}/${firstSubCategory.categoryName}`;
          // 埋点上报，value为二级类目名称
          lzReportClick({
            code: '产业',
            value: firstSubCategory.categoryName,
          });
        } else {
          // 如果没有二级类目，使用当前类目
          skuParams.value.categoryId = pathParams.categoryId;
          // 埋点上报，value为一级类目名称
          lzReportClick({
            code: '产业',
            value: currentCategory.categoryName,
          });
        }
      }
    }
    categoryList.value = processedCategories;
  }
  // 在getCategory执行完成后调用getSkuInfo，确保skuParams.value的准确性
  await getSkuInfo(skuParams.value);
  await getSkuParameterList(skuParams.value.categoryId);
};

const onFinish = ({ selectedOptions }: { selectedOptions: CategoryItem[] }): void => {
  fieldValue.value = selectedOptions.map((option: CategoryItem) => option.categoryName).join('/');
  show.value = false;
  // 清空固定区域的商品
  pinnedItems.value = [];
  skuParams.value = {
    categoryId: selectedCategory.value,
    skuInfoIds: [],
  };

  // 埋点上报：根据选择的层级决定埋点内容
  if (selectedOptions.length > 1) {
    // 如果有二级类目，埋二级类目
    lzReportClick({
      code: '产业',
      value: selectedOptions[selectedOptions.length - 1].categoryName,
    });
  } else if (selectedOptions.length === 1) {
    // 如果只有一级类目，埋一级类目
    lzReportClick({
      code: '产业',
      value: selectedOptions[0].categoryName,
    });
  }

  getSkuParameterList(selectedCategory.value);
  getSkuInfo(skuParams.value);
  // 重新设置滚动同步
  setupSyncScroll();
};
// 获取商品数据
const getSkuInfo = async (params: SkuParams, shouldAppend: boolean = false): Promise<void> => {
  const res: SkuItem[] | null = await getSkuDetailList(params);
  if (res) {
    if (shouldAppend) {
      // 追加新数据到现有数据中
      skuInfoList.value = [...skuInfoList.value, ...res];
      scrollableItems.value = [...scrollableItems.value, ...res];
    } else {
      // 替换所有数据（初始化时使用）
      skuInfoList.value = res;
      scrollableItems.value = [...res];
    }
    skuIds.value = skuInfoList.value.map((item: any) => item.id);
    // 重新设置滚动同步
    setupSyncScroll();
    getFixedTop();
  }
};

// 处理添加按钮点击事件
const handleAddButtonClick = (): void => {
  const totalItems = pinnedItems.value.length + scrollableItems.value.length;
  if (totalItems >= 4) {
    showToast('最多只能对比4个商品哦～');
    return;
  }
  showParamSelectPopup.value = true;
};

// 处理商品对比事件
const handleCompareProducts = (selectedProductIds: string[]): void => {
  // 获取当前已有商品的skuId列表
  const currentSkuIds = skuInfoList.value.map((item: SkuItem) => item.id);

  // 去重：过滤掉已经存在的商品ID
  const uniqueSelectedIds = selectedProductIds.filter((id) => !currentSkuIds.includes(id));

  // 如果去重后没有新商品，直接关闭弹窗并返回
  if (uniqueSelectedIds.length === 0) {
    showParamSelectPopup.value = false;
    showToast('所选商品已在对比列表中');
    return;
  }
  // 更新skuParams中的skuInfoIds
  skuParams.value.skuInfoIds = [...skuParams.value.skuInfoIds, ...selectedProductIds];
  // 调用getSkuInfo获取商品详情，使用追加模式
  getSkuInfo(
    {
      categoryId: skuParams.value.categoryId,
      skuInfoIds: selectedProductIds,
    },
    true,
  ).then(() =>
    compareEvent({
      categoryId: skuParams.value.categoryId,
      skuIds: skuIds.value,
    }),
  );
  // 关闭弹窗
  showParamSelectPopup.value = false;
  // 显示提示信息
  showToast('可左右滑动查看商品进行信息对比');
};
// 根据参数id获取对应的参数值
const getParameterValue = (item: SkuItem, parameterId: string): string => {
  const parameter = item.parameterValueList.find((param: ParameterValue) => param.id === parameterId);
  if (parameter) {
    return parameter.parametersValue;
  }

  return '-';
};
// 获取对比参数
const getSkuParameterList = async (categoryId: string): Promise<void> => {
  const res: ParamsItem[] | null = await getSkuDetailParameterList(categoryId);
  if (res) {
    paramsList.value = res; // 添加规格参数（放在第一位）
    paramsList.value.unshift({
      parametersName: '规格',
      id: '111',
    });
    paramsList.value.splice(1, 0, {
      id: '222',
      parametersName: '',
    });
    // 重新设置滚动同步
    nextTick(() => {
      setupSyncScroll();
    });
  }
};

// 计算是否可以删除
const canDelete = computed(() => {
  const totalItems = pinnedItems.value.length + scrollableItems.value.length;
  return totalItems > 2;
});

// 计算当前比对数据数量
const currentDataCount = computed(() => {
  return pinnedItems.value.length + scrollableItems.value.length;
});

// 获取删除按钮图标
const getDeleteIcon = (canDelete: boolean): string => {
  return canDelete ? '//img10.360buyimg.com/imgzone/jfs/t1/324598/13/11195/2055/68ad1d4dF5d4f1efa/9090d94d59099a97.png' : '//img10.360buyimg.com/imgzone/jfs/t1/337232/13/1769/2214/68ad1d4dF0ba1cda1/8f5651ac47573068.png';
};

// 获取固定按钮图标
const getPinIcon = (isPinned: boolean): string => {
  return isPinned ? '//img10.360buyimg.com/imgzone/jfs/t1/332330/25/2411/2345/68a7d979Fbf9f2879/33e5f8fcdd016710.png' : '//img10.360buyimg.com/imgzone/jfs/t1/294093/22/22916/2359/68a7d979Fc3318f61/dfe151efcf2527a5.png';
};

// 处理删除点击
const handleDeleteClick = (index: number, isPinned: boolean): void => {
  if (!canDelete.value) {
    showToast('至少要保留两条对比数据');
    return;
  }
  deleteItem(index, isPinned);
};

// 预览商品图片
const previewImage = (imageUrl: string): void => {
  showImagePreview({
    images: [imageUrl],
    startPosition: 0,
    closeable: true,
  });
};
// 钉在左侧功能 - 整列钉住
const pinToLeft = (index: number, isPinned: boolean = false): void => {
  if (isPinned) {
    // 如果是固定区域的数据，不做处理
    return;
  }

  // 检查固定区域是否已达到最大数量限制（最多3个固定，保证至少1个可滚动）
  if (pinnedItems.value.length >= 1) {
    showToast('最多只能固定1个商品哦～');
    return;
  }

  // 从滚动区域移除并添加到固定区域
  const item = scrollableItems.value.splice(index, 1)[0];
  pinnedItems.value.push(item);

  // 重新设置滚动同步
  nextTick(() => {
    setupSyncScroll();
  });
};

// 删除功能
const deleteItem = (index: number, isPinned: boolean = false): void => {
  let deletedItem;
  if (isPinned) {
    deletedItem = pinnedItems.value.splice(index, 1)[0];
  } else {
    deletedItem = scrollableItems.value.splice(index, 1)[0];
  }
  
  // 从skuInfoList中移除商品
  const skuIdToRemove = deletedItem.id;
  skuInfoList.value = skuInfoList.value.filter(item => item.id !== skuIdToRemove);
  
  // 从skuParams.skuInfoIds中移除商品ID
  skuParams.value.skuInfoIds = skuParams.value.skuInfoIds.filter(id => id !== skuIdToRemove);
};

// 取消固定功能
const unpinItem = (index: number): void => {
  const item = pinnedItems.value.splice(index, 1)[0];
  scrollableItems.value.unshift(item);

  // 重新设置滚动同步
  nextTick(() => {
    setupSyncScroll();
  });
};

// 存储滚动事件监听器的引用
// let scrollListeners: ScrollListener[] = [];

// 获取第一行高度（为了获取开关的定位高度）
const getFirstLabelHeight = (): number => {
  const firstLabel = document.querySelector('.category-selector-container .label') as HTMLElement;
  if (firstLabel) {
    console.log('firstLabel.offsetHeight', firstLabel.offsetHeight);
    return firstLabel.offsetHeight;
  }
  return 0;
};

// 添加响应式状态来触发重新计算
const domReady = ref(false);

// 动态计算第三个容器的固定定位top值
const getFixedTop = () => {
  // 依赖domReady来触发重新计算
  domReady.value;

  // 获取头部和前两个容器的高度
  const headerElement = document.querySelector('.header') as HTMLElement;
  const firstRowElement = document.querySelector('.category-selector-container:nth-child(1)') as HTMLElement;
  const secondRowElement = document.querySelector('.category-selector-container:nth-child(2)') as HTMLElement;

  let totalHeight = 0;

  // 计算头部高度
  if (headerElement) {
    totalHeight += headerElement.offsetHeight;
  }

  // 计算第一个容器高度
  if (firstRowElement) {
    totalHeight += firstRowElement.offsetHeight;
  }

  // 计算第二个容器高度（关键：这个高度不固定）
  if (secondRowElement) {
    totalHeight += secondRowElement.offsetHeight;
  }

  // 如果无法获取DOM元素，使用默认值
  if (totalHeight === 0) {
    totalHeight = 80; // 默认高度
  }

  console.log('计算第三个容器固定定位高度:', totalHeight, 'px');

  return `${totalHeight}px`;
};
let scrollListeners: { area: HTMLElement; listener: (e: Event) => void }[] = [];
let startX = 0;
let startScrollLefts: number[] = [];

const setupSyncScroll = (): void => {
  nextTick(() => {
    // 清除之前的监听器
    scrollListeners.forEach(({ area, listener }) => {
      area.removeEventListener('touchstart', listener);
      area.removeEventListener('touchmove', listener);
      area.removeEventListener('touchend', listener);
    });
    scrollListeners = [];

    const scrollableAreas = document.querySelectorAll<HTMLElement>('.scrollable-area');

    scrollableAreas.forEach((area, index) => {
      let active = false;

      let startX = 0;
      let startY = 0;
      let isHorizontal = false;

      const touchStart = (e: TouchEvent): void => {
        startX = e.touches[0].pageX;
        startY = e.touches[0].pageY;
        isHorizontal = false;
        startScrollLefts = Array.from(scrollableAreas).map((a) => a.scrollLeft);
      };

      const touchMove = (e: TouchEvent): void => {
        const dx = e.touches[0].pageX - startX;
        const dy = e.touches[0].pageY - startY;

        // 判断方向：横向滑动才阻止默认
        if (!isHorizontal) {
          if (Math.abs(dx) > Math.abs(dy)) {
            isHorizontal = true;
          } else {
            return; // 纵向滑动，不处理
          }
        }

        // 横向时，同步所有 scrollLeft
        scrollableAreas.forEach((a, i) => {
          a.scrollLeft = startScrollLefts[i] - dx;
        });

        e.preventDefault(); // ✅ 仅横向时阻止默认，纵向滚动仍然有效
      };

      const touchEnd = (): void => {
        active = false;
      };

      area.addEventListener('touchstart', touchStart as EventListener, { passive: true });
      area.addEventListener('touchmove', touchMove as EventListener, { passive: false });
      area.addEventListener('touchend', touchEnd as EventListener, { passive: true });

      scrollListeners.push({ area, listener: touchStart as EventListener });
      scrollListeners.push({ area, listener: touchMove as EventListener });
      scrollListeners.push({ area, listener: touchEnd as EventListener });
    });
  });
};

// 过滤后的参数列表（隐藏相同项功能）
const filteredParamsList = computed(() => {
  if (!hideSameItems.value) {
    return paramsList.value;
  }

  return paramsList.value.filter((param, index) => {
    // 前两项（规格和控制面板）始终显示
    if (index <= 1) {
      return true;
    }

    // 获取所有商品的该参数值
    const allItems = [...pinnedItems.value, ...scrollableItems.value];
    if (allItems.length <= 1) {
      return true; // 如果只有一个或没有商品，显示所有参数
    }

    const values = allItems.map((item) => getParameterValue(item, param.id));
    const uniqueValues = [...new Set(values)];

    // 如果所有值都相同（只有一个唯一值），则隐藏该参数
    return uniqueValues.length > 1;
  });
});

// 处理隐藏相同项按钮点击
const handleHideSameItems = (): void => {
  // 保存当前滚动位置
  const scrollableAreas = document.querySelectorAll('.scrollable-area');
  const currentScrollLeft = scrollableAreas.length > 0 ? (scrollableAreas[0] as HTMLElement).scrollLeft : 0;

  // 当隐藏相同项状态改变时，需要重新设置滚动同步
  nextTick(() => {
    setupSyncScroll();
    // 恢复滚动位置到所有滚动区域
    const newScrollableAreas = document.querySelectorAll('.scrollable-area');
    newScrollableAreas.forEach((area) => {
      (area as HTMLElement).scrollLeft = currentScrollLeft;
    });
  });
};

// 获取有差异的参数ID集合
const differenceParamsSet = computed(() => {
  const allItems = [...pinnedItems.value, ...scrollableItems.value];
  if (allItems.length <= 1) {
    return new Set(); // 如果只有一个或没有商品，没有差异
  }

  const differenceIds = new Set();
  paramsList.value.forEach((param, index) => {
    // 跳过前两项（规格和控制面板）
    if (index <= 1) return;

    const values = allItems.map((item) => getParameterValue(item, param.id));
    const uniqueValues = [...new Set(values)];

    // 如果有多个不同的值，说明存在差异
    if (uniqueValues.length > 1) {
      differenceIds.add(param.id);
    }
  });

  return differenceIds;
});

// 检查某个参数是否应该高亮
const shouldHighlight = (paramId: string): boolean => {
  return highlightDifferences.value && differenceParamsSet.value.has(paramId);
};

// 处理高亮差异按钮点击
const handleHighlightDifferences = (): void => {};
// 获取操作指南
const getActivityBase = async () => {
  rule.value = await getActivityRules();
};

onMounted(() => {
  getCategory();
  setupSyncScroll();
  getFirstLabelHeight();
  getActivityBase();
  // 设置DOM准备完成状态
  nextTick(() => {
    domReady.value = true;
  });

  const params = {
    onClickBtnBack: () => {
      if (showParamSelectPopup.value) {
        showParamSelectPopup.value = false;
        return;
      }
      window.jmfe.closeWebview();
    },
    controlType: '2',
  };
  window.jmfe.backControlRouter(params).then((res: any) => {
    console.log('🚀 ~ window.jmfe.backControlRouter ~ res:', res);
  });
});
</script>
<style scoped lang="scss">
.container {
  width: 7.5rem;
  background-color: #ffffff;
  min-height: 100vh;
  position: relative;
  .add-btn {
    width: 0.5rem;
    height: auto;
  }

  .header {
    width: 7.5rem;
    height: 1.7rem;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/325584/14/10654/5531/68ac21f2Fa65aed1d/552527a991645283.jpg') no-repeat;
    background-size: 100%;
    padding-top: 0.38rem;
    position: sticky;
    top: 0rem;
    z-index: 2;

    .header-title {
      width: 100%;
      text-align: center;
      font-size: 0.34rem;
      color: #ffffff;
      font-weight: bold;
    }

    .header-tip {
      width: 100%;
      text-align: center;
      font-size: 0.3rem;
      color: #c1d7ff;
      margin-top: 0.1rem;
    }
    .header-btn {
      width: 0.4rem;
      position: absolute;
      top: 0.2rem;
      right: 0;
    }
  }
  .category-selector-container {
    width: 100%;
    display: flex;
    .label {
      width: 1.1rem;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      background-color: #eff1f8;
      border-bottom: 1px solid #c5c6ca;
      // border-right: 1px solid #c5c6ca;
      text-wrap: balance;
      font-size: 0.22rem;
    }
    .special {
      height: 1rem;
      border-right: none;
      background-color: #ffffff;
    }

    .category-selector {
      width: calc(100% - 1.1rem);
      background: white;

      .category-selector-item {
        display: flex;
        padding: 0.3rem 0.3rem 0 0.3rem;
        .category-selector-item-label {
          width: 1.1rem;
          height: 100%;
          line-height: 0.5rem;
          color: #222222;
        }
        :deep(.van-cascader) {
          --van-cascader-header-height: 1rem;
          --van-cascader-option-font-size: 0.28rem;
        }
        :deep(.van-field__control) {
          padding-left: 0.1rem;
          font-size: 0.27rem;
          color: #949494;
        }

        :deep(.van-cell) {
          padding: 0;
          height: 0.5rem;
          background-color: #eff1f8;
          align-items: center;
          border-radius: 0.1rem;
          padding-right: 0.1rem;
        }
      }
      .title {
        margin-top: 0.2rem;
        padding-left: 0.3rem;
      }
      .h-100 {
        height: 100%;
      }
      .specifications-container {
        width: 100%;
        display: flex;

        .pinned-area {
          display: flex;
          overflow-x: auto; /* 允许横向滚动 */
          overflow-y: hidden; /* 避免纵向干扰 */
          -webkit-overflow-scrolling: auto; /* 关闭 iOS 橡皮筋 */
          overscroll-behavior-x: contain; /* 禁止继续传播横向回弹 */
          -ms-overflow-style: none; /* 隐藏 IE/Edge 滚动条 */
          scrollbar-width: none; /* 隐藏 Firefox 滚动条 */
          flex-shrink: 0;
          background-color: #f7f8ff;
          // border-right: 1px solid #e6f0ff;
          min-width: 0;

          &::-webkit-scrollbar {
            height: 4px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
          }

          &::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb:hover {
            background: #555;
          }
        }
        .pinned-area:-webkit-scrollbar {
          display: none; /* 隐藏 WebKit 滚动条 */
        }
        .scrollable-area:-webkit-scrollbar {
          display: none; /* 隐藏 WebKit 滚动条 */
        }
        .scrollable-area {
          display: flex;
          overflow-x: auto; /* 允许横向滚动 */
          overflow-y: hidden; /* 避免纵向干扰 */
          -webkit-overflow-scrolling: auto; /* 关闭 iOS 橡皮筋 */
          overscroll-behavior-x: contain; /* 禁止继续传播横向回弹 */
          -ms-overflow-style: none; /* 隐藏 IE/Edge 滚动条 */
          scrollbar-width: none; /* 隐藏 Firefox 滚动条 */
          flex: 1;
          background-color: #ffffff;
          min-width: 0;

          &::-webkit-scrollbar {
            height: 4px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
          }
        }

        .specifications {
          width: 3.2rem;
          min-width: 3.2rem;
          min-height: 1rem;
          padding-bottom: 0.3rem;
          padding-top: 0.3rem;
          border-right: 1px solid #c5c6ca;
          border-bottom: 1px solid #c5c6ca;
          flex-shrink: 0;
          text-align: center;
          height: 100%;
          &.pinned {
            // background-color: #e6f0ff;
            // border-right: none;
            position: relative;
          }
          .specifications-group {
            width: 100%;
            // height: 1rem;
            padding-left: 0.15rem;
            display: flex;
            flex-wrap: wrap;
            .specifications-item {
              width: 0.9rem;
              height: 0.4rem;
              background-color: #f2f5fa;
              border-radius: 0.05rem;
              font-size: 0.18rem;
              line-height: 0.4rem;
              text-align: center;
              color: #222222;
              margin-right: 0.1rem;
              margin: 0 0.1rem 0.1rem 0;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          .sku-name {
            width: 100%;
            height: 0.6rem;
            padding: 0 0.1rem;
            text-align: left;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 0.22rem;
          }
          .sku-image {
            max-width: 1.5rem;
            max-height: 1.5rem;
            margin: 0.1rem auto 0;
            object-fit: contain;
            cursor: pointer;
          }
          .sku-actions {
            display: flex;
            justify-content: center;
            gap: 0.1rem;
            margin-top: 0.1rem;
            .action-icon {
              width: 1.2rem;
              // height: 0.42rem;
              cursor: pointer;
              transition: all 0.2s;
            }
          }
          .buy-btn {
            width: 2.46rem;
            height: 0.6rem;
            background-color: #005aff;
            border-radius: 0.3rem;
            text-align: center;
            line-height: 0.6rem;
            font-size: 0.25rem;
            color: #ffffff;
            margin: 0.3rem auto 0;
          }
        }
        .line-center {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .param-value-container {
          width: calc(50vw - 0.55rem);
          min-width: calc(50vw - 0.55rem);
          padding-bottom: 0.3rem;
          margin-top: 0.3rem;
          border-left: 1px solid #f4f4f5;
          border-bottom: 1px solid #f4f4f5;
          flex-shrink: 0;
        }
      }
    }
  }
  .category-selector-container:nth-child(2) {
    position: sticky;
    top: 1.7rem;
    z-index: 2;
  }
  .category-selector-container:nth-child(3) {
    position: fixed;
    top: v-bind(getFixedTop);
    left: 0;
    width: 100%;
    z-index: 1;
  }
  .category-selector-container:nth-child(4) {
    margin-top: 1rem;
  }
  .control-switches {
    display: flex;
    gap: 0.6rem;
    background-color: #fff;
    align-items: center;
    height: 100%;
    // position: sticky;
    // top: 1.5rem;

    .switch-item {
      display: flex;
      align-items: center;
      gap: 0.2rem;

      .switch-label {
        font-size: 0.26rem;
        color: #333;
        white-space: nowrap;
      }

      :deep(.van-switch) {
        width: 1rem;
        height: 0.4rem;

        .van-switch__node {
          width: 0.28rem;
          height: 0.28rem;
          top: 0.06rem;
          left: 0.06rem;
        }

        &.van-switch--on .van-switch__node {
          transform: translateX(0.6rem);
        }
      }
    }
  }

  .highlight-cell {
    color: #005aff;
  }
  .font-bold {
    font-weight: bold;
  }
  // .operation-switch {
  //   width: 100%;
  //   height: 1rem;
  //   position: absolute;
  //   top: 9.3rem;
  // }
}
</style>
<style>
.font-27 {
  font-size: 0.27rem;
}
html,
body {
  height: 100%;
  overflow-x: hidden;
  overscroll-behavior-x: none;
  overscroll-behavior-y: none;
  -webkit-overflow-scrolling: auto;
}
* {
  box-sizing: border-box;
}
::-webkit-scrollbar {
  display: none;
}
.van-floating-bubble {
  width: 0.5rem;
  height: 2.5rem;
  border-radius: 0.5rem !important;
  /* top: -4rem; */
  z-index: 2;
  background: none;
}
.van-image-preview__image {
  width: 50%;
}
</style>
