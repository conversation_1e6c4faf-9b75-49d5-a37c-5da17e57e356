<template>
    <div class="prizeAllClass">
        <div v-if="prizeList.length >= 3" ref="swiperRef" class="swiper-container">
            <div class="swiper-wrapper">
                <div class="swiper-slide" v-for="(item, index) in prizeList" :key="index">
                    <div class="prizeClass">
                      <img :src="item.prizeImg" alt="" />
                      <div class="prizeNameClass">{{item.prizeName}}</div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else class="swiper-container1">
            <div class="swiper-wrapper">
                <div class="swiper-slide1" v-for="(item, index) in prizeList" :key="index">
                    <div class="prizeClass">
                      <img :src="item.prizeImg" alt="" />
                      <div class="prizeNameClass">{{item.prizeName}}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper-bundle.css'; // 引入Swiper样式
import { configData } from '../common';

const moduleName = 'getPrizes';
const { list } = configData.value[moduleName];
const prizeList = list;
Swiper.use([Autoplay]); // 使用Swiper的扩展模块
/* eslint-disable no-new */
/* ts-plugin */
const swiperRef = ref(null);
onMounted(() => {
  nextTick(() => {
    new Swiper(swiperRef.value, {
      // Swiper配置
      slidesPerView: 3, // 同时显示的slide数量
      spaceBetween: 18, // slide之间的间距
      loop: true, // 是否循环播放
      autoplay: {
        delay: 2000, // 自动播放的间隔
      },
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
      },
      pagination: {
        el: '.swiper-pagination',
        clickable: true, // 分页器是否可点击
      },
    });
  });
});

</script>

<style lang="scss" scoped>
 .prizeAllClass {
      margin-top: 0.5rem;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      .swiper-container {
        width: 5.87rem;
        height: 100%;
        overflow: hidden;
      }

      .swiper-wrapper {
        width: 5.87rem;
      }

      .swiper-slide {
        text-align: center;
        font-size: 18px;
        background: linear-gradient(to bottom,#bdd2fd,#89a4f0);
        display: flex;
        justify-content: center;
        height: 1.88rem;
        width: 1.71rem;
        border-radius: 0.16rem;
        .prizeClass{
          display: flex;
          flex-direction: column;
          position: relative;
          width: 100%;
          align-items: center;
          overflow: hidden;
          img {
            height: 1.5rem;
            width: 1.5rem;
            object-fit: cover;
          }

          .prizeNameClass{
            margin-top: 0.05rem;
            font-size: 0.16rem;
            font-weight: bold;
            color: #000000;
            width: 100%;
            align-items: center;
          }
        }
      }
      .swiper-container1{
        width: 5.87rem;
        height: 100%;
        overflow: hidden;
        .swiper-slide1{
         text-align: center;
        font-size: 18px;
        background: #fff;
        display: flex;
        justify-content: center;
        // align-items: center;
        height: 1.88rem;
        width: 1.71rem;
        border-radius: 0.16rem;
        margin:0 0.12rem;
        .prizeClass{
            display: flex;
            flex-direction: column;
            position: relative;
            width: 100%;
            align-items: center;
            overflow: hidden;
            img {
              height: 1.5rem;
              width: 1.5rem;
              object-fit: cover;
            }

            .prizeNameClass{
              margin-top: 0.05rem;
              font-size: 0.16rem;
              font-weight: bold;
              color: #000000;
              width: 100%;
              align-items: center;
            }
          }
        }
      }
    }
    </style>
