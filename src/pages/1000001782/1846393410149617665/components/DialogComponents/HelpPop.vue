<template>
    <div class="bg">
        <div v-if="helpType === 3" class="helpHasVipClass">
            <div class="btnClass" @click="joinAcClick()"></div>
        </div>
        <div v-if="helpType === 2" class="helpSuccessClass">
            <div class="btnClass" @click="joinAcClick()"></div>
        </div>
        <div class="closeClass" @click="joinAcClick()"></div>
    </div>
</template>
<script lang="ts" setup>

const props = defineProps({
  helpType: {
    type: Number,
    required: true,
  },
});
const emits = defineEmits(['closePop']);

const closeClickPop = () => {
  emits('closePop');
};
// 继续参与活动
const joinAcClick = () => {
  // window.location.href = `${encodeURIComponent(window.location.href)}`;
  closeClickPop();
};
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style lang="scss" scoped>
.bg{
    display:flex;
    flex-direction: column;
    align-items: center;
   .helpHasVipClass{
     background: url(//img10.360buyimg.com/imgzone/jfs/t1/257060/21/2847/123149/676a5eb9F4f4b4ec2/9eee515fa9350ed1.png) no-repeat;
     background-size: 100%;
     width: 6.46rem;
     height: 7.09rem;
     display: flex;
     justify-content: center;
     position:relative;
     .textClass{
      max-width: 5.60rem;
      text-align: center;
      font-size: 0.36rem;
      color: #dab695;
     }
     .btnClass{
        width: 4.46rem;
        height: 0.84rem;
        position: absolute;
        bottom: 0.46rem;
     }
   }
   .helpSuccessClass{
     background: url(//img10.360buyimg.com/imgzone/jfs/t1/265070/18/2490/119756/676a5ebdFfcd8ad98/78212705780a7ced.png) no-repeat;
     background-size: 100%;
     width: 6.46rem;
     height: 7.09rem;
     display: flex;
     justify-content: center;
     position:relative;
     .btnClass{
        width: 4.8rem;
       height: 0.84rem;
       position: absolute;
       bottom: 0.7rem;
     }
   }
   .closeClass{
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/22151/22/20356/1774/66988ba1Fba0d4cc9/a2893f03a1e25920.png) no-repeat;
    background-size: 100%;
    width: 0.6rem;
    height: 0.6rem;
    margin-top: 0.4rem;
   }
}
</style>
