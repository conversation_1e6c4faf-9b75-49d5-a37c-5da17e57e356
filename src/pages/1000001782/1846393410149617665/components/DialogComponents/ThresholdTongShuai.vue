<template>
  <VanPopup v-model:show="limitPopup" :closeOnClickOverlay="false" position="center">
    <div class="bg">
        <div class="joinVipClass">
            <div class="btnClass" @click="joinClick()"></div>
        </div>
    </div>
  </VanPopup>
</template>
<script setup lang="ts">
import { defineEmits, defineProps, inject, ref, watch } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo: any = inject('baseInfo') as BaseInfo;
const props = defineProps(['data', 'show']);
const emits = defineEmits(['update:show']);

const limitPopup = ref(props.show);

// 立即入会
const joinClick = () => {
  console.log('立即入会', baseInfo.openCardLink);
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(window.location.href)}`;
};

watch(
  () => props.show,
  (val) => {
    limitPopup.value = val;
  },
);
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style lang="scss" scoped>
.bg{
    display:flex;
    flex-direction: column;
    align-items: center;
   .joinVipClass{
     background: url(//img10.360buyimg.com/imgzone/jfs/t1/259786/28/2450/111496/676a5eb6F0160f399/997bc1a000a3d3bf.png) no-repeat;
     background-size: 100%;
     width: 6.45rem;
     height: 6.79rem;
     padding-top: 1.24rem;
     display: flex;
     justify-content: center;
     position:relative;
     .btnClass{
        width: 3.82rem;
        height: 0.84rem;
        position: absolute;
        bottom: 0.7rem;
     }
   }
   .closeClass{
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/135478/33/38952/859/6603b785Fcf72b014/e0eb66bc3fda8f45.png) no-repeat;
    background-size: 100%;
    width: 0.6rem;
    height: 0.6rem;
    margin-top: 0.4rem;
   }
}
</style>
