<template>
    <div class="bg">
        <div class="contentClass">
          <div class="redBagClass">+{{ parseFloat(redBagCount * 0.1).toFixed(1) }}<span>元</span></div>
        </div>
    </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  redBagCount: {
    type: Number,
    required: true,
  },
});
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style lang="scss" scoped>
.bg{
    display:flex;
    flex-direction: column;
    align-items: center;
   .contentClass{
     background: url(//img10.360buyimg.com/imgzone/jfs/t1/247262/36/6196/33712/6603eb47F6fa080a3/1575eebfc5155198.png) no-repeat;
     background-size: 100%;
     width: 4.93rem;
     height: 6.13rem;
     display: flex;
     justify-content: center;
      .redBagClass{
        position: absolute;
        top: 3.77rem;
        color: #fff;
        font-size: 0.40rem;
        span{
          font-size: 0.3rem;
        }
      }
   }
}
</style>
