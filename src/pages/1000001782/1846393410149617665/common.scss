@use "sass:math";

@font-face {
  font-weight: normal;
  font-family: "FZPSXIHJW--GB1-0";
  font-style: normal;
  //src: url(./public/assets/font/FZPSXIHJW.TTF) format("ttf");
  font-display: swap;
}

@font-face {
  font-weight: normal;
  font-family: "FZPSHJW--GB1-0";
  font-style: normal;
  //src: url(./public/assets/font/FZPSHJW.TTF) format("ttf");
  font-display: swap;
}

@function px-to-rem($pixels) {
  @return math.div($pixels, 200) + rem;
}

.no-scrollbar {
  &::-webkit-scrollbar {
    display: none;
  }
}

.text-hide-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.popup-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 6.5rem;
  height: 8rem;
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
  border-radius: 0.2rem;
}

.close-popup-btn {
  position: absolute;
  bottom: -0.95rem;
  left: 50%;
  width: 0.6rem;
  height: 0.6rem;
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/247369/40/2382/2087/659a0f32F4f397f8f/0a31f78638473080.png);
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
  transform: translateX(-50%);
}
