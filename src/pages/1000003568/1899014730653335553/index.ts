import { createApp } from 'vue';
import { initRem } from '@/utils/client';
import root from './App.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';
import { init } from '@/utils';
import EventTrackPlugin from '@/plugins/EventTracking';
import { httpRequest } from '@/utils/service';

initRem();

const app = createApp(root);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: true,
  disableNotice: false,
  disableShare: true,
};

const getDecoData = async () => {
  try {
    const { data } = await httpRequest.post('common/getActivityConfig');
    app.provide('decoData', JSON.parse(data));
  } catch (error: any) {
    console.error(error);
  }
};
init(config).then(async ({ baseInfo, pathParams, userInfo }) => {
  await getDecoData();
  app.provide('userInfo', userInfo);
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, {});
  app.mount('#app');
});
