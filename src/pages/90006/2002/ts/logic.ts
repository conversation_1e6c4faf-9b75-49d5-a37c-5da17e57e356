import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref } from 'vue';
import '@/components/Threshold2/CPBStyle.scss';

// 领取攻略弹窗
export const strategyPopup = ref(false);

export const actData = ref({
  num: 0, // 期数
  exposeProducts: true, // 是否展示曝光商品
  promotionStartTime: '', // 销开始时间
  promotionEndTime: '', // 促销结束时间
  giftPoster: '', // 赠品海报
  shopId: '', // 店铺id
  activityId: '', // 活动id
});

// 支付状态
export const userType = ref(0);

// 订单校验
export const checkOrderStatus = async () => {
  try {
    const res = await httpRequest.post('/90006/checkOrderStatus',
      {
        num: actData.value.num,
        activityId: actData.value.activityId,
      });
    userType.value = res.data ?? 0;
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

// 展示门槛显示弹框
export const showLimit = ref(false);

// 活动主接口
export const getActData = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90006/activity');
    console.log(res);
    actData.value = res.data ?? {
      num: 0,
      exposeProducts: true,
      promotionStartTime: '',
      promotionEndTime: '',
      giftPoster: '',
      shopId: '',
      activityId: '',
    };
  } catch (error: any) {
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 轮询支付状态
export const pollingPayStatus = async () => {
  await checkOrderStatus();
  if (userType.value === 0) {
    setTimeout(() => {
      pollingPayStatus();
    }, 2000);
  } else {
    showToast('支付成功');
  }
};

export interface Sku {
  jdPrice: number;
  skuId: number;
  skuName: string;
  skuMainPicture: string;
}

export const skuList = ref<Sku[]>([]);
export const pageNum = ref(1);
export const total = ref(0);

// 获取Sku列表
export const getSkuList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90006/skuListPage', {
      pageNum: pageNum.value,
      pageSize: 10,
    });
    closeToast();
    // res.data.forEach((item: any) => {
    //   item.jdPrice /= 1000;
    // });
    skuList.value.push(...res.data.records);
    total.value = res.data.total;
  } catch (error: any) {
    closeToast();
    console.error();
  }
};
export const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};
