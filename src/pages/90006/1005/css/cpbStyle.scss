// 顶部样式
.common-message-header-cpb {
  padding-top: 1.4rem;
  font-size: 0.4rem;
  color: #8d6a35;
  font-weight: bold;
}
// 利用footer来做关闭按钮
.common-message-footer-cpb {
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/197336/4/42035/9267/6612017fF6c77bfe5/6c3dc7a8aa2df349.png');
  background-size: 100%;
  position: absolute;
  width: 2.49rem;
  height: 0.53rem;
  right: 0.65rem;
  top: 3.6rem;
}
// 内容
.common-message-content-cpb {
  font-size: 0.28rem;
  line-height: 0.36rem;
  margin-top: 0.3rem;
  color: #8d6a35;
  padding: 0 0.2rem;
}
// 通用主题
.message-cpb {
  width: 5.17rem !important;
  height: 3.05rem !important;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/184968/27/43948/10109/6612017fFaec51791/c85d7468d1d5d07a.png') no-repeat !important;
  background-size: 100% 100% !important;
  .van-dialog__header {
    @extend .common-message-header-cpb;
  }
  .van-dialog__footer {
    @extend .common-message-footer-cpb;
    .van-dialog__confirm {
      color: transparent;
      background: transparent;
    }
  }
  .van-dialog__message {
    @extend .common-message-content-cpb;
  }
}
