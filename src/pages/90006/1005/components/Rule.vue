<template>
  <div class="bk">
    <div class="title">活动规则</div>
    <div class="content">
      <div>{{ ruleText || rule }}</div>
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
import { isPreview } from '@/utils';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref } from 'vue';

const props = withDefaults(defineProps<{ ruleText: string }>(), {
  ruleText: '',
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const rule = ref('');

const getRule = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    closeToast();
    const res = await httpRequest.get('/common/getRule');
    rule.value = res.data;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
!isPreview && getRule();
</script>

<style lang="scss">
@font-face {
  font-family: 'EnglishFont';
  src: url('../font/Cronos.ttf') format('truetype');
}

@font-face {
  font-family: 'ChineseFont';
  src: url('../font/fzxh.TTF') format('truetype');
}
</style>
<style scoped lang="scss">
.bk {
  width: 5.17rem;
  height:5.26rem;
  color: #000004;
  padding-top: 0.3rem;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/87543/34/39477/11573/6612017eF96abffd9/58529415d99ec272.png") no-repeat;
  background-size: 100%;
  font-family: 'EnglishFont', 'ChineseFont', sans-serif;
  .title {
    text-align: center;
    font-size: 0.3rem;
    font-weight: bold;
    font-family: 'EnglishFont', 'ChineseFont', sans-serif;
    margin-bottom: 0.15rem;
  }
  .act-time {
    font-size: 0.24rem;
    font-family: 'EnglishFont', 'ChineseFont', sans-serif;
    text-align: center;
    margin-bottom: 0.32rem;
  }
  .content {
    width: 5.2rem;
    height: 4.2rem;
    margin: 0 auto;
    padding: 0 0 0.25rem;
    div {
      height: 100%;
      overflow-y: scroll;
      white-space: pre-wrap;
      word-break: break-all;
      font-size: 0.18rem;
      line-height: 0.28rem;
      padding: 0 0.2rem 0.3rem 0.2rem;
    }
  }
}
.close{
  width:0.49rem;
  height: 0.49rem;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/174703/25/39883/1360/66120180Ffcba11af/1727b01325d0590a.png") no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
