import { computed, reactive } from 'vue';

export const furnish = reactive({
  pageBg: '',
  actBg: '', // 主页背景图
  actBg2: '', // 主页背景图2
  actBgColor: '', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '', // 按钮字体颜色
  btnImg: '', // 按钮背景图
  giftImg: '',
  step: '',
  popupImg: '',
  popupLink: '',
  // 攻略图
  strategyPopupImg: '',
  linkUrl: '',
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  backgroundImage: furnish.btnImg ? `url("${furnish.btnImg}")` : '',
  color: furnish.btnColor ?? '',
}));

const popupImg = computed(() => ({
  backgroundImage: furnish.popupImg ? `url("${furnish.popupImg}")` : '',
}));

export default {
  pageBg,
  shopNameColor,
  headerBtn,
  popupImg,
};
