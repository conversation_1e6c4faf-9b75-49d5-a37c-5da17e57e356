<template>
  <div class="bk">
    <div class="content">
      <div class="title">锁权记录</div>
      <div class="tab0" v-if="status === 2">
        <div>暂无锁权记录~</div>
      </div>
      <div class="tab1" v-if="status === 0 || status === 1">
        <div style="line-height: 0.9rem">
          您在{{ exchangeTime }} 锁定权益
        </div>
      </div>
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref } from 'vue';
import { isPreview } from '@/utils';

// 是否锁权   0 已锁权  1 已失效  2未锁权
const status = ref(1);
const exchangeTime = ref('');

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const getMyRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90006/prizeRecord');
    if (res.data === null) {
      // 未锁权
      status.value = 2;
    } else {
      status.value = res.data.status;
      exchangeTime.value = res.data.joinTime ? dayjs(res.data.joinTime).format('YY年M月D日 HH:mm:ss') : '';
    }
    closeToast();
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

!isPreview && getMyRecord();

</script>
<style scoped lang="scss">
.bk {
  width: 6.4rem;
  height: 5.88rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/304322/15/3276/27711/682afc36F0d33cda8/9f795ac26b0835b5.png) no-repeat;
  background-size: 100%;
}
.content {
  .title{
    margin: 0 auto;
    text-align: center;
    font-size: 0.3rem;
    padding: 0.2rem 0 0 0;
  }
  .tab0 {
    padding: 1.2rem 0;
    div {
      text-align: center;
      font-size: 0.26rem;
    }
  }
  .tab1{
    padding: 0.8rem 0;
    div {
      text-align: center;
      font-size: 0.26rem;
    }
  }
}
.close{
  width:0.49rem;
  height: 0.49rem;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/174703/25/39883/1360/66120180Ffcba11af/1727b01325d0590a.png") no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
