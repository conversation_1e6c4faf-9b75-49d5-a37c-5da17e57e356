<template>
  <div class="rule-bk">
    <div class="content" v-html="rule"></div>
    <div class="closeDiv" @click="close()"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/242407/37/20339/52367/6711cc28F5050a8e4/2cdd094f1fc3687b.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: 7.64rem;
  width:6.01rem;
  padding-top:1.10rem;
  .closeDiv {
    position: absolute;
    bottom: 0;
    //background:red;
    height:0.6rem;
    width: 0.6rem;
    left: calc(50% - 0.6rem / 2);
  }

  .content {
    height: 5rem;
    padding:0 0.3rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #fde8d1;
    white-space: pre-wrap;
  }
}
</style>
