<template>
  <div id="page">
    <div class="activity-container" :style="{ backgroundImage: `url(${decoData.pageBg})` }">
      <!-- 活动规则 -->
      <div class="rule-button" @click="onClickRule"></div>
      <!-- 我的奖品 -->
      <div class="my-prize-button" @click="onClickMyPrize"></div>
      <!-- 转段有礼 -->
      <div class="kinds-list">
        <div class="kind-section" v-for="(kind, index) in kinds" :key="index" :style="{ backgroundImage: `url(${kind.bgImage})` }">
          <!-- 系列标题 -->
          <div class="kind-title">
            <span class="kind-title-value">{{ kind.kindName }}</span>
            <span class="kind-title-after">（转段有礼）</span>
          </div>
          <!-- 进度条 -->
          <div class="progress-bar" :style="{ backgroundImage: `url(${decoData.progressBg})` }">
            <!-- 赠品信息 -->
            <div v-for="gift in kind.gifts" :key="gift.prizeId" class="progress-gift">
              <div class="gift-progress-icon" :style="{ backgroundImage: `url(${decoData.progressIcon})` }">
                <div class="gift-tip">
                  <span>{{ gift.prizeTips1 }}</span>
                  <span>{{ gift.prizeTips2 }}</span>
                  <span>{{ gift.prizeTips3 }}</span>
                  <span>{{ gift.prizeTips4 }}</span>
                </div>
                <div class="gift-img" @click="onGiftClick(gift)">
                  <img class="gift-img-value" :src="gift.prizeImage" />
                  <div class="receive-button" :class="[`receive-button-${gift.prizeState}`, gift.prizeState !== '0' ? 'disable' : '']" :style="{ backgroundImage: `url(${decoData.receiveButton})` }"></div>
                  <img class="note-image" :src="gift.notesImage" />
                </div>
              </div>
            </div>
          </div>
          <!-- sku -->
          <div class="sku-list">
            <div class="sku-item" v-for="(good, idx) in kind.goods" @click="gotoShopPage(1000015205)">
              <div class="sku-item-img">
                <ImageAutoResize :imgUrl="good.image" />
              </div>
              <div class="buy-button" :style="{ backgroundImage: `url(${decoData.buyButton})` }"></div>
            </div>
          </div>
          <div class="only-once">
            <span>*活动期间内仅可兑换一次</span>
            <img class="prize-transform" :src="decoData.transformIcon" />
          </div>
        </div>
      </div>
      <!-- 查看活动规则 -->
      <div class="bottom-rule-button" @click="onClickRule" :style="{ backgroundImage: `url(${decoData.bottomRuleButton})` }"></div>
    </div>
  </div>
  <!-- 规则弹窗 -->
  <RuleDialog v-model:show="showRuleDialog" :rule-text="ruleText" />
  <!-- 我的奖品弹窗 -->
  <MyPrizeDialog v-model:show="showMyPrizeDialog" :my-prize-list="myPrizeList" @submitAddressSuccessInMyPrizeDialog="onSubmitAddressSuccessInMyPrizeDialog" />
  <!-- 入会弹窗 -->
  <!-- <OpenCardDialog v-model:show="openCardDialog" /> -->
  <!-- 填写地址弹窗 -->
  <AddressDialog v-model:show="showAddressDialog" :readonly="isViewAddress" :grantId="grantId" @submitSuccess="onSubmitAddress" />
  <!-- 填写地址成功提示弹窗 -->
  <SubmitAddressSuccessDialog v-model:show="openSubmitAddressSuccessDialog" />
</template>

<script lang="ts" setup>
/* eslint-disable */
import { inject, reactive, provide, computed, ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { closeToast, showLoadingToast, showToast, Swipe as VanSwipe, SwipeItem as VanSwipeItem, Popup as VanPopup, CountDown as VanCountDown } from 'vant';
import { debounce } from 'lodash';
import dayjs from 'dayjs';
import { areaList } from '@vant/area-data';

import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { gotoShopPage, addSkuToCart, gotoSkuPage } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import openCard from '@/utils/openCard';

import RuleDialog from '../components/RuleDialog.vue';
import OpenCardDialog from '../components/OpenCardDialog.vue';
import AddressDialog from '../components/AddressDialog.vue';
import SubmitAddressSuccessDialog from '../components/SubmitAddressSuccessDialog.vue';
import MyPrizeDialog from '../components/MyPrizeDialog.vue';
import ImageAutoResize from '../components/ImageAutoResize.vue';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('baseInfo', baseInfo);

const pathParams: any = inject('pathParams');
console.log('pathParams', pathParams);

const baseUserInfo: any = inject('baseUserInfo');
console.log('baseUserInfo', baseUserInfo);

const decoData: any = inject('decoData');
console.log('decoData', decoData);

const delayToast = (e: string, time = 1000): Promise<void> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      showToast(e);
      resolve();
    }, time);
  });
};

// 规则弹窗
const showRuleDialog = ref(false);
const ruleText = ref('');
// 点击活动规则按钮
const onClickRule = async () => {
  try {
    showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
    const res = await httpRequest.get('/common/getRule');
    ruleText.value = res.data || '';
    showRuleDialog.value = true;
  } catch (e: any) {
    showToast(e.message || '获取失败');
  } finally {
    closeToast();
  }
};

// 我的奖品弹窗
const showMyPrizeDialog = ref(false);
// 我的奖品列表
const myPrizeList = ref([]);
// 点击我的奖品按钮
const onClickMyPrize = async () => {
  try {
    showMyPrizeDialog.value = true;
  } catch (e: any) {
    showToast(e.message || '获取失败');
  } finally {
    closeToast();
  }
};
const onSubmitAddressSuccessInMyPrizeDialog = async () => {
  getHomeData({ showLoading: false });
};

// 入会弹窗
/* const openCardDialog = ref(false);
watch(
  () => baseInfo?.memberLevel,
  (val) => {
    if (val == null) {
      openCardDialog.value = true;
    }
  },
  { immediate: true },
); */

// 填写地址弹窗
const showAddressDialog = ref(false);
// 是否是查看地址
const isViewAddress = ref(false);
// 填写地址的grantId
const grantId = ref('');
// 地址表单数据
const onSubmitAddress = async () => {
  openSubmitAddressSuccessDialog.value = true;
  getHomeData({ showLoading: false });
};
// 填写地址成功提示弹窗
const openSubmitAddressSuccessDialog = ref(false);

// 点击领奖
const onGiftClick = async (gift: any) => {
  const {
    prizeId,
    prizeState,
    prizeType, // 2-京豆；3-实物；8-京东e卡
  } = gift;

  // 非可领取状态时，根据 prizeState 给出提示
  if (prizeState !== '0') {
    const stateMap: Record<string, string> = {
      '1': '未检测到符合条件历史订单',
      '2': '未检测到符合条件转段订单',
      '3': '您的购买顺序不符',
      '4': '奖品已领取',
      '5': '奖品已抢光',
      '6': '您的转段顺序不符',
      '7': '您的历史订单罐数不足',
      '8': '您的转段订单罐数不足',
    };
    showToast(stateMap[prizeState] || '暂不可领取');
    return;
  }

  // 可领取逻辑
  try {
    showLoadingToast({ message: '领取中...', forbidClick: true, duration: 0 });
    const res = await httpRequest.post('/99659/draw', { prizeId });

    closeToast();
    if (prizeType === 3) {
      showToast({
        message: '领取成功,请填写收货地址',
        duration: 1000,
        forbidClick: true,
        onClose: () => {
          grantId.value = res.data;
          showAddressDialog.value = true;
        },
      });
    } else {
      showToast('领取成功');
    }
    getHomeData({ showLoading: false });
  } catch (e: any) {
    closeToast();
    showToast(e.message || '领取失败');
  }
};
// 获取活动主接口返回信息
const kinds = ref([]);

const getHomeData = async (options = { showLoading: true }) => {
  const { showLoading = true } = options;

  try {
    if (showLoading) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
    }

    const { data } = await httpRequest.post('/99659/main');
    kinds.value = data.kinds || [];
    myPrizeList.value = data.prizes || [];
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  } finally {
    if (showLoading) {
      closeToast();
    }
  }
};

onMounted(async () => {
  await getHomeData();
});
</script>

<style lang="scss" scoped>
#page {
  width: 100vw;
  height: 100vh;
  overflow: auto;
}

.activity-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  min-height: 100vh;
  padding-top: 12rem;
  background-color: #b5bfca;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/299354/13/23326/313575/6877432dF20f8722f/8363d80050b8b4c0.png');
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
}

/* 活动规则 */
.rule-button {
  position: absolute;
  top: 0.72rem;
  right: 0;
  width: 1.3rem;
  height: 0.35rem;
}

/* 我的奖品 */
.my-prize-button {
  position: absolute;
  top: 1.16rem;
  right: 0;
  width: 1.3rem;
  height: 0.4rem;
}

.kind-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (736 / (1 * 100)) * 1rem;
  height: (388 / (1 * 100)) * 1rem;
  margin-bottom: 0.12rem;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .kind-title {
    position: absolute;
    top: 0;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 5.78rem;
    height: 0.6rem;
    color: #fff;
    transform: translateX(-50%);

    .kind-title-value {
      font-size: 0.37rem;
    }

    .kind-title-after {
      font-size: 0.24rem;
    }
  }

  .progress-bar {
    position: absolute;
    top: 1.26rem;
    left: 50%;
    z-index: 3;
    width: (660 / (1 * 100)) * 1rem;
    height: (21 / (1 * 100)) * 1rem;
    background-image: url('../asset/progress.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    transform: translateX(-50%);
  }

  .progress-gift {
    position: absolute;
    top: 50%;
    left: 46%;
    display: flex;
    flex-direction: column;
    align-items: center;
    transform: translateY(-50%);
  }

  .gift-progress-icon {
    width: (33 / (1 * 100)) * 1rem;
    height: (33 / (1 * 100)) * 1rem;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }

  .gift-tip {
    position: absolute;
    top: -0.4rem;
    left: 50%;
    display: flex;
    align-items: baseline;
    word-break: keep-all;
    transform: translateX(-50%);

    span {
      &:nth-child(odd) {
        font-size: 0.2rem;
      }

      &:nth-child(even) {
        color: #d80505;
        font-size: 0.24rem;
      }
    }
  }

  .gift-img {
    position: absolute;
    top: 0.45rem;
    left: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    transform: translateX(-50%);
  }

  .gift-img-value {
    width: 0.8rem;
    margin-bottom: 0.1rem;
  }

  .note-image {
    position: absolute;
    top: 50%;
    right: 0;
    width: 0.44rem;
    transform: translate(100%, -100%);
  }

  .receive-button {
    width: (63 / (1 * 100)) * 1rem;
    height: (16 / (1 * 100)) * 1rem;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;

    &.disable {
      filter: grayscale(1);
    }
  }

  .sku-list {
    position: absolute;
    top: 1.55rem;
    left: 50%;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    width: 6rem;
    transform: translateX(-50%);
  }

  .sku-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .sku-item-img {
    display: flex;
  }

  .buy-button {
    width: (114 / (1 * 100)) * 1rem;
    height: (32 / (1 * 100)) * 1rem;
    margin-top: 0.1rem;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }

  .only-once {
    position: absolute;
    bottom: 0.2rem;
    left: 50%;
    font-size: 0.12rem;
    transform: translateX(-50%);

    .prize-transform {
      position: absolute;
      top: -0.8rem;
      left: 50%;
      width: 0.72rem;
      transform: translateX(-50%);
    }
  }
}

.bottom-rule-button {
  width: (238 / (1 * 100)) * 1rem;
  height: (46 / (1 * 100)) * 1rem;
  margin-bottom: 0.2rem;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}
</style>

<style lang="scss">
* {
  line-height: 1;

  ::-webkit-scrollbar {
    display: none;
  }
}

// 禁止页面回弹行为
html,
body {
  overscroll-behavior: none none;
}

button {
  margin: 0;
  padding: 0;
  background-color: transparent;
  border: 0;
  outline: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0%); /* 透明色，禁用默认高亮效果 */
}

.text-overflow-line {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-overflow-2-line {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 弹窗 */
.my-dialog.my-popup {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 7.5rem;
  overflow-y: unset;
  background: none;

  &.van-popup--center {
    max-width: 100%;
  }
}

.dialog-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
}

/* 关闭弹窗icon */
.dialog-close-btn {
  position: absolute;
  bottom: -0.8rem;
  left: 50%;
  width: (55 / (1 * 100)) * 1rem;
  height: (55 / (1 * 100)) * 1rem;
  background-color: #fff;
  background-image: url('../asset/dialog-close-icon.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  border-radius: 50%;
  transform: translateX(-50%);
}

.empty-text {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 0.26rem;
  transform: translate(-50%, -50%);
}
</style>
