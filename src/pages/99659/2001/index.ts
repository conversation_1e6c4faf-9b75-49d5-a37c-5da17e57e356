import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin, { EventTrackParams } from '@/plugins/EventTracking';
import { setToastDefaultOptions, allowMultipleToast } from 'vant';

import '@/style';
import 'animate.css';
import 'vant/lib/index.css';
// import './style';

// 全局设置loading toast配置
setToastDefaultOptions('loading', {
  forbidClick: true,
  duration: 0,
  message: '请稍候',
});
// 全局设置 普通toast配置
setToastDefaultOptions({
  duration: 2000,
  forbidClick: true,
});
// 允许多个toast同时展示
allowMultipleToast();

// 页面自适应
initRem();

const app = createApp(index);

// 初始化页面
const config: InitRequest = {
  disableThresholdPopup: true,
  backActRefresh: false,
  disableNotice: true,
  showFinishedPage: true,
  showUnStartPage: true,
  urlPattern: '/custom/:activityType/:templateCode',
};

init(config).then(({ baseInfo, pathParams, userInfo, decoData }) => {
  // 设置页面title
  document.title = baseInfo?.activityName || '转段有礼';
  app.provide('baseInfo', baseInfo);
  app.provide('pathParams', pathParams);
  app.provide('baseUserInfo', userInfo);
  app.provide('decoData', decoData);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.mount('#app');
});
