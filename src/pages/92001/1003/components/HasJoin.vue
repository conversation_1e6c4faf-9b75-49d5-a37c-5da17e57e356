<template>
  <div class="rule-bk">
    <div class="btn1" @click="jump(1)"></div>
    <div class="btn2" @click="jump(2)"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script setup lang="ts">
import { BaseInfo } from '@/types/BaseInfo';
import { inject } from 'vue';
import { gotoShopPage } from '@/utils/platforms/jump';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const jump = (type) => {
  if (type === 1) {
    emits('close', 1);
  } else {
    gotoShopPage(baseInfo.shopId);
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-repeat: no-repeat;
  background-image: url('../assets/hasJoin.png');
  background-size: 100%;
  width: 6.15rem;
  height: 5.25rem;
  padding: 1rem 0 0 0;
  position: relative;
  .btn1 {
    width: 2.3rem;
    height: 0.76rem;
    position: absolute;
    top: 3.88rem;
    left: 0.5rem;
  }
  .btn2 {
    width: 2.3rem;
    height: 0.76rem;
    position: absolute;
    top: 3.88rem;
    right: 0.5rem;
  }
  .close {
    width: 0.41rem;
    height: 0.42rem;
    position: absolute;
    top: 0.25rem;
    right: 0.31rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
