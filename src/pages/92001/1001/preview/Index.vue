<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="kv">
      <img :src="furnish.actBg" alt="" />
      <div class="btn">
        <img style="width: 1.6rem" :src="furnish.prizeBtn" alt="" @click="myPrize = true" />
        <img style="width: 1.22rem" :src="furnish.ruleBtn" alt="" @click="showRulePopup" />
      </div>
    </div>
    <div class="prize-list" v-if="prizeList.length">
      <template v-for="(item, index) in prizeList" :key="index">
        <div v-if="item.seriesImg" class="item"  :style="{ backgroundImage: `url(${item.seriesImg})` }">
          <div class="num" :style="furnishStyles.prizeTextColor.value">剩余数量：{{item.sendTotalCount}}</div>
          <img class="get-btn" :src="furnish.getPrizeBtn" alt="" @click="ShowToast" />
        </div>
      </template>
    </div>
    <div class="index-rule-bk" :style="furnishStyles.ruleBox.value">
      <div class="content">{{ ruleTest }}</div>
    </div>

    <img class="more"  :src="furnish.moreBg" alt="" @click="ShowToast" />
  </div>
  <!--活动规则弹窗-->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="myPrize">
    <PrizePopup @close="myPrize = false"></PrizePopup>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, inject, onMounted } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';
import PrizePopup from '../components/MyPrizePopup.vue';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const shopName = ref('');

const isLoadingFinish = ref(true);
const prizeList = ref<any>(defaultStateList);

const showRule = ref(false);
const ruleTest = ref('');
const myPrize = ref(false);
// 活动规则相关
const showRulePopup = async () => {
  showRule.value = true;
};

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 装修时选择框
const showSelect = ref(false);
// 装修展示页面  1主页 2成功页 3领取攻略
const activeKey = ref('1');
const isSuccess = ref(false);
const selectedId = ref(1); // 装修时选择框序号

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 页面截图
const isCreateImg = ref(false);
const tabNum = ref('0');
const createImg = async () => {
  showRule.value = false;
  isCreateImg.value = true;
  tabNum.value = activeKey.value;
  const interactC = document.getElementById('interact-c') as HTMLElement;
  useHtmlToCanvas(interactC).then(() => {
    activeKey.value = tabNum.value;
  });
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleTest.value = data.rules;
  if (data.prizeList?.length) {
    prizeList.value = data.prizeList;
  } else {
    prizeList.value = defaultStateList;
  }
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
  showSelect.value = false;
});
// 点击边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

registerHandler('activeKey', (data: any) => {
  activeKey.value = data;
  // if (activeKey.value === '3') {
  //   showRulePopup();
  // }
  if (activeKey.value === '2') {
    isSuccess.value = true;
    showRule.value = false;
  }
  if (activeKey.value === '1') {
    isSuccess.value = false;
    showRule.value = false;
  }
});

onMounted(() => {
  if (activityData) {
    prizeList.value = activityData.prizeList;
    ruleTest.value = activityData.rules;
    shopName.value = activityData.shopName;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>

<style>
@font-face {
  font-family: 'FZRuiZHJW_Zhun';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZRUIZHJW/FZRuiZHJW_Zhun.TTF');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'FZRuiZHJW_Zhun';
}
</style>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.2rem;
  position: relative;
}
.kv {
  position: relative;
  margin-bottom: 0.35rem;
  img {
    width: 100%;
  }
  .btn {
    position: absolute;
    right: 0;
    top: 0.23rem;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    img {
      width: fit-content;
      height: 0.4rem;
      margin-bottom: 0.1rem;
      cursor: pointer;
    }
  }
}
.prize-list {
  .item {
    width: 7.1rem;
    height: 7.9rem;
    background-repeat: no-repeat;
    background-size: 100%;
    margin: 0 auto 0.4rem;
    padding-top: 5.3rem;
    .num {
      font-size: 0.25rem;
      line-height: 0.25rem;
      height: 0.25rem;
      text-align: center;
      font-weight: bold;
      margin-bottom: 0.65rem;
    }
    .get-btn {
      width: 2.56rem;
      margin: 0 auto;
      cursor: pointer;
    }
  }
}
.index-rule-bk {
  width: 7.11rem;
  height: 9.5rem;
  margin: 0 auto;
  background-repeat: no-repeat;
  background-size: 100%;
  padding: 1.1rem 0.23rem 0.25rem;
  .content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
    font-size: 0.2rem;
  }
}
.more {
  margin: 0.3rem auto;
  cursor: pointer;
  width: 3.96rem;
  height: 0.58rem;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
