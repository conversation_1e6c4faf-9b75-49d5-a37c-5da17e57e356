<template>
  <div class="task-list">
    <div class="task-item" v-for="(task, index) in taskList" :key="index">
      <div class="task-info">
        <img class="task-icon" :src="task.taskInfo.icon" alt="" />
        <div class="task-content">
          <div class="task-name">
            <div>
              {{ task.taskInfo.name }}<span v-if="task.taskType === 3">{{ task.finishCnt }}/{{ task.totalCnt }}</span>
            </div>
            <!-- 任务类型3: 浏览商品 -->
            <div v-if="task.taskType === 3" :class="['task-btn', { 'task-btn-disabled': task.finishCnt >= task.totalCnt }]" @click="handleTaskClick(task)">
              {{ task.finishCnt >= task.totalCnt ? task.taskInfo.finishBtn || '已完成' : task.taskInfo.btn }}
            </div>
            <!-- 任务类型13或1: 当完成次数大于0时显示已完成，否则显示按钮 -->
            <div v-else-if="task.taskType === 13 || task.taskType === 1" :class="['task-btn', { 'task-btn-disabled': task.finishCnt > 0 }]" @click="handleTaskClick(task)">
              {{ task.finishCnt > 0 ? task.taskInfo.finishBtn || '已完成' : task.taskInfo.btn }}
            </div>
            <div class="task-btn" v-else @click="handleTaskClick(task)">
              {{ task.taskInfo.btn }}
            </div>
          </div>
          <div class="task-desc">{{ task.taskInfo.desc.replaceAll('XXX', pointName) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, onMounted, ref, Ref, onUnmounted } from 'vue';
import type { TaskListItem } from '../scripts/types';
import { followShop, getTaskList, doTask } from '../scripts/ajax';
import { showToast, allowMultipleToast, showSuccessToast } from 'vant';
import type { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';

const baseInfo = inject('baseInfo') as BaseInfo;
const pointName = inject('pointName') as Ref<string>;
const pathParams = inject('pathParams') as any;
const emits = defineEmits(['refreshPoint']);
const handleTaskClick = async (task: TaskListItem) => {
  //   任务已完成;
  if ((task.taskType === 3 && task.finishCnt >= task.totalCnt) || ((task.taskType === 13 || task.taskType === 1) && task.finishCnt > 0)) {
    return;
  }
  //   开卡任务
  if (task.taskType === 13) {
    const url = new URL(baseInfo.openCardLink);
    url.searchParams.set('returnUrl', window.location.href);
    url.searchParams.set('taskType', '13');
    window.location.href = url.toString();
    return;
  }
  //  关注任务
  if (task.taskType === 1) {
    const res = await followShop();
    if (res) {
      showToast(`${task.taskInfo.tips},获得${pointName.value}+50`);
      task.finishCnt += 1;
      const timer = setTimeout(() => {
        clearTimeout(timer);
        emits('refreshPoint');
      }, 2000);
    }
    return;
  }
  //  浏览商品任务
  if (task.taskType === 3) {
    // 记录开始浏览时间
    localStorage.setItem('browseStartTime', Date.now().toString());
    localStorage.setItem('browseTaskId', task.taskId.toString());
    gotoSkuPage(task.skuId);
    return;
  }
  //  去下单
  if (task.taskType === 8) {
    gotoShopPage(baseInfo.shopId);
  }
};

const taskList = ref<TaskListItem[]>([]);

const getList = async () => {
  const res = await getTaskList();
  taskList.value = res.map((item: TaskListItem) => {
    console.log('🚀 ~ taskList.value=res.data.map ~ item:', {
      ...item,
      taskInfo: JSON.parse(item.showJson),
    });
    return {
      ...item,
      taskInfo: JSON.parse(item.showJson),
    };
  });
};

// 页面可见性变化处理函数
const handleVisibilityChange = async () => {
  if (document.visibilityState === 'visible') {
    const startTime = localStorage.getItem('browseStartTime');
    const taskId = localStorage.getItem('browseTaskId');
    if (startTime && taskId) {
      const browseDuration = dayjs().valueOf() - parseInt(startTime, 10);
      // 判断浏览时间是否达到5秒
      if (browseDuration >= 5000) {
        const task = taskList.value.find((t) => t.taskId.toString() === taskId);
        if (task && task.finishCnt < task.totalCnt) {
          const res = await doTask(task.skuId, task.taskId);
          if (res) {
            showToast(`${task.taskInfo.tips},获得${pointName.value}+20`);
            const timer = setTimeout(() => {
              clearTimeout(timer);
              emits('refreshPoint');
            }, 2000);
            getList();
          }
        }
        // 清除存储的时间戳和任务ID
        localStorage.removeItem('browseStartTime');
        localStorage.removeItem('browseTaskId');
      } else {
        showToast('抱歉，浏览时间不足');
        // 清除存储的时间戳和任务ID
        localStorage.removeItem('browseStartTime');
        localStorage.removeItem('browseTaskId');
      }
    }
  }
};

onMounted(async () => {
  await getList();
  // 添加页面可见性监听
  document.addEventListener('visibilitychange', handleVisibilityChange);
});

// 组件卸载时移除监听器
onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange);
});
</script>

<style scoped>
.task-list {
}

.task-item {
  background-color: #fdf7ee;
  border-radius: 0.1rem;
  margin-top: 0.1rem;
  padding: 0.15rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.task-item:last-child {
  border-bottom: none;
}

.task-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.task-icon {
  width: 0.6rem;
  margin-right: 0.1rem;
}

.task-content {
  flex: 1;
}

.task-name {
  font-size: 0.16rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.05rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-desc {
  font-size: 0.12rem;
  color: #999;
}

.task-action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.task-reward {
  font-size: 0.14rem;
  color: #016143;
  font-weight: bold;
  margin-bottom: 0.05rem;
}

.task-progress {
  font-size: 0.12rem;
  color: #999;
  margin-bottom: 0.05rem;
}

.task-btn {
  background-color: #016143;
  color: #fff;
  padding: 0.05rem 0.3rem;
  border-radius: 0.3rem;
  font-size: 0.14rem;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.task-btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
