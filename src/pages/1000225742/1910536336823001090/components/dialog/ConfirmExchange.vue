<template>
  <Popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false">
    <div class="dialog-bg">
      <div class="box">
        <div class="title">兑换确认</div>
        <div class="content">
          <div class="confirm-content">
            <div class="confirm-text">
              确认消耗
              <span class="point-value">{{ prizeInfo.score }}</span>
              {{ pointName }}
            </div>
            <div class="confirm-text">
              兑换奖品<span class="point-value">【{{ prizeInfo.prizeName }}】</span>吗？
            </div>
            <div class="btn-group">
              <div class="btn cancel-btn" @click="handleCancel">取消</div>
              <div class="btn confirm-btn" @click="handleConfirm">确认</div>
            </div>
          </div>
        </div>
      </div>
      <Icon name="close" size="50" color="#007a54" class="close-icon" @click="handleCancel"></Icon>
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps, inject, ref, PropType } from 'vue';
import { Popup, Icon, showToast } from 'vant';
import dayjs from 'dayjs';
import type { ExchangePrizeType } from '../../scripts/types';

const pointName = inject('pointName');

const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  prizeInfo: {
    type: Object as PropType<ExchangePrizeType>,
    required: true,
    default: () => ({}),
  },
});

const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog', 'exchangeSuccess']);

const handleCancel = () => {
  emits('closeDialog');
};

const handleConfirm = async () => {
  emits('exchangeSuccess');
  emits('closeDialog');
};
</script>

<style scoped lang="scss">
.dialog-bg {
  padding-bottom: 0.2rem;
  position: relative;
}
.box {
  background-color: #007a54;
  width: 3rem;
  height: 3.7rem;
  padding: 0.08rem;
  box-sizing: border-box;
  position: relative;
  border-radius: 0.13rem;
  .title {
    font-weight: bold;
    background: url(../../assets/dialog-til.png) no-repeat;
    background-size: 100% 100%;
    width: 1.7rem;
    height: 0.4rem;
    position: absolute;
    top: 0rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    line-height: 0.4rem;
    font-size: 0.24rem;
    font-weight: bold;
    color: #016143;
  }
  .content {
    background-color: #fff;
    border: #00573c solid 0.02rem;
    width: 100%;
    border-radius: 0.13rem;
    height: 100%;
    padding: 0.1rem;

    .confirm-content {
      text-align: center;
      margin-top: 1rem;
      .confirm-text {
        font-size: 0.18rem;
        color: #333;
        margin-bottom: 0.1rem;
        line-height: 1.5;

        .point-value {
          color: #007a54;
          font-weight: bold;
        }
      }

      .btn-group {
        display: flex;
        justify-content: center;
        position: absolute;
        bottom: 0.5rem;
        left: 0;
        right: 0;

        .btn {
          border-radius: 0.2rem;
          font-size: 0.16rem;
          padding: 0.08rem 0.3rem;
          line-height: 1;
          letter-spacing: 0.05rem;
        }
        color: #fff;

        .cancel-btn {
          background-color: #818181;
          margin-right: 0.15rem;
        }

        .confirm-btn {
          background-color: #016143;
        }
      }
    }
  }
}
.close-icon {
  margin-top: 0.2rem;
  margin-left: 50%;
  transform: translateX(-50%);
  font-weight: bold;
}
</style>
