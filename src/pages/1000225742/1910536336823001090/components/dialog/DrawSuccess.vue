<template>
  <Popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false">
    <div class="dialog-bg">
      <div class="box">
        <div class="content">
          <div class="content-title">{{ !prizeInfo.prizeType ? '很遗憾 未抽中哦！' : '恭喜您 中奖啦!' }}</div>
          <img class="content-img" :src="prizeInfo.prizeImg" alt="" />
          <div class="content-tips" v-if="[2, 4].includes(prizeInfo.prizeType)">+{{ prizeInfo.prizeName }}</div>
          <div class="content-name" v-if="prizeInfo.prizeType !== 0">恭喜您抽中<br />【{{ prizeInfo.prizeName }}】</div>
          <div class="content-name" v-else>此次未中奖<br />再接再厉!</div>
          <div class="content-exchange" v-if="prizeInfo.prizeType === 3">
            <div class="content-btn" @click="fillInManually">填写邮寄地址</div>
            <div class="content-exchange-desc">请在中奖后1小时内填写地址，否则视作放弃奖品</div>
          </div>
          <div class="content-exchange" v-else>
            <div class="content-btn" @click="handleCancel">我知道了</div>
            <div class="content-exchange-desc" v-if="prizeInfo.prizeType === 2">京豆已放入您的账户中，可在<span>个人中心-京豆</span>中查看</div>
            <div class="content-exchange-desc" v-if="prizeInfo.prizeType === 4">积分已放入您的账户中，可在<span>店铺会员页</span>中查看</div>
          </div>
        </div>
      </div>
      <Icon name="close" size="50" color="#007a54" class="close-icon" @click="handleCancel"></Icon>
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, PropType } from 'vue';
import { Popup, Icon, showToast } from 'vant';
import dayjs from 'dayjs';
import type { PrizeInfoType } from '../../scripts/types.ts';

const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  prizeInfo: {
    type: Object as PropType<PrizeInfoType>,
    required: true,
    default: () => ({}),
  },
});

const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog', 'fillInTheAddress']);

const handleCancel = () => {
  emits('closeDialog');
};

const fillInManually = async () => {
  emits('fillInTheAddress');
};
</script>

<style scoped lang="scss">
.dialog-bg {
  padding-bottom: 0.2rem;
  position: relative;
}
.box {
  background-color: #007a54;
  width: 3rem;
  height: 3.7rem;
  padding: 0.08rem;
  box-sizing: border-box;
  position: relative;
  border-radius: 0.13rem;
  .content {
    background-color: #fff;
    border: #00573c solid 0.02rem;
    width: 100%;
    border-radius: 0.13rem;
    color: #000;
    height: 100%;
    padding-top: 0.2rem;
    text-align: center;
    &-title {
      font-size: 0.2rem;
      font-weight: bold;
      margin-bottom: 0.1rem;
    }
    &-tips {
      font-size: 0.1rem;
      background-color: #ff0000;
      line-height: 1;
      border-radius: 0.6rem 0.3rem 0.3rem 0;
      position: absolute;
      right: 0.5rem;
      top: 0.7rem;
      color: #fff;
      padding: 0.05rem 0.1rem;
    }
    &-img {
      width: 2rem;
      height: 1.7rem;
      object-fit: contain;
      margin: 0 auto 0.03rem;
      display: block;
    }
    &-name {
      font-size: 0.18rem;
    }
    &-exchange {
      position: absolute;
      bottom: 0.2rem;
      left: 0;
      right: 0;
      text-align: center;
      .content-btn {
        line-height: 1;
        padding: 0.1rem 0.2rem;
        border-radius: 0.1rem;
        background-color: #016143;
        color: #fff;
        font-size: 0.16rem;
        display: inline-block;
        white-space: nowrap;
        margin: 0 auto 0.02rem;
      }
      .content-exchange-desc {
        font-size: 0.1rem;
        color: #555;
        span {
          color: #016143;
        }
      }
    }
  }
}
.close-icon {
  margin-top: 0.2rem;
  margin-left: 50%;
  transform: translateX(-50%);
  font-weight: bold;
}
</style>
