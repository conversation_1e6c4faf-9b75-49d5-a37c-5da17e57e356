<template>
  <div class="order-bk">
    <div class="close" @click="close" />
    <div class="title">
      <div class="leftLineDiv" />
      <div>我的订单</div>
      <div class="rightLineDiv" />
      <img alt="" data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="content">
      <div v-for="(item, index) in orderList" :key="index">
        <div class="order-list">
          <div class="order-list-line1">
            <span>订单编号</span>
            <span>下单时间:{{ dayjs(item.orderStartTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
          </div>
          <div class="order-list-line2">
            <span>{{ item.orderId }}</span>
            <span>￥{{ Number(item.orderPrice).toFixed(2) }}</span>
            <span class="order-status">{{ item.status }}</span>
          </div>
        </div>
      </div>
      <div v-if="!orderList.length" class="no-data">确认收货后方可查看订单信息</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';

interface orderInfo {
  orderId: string;
  orderPrice: string;
  orderStartTime: string;
  status: string;
}

const orderList = ref([] as orderInfo[]);
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
const getMyOrder = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90111/oderList');
    orderList.value = data;
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

getMyOrder();
</script>

<style scoped lang="scss">
.order-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;
  height: 8.26rem;
  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/58825/13/20897/7405/63084eadE7f6b9252/0db6644ea77ae3b0.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    border-radius: 0.2rem 0.2rem 0 0;
    color: #fff;
    .leftLineDiv {
      margin-right: 0.1rem;
      width: 34px;
      height: 4px;
      line-height: 0.08rem;
      background: linear-gradient(to left, rgb(243, 167, 50), rgb(255, 255, 255));
      border-radius: 4px;
    }
    .rightLineDiv {
      margin-left: 0.1rem;
      width: 34px;
      height: 4px;
      line-height: 0.08rem;
      background: linear-gradient(to right, rgb(243, 167, 50), rgb(255, 255, 255));
      border-radius: 4px;
    }
  }
  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }
  .no-data {
    text-align: center;
    line-height: 35vh;
    font-size: 0.24rem;
    color: #8c8c8c;
  }
  .content {
    position: absolute;
    top: 1.5rem;
    left: 0.3rem;
    height: 8rem;
    width: 6.9rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    .order-list {
      margin-left: calc(50% - 6.9rem / 2);
      padding: 0.05rem 0.4rem;
      width: 6.9rem;
      height: 1.06rem;
      margin-bottom: 0.14rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/105172/35/44995/1314/6503b473F030c0abf/73e798967da06461.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .order-list-line1 {
        margin-bottom: 0.2rem;
        display: flex;
        justify-content: space-between;
        color: #999999;
        font-size: 0.19rem;
      }
      .order-list-line2 {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        color: #262626;
        font-size: 0.23rem;
      }
      .order-status {
        //color: #FE9F11;
      }
    }
  }
}
</style>
