<template>
  <div class="main">
    <img :src="decorationInfo.pageBg" alt="" class="bg" />
    <div class="content">
      <div class="info" :style="{ color: decorationInfo?.otherColor }">
        <div :style="{ color: decorationInfo?.ruleTextColor }" class="rule">
          {{ decorationInfo.ruleText }}
        </div>
        <div class="mainContent">
          <div class="btnList">
            <div
              :style="{
                color: decorationInfo?.cardDrawTextColor,
                backgroundColor: decorationInfo?.cardDrawBtnColor,
              }"
              @click="debouncedDraw"
              class="btn left">
              点击抽卡
            </div>
            <div
              :style="{
                color: decorationInfo?.cardTextColor,
                backgroundColor: decorationInfo?.cardBtnColor,
              }"
              @click="showPopup('exchangeEnergy', { ...decorationInfo })"
              class="btn right">
              我的卡片
            </div>
          </div>
          <div class="count">剩余抽卡次数：{{ decorationInfo.cardCountLimitDay }}次</div>
        </div>
      </div>
      <div class="btn_group">
        <div
          :style="{
            color: decorationInfo?.dialogTextColor,
            backgroundColor: decorationInfo?.dialogBtnColor,
          }"
          class="btn"
          @click="showPopup('exchangeRecord', { ...decorationInfo })"
          v-click-track="'dhjl'">
          兑换记录
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { httpRequest } from '@/utils/service';
import { computed } from 'vue';
import { debounce, preview } from '../../Utils';
import { showPopup, upDataDecoration, checkThreshold } from '../DataHooks';
import { closeToast, showLoadingToast, showToast } from 'vant';

const props = defineProps(['decorationInfo', 'moduleName']);

const decorationInfo = computed(() => props.decorationInfo);

const drawCard = async () => {
  if (preview) return;
  if (!checkThreshold()) return;
  if (decorationInfo.value.cardCountLimitDay <= 0) {
    showToast('剩余抽卡次数不足');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/99211/drawCard');
    console.log(data, '抽卡');
    closeToast();
    showPopup('drawCardResult', {
      ...data.drawCard,
      moduleName: props.moduleName,
    });
    await upDataDecoration(props.moduleName);
  } catch (err: any) {
    showToast(err.message);
  }
};
const debouncedDraw = debounce(drawCard, 300);
</script>

<style scoped lang="scss">
.main {
  position: relative;
  height: 100%;
  overflow: hidden;
  min-height: 7.15rem;

  .bg {
    width: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
  }

  .content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    padding-top: 1.5rem;

    .rule {
      width: 6rem;
      margin: 0 auto;
      overflow-y: scroll;
      height: 0.8rem;
      font-size: 0.3rem;
      white-space: pre-wrap;
      word-break: break-all;
    }

    .info {
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .mainContent {
        width: 3.2rem;
        height: 1.5rem;
        overflow: scroll;
        margin: 3.3rem auto 0;

        .btnList {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .btn {
            width: 1.6rem;
            height: 0.74rem;
            line-height: 0.3rem;
            border-radius: 0.1rem;
            font-size: 0.3rem;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .left {
            background-color: #fff;
            border-radius: 0.35rem 0 0 0.35rem;
          }

          .right {
            background-color: #000;
            border-radius: 0 0.35rem 0.35rem 0;
          }
        }

        .count {
          font-size: 0.16rem;
          text-align: left;
          text-indent: 0.2rem;
          margin-top: 0.1rem;
        }
      }
    }

    .btn_group {
      position: absolute;
      top: 0.5rem;
      right: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      border-radius: 10px;
      text-align: center;

      .btn {
        width: 1.8rem;
        height: 0.46rem;
        background-color: #ffffff;
        border-radius: 0.23rem 0 0 0.23rem;
        font-size: 0.24rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.1rem;
      }
    }
  }
}
</style>
