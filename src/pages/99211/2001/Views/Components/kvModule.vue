<template>
  <div class="main">
    <img :src="decorationInfo?.pageBg" alt="" class="bg" />
    <div class="content">
      <div class="info" :style="{ color: decorationInfo?.otherTextColor }">
        <div class="energyValue">我的能量值：{{ currentEnergy || 0 }}</div>
        <div
          class="member_btn"
          @click="openCard"
          :style="{
            color: decorationInfo?.memberTextColor,
            backgroundColor: decorationInfo?.memberBtnColor,
          }">
          加入骁龙会员
        </div>
        <div class="des">{{ decorationInfo.ruleText }}</div>
      </div>
      <div class="btn_group">
        <div
          :style="{
            color: decorationInfo?.ruleTextColor,
            backgroundColor: decorationInfo?.ruleBtnColor,
          }"
          class="btn"
          @click="showPopup('rule', { hasAgree: decorationInfo.hasAgree, moduleName })"
          v-click-track="'hdgz'">
          活动规则
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, onMounted, ref } from 'vue';
import { preview } from '../../Utils';
import { showPopup, checkThreshold, currentEnergy, getEnergy } from '../DataHooks';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const showRule = ref(false);

const props = defineProps(['decorationInfo', 'moduleName']);
const decorationInfo = computed(() => props.decorationInfo);
const openCard = () => {
  if (preview) return;
  if (!checkThreshold()) return;
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};
if (!preview && !decorationInfo.value.hasAgree) {
  showPopup('rule', {
    hasAgree: decorationInfo.value.hasAgree,
    moduleName: props.moduleName,
  });
}
onMounted(() => {
  !preview && getEnergy();
});
</script>

<style scoped lang="scss">
.main {
  position: relative;
  height: 100%;
  overflow: hidden;
  min-height: 9rem;

  .bg {
    width: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
  }

  .content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;

    .info {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      padding-top: 6.6rem;

      .energyValue {
        font-size: 0.3rem;
      }

      .member_btn {
        font-size: 0.3rem;
        margin: 0.3rem auto;
        width: 2.62rem;
        height: 0.73rem;
        border-radius: 0.37rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .des {
        font-size: 0.18rem;
        width: 5.5rem;
        margin: 0 auto;
      }
    }

    .btn_group {
      position: absolute;
      top: 1.5rem;
      right: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      border-radius: 10px;
      text-align: center;

      .btn {
        width: 1.4rem;
        height: 0.46rem;
        background-color: #ffffff;
        border-radius: 0.23rem 0 0 0.23rem;
        font-size: 0.24rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
