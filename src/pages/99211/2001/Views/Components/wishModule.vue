<template>
  <div class="main">
    <img :src="decorationInfo.pageBg" alt="" class="bg" />
    <div class="content" :style="{ color: decorationInfo?.otherColor }">
      <div class="info">
        <div class="rule">{{ decorationInfo.ruleText }}</div>
        <div class="mainContent">
          <div class="prizeItem" v-for="(it, idx) in prizeList" :key="idx">
            <template v-if="it.prizeImg">
              <div class="left">
                <div class="box">
                  <img v-if="it.prizeImg" draggable="false" class="prizeItemBg" :src="it.prizeImg" alt="" />
                  <div
                    class="prizeItemText"
                    :style="{
                      color: decorationInfo?.prizeWishTextColor,
                      backgroundColor: decorationInfo?.prizeWishBtnColor,
                    }">
                    愿望{{ idx + 1 }}
                  </div>
                </div>
                <div class="reduce">奖品共计{{ it.sendTotalCount }}份</div>
              </div>
              <div class="right">
                <div class="contentBox">
                  <div class="des">
                    <div class="des_name">{{ it.prizeName }}</div>
                    <div class="des_count">已有{{ preview ? 'xx' : it.totalWishCount || 0 }}人许愿该奖品</div>
                  </div>
                  <div class="btnList">
                    <span
                      class="reduce"
                      @click="reduce(it)"
                      :style="{
                        color: decorationInfo?.addTextColor,
                        backgroundColor: decorationInfo?.addBtnColor,
                      }">
                      -
                    </span>
                    <span
                      class="count"
                      :style="{
                        color: decorationInfo?.highlightTextColor,
                      }">
                      {{ it.exchangeCount }}
                    </span>
                    <span
                      class="add"
                      @click="add(it)"
                      :style="{
                        color: decorationInfo?.addTextColor,
                        backgroundColor: decorationInfo?.addBtnColor,
                      }">
                      +
                    </span>
                  </div>
                </div>
                <div
                  class="btn"
                  @click="wish(it)"
                  :style="{
                    color: decorationInfo?.wishesTextColor,
                    backgroundColor: decorationInfo?.wishesBtnColor,
                  }">
                  提交愿望
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <div class="btn_group">
        <div
          :style="{
            color: decorationInfo?.dialogTextColor,
            backgroundColor: decorationInfo?.dialogBtnColor,
          }"
          class="btn"
          @click="
            showPopup('wishRecord', {
              wishRecord: decorationInfo.wishRecords,
              moduleName,
            })
          "
          v-click-track="'wdxyjl'">
          我的许愿记录
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { httpRequest } from '@/utils/service';
import { computed, onMounted, ref, watch } from 'vue';
import { preview } from '../../Utils';
import { showPopup, getEnergy, upDataDecoration, checkThreshold, currentEnergy } from '../DataHooks';
import { closeToast, showLoadingToast, showToast } from 'vant';

const props = defineProps(['decorationInfo', 'moduleName']);
const decorationInfo = computed(() => props.decorationInfo);

const prizeList = ref([]) as any;

const reduce = (item: any) => {
  if (+item.exchangeCount == 1) {
    showToast('不能再减了');
    return false;
  }
  item.exchangeCount = +item.exchangeCount - 1;
};

const add = (item: any) => {
  if (+decorationInfo.value.wishCounts == +item.exchangeCount + +item.myWishCount) {
    showToast('不能再加了');
    return false;
  }
  item.exchangeCount = +item.exchangeCount + 1;
};

const wish = async (item: any) => {
  if (preview) return;
  if (!checkThreshold()) return;
  const expensiveEnergyValue = +decorationInfo.value.energyValue * item.exchangeCount;
  if (+decorationInfo.value.wishCounts == +item.myWishCount) {
    showToast('许愿本奖品次数已达上限');
    return false;
  }
  if (+currentEnergy.value < expensiveEnergyValue) {
    showToast('能量值不足，赚取能量值后再来吧');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/99211/wish', {
      giftId: item.id, // 奖品id,
      wishCount: item.exchangeCount,
    });
    closeToast();
    showPopup('wishSuccess', {
      showStartTime: data.showStartTime,
      moduleName: props.moduleName,
    });
    await upDataDecoration(props.moduleName);
    await getEnergy();
  } catch (err: any) {
    showToast(err.message);
  }
};

onMounted(() => {
  if (!preview) {
    if (decorationInfo.value?.isShowMessage) {
      showToast('能量许愿已开奖，请前往【我的许愿记录】查看中奖情况');
    }
  }
});
watch(
  () => props.decorationInfo?.prizeList,
  (newList) => {
    prizeList.value = newList?.map((item: any) => ({
      ...item,
      exchangeCount: 1,
    }));
  },
  {
    deep: true,
    immediate: true,
  },
);
</script>

<style scoped lang="scss">
.main {
  position: relative;
  height: 100%;
  overflow: hidden;
  min-height: 11.1rem;

  .bg {
    width: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
  }

  .content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    padding-top: 2rem;

    .rule {
      width: 7rem;
      margin: 0 auto;
      overflow-y: scroll;
      height: 1.2rem;
      font-size: 0.24rem;
      white-space: pre-wrap;
      word-break: break-all;
    }

    .info {
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .mainContent {
        width: 6.5rem;
        height: 7rem;
        overflow: scroll;
        margin: 0.2rem auto;
        padding-top: 0.2rem;

        .prizeItem {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.25rem;
          width: 100%;

          .left {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-left: 0.2rem;

            .box {
              position: relative;

              .prizeItemBg {
                width: 1.6rem;
                height: 1.8rem;
              }

              .prizeItemText {
                position: absolute;
                top: -0.1rem;
                left: -0.1rem;
                width: 0.9rem;
                height: 0.3rem;
                font-size: 0.2rem;
                border-radius: 0.16rem;
              }
            }

            .reduce {
              font-size: 0.2rem;
            }
          }

          .right {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-left: 0.2rem;

            .contentBox {
              display: flex;
              flex-direction: column;
              text-align: left;

              .des {
                max-width: 2.4rem;

                .des_name {
                  font-size: 0.32rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                .des_count {
                  font-size: 0.18rem;
                  margin-top: 0.1rem;
                  margin-bottom: 0.2rem;
                }
              }

              .btnList {
                display: flex;

                .reduce {
                  font-size: 0.2rem;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  width: 0.42rem;
                  height: 0.35rem;
                  border-radius: 0.2rem 0 0 0.2rem;
                }

                .add {
                  font-size: 0.2rem;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  width: 0.42rem;
                  height: 0.35rem;
                  border-radius: 0 0.2rem 0.2rem 0;
                }

                .count {
                  font-size: 0.34rem;
                  padding: 0 0.25rem;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                }
              }
            }

            .btn {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 1.9rem;
              height: 0.6rem;
              background: #fff;
              color: #333;
              font-size: 0.25rem;
              border-radius: 0.3rem;
            }
          }
        }
      }
    }

    .btn_group {
      position: absolute;
      top: 0.5rem;
      right: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      border-radius: 10px;
      text-align: center;

      .btn {
        width: 1.8rem;
        height: 0.46rem;
        background-color: #ffffff;
        border-radius: 0.23rem 0 0 0.23rem;
        font-size: 0.24rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.1rem;
      }
    }
  }
}
</style>
