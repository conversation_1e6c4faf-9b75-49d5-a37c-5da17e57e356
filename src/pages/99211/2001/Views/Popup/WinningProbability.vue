<template>
  <div class="main" :style="{ backgroundImage: `url(${decoData.dialogBg})` }">
    <div
      class="title"
      :style="{
        color: decoData?.titleTextColor,
      }">
      中奖概率公示
    </div>
    <div
      class="content"
      :style="{
        color: decoData?.otherTextColor,
      }">
      {{ popupData?.winningProbability.ruleText }}
    </div>
  </div>
  <div class="close" @click="hidePopup('winningProbability')"></div>
</template>

<script lang="ts" setup>
import { hidePopup, popupData } from '../DataHooks';

const props = defineProps(['decoData']);
</script>

<style scoped lang="scss">
.main {
  width: 6rem;
  height: 6.78rem;
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 0.2rem 0.4rem;

  .title {
    font-size: 0.4rem;
    margin-bottom: 0.2rem;
    display: flex;
    justify-content: center;
    align-items: center;

    &:after,
    &:before {
      content: '';
      width: 0.04rem;
      height: 0.3rem;
      background-color: #f9c104;
      margin: 0 0.1rem;
    }
  }

  .content {
    width: 100%;
    overflow-y: auto;
    scrollbar-width: none;
    font-size: 0.2rem;
    height: 5.5rem;
    word-break:break-all;
    white-space: pre-wrap;
  }
}

.close {
  width: 0.6rem;
  height: 0.6rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/311774/5/3391/4556/68301533F5c43bf4b/ac7dac7b9c6e276e.png) no-repeat;
  background-size: 100% 100%;
  margin: 0.3rem auto 0;
}
</style>
