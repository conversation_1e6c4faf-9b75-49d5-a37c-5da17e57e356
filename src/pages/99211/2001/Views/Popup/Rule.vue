<template>
  <div class="main" :style="{ backgroundImage: `url(${decoData.dialogBg})` }">
    <div class="title" :style="{ color: decoData?.titleTextColor }">活动规则和隐私协议</div>
    <div
      class="content"
      :style="{
        color: decoData?.otherTextColor,
        height: popupData?.rule.hasAgree ? '5.5rem' : '3.5rem',
      }">
      {{ ruleTest }}
    </div>
    <div v-if="!popupData?.rule.hasAgree" class="agreeBox">
      <div class="agreeItem" @click="changeChecked">
        <img class="img-icon" :src="checked1 ? CHECKBOX_ICONS.checked : CHECKBOX_ICONS.unchecked" alt="" />
        <div
          :style="{
            color: decoData?.otherTextColor,
          }">
          我已同意并仔细阅读活动规则
        </div>
      </div>
      <div class="agreeItem" @click="changeCheckedDetail">
        <img class="img-icon" :src="checked2 ? CHECKBOX_ICONS.checked : CHECKBOX_ICONS.unchecked" alt="" />
        <div
          :style="{
            color: decoData?.otherTextColor,
          }">
          我已同意并仔细阅读隐私协议
        </div>
      </div>
    </div>
    <div v-if="!popupData?.rule.hasAgree" class="btn_group">
      <div
        class="btn"
        @click="agreeRule"
        :style="{
          color: decoData?.highlightBtnTextColor,
          backgroundColor: decoData?.highlightBtnBgColor,

        }">
        同意
      </div>
      <div
        class="btn"
        @click="goBack"
        :style="{
          color: decoData?.btnTextColor,
          backgroundColor: decoData?.btnBgColor,
        }">
        不同意
      </div>
    </div>
  </div>
  <div v-if="popupData?.rule.hasAgree" class="close" @click="hidePopup('rule')"></div>
</template>

<script lang="ts" setup>
import { inject, ref } from 'vue';
import { httpRequest } from '@/utils/service';
import { hidePopup, popupData, upDataDecoration } from '../DataHooks';
import { showToast } from 'vant';

const props = defineProps(['decoData']);
const checked1 = ref(false);
const checked2 = ref(false);

let ruleTest = inject('ruleText') as any;
const CHECKBOX_ICONS = {
  checked: '//img10.360buyimg.com/imgzone/jfs/t1/306149/11/4534/3337/683042d6F8895df91/4b43caf87274b5a8.png',
  unchecked: '//img10.360buyimg.com/imgzone/jfs/t1/288717/19/8993/3052/683042d6F9a7a4f67/61b5512b09182339.png',
};

const changeChecked = () => {
  checked1.value = !checked1.value;
};
const changeCheckedDetail = () => {
  checked2.value = !checked2.value;
};

const goBack = () => {
  window.jmfe.closeWebview().then(({ status, msg }) => {
    if (status === "0" || status === "0000") {
      //调用成功，即将关闭webview
      console.log(status, msg, "调用成功，即将关闭webview");
    }
  });
};

const agree = async () => {
  try {
    await httpRequest.post('/99211/agree');
    await upDataDecoration(popupData?.rule.moduleName);
  } catch (error) {
    console.error();
  }
};

// 同意
const agreeRule = async () => {
  if (checked1.value && checked2.value) {
    await agree();
    hidePopup('rule');
  } else {
    showToast('请先阅读并同意规则和隐私协议');
  }
};
</script>

<style scoped lang="scss">
.main {
  width: 6rem;
  height: 6.78rem;
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 0.2rem 0.4rem;

  .title {
    font-size: 0.4rem;
    margin-bottom: 0.2rem;
    display: flex;
    justify-content: center;
    align-items: center;

    &:after,
    &:before {
      content: '';
      width: 0.04rem;
      height: 0.3rem;
      background-color: #f9c104;
      margin: 0 0.1rem;
    }
  }

  .content {
    width: 100%;
    overflow-y: auto;
    scrollbar-width: none;
    font-size: 0.2rem;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .agreeBox {
    margin-top: 0.2rem;

    .agreeItem {
      margin-bottom: 0.1rem;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .img-icon {
        width: 0.3rem;
        height: 0.3rem;
        margin-right: 0.2rem;
      }

      div {
        font-size: 0.2rem;
      }
    }
  }
}

.btn_group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.2rem;

  .btn {
    width: 2.5rem;
    height: 0.68rem;
    line-height: 0.68rem;
    text-align: center;
    border-radius: 0.5rem;
  }
}

.close {
  width: 0.6rem;
  height: 0.6rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/311774/5/3391/4556/68301533F5c43bf4b/ac7dac7b9c6e276e.png) no-repeat;
  background-size: 100% 100%;
  margin: 0.3rem auto 0;
}
</style>
