<template>
  <div class="bg">
    <div class="header-img"></div>
    <div class="input-box">
      <span>【 </span>
      <input type="text" placeholder="请输入最多6个字" maxlength="6" v-model="nikeName" />
      <!-- <span>可输入6个字 </span> -->
      <span> 】</span>
    </div>
    <div class="next-button" @click="hancleNext"></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const emits = defineEmits(['hancleNext']);
const nikeName = ref('');
// 校验敏感词
const checkNickName = async () => {
  try {
    const data = await httpRequest.post('/1000014485/10001/checkSensitiveWord', { nick: nikeName.value });
    if (data.data) {
      emits('hancleNext', nikeName.value);
      httpRequest.post('1000014485/10001/bury/takePart');
    }
    console.log('data', data);
  } catch (error: any) {
    showToast('昵称中含有敏感词汇，请重新输入');
    console.log(error);
  }
};
// 生成我的龙年生活
const hancleNext = () => {
  console.log(nikeName.value);
  const reg = /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g;
  if (reg.test(nikeName.value)) {
    showToast('昵称不允许输入表情哦');
  } else if (nikeName.value.length < 1) {
    showToast('请您输入昵称');
  } else if (nikeName.value.length > 6) {
    showToast('昵称最多6个字哦');
  } else {
    checkNickName();
  }
};
</script>

<style lang="scss" scoped>
.bg {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/231946/31/13632/137078/65ba178eFc86ee67e/7102394d07f80aa2.png');
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 100vh;
  position: relative;
  padding-top: 0.5rem;
  background-position: center;
  .header-img {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/242503/23/4226/16820/65ba178bF7db372d7/8228e9a94fdf2b59.png');
    background-size: 100%;
    background-repeat: no-repeat;
    width: 4.68rem;
    height: 1.32rem;
    display: inline-block;
    margin-left: 1.1rem;
    margin-bottom: 1.5rem;
  }
  .input-box {
    width: 5.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff3e3;
    margin: 0 auto;
    // position: absolute;
    // top: 5rem;
    // left: 50%;
    // transform: translate(-50%);
    font-size: 0.5rem;
    margin-bottom: 3rem;
  }
  input {
    background: none;
    border: none;
    outline: none;
    width: 4.5rem;
    text-align: center;
    &::placeholder {
      color: #fff3e3;
      text-align: center;
    }
  }

  .next-button {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/98049/4/47750/33264/65ba01d9F8737620f/0b3078f271754210.png');
    background-size: 100%;
    background-repeat: no-repeat;
    width: 5.06rem;
    height: 1.27rem;
    margin: 0 auto;
  }
}
</style>
