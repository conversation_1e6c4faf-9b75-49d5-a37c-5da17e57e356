<template>
  <div class='bg' :style='furnishStyles.pageBg.value' v-if='isLoadingFinish'>

    <!-- 非会员点击遮罩层 -->
    <div class='no-member-mask' @click.stop='openPopup("noMemberPopup")' v-if='!baseInfo.levelQualify'></div>

    <div v-show='isShowHome'>
      <div class='header-kv select-hover'>
        <img :src="furnish.actBg ?? '//img10.360buyimg.com/imgzone/jfs/t1/246398/33/23985/2706/6731cd5aF3e1b3336/8e2b294dbd0f9ac8.png'" alt='' class='kv-img' />
        <div class='header-content'>
          <div class='header-btn' :style='furnishStyles.headerBtn.value' @click='showRulePopup()' v-click-track="'hdgz'">
            <div>活动规则</div>
          </div>
          <div class='header-btn' :style='furnishStyles.headerBtn.value' @click="openPopup('myPrizePopup')" v-click-track="'wdjp'">
            <div>我的奖品</div>
          </div>
          <div class='header-btn' :style='furnishStyles.headerBtn.value' @click='openTaskPopup()'>
            <div>补签任务</div>
          </div>
        </div>
      </div>

      <div class='calendar-bg' :style='furnishStyles.calendarBg.value'>
        <div class='sign-in-area' v-if='isSign'>
          <img :src='furnish.signInAfterIcon' alt='' class='icon' />
          <div class='text'>
            <p class='title'>今日已签到</p>
            <p class='tip'>真棒！再接再厉哦~</p>
          </div>
          <div class='btn'>
            <img :src='furnish.signInAfterBt' alt='' style='width: 1.5rem' />
            <div class='sign-days'>已累计签到{{ signDays }}天</div>
          </div>
        </div>
        <div class='sign-in-area' v-else>
          <img :src='furnish.signInBeforeIcon' alt='' class='icon' />
          <div class='text'>
            <p class='title title-before'>今日未签到</p>
            <p class='tip'>赶紧点击按钮签到哦~</p>
          </div>
          <div class='btn'>
            <img :src='furnish.signInBeforeBt' alt='' style='width: 1.5rem' @click='toSign()' v-click-track="'dwqd'" />
            <div class='sign-days'>累计签{{ signDays }}天</div>
          </div>
        </div>
        <div class='info'> {{ dayjs().format('MM月') }}</div>
        <Calendar :signList='signRecordDetailList' @selectSigned='toSign'></Calendar>
      </div>

      <div class='bg-view'>
        <img class='view-title' src='//img10.360buyimg.com/imgzone/jfs/t1/210303/13/47555/3433/6732f5a4F4fd8e92e/eb513be6960e6004.png' alt=''>
        <div class='gift-view' v-for='(item,index) in dayPrize' :key='index'>
          <img :src='item.prizeImg' style='width: 1rem' alt=''>
          <div class='gift-info'>
            <div>每日签到可得{{ item.prizeName }}</div>
            <div class='tip'>*积分自动发放，无需领取</div>
          </div>
        </div>
      </div>

      <div class='bg-view'>
        <img class='view-title' src='//img10.360buyimg.com/imgzone/jfs/t1/237781/29/27265/4373/6732f5a3F78e7f9bf/9d0a560d0630be3e.png' alt=''>
        <div class='gift-view' v-for='(item,index) in signPrize' :key='index'>
          <img :src='item.prizeImg' style='width: 1rem' alt=''>
          <div class='gift-info'>
            <div>累计签到{{ item.signDays }}天可得</div>
            <div class='gift-name'>({{ item.prizeName }})</div>
            <div class='gift-stock'>剩余{{ item.remainNum }}份</div>
          </div>
          <div class='draw-btn' :class='{gray:item.prizeStatus!==4}' @click='drawPrize(item)'>{{ getDrawBtnStatus(item) }}</div>
        </div>
      </div>

      <div class='bg-view'>
        <div class='gift-view' style='margin-top: 0'>
          <img src='//img10.360buyimg.com/imgzone/jfs/t1/242075/22/23135/3218/67330a81F3c2bfd87/62b61c0df6442b3c.png' style='width: 1rem' alt=''>
          <div class='gift-info'>
            <div>做任务得补签卡</div>
            <div class='tip'>当前可用补签卡张数：{{ repairSign }}</div>
          </div>
          <div class='draw-btn' @click='openTaskPopup()'>立即参与 ></div>
        </div>
      </div>
    </div>

    <Transition enter-active-class='animate__animated animate__fadeIn' mode='out-in'>
      <component :is='curComponent' :currentTask='currentTask' @canToggleComponent='handleToggleComponent' @backToHome='taskBackToHome' @doTask='doTask' v-if='!isShowHome'></component>
    </Transition>

    <VanPopup v-model:show='popupShow' :close-on-click-overlay='false'>
      <!-- 规则弹窗 -->
      <RulePopup :rule='ruleText' v-if="popupName === 'rulePopup'"></RulePopup>
      <!-- 我的奖品弹窗 -->
      <MyPrize v-if="popupName === 'myPrizePopup'" @saveAddress='toSaveAddress'></MyPrize>
      <!-- 领奖成功弹窗 -->
      <DrawSuccessPopup v-if="popupName === 'drawSuccessPopup'" :currentGift='currentGift' @saveAddress='toSaveAddress'></DrawSuccessPopup>
      <!-- 领奖失败弹窗 -->
      <DrawFailPopup v-if="popupName === 'drawFailPopup'"></DrawFailPopup>
      <!-- 非会员弹窗 -->
      <NoMemberPopup v-if="popupName === 'noMemberPopup'"></NoMemberPopup>
      <!-- 填写地址弹窗|查看地址 -->
      <AddressPopup v-if="popupName === 'addressPopup'" :prizeInfo='currentGift'></AddressPopup>
    </VanPopup>

    <VanPopup teleport='body' v-model:show='taskPopup' position='bottom'>
      <TaskPopup :taskList='taskList' :repairSign='repairSign' @searchTaskDetail='searchTaskDetail'></TaskPopup>
    </VanPopup>

    <!-- 保存地址弹窗 -->
    <!--      <SaveAddress v-if='showSaveAddress' :addressId='addressId' :activityPrizeId='activityPrizeId' @close='showSaveAddress = false'></SaveAddress>-->
  </div>
</template>

<script lang='ts' setup>
import 'animate.css';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from './ts/furnishStyles';
import { inject, reactive, ref, onMounted, nextTick, shallowRef } from 'vue';

import { popupShow, popupName, openPopup, closePopup } from './ts/popup';
import MyPrize from './components/MyPrize.vue';
import RulePopup from './components/RulePopup.vue';
import TaskPopup from './components/TaskPopup.vue';
import AddressPopup from './components/AddressPopup.vue';
import DrawFailPopup from './components/DrawFailPopup.vue';
import NoMemberPopup from './components/NoMemberPopup.vue';
import DrawSuccessPopup from './components/DrawSuccessPopup.vue';

import TaskPage from './components/TaskPage.vue';
import Calendar from './components/Calendar.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { IPrize, ITask, ITaskSkus } from './ts/type';
import { addSkuToCart, gotoSkuPage } from '@/utils/platforms/jump';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;

const isShowHome = ref(true); // 是否显示首页
const taskPopup = ref(false);
const isLoadingFinish = ref(false);

const isSign = ref(false); // 今日是否签到
const signDays = ref(0); // 签到总天数
const repairSign = ref(0); // 补签卡数量
const signRecordDetailList = ref([] as number[]); // 历史签到天
const prizeList = ref([]); // 奖品列表
const ruleText = ref('');

// 当前显示的组件
const curComponent = shallowRef(TaskPage);

// 处理切换组件
const handleToggleComponent = (componentName: string) => {
  console.log(componentName);
  // curComponent.value = componentList[componentName];
};

const getDrawBtnStatus = (prize: IPrize) => {
  switch (prize.prizeStatus) {
    case 1:
      return '未达标';
    case 2:
      return '已领取';
    case 3:
    case 4:
      return '立即领取 ＞';
    default:
      return '';
  }
};

// 获取签到信息
const getCalendar = async () => {
  try {
    const { data } = await httpRequest.post('/99203/calendar', { shareId: pathParams.shareIdn, helpType: pathParams.helpType });
    data.signRecordDetailList.forEach((item: any, index: number) => {
      const time = dayjs(item.dayTime).format('YYYYMMDD');
      data.signRecordDetailList[index].dayTime = Number(time);
    });
    isSign.value = data.sign;
    signDays.value = data.signDays;
    repairSign.value = data.signDetailMap.resignChance.leftChance;
    signRecordDetailList.value = data.signRecordDetailList;
    if (data.signDetailMap.errMsg) {
      setTimeout(() => {
        showToast(data.signDetailMap.errMsg);
      }, 500);
    }
  } catch (error: any) {
    console.error(error);
  }
};

const taskBackToHome = () => {
  isShowHome.value = true;
  taskPopup.value = true;
  // 刷新补签卡数量
  getCalendar();
};

const taskList = ref([]);
// 获取任务列表
const openTaskPopup = async () => {
  try {
    showLoadingToast({
      message: '',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/99203/getTaskList');
    closeToast();
    if (data?.length > 0) {
      taskList.value = data;
      taskPopup.value = true;
    } else if (data.message) {
      showToast(data.message);
    }
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }

};

const currentTask = ref({});
// 获取任务详情
const searchTaskDetail = async (taskId: string) => {
  try {
    const { data, message } = await httpRequest.post('/99203/getTaskDetail', { taskId });
    closeToast();
    if (data) {
      currentTask.value = data;
      taskPopup.value = false;
      isShowHome.value = false;
    } else {
      showToast(message);
    }
  } catch (error: any) {
    console.error();
    showToast(error.message);
  }
};

// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleText.value = data;
    }
    openPopup('rulePopup');
  } catch (error: any) {
    console.error();
  }
};

const jumpLinkUrl = (url?: string) => {
  if (url) {
    window.location.href = url;
  }
};

// 做任务
const doTask = async (task: ITask, taskDetail: ITaskSkus) => {
  try {
    const taskParams = {};
    if (task.taskType === 4) {
      taskParams.url = taskDetail.url;
    } else {
      taskParams.skuId = String(taskDetail.skuId);
    }
    if (task.taskType === 8) {
      await gotoSkuPage(taskDetail.skuId);
      return;
    }
    const { data } = await httpRequest.post('/99203/finishTask', { taskId: task.id, taskParams });
    closeToast();
    if (data) {
      // 刷新任务详情（更新数据状态）
      await searchTaskDetail(task.id);
      switch (task.taskType) {
        case 3:// 浏览商品
          await gotoSkuPage(taskDetail.skuId);
          break;
        case 7:// 加购商品
          await addSkuToCart(taskDetail.skuId);
          break;
        case 4:// 浏览会场
          await jumpLinkUrl(taskDetail?.url);
          break;
        default:
          break;
      }
    }
  } catch (error: any) {
    console.error();
    showToast(error.message);
  }
};

const currentGift = ref({});
// 保存实物地址相关
const addressId = ref('');
const activityPrizeId = ref('');
const toSaveAddress = (prize: IPrize) => {
  currentGift.value = prize;
  openPopup('addressPopup');
};

// 每日奖品数据
const dayPrize = ref([]);
const signPrize = ref([]);
// 获取礼品信息
const getPrizeList = async () => {
  try {
    const { data } = await httpRequest.post('/99203/getPrizeList');
    prizeList.value = data;
    dayPrize.value = data.filter((i: IPrize) => i.signType === 0);
    signPrize.value = data.filter((i: IPrize) => i.signType === 1);
  } catch (error: any) {
    console.error(error);
  }
};

// 签到
const toSign = async (signDate?: string) => {
  try {
    showLoadingToast({
      message: '',
      forbidClick: true,
      duration: 0,
    });
    if (signDate && repairSign.value <= 0) {
      showToast('补签失败\n补签卡数量不足，可参与补签任务获取');
      return;
    }
    const { data } = await httpRequest.post(`/99203/${signDate ? 'reSign' : 'sign'}`, { signDateOrigin: signDate ?? '' });
    closeToast();
    await Promise.all([getCalendar(), getPrizeList()]);
    if (data?.prizeType) {
      if (signDate) {
        showToast(`补签成功\n获得奖励${data.prizeName}`);
      } else {
        showToast(`签到成功\n获得每日签到奖励${data.prizeName}`);
      }
    } else if (signDate) {
      showToast('补签成功');
    } else {
      showToast('签到成功');
    }
  } catch (error: any) {
    await getCalendar();
    closeToast();
    showToast(error.message);
  }
};

// 领取奖品接口
const drawPrize = async (prize: IPrize) => {
  if (prize.prizeStatus === 3) {
    showToast('奖品已领光');
    return;
  }
  if (prize.prizeStatus === 4) {
    try {
      showLoadingToast({
        message: '',
        forbidClick: true,
        duration: 0,
      });
      currentGift.value = prize;
      const { data } = await httpRequest.post('/99203/receivePrize', { giftId: String(prize.id) });
      closeToast();
      if (data.giftRecordId) {
        currentGift.value = data.giftRecord;
        openPopup('drawSuccessPopup');
        await getPrizeList();
      }
    } catch (error: any) {
      closeToast();
      showToast(error.message);
    }
  }
};

const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getCalendar(), getPrizeList()]);
    isLoadingFinish.value = true;

    closeToast();
  } catch (error: any) {
    console.error(error);

    closeToast();
  }
};

init();
// onMounted(() => {
//
//   nextTick(() => {
//     // start: 实现沉浸式
//     const isIOS: boolean = window.jmfe?.isIOS?.() ?? false; // 使用可选链操作符和默认值
//     const params = {
//       canPull: '0',
//       supportTran: '1',
//       titleImgWidth: isIOS ? '300' : '100',
//       tranParams: {
//         naviMenuType: 'wb',
//         whiteImg: '',
//         blackImg: '',
//       },
//     };
//     window.jmfe.configNavigationBar(params)
//       .then((data: any) => {
//         console.log(data);
//       })
//       .catch((error: any) => {
//         console.log(error);
//       });
//     // end: 实现沉浸式
//   });
// });
</script>

<style scoped lang='scss'>
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.gray {
  color: #8b7c7c;
  filter: grayscale(100%);
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.no-member-mask {
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 9;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 2rem;
    right: 0;

    .header-btn {
      height: .41rem;
      line-height: .45rem;
      border-radius: .2rem 0 0 0.2rem;
      background-color: #CA8970;
      color: #FFFFFF;
      cursor: pointer;
      padding: 0 0.2rem;
      font-size: 0.19rem;
      text-align: center;
      margin-bottom: .15rem;
    }
  }
}

.calendar-bg {
  background-repeat: no-repeat;
  background-size: 100%;
  width: 6.9rem;
  height: 9.7rem;
  margin: 0.3rem auto 0;
  padding: 0.15rem;

  .sign-in-area {
    height: 2rem;
    display: flex;
    align-items: center;
    padding: 0 0.3rem;

    .icon {
      width: 1rem;
    }

    .text {
      flex: 1;
      padding-left: 0.38rem;

      .title {
        font-size: 0.38rem;
        font-weight: bold;
        color: #CA8970;
      }

      .title-before {
        color: #CA8970;
      }

      .tip {
        font-size: 0.22rem;
        color: #3C3C3C;
      }
    }

    .sign-days {
      font-size: .18rem;
      color: #CA8970;
      text-align: center;
    }
  }

  .info {
    width: .67rem;
    text-align: center;
    margin: 0.8rem 0 0 1.5rem;
    padding-bottom: .2rem;
    border-bottom: 4px solid #CA8970;
    color: #000000;
    font-size: 0.3rem;
  }
}

.bg-view {
  width: 6.73rem;
  margin: .35rem auto 0;
  border-radius: .2rem;
  background-color: #FFFFFF;
  padding: .45rem .36rem;

  .view-title {
    height: .38rem;
  }

  .gift-view {
    margin-top: .45rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .gift-info {
      padding-left: .35rem;
      color: #000000;
      font-size: .29rem;
      flex: 1;

      .tip {
        font-size: .22rem;
        color: #3C3C3C;
        margin-top: .13rem;
      }

      .gift-name {
        font-size: .22rem;
        color: #3C3C3C;
        margin-top: .13rem;
      }

      .gift-stock {
        font-size: .18rem;
        color: #3C3C3C;
        margin-top: .08rem;
      }
    }

    .draw-btn {
      width: 1.51rem;
      height: .45rem;
      line-height: .48rem;
      background-color: #CA8970;
      color: #FFFFFF;
      text-align: center;
      font-size: .2rem;
      border-radius: .22rem;
    }
  }
}
</style>
