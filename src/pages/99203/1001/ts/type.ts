export interface FormType {
  realName: string;
  mobile: string;
  province: string;
  city: string;
  county: string;
  address: string;
}

export interface IPrize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  prizeStatus: number;
  createTime: string;
  signDate: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  dealStatus: number;
  signDays: number;
  signType: number;
  realName: string;
  deliverName: string;
  deliverNo: string;
  id: string;
  activityPrizeId: string;
  addressId: string;
}

export interface IPrizeType {
  prizeType: number;
  prizeName: string;
  prizeImg: string;
  result: any;
  activityPrizeId: string;
  addressId: string;
  userPrizeId: string;
}

export interface ITaskSkus {
  jdPrice: number,
  skuId: number,
  skuMainPicture: string,
  skuName: string,
  status: number,
  img?: string,
  url?: string,
  urlName?: string,
}

export interface IHelpRecords {
  avatar: string,
  helpName: string,
}

export interface ITaskDeatil {
  taskSkus: ITaskSkus[];
  helpMeRecords: IHelpRecords[];
  shareId: string,
}

export interface ITask {
  id: string;
  dayLimit: number;
  hasGiveChance: number;
  giveChancePerTask: number;
  limitType: number;
  needTimesPerTask: number;
  taskDetail: ITaskDeatil | null;
  taskType: number;
  totalChanceLimit: number;
  totalLimit: number;
}
