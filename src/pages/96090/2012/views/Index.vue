<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <!--          {{ shopName }}-->
        </div>
        <div>
          <div class="header-btn" v-click-track="'hdgz'" :style="furnishStyles.headerBtnRules.value" @click="showRulePopup" />
          <div class="header-btn my-prizes" v-click-track="'wdjp'" :style="furnishStyles.headerBtnMyPrizes.value" @click="showMyPrizePop" />
        </div>
      </div>
    </div>
    <!--会员复购限定特权-->
    <div class="equitySelection" :style="furnishStyles.equitySelection.value">
      <div class="qualificationBg" :style="furnishStyles.qualificationBg.value">
        <!-- <div class="text" :style="furnishStyles.qualificationTextColor.value">
          {{ text }}
        </div> -->
        <div class="stepOne">
          <div class="beforeOrder" :class="{ gary: !hasOrdBefore }" :style="furnishStyles.preOrderBg.value">
            {{ dayjs(activityDataInfo.oldOrderStartTime).format('YYYY.M.D') }}-{{ dayjs(activityDataInfo.oldOrderEndTime).format('YYYY.M.D') }}
            <img v-if="hasOrdBefore" :src="furnish.orderFinishIcon" alt="" class="orderFinishIcon" />
          </div>
          <div class="afterOrder" :class="{ gary: !activityDataInfo.hasOrder }" :style="furnishStyles.postOrderBg.value">
            {{ dayjs(activityDataInfo.newOrderStartTime).format('YYYY.M.D') }}-{{ dayjs(activityDataInfo.newOrderEndTime).format('YYYY.M.D') }}
            <img v-if="activityDataInfo.hasOrder" :src="furnish.orderFinishIcon" alt="" class="orderFinishIcon" />
          </div>
          <img :src="furnish.orderIcon" class="and" alt="" />
        </div>
        <img v-if="!activityDataInfo.hasOrder" :src="furnish.orderUnfinishedIcon" alt="" class="orderUnfinishedIcon" />
        <div class="text" :style="furnishStyles.qualificationTextColor.value">
          {{ text }}
        </div>
        <!-- <div class="toBuyBtn" v-click-track="'ljgm'" @click="buyNow" /> -->
      </div>
      <div class="progressBarBg" :style="furnishStyles.progressBarBg.value">
        <div :style="furnishStyles.topTextColor.value">
          <div class="num">×{{ activityItemTotal }}</div>
          <div class="get">得</div>
          <div class="progressLineBox">
            <div class="progress">
              <div class="bubble" :style="{ width: progressWidth }">
                <div v-if="progressBar !== 0" class="point" />
              </div>
            </div>
            <div class="rate">{{ itemTotal > activityItemTotal ? activityItemTotal : itemTotal }}/{{ activityItemTotal }}</div>
          </div>
          <img v-if="itemTotal < activityItemTotal" :src="furnish.orderUnfinishedIcon" alt="" class="orderUnfinishedIcon" />
        </div>
        <div class="bottomText" :style="furnishStyles.bottomTextColor.value">{{ activityDataInfo.orderStrokeCount === 1 ? `单笔订单` : `${orderNum}笔订单及以上` }}购买{{ orderSkuisExposure === 0 ? '全店' : '指定' }}商品任意{{ activityItemTotal }}件，订单完成24小时后更新进度。</div>
      </div>
      <img class="toBuyBtn" :src="furnish.equitySelectionToBug" alt="" v-click-track="'ljgm'" @click="buyNow" />
      <!-- <div class="orderTipsText" :style="furnishStyles.orderLimitTextColor.value">
               <div>*{{dayjs(oldOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss') }}~{{ dayjs(oldOrderEndTime).format('YYYY年MM月DD日 HH:mm:ss')}}在伊利牛奶京东自营旗舰店有已完成的订单 </div>
        <div>*{{ dayjs(oldOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss') }}~{{ dayjs(oldOrderEndTime).format('YYYY年MM月DD日 HH:mm:ss') }}在{{ shopName }}有已完成的订单</div>
        <div>*在活动期间复购下单，订单完成48h内可来领取权益</div>
      </div> -->
    </div>
    <!--限时惊喜福利-->
    <div class="timeLimitedPrizeBg" :style="furnishStyles.timeLimitedPrizeBg.value">
      <div class="circle" v-if="status === 1 || status === 2 || status === 6">
        <span v-if="status === 1 || status === 2">+{{ registrationPrizeList[0].prizeName }}</span>
        <span v-if="status === 6">剩余{{ registrationPrizeList[0].remainCount >= 0 ? registrationPrizeList[0].remainCount : 0 }}份</span>
      </div>
      <div :class="status === 3 || status === 5 ? 'timeLimitedShortText' : 'timeLimitedText'" :style="furnishStyles.timeLimitedTextColor.value">
        <div>
          <!--没有前置机订单-->
          <span class="fontLarge" v-if="status === 0">
            <br />
            您没有前置订单<br />
            不满足复购身份
          </span>
          <!-- 奖品已发完-->
          <span class="fontLarge" v-if="status === 3">
            很遗憾，当前奖品已发完<br />
            您未获得限时惊喜福利<br />
            直接去领取权益吧！
          </span>
          <!-- 领取成功-->
          <span class="fontLarge" v-if="status === 2">
            您已完成本月购买任务，<br />
            限时惊喜福利已发放至您的账户内,<br />
            感谢您的参与哦！
          </span>
          <!-- 未报名-->
          <span class="fontLarge" v-if="status === 6">
            为您准备1份惊喜福利<br />
            本月完成购买后<br />
            <span class="fontSmall">可以额外领取{{ registrationPrizeList[0].prizeName }}！</span>
          </span>
          <!-- 报名成功-->
          <span class="fontLarge" v-if="status === 5">
            您已报名成功，<br />
            快去选购商品吧！<br />
            <span style="font-size: 0.23rem">（奖品数量有限，先到先得哦!）</span>
          </span>
          <!--任务完成  可以领取-->
          <span class="fontLarge" v-if="status === 1">
            您已完成本月购买任务<br />
            限时惊喜福利领取后<br />
            24小时内到账<br />
            感谢您的参与哦<br />
          </span>
        </div>
      </div>
      <div class="timeLimitedBtnBg" v-if="status === 5 || status === 1 || status === 6" :style="furnishStyles.timeLimitedBtnBg.value">
        <div v-if="status === 5" v-click-track="'xgsp'" @click="buyNow">选购<br />商品</div>
        <div v-if="status === 1" v-click-track="'ljlq-xs'" @click="receiveRegistrationPrize(registrationPrizeList[0])">立即<br />领取</div>
        <div v-if="status === 6" v-click-track="'ljbm'" @click="registration">立即<br />报名</div>
      </div>
    </div>
    <!--权益选择-->
    <div class="hotZoneBox">
      <img class="hotZone" :src="furnish.prizeBg" alt="" />
      <!-- <div class="chooseTitle">
        <div class="box" :style="furnishStyles.choosePrizeTitleColor.value">
          <div class="chooseTitleTextBefore">{{ multiplePrizeNum }}</div>
          <div class="chooseTitleText">大权益任选其</div>
          <div class="chooseTitleTextAfter">{{ multiplePrizeCanReceiveNum }}</div>
        </div>
      </div> -->
      <div class="prizeBox">
        <div class="choosePrizes">
          <div class="choosePrizesBox">
            <div class="list-view" v-for="(item, index) in multiplePrizeList" :key="index">
              <div class="itemBg" :style="furnishStyles.prizeItemBg.value">
                <div class="equity_name">
                  <div>
                    <svg width="100%" height="100%" viewBox="0 0 300 300">
                      <defs>
                        <path id="semi" d="M55 100a50 25 0 1 1 225 0"></path>
                      </defs>
                      <use xlink:href="#semi" stroke="none" fill="none"></use>
                      <text text-anchor="middle" :style="[furnishStyles.prizeItemTitleColor.value, getFontSizeStyle(item.prizeName.length)]">
                        <textPath xlink:href="#semi" startOffset="50%">
                          {{ item.prizeName }}
                        </textPath>
                      </text>
                    </svg>
                  </div>
                </div>
                <img class="equity_img" :src="item.prizeImg ? item.prizeImg : '//img10.360buyimg.com/imgzone/jfs/t1/176585/24/10488/6916/60a4cb50E562734ab/f9ab956ec09a4146.png'" alt="" />
                <div class="equity_num">{{ item.remainCount }}</div>
              </div>
              <div v-if="item.status === 1" class="equity_btn" v-click-track="'ljlq-qy'" @click="getPrize(item)" :style="furnishStyles.getPrizeBtn.value">立即领取</div>
              <div v-else class="equity_btn noJurisdiction" v-click-track="'ljlq-qy'" :style="furnishStyles.getPrizeBtn.value" @click="getPrize(item)">立即领取</div>
              <img v-if="item.status === 3" src="//img10.360buyimg.com/imgzone/jfs/t1/253763/29/25701/5447/67bd5c6fF5d476128/0b36925731a17021.png" alt="" class="no-stock" />
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="equity_tips">温馨提示：权益兑换会有专属的兑奖码，请正确输入您的手机号等信息，确保无误。 以免充值错误。兑换手机号需确保已注册平台账号，否则激活失败不负责补偿。 兑奖码有效期为xxxx-xx-xx~xxxx-xx-xx，过期无效，请及时兑换。</div> -->
    </div>
    <!--曝光商品-->
    <div ref="skuTitle" class="sku" v-if="skuList.length">
      <img :src="furnish.showSkuBg" class="sku-list-img" alt="" />
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuList" :key="index" @click="gotoSkuPage(item.skuId)">
          <img :src="item.showSkuImage" alt="">
          <div class="sku-price">
            {{ item.jdPrice }}
          </div>
        </div>
      </div>
    </div>
    <!-- 活动门槛 -->
    <Threshold :showPopup="showLimit" @closeDialog="showLimit = false" :canNotCloseJoin="furnish.canNotCloseJoinPopup" :data="baseInfo?.thresholdResponseList" />
    <!-- 非会员拦截 -->
    <OpenCard :showPopup="showOpenCard" @closeDialog="showOpenCard = false" :canNotCloseJoin="furnish.canNotCloseJoinPopup" />
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false" />
    </VanPopup>
    <!--我的奖品-->
    <VanPopup teleport="body" v-model:show="showMyPrize">
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone" />
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress">
      <SaveAddress :addressId="addressId" @close="closeAddress" />
    </VanPopup>
    <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="isShowConfirmPopup">
      <GiftConfirm :giftInfo="giftItem" @close="isShowConfirmPopup = false" :multiplePrizeNum="multiplePrizeNum" :multiplePrizeCanReceiveNum="multiplePrizeCanReceiveNum" @upData="upDataInit" @drawSuccess="drawSuccessFun" />
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { computed, inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { callShare } from '@/utils/platforms/share';
import constant from '@/utils/constant';
import { gotoSkuPage } from '@/utils/platforms/jump';
import RulePopup from '../components/RulePopup.vue';
import Threshold from '../components/Threshold.vue';
import GiftConfirm from '../components/GiftConfirm.vue';
import OpenCard from '../components/OpenCard.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import dayjs from 'dayjs';
import MyPrize from '../components/MyPrize.vue';
import { defaultLadderPrizeList, defaultMultiplePrizeList, defaultStateList } from '../ts/default';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const endTime = ref(0);
const isStart = ref(false);
const isEnd = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
  if (now > endTime.value) {
    isEnd.value = true;
  }
  if (now < endTime.value) {
    isEnd.value = false;
  }
};
// 店铺名称
const shopName = ref(baseInfo.shopName);
// 门槛弹窗
const showOpenCard = ref(false);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 我的奖品弹窗
const showMyPrize = ref(false);
// 单次领取弹窗
const isShowConfirmPopup = ref(false);
// 曝光商品
const showGoods = ref(false);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
  buyTimes: number;
};
const ladderPrizeList = ref<Prize>([]);
ladderPrizeList.value = defaultLadderPrizeList;
const registrationPrizeList = ref<Prize>([defaultStateList]);
const status = ref(0);
const multiplePrizeList = ref<Prize>([]);
multiplePrizeList.value = defaultMultiplePrizeList;
const Size = [
  {
    10: '22px',
  },
  {
    9: '25px',
  },
  {
    8: '27px',
  },
  {
    7: '30px',
  },
  {
    6: '30px',
  },
  {
    5: '30px',
  },
  {
    4: '30px',
  },
  {
    3: '30px',
  },
  {
    2: '30px',
  },
  {
    1: '30px',
  },
];
const fontSizeCache = new Map<number, string>();

const FindFontSize = (length: number) => {
  if (!length || length === 0) {
    return null;
  }
  if (fontSizeCache.has(length)) {
    return fontSizeCache.get(length);
  }
  const item = Size.find((obj) => Object.keys(obj).includes(length.toString()));
  if (item) {
    const [key, value] = Object.entries(item)[0];
    fontSizeCache.set(length, value?.toString());
    console.log(value, 'value');
    return value;
  }
  return null;
};

const getFontSizeStyle = (length: number) => ({
  fontSize: FindFontSize(length),
});

// 进度条
const progressBar = ref(0);
const progressWidth = ref('');

const giftItem = ref({});
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const skuTitle = ref();
const buyNow = () => {
  skuTitle.value.scrollIntoView({ behavior: 'smooth' });
};
// 展示门槛显示弹框
const showLimit = ref(false);

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const userReceiveRecordId = ref('');

// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error: any) {
    console.error();
  }
};
const showMyPrizePop = () => {
  showMyPrize.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  id: 1,
  prizeName: '',
  prizeImg: '',
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  exchangeImg: '',
});
// 展示卡密
const showCardNum = (distribute: any) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = distribute[item];
  });
  copyCardPopup.value = true;
};
// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}
const activityDataInfo = ref<any>({});
const activityGiftRecords = reactive([] as ActivityGiftRecord[]);
// 多选奖品可领取数量
const multiplePrizeCanReceiveNum = ref(0);
// 多选奖品数量
const multiplePrizeNum = ref(0);
// 活动要求购买件数
const activityItemTotal = ref(0);
// 订单笔数
const orderNum = ref(0);
// 订单商品  0 全店 1 指定 2 排除
const orderSkuisExposure = ref(0);
// 已购数量
const itemTotal = ref(0);
const oldOrderEndTime = ref();
const oldOrderStartTime = ref();
const hasOrdBefore = ref(false);
const text = computed(() => (hasOrdBefore.value ? `您在${dayjs(oldOrderStartTime.value).format('YYYY年MM月DD日')}~${dayjs(oldOrderEndTime.value).format('YYYY年MM月DD日')}有购买记录，复购获得特权资格吧！` : `您在${dayjs(oldOrderStartTime.value).format('YYYY年MM月DD日')}~${dayjs(oldOrderEndTime.value).format('YYYY年MM月DD日')}无购买记录，未获得特权资格。`));

// 获取阶梯奖品
const getLadderPrizeInfo = async () => {
  try {
    const { data } = await httpRequest.post('/96090/ladderPrizeInfo');
    ladderPrizeList.value = data;
  } catch (error: any) {
    console.error(error);
  }
};

// 主接口获取信息
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/96090/activity');
    hasOrdBefore.value = data.hasOrdBefore;
    activityDataInfo.value = data;
    if (!hasOrdBefore.value) {
      if (furnish.isShowJump && furnish.jumpUrl) {
        window.location.href = furnish.jumpUrl;
        return;
      }
      showLimit.value = true;
    }
    data.multiplePrize.forEach((item: any) => {
      item.remainCount = item.remainCount >= 0 ? item.remainCount : 0;
    });
    registrationPrizeList.value = data.registrationPrizeList;
    status.value = data.registrationPrizeList[0].status;
    multiplePrizeList.value = data.multiplePrize;
    multiplePrizeCanReceiveNum.value = data.multiplePrizeCanReceiveNum;
    multiplePrizeNum.value = data.multiplePrizeNum;
    activityItemTotal.value = data.activityItemTotal;
    orderNum.value = data.orderStrokeStatus.toString();
    itemTotal.value = data.itemTotal ?? 0;
    oldOrderEndTime.value = data.oldOrderEndTime;
    oldOrderStartTime.value = data.oldOrderStartTime;
    progressBar.value = data.progressBar;
    progressWidth.value = `${data.progressBar * 100}%`;
    orderSkuisExposure.value = data.orderSkuisExposure;
    // progressBar.value = 0.6;
    // progressWidth.value = '60%';
  } catch (error: any) {
    console.error(error);
  }
};
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

// 获取曝光商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/96090/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 20,
    });
    skuList.value = data.records;
    total.value = data.total;
    pagesAll.value = data.pages;
  } catch (error: any) {
    console.error(error);
  }
};
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivityInfo(), getLadderPrizeInfo(), getSkuList()]);
    closeToast();
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 1 || baseInfo.thresholdResponseList[0]?.thresholdCode === 2) {
      showLimit.value = true;
    }
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
      showOpenCard.value = true;
      console.log('非会员');
      return;
    }
  } catch (error: any) {
    closeToast();
  }
};

const upDataInit = async () => {
  await Promise.all([getActivityInfo(), getLadderPrizeInfo(), getSkuList()]);
};

// 报名接口
const registration = async (val) => {
  try {
    getTime();
    if (!isStart.value) {
      showToast('活动未开始~');
      return;
    }
    if (isEnd.value) {
      showToast('活动已结束~');
      return;
    }
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data, code } = await httpRequest.post('/96090/registration');
    console.log(data);
    closeToast();
    if (code === 200) {
      showToast('报名成功~');
    }
    upDataInit();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

// 领奖接口(限时奖品&阶梯)
const receiveRegistrationPrize = async (val) => {
  try {
    getTime();
    if (!isStart.value) {
      showToast('活动未开始~');
      return;
    }
    if (isEnd.value) {
      showToast('活动已结束~');
      return;
    }
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/96090/receiveRegistrationPrize', { prizeId: val.prizeId });
    closeToast();
    if (data.distribute.status) {
      showToast('领取成功~');
      upDataInit();
    }
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

// 校验领奖资格
const checkQualification = (val) => {
  if (!isStart.value) {
    showToast('活动未开始~');
    return false;
  }
  if (isEnd.value) {
    showToast('活动已结束~');
    return false;
  }
  if (val.status === 0) {
    showToast('您不符合活动条件，无法领取~');
    return false;
  }
  if (val.status === 2) {
    showToast('您已领取过该奖品~');
    return false;
  }
  if (val.status === 3) {
    showToast('手慢了，奖品已领光~');
    return false;
  }
  if (val.status === 4) {
    showToast('领取数量已经达到上限~');
    return false;
  }
  return val.status === 1;
};

// 领取各个奖品判断
const getPrize = async (prize: any) => {
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 1501) {
    showLimit.value = true;
    return;
  }
  // 校验资格
  if (!checkQualification(prize)) {
    return;
  }
  giftItem.value = prize;
  isShowConfirmPopup.value = true;
};

// 确认领取弹窗的回调
const drawSuccessFun = async () => {
  isShowConfirmPopup.value = false;
};

const showSavePhone = (id: string, desc: string) => {
  userReceiveRecordId.value = id;
  planDesc.value = desc;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

const closeAddress = () => {
  showSaveAddress.value = false;
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style>
@font-face {
  font-family: 'FZZZHJTFont';
  src: url('../style/fzzzhjt.ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'FZZZHJTFont';
  letter-spacing: 0px;
}
</style>
<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  margin-bottom: 1.2rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.28rem;
    height: 0.45rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .my-prizes {
    position: relative;
    top: 1.9rem;
    right: 0.2rem;
    width: 0.8rem;
    height: 0.85rem;
    z-index: 100;
  }
}
.equitySelection {
  width: 7.5rem;
  height: 7.22rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  .qualificationBg {
    width: 7.5rem;
    height: 1.61rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
    position: absolute;
    top: 2.1rem;
    left: 0;
    right: 0;
    padding: 0.5rem 0 0 1.3rem;
    .text {
      padding-top: 0.1rem;
      font-size: 0.15rem;
      font-stretch: normal;
      letter-spacing: 0;
      text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;
    }
    .toBuyBtn {
      width: 1rem;
      height: 1rem;
      border-radius: 100%;
      margin: 0 auto;
      background-repeat: no-repeat;
      background-size: 100%;
      position: absolute;
      top: 0.4rem;
      right: 0.5rem;
    }
    .stepOne {
      display: flex;
      align-items: center;
      position: relative;
      width: fit-content;
      .beforeOrder,
      .afterOrder {
        position: relative;
        width: 2.7rem;
        height: 0.58rem;
        background-repeat: no-repeat;
        background-size: 100%;
        font-size: 0.19rem;
        line-height: 0.58rem;
        padding: 0 0.1rem;
        .orderFinishIcon {
          position: absolute;
          top: -0.35rem;
          width: 0.88rem;
          left: 0.6rem;
        }
      }
      .beforeOrder {
        margin-right: -0.2rem;
      }
      .afterOrder {
        text-align: right;
      }
      .gary {
        filter: grayscale(0.8);
      }
      .and {
        position: absolute;
        top: -0.1rem;
        left: 50%;
        transform: translateX(-50%);
        width: 0.7rem;
      }
    }
    .orderUnfinishedIcon {
      position: absolute;
      top: 0.4rem;
      right: 0.2rem;
      width: 1.3rem;
    }
  }
  .progressBarBg {
    width: 7.5rem;
    height: 2.43rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
    position: absolute;
    top: 3.65rem;
    left: 0;
    right: 0;
    .num {
      position: relative;
      margin: 0 auto;
      text-align: center;
      top: 0.45rem;
      font-size: 0.3rem;
    }
    .get {
      position: relative;
      margin: 0 auto;
      text-align: center;
      top: 0.6rem;
      font-size: 0.3rem;
    }
    .progressLineBox {
      display: flex;
      margin: 0 auto;
      width: 6rem;
      position: relative;
      top: 0.6rem;
      .progress {
        background: url(//img10.360buyimg.com/imgzone/jfs/t1/167858/31/38810/7626/66e4f188F4067c97f/c71efac3074ef1f3.png) no-repeat;
        background-size: 100%;
        width: 5.58rem;
        height: 0.23rem;
        position: relative;
        transform: translateY(25%);
        .bubble {
          background: url(//img10.360buyimg.com/imgzone/jfs/t1/37594/26/24692/1341/66eb9924Fba700108/a141ab2ae35e4d8b.png) no-repeat;
          background-size: 100% 100%;
          margin-top: 0.01rem;
          height: 0.23rem;
          position: relative;
          border-radius: 0.4rem;
        }
        .point {
          background: url(//img10.360buyimg.com/imgzone/jfs/t1/244014/34/19491/1043/66eb9fcfF008f5122/4dbe781631527f85.png) no-repeat;
          background-size: 100%;
          width: 0.23rem;
          height: 0.23rem;
          position: absolute;
          right: 0;
        }
      }
      .rate {
        text-align: right;
        font-size: 0.24rem;
        margin: 0 0 0 0.1rem;
      }
    }
    .orderUnfinishedIcon {
      position: absolute;
      top: 0.4rem;
      right: 0.2rem;
      width: 1.3rem;
    }
    .bottomText {
      position: relative;
      margin: 0 auto;
      text-align: center;
      top: 0.45rem;
      font-size: 0.19rem;
      line-height: 0.6rem;
      text-shadow: -1px -1px 0 #fff, /* Top-left shadow */ 1px -1px 0 #fff, /* Top-right shadow */ -1px 1px 0 #fff, /* Bottom-left shadow */ 1px 1px 0 #fff; /* Bottom-right shadow */
    }
  }
  .orderTipsText {
    width: 7.5rem;
    height: 0.6rem;
    position: absolute;
    bottom: 0.4rem;
    font-size: 0.16rem;
    padding: 0 0.5rem;
  }
  .toBuyBtn {
    width: 1.8rem;
    position: absolute;
    top: 6rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
.timeLimitedPrizeBg {
  width: 7.5rem;
  height: 3.9rem;
  margin: 0.2rem auto;
  background-repeat: no-repeat;
  background-size: 100%;
  position: relative;
  .circle {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/186791/36/48536/6429/67052e84F806b21d7/ef2aba331f635d86.png) no-repeat;
    background-size: 100% 100%;
    width: 0.72rem;
    height: 0.72rem;
    color: #0069d0;
    font-size: 0.13rem;
    text-align: center;
    line-height: 0.15rem;
    word-break: break-all;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 0.06rem;
    position: absolute;
    top: 1.15rem;
    left: 1.65rem;
  }
  .timeLimitedText {
    position: absolute;
    top: 1.45rem;
    left: 2.7rem;
    line-height: 0.4rem;
    .fontLarge {
      font-size: 0.28rem;
    }
    .fontSmall {
      font-size: 0.23rem;
    }
  }
  .timeLimitedShortText {
    position: absolute;
    top: 1.4rem;
    left: 2.5rem;
    line-height: 0.4rem;
    .fontLarge {
      font-size: 0.32rem;
    }
    .fontSmall {
      font-size: 0.23rem;
    }
  }
  .timeLimitedBtnBg {
    background-size: 100% 100%;
    width: 1.5rem;
    height: 1.54rem;
    position: absolute;
    top: 1.5rem;
    right: 0.5rem;
    text-align: center;
    box-sizing: border-box;
    padding-top: 0.3rem;
    /* line-height: 1.2; */
    font-size: 0.24rem;
  }
}
.hotZoneBox {
  width: 7.5rem;
  margin: 0 auto;
  position: relative;
  top: 0.2rem;
  .hotZone {
    width: 7.5rem;
  }
  .chooseTitle {
    position: absolute;
    top: 0.6rem;
    left: 50%;
    transform: translate(-50%);
    color: #fff;
    line-height: 0.6rem;
    box-sizing: border-box;
    white-space: nowrap;
    display: inline-block;
    width: auto;
    margin: 0 0 0 0.04rem;
    .box {
      display: flex;
      .chooseTitleText {
        margin: 0.08rem 0 0;
        font-size: 0.4rem;
        font-family: FZZZHJTFont, serif;
      }
      .chooseTitleTextBefore {
        font-size: 0.6rem;
        font-family: FZZZHJTFont, serif;
        width: auto;
        white-space: nowrap;
      }
      .chooseTitleTextAfter {
        margin: 0.08rem 0 0;
        font-size: 0.4rem;
        font-family: FZZZHJTFont, serif;
        width: auto;
        white-space: nowrap;
      }
    }
  }
  .prizeBox {
    position: absolute;
    top: 1rem;
    left: 50%;
    transform: translate(-50%);

    .choosePrizes {
      width: 6.7rem;
      background-repeat: no-repeat;
      background-size: 100%;
      margin: 0.1rem auto 0;
      .prizeLimit {
        text-align: center;
        height: 1rem;
        line-height: 1rem;
        margin-top: 1.3rem;
        color: #f0f8ff;
      }
      .choosePrizesBox {
        margin: 0 auto;
        width: 6.9rem;
        height: auto;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
        grid-gap: 0.1rem;
        box-sizing: border-box;
        overflow: hidden;
        .list-view {
          width: 100%;
          height: auto;
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
          grid-gap: 0.1rem;
          box-sizing: border-box;
          overflow: hidden;
          margin-bottom: 0.05rem;
          position: relative;
          .itemBg {
            width: 2.05rem;
            height: 2.97rem;
            border-radius: 0.25rem;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            color: #fff;
            padding: 0.13rem;
            // box-sizing: border-box;
            text-align: center;
            // display: flex;
            // align-items: center;
            // flex-direction: column;
            // justify-content: center;
            border: 1px solid hwb(0deg 100% 0/27%);
            position: relative;
            margin-bottom: 0.1rem;
            .equity_img {
              width: 1.5rem;
              height: 1.5rem;
              position: absolute;
              left: 50%;
              transform: translate(-50%);
              top: 0.7rem;
            }
            .equity_name {
              background-repeat: no-repeat;
              // height: 0.6rem;
              background-size: contain;
              position: absolute;
              top: 0;
              left: -0.2rem;
              right: 0;
              font-size: 0.22rem;
              line-height: 0.65rem;
              // 单行超出展示...
              word-break: break-all;
              display: -webkit-box;
              overflow: hidden;
              text-overflow: ellipsis;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }
            .equity_num {
              position: absolute;
              left: 1.2rem;
              bottom: 0.15rem;
              font-size: 0.26rem;
              text-shadow: 0 0.03rem 0.04rem rgba(0, 0, 0, 0.5);
            }
          }
          .no-stock {
            position: absolute;
            top: 1.2rem;
            left: 0.4rem;
            width: 1.3rem;
          }
          .equity_btn {
            width: 1.62rem;
            height: 0.5rem;
            border-radius: 0.22rem;
            background-repeat: no-repeat;
            background-size: cover;
            font-size: 0.24rem;
            line-height: 1;
            z-index: 99;
            margin: 3.05rem 0 0 -0.95rem;
            color: #c88e2c;
            text-align: center;
            line-height: 0.45rem;
            position: relative; /* 确保伪元素定位相对于按钮 */
          }
          .noJurisdiction {
            filter: grayscale(1);
          }
          // .equity_btn::after {
          //   content: '';
          //   position: absolute;
          //   top: 0;
          //   left: 0;
          //   width: 100%;
          //   height: 100%;
          //   background-color: rgba(0, 0, 0, 0.5); /* 半透明灰色 */
          //   border-radius: 0.22rem; /* 与按钮的圆角保持一致 */
          //   pointer-events: none; /* 确保遮罩层不会阻止点击事件 */
          // }
        }
      }
    }
  }
  .equity_tips {
    position: absolute;
    bottom: 0.35rem;
    left: 50%;
    transform: translate(-50%);
    width: 6rem;
    font-size: 0.16rem;
    color: #fff;
    line-height: 0.3rem;
    text-align: left;
  }
}
.sku {
  width: 7.5rem;
  padding: 0.2rem 0;
  margin: 0rem auto;
  .sku-list-img {
    width: 7.12rem;
    height: auto;
    margin: 0 auto;
  }
  .sku-list {
    width: 7.25rem;
    margin: 0 auto;
    top: 1.9rem;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .sku-item {
    width: 3.6rem;
    overflow: hidden;
    position: relative;
    img{
      width: 3.6rem;
    }
    .go-sku-btn {
      position: absolute;
      bottom: 0.35rem;
      left: 1.85rem;
      width: 1.6rem;
      height: 0.53rem;
      //background-color: aliceblue;
    }
    .sku-price {
      position: absolute;
      font-size: 0.33rem;
      bottom: 0.7rem;
      left: 0.65rem;
      text-align: center;
      margin-top: 0.2rem;
      background: linear-gradient(to bottom, #fdf0d0, #f3c03e);/* 从上到下的渐变颜色 */
      -webkit-background-clip: text;/* 仅对文字应用背景 */
      -webkit-text-fill-color: transparent;/* 使文字本身透明 */
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
