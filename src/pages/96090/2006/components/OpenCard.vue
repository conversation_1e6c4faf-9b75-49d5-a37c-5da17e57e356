<template>
  <VanPopup teleport="body" v-model:show="isShowPopup" @click-overlay='closeDialog'>
    <div class="box">
      <div class='dialog'>
        <div class="confirm-btn" @click="openCard"/>
      </div>
    </div>
  </VanPopup>
</template>

<script setup lang='ts'>
import { computed, defineEmits, inject } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  canNotCloseJoin: {
    type: String,
    required: true,
    default: '1',
  },
});
const isShowPopup = computed(() => props.showPopup);

const openCard = async () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

const emits = defineEmits(['closeDialog']);

const closeDialog = () => {
  // B端配置1为可关闭，2为不可关闭
  if (props.canNotCloseJoin !== '2') {
    emits('closeDialog');
  }
};
</script>
<style lang='scss' scoped>
.box {

  .dialog {
    width: 6.67rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/245811/35/18022/316364/66dab206F419fc812/ee0ba5d53a2ab541.png) no-repeat;
    height: 8rem;
    background-size: contain;
    box-sizing: border-box;
    text-align: center;

    .confirm-btn{
      font-size: 0.26rem;
      position: absolute;
      width: 2rem;
      height: 0.67rem;
      top: 4.7rem;
      left: 2.33rem;
    }
  }

}

</style>
