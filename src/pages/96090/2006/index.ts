import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';
import { gotoErrorPage } from '@/utils/errorHandler';
// import './style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  backActRefresh: true,
  urlPattern: '/custom/:activityType/:templateCode',
};

const _decoData = {
  actBg: '',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/166924/33/49483/47044/67052e82Fc948f780/358c5f0e9eb52583.png',
  actBgColor: '#c0e3ff',
  shopNameColor: '#fff',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/182827/22/49312/5829/67052e89F116813de/8146fcc15c71bb13.png',
  myPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/200071/7/45232/3533/67052e84Faed99043/ccb78ed4e6cd0188.png',
  equitySelection: '//img10.360buyimg.com/imgzone/jfs/t1/226832/30/35803/69678/67b3f079Fe2446433/b4fe8a34e7baa892.png',
  qualificationBg: '//img10.360buyimg.com/imgzone/jfs/t1/263367/5/21043/47577/67b40170Fc29a3fe5/6ad68b3aeaa00aa9.png',
  qualificationTextColor: '#1979d6',
  progressBarBg: '//img10.360buyimg.com/imgzone/jfs/t1/229005/11/39238/30206/67b3f079F78e49332/b46e977a2a9a273e.png',
  topTextColor: '#eab434',
  equitySelectionToBug: '//img10.360buyimg.com/imgzone/jfs/t1/256977/4/21243/8718/67b3f079F9a28d823/82dc3bf050d420a5.png',
  // 前置订单背景图
  preOrderBg: '//img10.360buyimg.com/imgzone/jfs/t1/267043/25/20743/3189/67b3f0c7F53fbc862/a90cef1e6fb9696a.png',
  // 后置订单背景图
  postOrderBg: '//img10.360buyimg.com/imgzone/jfs/t1/262714/37/21365/8627/67b42011F3b869556/de316278b0623fd6.png',
  // 订单时间颜色
  orderTimeColor: '#fff',
  // 前后订单中间Icon
  orderIcon: '//img10.360buyimg.com/imgzone/jfs/t1/236168/36/33862/3848/67b3f07aFb1197d36/092c4ecb8b658940.png',
  // 已完成订单Icon
  orderFinishIcon: '//img10.360buyimg.com/imgzone/jfs/t1/254763/19/22326/2806/67b3f07aF7f5b4840/8957a02d7d73cad4.png',
  // 未达标Icon
  orderUnfinishedIcon: '//img10.360buyimg.com/imgzone/jfs/t1/228730/16/37233/3817/67b3f079F6bcd4d3e/de00052ca0b958a0.png',
  bottomTextColor: '#1b79d5',
  orderLimitTextColor: '#c6dbf9',
  timeLimitedPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/93879/28/52762/59714/67062112F1fbf70c0/07e7854d26dd4d2d.png',
  timeLimitedTextColor: '#0069d0',
  timeLimitedBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/248033/22/20445/22719/67052e84F1ac68fc5/b33dc52022c9ada1.png',
  timeLimitedBtnTextColor: '#ffffff',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/244374/2/20832/39571/67065479F55583a34/e0ee81b9d4cecbba.png',
  prizeItemBg: '//img10.360buyimg.com/imgzone/jfs/t1/167432/30/47772/8823/66f8ae43Fb0617e57/7a9267c696866378.png',
  prizeItemTitleColor: '#ae7116',
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/184337/26/49263/6105/67052e89Fcac9ba65/2a2ca0f28ed6667b.png',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/97932/28/50157/1531062/67052e89F523e8ff4/088078ae93bc7576.png',
  priceColor: '#f6ce69',
  jumpUrl: '',
  isShowJump: true,
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/162797/1/47417/46510/67052e80F511a12a0/1119ca064f142b16.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/244185/5/19965/24164/67052e80F7db94c35/d12bcd2dd53794a4.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/248902/31/20523/48764/67052e80F656a482e/6f42063ab1daf9fd.png',
  canNotCloseJoinPopup: '1',
  hotZoneList: [],
};

init(config).then(({ baseInfo, pathParams, decoData }) => {
  // 设置页面title
  document.title = baseInfo?.activityName || '复购有礼';
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', decoData);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.mount('#app');
});
