import { computed, reactive } from 'vue';

export const furnish = reactive({
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 活动规则
  ruleBg: '',
  // 我的奖品
  myPrizeBg: '',
  // 奖励区域背景图
  prizeBg: '',
  // 资格提示字体颜色
  isAccordColor: '',
  // 门槛金额颜色
  thresholdColor: '',
  // 品牌旗舰专区
  branZone: '',
  // 订单限制提示字体颜色
  orderLimitTextColor: '',
  // N选M区域单个礼品背景
  prizeItemBg: '',
  // N选M区域单个礼品标题颜色
  prizeItemTitleColor: '',
  // N选M区域领取奖品按钮
  getPrizeBtn: '',
  // 参与活动商品背景
  showSkuBg: '',
  // 价格颜色
  priceColor: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  canNotCloseJoinPopup: '',
  hotZoneList: [],
  jumpUrl: '',
  isShowJump: true,
  btnToTop: '',
  footerIsOpen: '',
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));
const headerBtnRules = computed(() => ({
  backgroundImage: furnish.ruleBg ? `url("${furnish.ruleBg}")` : '',
}));
const headerBtnMyPrizes = computed(() => ({
  backgroundImage: furnish.myPrizeBg ? `url("${furnish.myPrizeBg}")` : '',
}));
const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));
const prizeBg = computed(() => ({
  backgroundImage: furnish.prizeBg ? `url("${furnish.prizeBg}")` : '',
}));
const isAccordColor = computed(() => ({
  color: furnish.isAccordColor ?? '',
}));
const thresholdColor = computed(() => ({
  color: furnish.thresholdColor ?? '',
}));
const orderLimitTextColor = computed(() => ({
  color: furnish.orderLimitTextColor ?? '',
}));
// const branZone = computed(() => ({
//   backgroundImage: furnish.branZone ? `url("${furnish.branZone}")` : '',
// }));
const prizeItemBg = computed(() => ({
  backgroundImage: furnish.prizeItemBg ? `url("${furnish.prizeItemBg}")` : '',
}));
const prizeItemTitleBg = computed(() => ({
  // backgroundImage: furnish.prizeItemTitleBg ? `url("${furnish.prizeItemTitleBg}")` : '',
  fill: furnish.prizeItemTitleColor ?? '',
}));
const prizeItemTitleColor = computed(() => ({
  color: furnish.prizeItemTitleColor ?? '',
}));
const getPrizeBtn = computed(() => ({
  backgroundImage: furnish.getPrizeBtn ? `url("${furnish.getPrizeBtn}")` : '',
}));
const showSkuBg = computed(() => ({
  backgroundImage: furnish.showSkuBg ? `url("${furnish.showSkuBg}")` : '',
}));
const priceColor = computed(() => ({
  color: furnish.priceColor ?? '',
}));

const btnToTop = computed(() => ({
  backgroundImage: furnish.btnToTop ? `url("${furnish.btnToTop}")` : '',
}));

export default {
  pageBg,
  shopNameColor,
  headerBtnRules,
  headerBtnMyPrizes,
  prizeBg,
  isAccordColor,
  thresholdColor,
  // branZone,
  orderLimitTextColor,
  prizeItemBg,
  prizeItemTitleBg,
  prizeItemTitleColor,
  getPrizeBtn,
  showSkuBg,
  priceColor,
  btnToTop,
};
