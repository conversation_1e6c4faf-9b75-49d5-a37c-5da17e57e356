import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData = {
  actBg: '',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/265415/34/21403/148731/67b470dcFed2c33ed/f198bd2adcfcd14c.png',
  actBgColor: '#efd8ac',
  shopNameColor: '',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/196549/37/48402/5849/66f51f3cFf371fc6c/2339fed9176b4c96.png',
  myPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/87898/2/46100/5925/66f51f35Ffbf1ce76/e22717ae394558d9.png',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/268865/35/21330/58096/67b470dcFa498e4aa/4d735f0e6f22aa70.png',
  isAccordColor: '#85420b',
  orderLimitTextColor: '#85420b',
  thresholdColor: '#f0f8ff',
  prizeItemBg: '//img10.360buyimg.com/imgzone/jfs/t1/228657/31/31061/11137/67b470dcF00e117f3/912df122e9c9316e.png',
  prizeItemTitleColor: '#85420b',
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/253237/2/22515/521/67b470ddF1393b511/cdd91ff2e1f55336.png',
  branZone: '//img10.360buyimg.com/imgzone/jfs/t1/253946/2/22199/60845/67b470dcFef0c04bd/8b2bc5e6e55512af.png',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/273153/22/9079/50611/67e14143F9b7fa5de/63c2906b2d714bf2.png',
  footerIsOpen: '2',
  btnToTop: '//img10.360buyimg.com/imgzone/jfs/t1/131310/10/50027/76785/6721ce7fF58f40968/40711efada845b81.png',
  priceColor: '#85420b',
  jumpUrl: '',
  isShowJump: true,
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/216973/7/44448/35565/6704a24dF95fd447d/a746e76753d2b4a3.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/174328/18/45163/21611/6704a24cFabf19fb2/6ba253827b895b26.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/180520/22/49437/189250/6704a24cFc1441794/ff6d99d06475ea12.png',
  canNotCloseJoinPopup: '',
  hotZoneList: [],
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '复购有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  // app.provide('decoData', _decoData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
