<template>
  <VanPopup teleport="body" v-model:show="isShowPopup" @click-overlay='closeDialog'>
    <div class="box">
      <div class='dialog'>
        <div class="confirm-btn" @click="openCard"/>
      </div>
    </div>
  </VanPopup>
</template>

<script setup lang='ts'>
import { computed, defineEmits, inject } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  canNotCloseJoin: {
    type: String,
    required: true,
    default: '1',
  },
});
const isShowPopup = computed(() => props.showPopup);

const openCard = async () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

const emits = defineEmits(['closeDialog']);

const closeDialog = () => {
  // B端配置1为可关闭，2为不可关闭
  if (props.canNotCloseJoin !== '2') {
    emits('closeDialog');
  }
};
</script>
<style lang='scss' scoped>
.box {

  .dialog {
    width: 6rem;
    background: url(.././assets/open.gif) no-repeat;
    height: 11rem;
    background-size: contain;
    box-sizing: border-box;
    text-align: center;

    .confirm-btn{
      font-size: 0.26rem;
      position: absolute;
      width: 2.88rem;
      height: 0.67rem;
      top: 7.7rem;
      left: 1.59rem;
      //background-color: #84353561;
    }
  }

}

</style>
