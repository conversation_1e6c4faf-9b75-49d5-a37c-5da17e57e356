<template>
  <div class="previewItem">
    <img :src="data.bg" alt="" />
    <div class="hotZone" :class="{ op: !preview }" v-for="(it, index) in data.hotZoneList" @click="handleClick(it)" :key="index" :style="hotZoneStyle(it)">热区 {{ String(index + 1).padStart(2, '0') }}</div>
  </div>
</template>
<script setup lang="ts">
import { computed, CSSProperties, defineProps } from 'vue';
import { preview } from '../Utils';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

const props = defineProps(['data', 'width']);

const imgWidth = computed(() => `${props.width ?? 7.5}rem`);

const hotZoneStyle = (item: any): CSSProperties => {
  function conversion(num: number) {
    return ((num * 2) / 100) * ((props.width ?? 7.5) / 7.5);
  }
  return {
    left: `${conversion(item.left)}rem`,
    top: `${conversion(item.top)}rem`,
    width: `${conversion(item.width)}rem`,
    height: `${conversion(item.height)}rem`,
  };
};

const emits = defineEmits(['hotClick']);
const handleClick = (hotZone: any) => {
  if (preview) return;
  if (hotZone.type === 1 && hotZone.url) {
    lzReportClick({ code: 'rqUrl', value: hotZone.url });
    window.location.href = hotZone.url;
  } else if (hotZone.type === 2) {
    emits('hotClick', hotZone.eventName);
  }
};
</script>
<style scoped lang="scss">
.previewItem {
  width: v-bind(imgWidth);
  position: relative;
  img {
    width: 100%;
    vertical-align: middle;
  }
  .hotZone {
    position: absolute;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .op {
    opacity: 0;
  }
}
</style>
