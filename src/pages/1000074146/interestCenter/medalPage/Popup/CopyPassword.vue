<template>
  <div class="main" :style="{ backgroundImage: `url(${decoData.bg})` }">
    <div class="content" :style="{ color: decoData.color }">
      <div>
        <div class="copy-text">
          <div class="num" v-if="copyPassword.num" :style="{ backgroundImage: `url(${decoData.cardNumBg})` }">卡号：{{ copyPassword.num }}</div>
          <div class="password" v-if="copyPassword.password" :style="{ backgroundImage: `url(${decoData.cardNumBg})` }">卡密：{{ copyPassword.password }}</div>
        </div>
        <div class="tip-text">
          <div v-if="copyPassword.tipImg" @click="tipPopup = true">点击查看权益详情及兑换攻略</div>
        </div>
      </div>
      <div class="btn copy-btn" :copy-text="copyText"></div>
    </div>
  </div>
  <VanPopup v-model:show="tipPopup" teleport="body">
    <div class="tip-img">
      <img :src="copyPassword.tipImg" alt="" />
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { popupData, hidePopup } from '../DataHooks';
import Clipboard, { copy } from 'clipboard';
import { showToast } from 'vant';

const props = defineProps(['decoData']);
const copyPassword = computed(() => popupData.copyPassword);
const copyText = computed(() => {
  if (copyPassword.value.num && copyPassword.value.password) {
    return `卡号：${copyPassword.value.num}
卡密：${copyPassword.value.password}`;
  }
  if (copyPassword.value.num) {
    return copyPassword.value.num;
  }
  if (copyPassword.value.password) {
    return copyPassword.value.password;
  }
  return '';
});

const tipPopup = ref(false);

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>

<style scoped lang="scss">
.main {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/131973/12/49691/44464/67244024Ffa0b181c/e5a60420f179a830.png');
  width: 6.23rem;
  height: 8.2rem;
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 3.24rem 0.02rem 0rem;
  .content {
    .copy-text {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 1.7rem;
      justify-content: space-around;
      margin-bottom: 0.6rem;
      .num,
      .password {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/238585/25/20437/654/67244871Fdf62d6d6/85120660b655e010.png');
        background-repeat: no-repeat;
        background-size: 100%;
        width: 4.69rem;
        height: 0.67rem;
        line-height: 0.67rem;
        text-align: center;
        font-size: 0.3rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 0.1rem;
      }
    }
    .tip-text {
      text-align: center;
      height: 0.3rem;
      line-height: 0.3rem;
      font-size: 0.3rem;
      text-decoration: underline;
    }

    .btn {
      position: absolute;
      bottom: 0.8rem;
      width: 3.8rem;
      height: 0.7rem;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
.tip-img {
  width: 6.5rem;
  img {
    width: 6.5rem;
  }
}
</style>
