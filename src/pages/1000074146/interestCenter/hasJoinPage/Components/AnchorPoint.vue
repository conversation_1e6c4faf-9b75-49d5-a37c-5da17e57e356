<template>
  <div class="main">
    <img :src="decoData.bg" alt="" class="bg" />
    <div class="content">
      <img :src="decoData.titleImg" class="title-img" alt="" />
      <div class="level-list swiper-container" id="anchorPointLevelList">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(it, idx) in decoData.memberLevels" :key="idx">
            <HotZone :data="it.hotZoneSetting" :width="6.5" @hotClick="hotClick"></HotZone>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import HotZone from '../../Components/HotZone.vue';
import { preview, scrollToAnchor } from '../../Utils';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import Swiper from 'swiper';
import 'swiper/swiper.min.css';
import { computed, effect, inject, nextTick, onMounted, ref, watch } from 'vue';
import { actLevel } from '../DataHooks';

const MemberInfo = inject('memberInfo') as any;

const props = defineProps(['decorationInfo']);
const decoData = computed(() => props.decorationInfo);

const hotClick = (eventName: string) => {
  if (preview) return;
  lzReportClick({ code: 'mdtz', value: eventName });
  scrollToAnchor(eventName);
};

let levelSwiper: Swiper | null = null;

effect(() => {
  if (preview) {
    const currentLevel = decoData.value.currentLevel ?? 0;
    levelSwiper?.slideTo(currentLevel);
  }
});

watch(
  () => actLevel.value,
  (val) => {
    const index = decoData.value.memberLevels.findIndex((it: any) => it.level === val);
    levelSwiper?.slideTo(index);
  },
);

onMounted(() => {
  nextTick(() => {
    levelSwiper = new Swiper('#anchorPointLevelList', {
      observeSlideChildren: true,
      observer: true,
      spaceBetween: 10,
      allowTouchMove: false,
    });
  });
});
</script>

<style scoped lang="scss">
.main {
  position: relative;
  width: 7rem;
  margin: 0 auto;
  height: 100%;
  overflow: hidden;
  .bg {
    width: 100%;
  }
  .content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding-top: 0.49rem;
  }
  .title-img {
    width: 100%;
    height: 0.74rem;
    object-fit: contain;
    margin-bottom: 0.45rem;
  }
  .level-list {
    width: 6.5rem;
    margin: 0 auto;
    overflow: hidden;
  }
}
</style>
