<template>
  <div class="record-bk">
    <div class="item" v-for="(item, index) in record" :key="index">
      <div class="title">{{ moduleNameMap.find((it) => it.moduleName === item.moduleName)?.label ?? item.moduleName }}</div>
      <div class="content" v-if="item.moduleName === '笔笔返京豆'">
        <div>兑换时间：{{ dayjs(item.createTime).format('YYYY.MM.DD HH:mm:ss') }}</div>
        <div v-if="item.orderId !== '0'">订单编号：{{ item.orderId }}</div>
        <div v-if="item.orderId !== '0'">订单实付金额：{{ item.orderAmount }}元</div>
      </div>
      <div class="content" v-else>
        <div>领取时间：{{ dayjs(item.createTime).format('YYYY.MM.DD HH:mm:ss') }}</div>
      </div>
      <div class="footer">
        <div v-if="item.moduleName === '笔笔返京豆'" class="sendNum">
          <div v-if="item.orderId !== '0'">
            返还京豆：<span>{{ item.sendNum }}个</span>京豆价值：<span>{{ (item.sendNum / 100).toFixed(2) }}元</span>
          </div>
          <div v-else>{{ item.prizeName }}</div>
        </div>
        <div v-else-if="item.prizeType === 7 && item.missReason" @click="showCopyPopup(item)">查看兑换码</div>
        <div v-else-if="item.prizeType === 3 && item.missReason" @click="showCopyPopup(item)">查看地址</div>
        <!--        <div v-else-if="item.prizeType === 30">京东plus年卡</div>-->
        <div v-else>{{ item.prizeName }}</div>
      </div>
      <div class="type">{{ memberType ? 'S+会员' : '店铺会员' }}</div>
    </div>
    <div class="no-data" v-if="!record.length">暂无数据~</div>
  </div>

  <VanPopup v-model:show="copyPopup" teleport="body">
    <div class="copy-bk">
      <img :src="getImg()" alt="" class="prizeImg" />
      <div v-if="type === 1">
        <div class="card1">
          <div class="card-code">{{prizeInfo.missReason.cardPassword}}</div>
          <div class="copy-btn" :copy-text="prizeInfo.missReason.cardPassword"></div>
        </div>
      </div>
      <div v-if="type === 2">
        <div class="card2">
          <div>
            <div class="card-code">{{prizeInfo.missReason.cardNumber}}</div>
            <div class="copy-btn" :copy-text="prizeInfo.missReason.cardNumber"></div>
          </div>
          <div>
            <div class="card-code">{{prizeInfo.missReason.cardPassword}}</div>
            <div class="copy-btn" :copy-text="prizeInfo.missReason.cardPassword"></div>
          </div>
        </div>
      </div>
      <div v-if="type === 3">
        <div class="address">
          <VanField class="input" placeholder="请输入" v-model="prizeInfo.realName"></VanField>
          <VanField class="input" placeholder="请输入" :maxlength="11" v-model="prizeInfo.mobile"></VanField>
          <VanField class="input" readonly placeholder="请选择" v-model="addressCode" @click="addressSelects = true"></VanField>
          <VanField class="input" placeholder="请输入" v-model="prizeInfo.address"></VanField>
          <div class="save-btn" @click="saveAddress"></div>
        </div>
      </div>
    </div>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, ref, computed } from 'vue';
import Clipboard from 'clipboard';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { areaList } from '@vant/area-data';

// 只有兑换码
const img1 = 'https://img10.360buyimg.com/imgzone/jfs/t1/14605/10/21245/44533/66a0a7feF8cb3c5d2/b95bda5fd3878e35.png';
// 卡号卡密
const img2 = 'https://img10.360buyimg.com/imgzone/jfs/t1/98473/36/31526/48284/66a0ad67Ff24ccbc0/4f2a43200e833e85.png';
// 实物
const img3 = 'https://img10.360buyimg.com/imgzone/jfs/t1/224087/36/23592/38890/66a0ad96F49bff95d/bd90536887af94b2.png';

const memberType = inject('memberType') as boolean;
const addressSelects = ref(false);

const moduleNameMap = [
  {
    moduleName: '笔笔返京豆',
    label: '全年笔笔返',
  },
  {
    moduleName: '开卡优惠券',
    label: 'S+会员开卡礼包',
  },
  {
    moduleName: '月度优惠券',
    label: memberType ? 'S+会员月度券包' : '会员月度券包',
  },
  {
    moduleName: '无门槛专享券',
    label: memberType ? 'S+会员月度券包' : '会员月度券包',
  },
  {
    moduleName: '首单礼',
    label: '首购有礼',
  },
  {
    moduleName: '复购礼',
    label: 'S+会员复购券',
  },
  {
    moduleName: '满额礼',
    label: '满额礼',
  },
  {
    moduleName: '生日礼',
    label: '生日礼',
  },
];
const record = ref<any[]>([
  {
    createTime: '2021-09-01 12:00:00',
    moduleName: '笔笔返京豆',
    sendNum: 100,
    orderId: '23',
  },
  {
    createTime: '2021-09-01 12:00:00',
    moduleName: '开卡优惠券',
    prizeName: '优惠券',
  },
  {
    createTime: '2021-09-01 12:00:00',
    moduleName: '月度优惠券',
    prizeName: '优惠券',
  },
  {
    createTime: '2021-09-01 12:00:00',
    moduleName: '无门槛专享券',
    prizeName: '优惠券',
  },
  {
    createTime: '2021-09-01 12:00:00',
    moduleName: '首单礼',
    prizeName: '优惠券',
  },
  {
    createTime: '2021-09-01 12:00:00',
    moduleName: '复购礼',
    prizeName: '优惠券',
  },
  {
    createTime: '2021-09-01 12:00:00',
    moduleName: '满额礼',
    prizeName: '优惠券',
  },
  {
    createTime: '2021-09-01 12:00:00',
    moduleName: '生日礼',
    prizeName: '优惠券',
  },

]);
const type = ref(1);
const copyPopup = ref(false);
const prizeInfo = ref({});

const init = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/member/starbucks/common/equityRecord');
    data.forEach((e) => {
      if (e.prizeType === 7) {
        e.missReason = JSON.parse(e.missReason);
      }
    });
    record.value = data;
  } finally {
    closeToast();
  }
};
init();

const addressCode = computed(() => {
  if (prizeInfo.value.province && prizeInfo.value.city && prizeInfo.value.county) {
    return `${prizeInfo.value.province}/${prizeInfo.value.city}/${prizeInfo.value.county}`;
  }
  return '';
});

const confirmAddress = (addressItemList: any) => {
  prizeInfo.value.province = addressItemList.selectedOptions[0].text;
  prizeInfo.value.city = addressItemList.selectedOptions[1].text;
  prizeInfo.value.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const showCopyPopup = (item: any) => {
  prizeInfo.value = item;
  copyPopup.value = true;
};

const saveAddress = async () => {
  if (!prizeInfo.value.realName) {
    showToast('请输入收货人');
    return;
  }
  if (!prizeInfo.value.mobile) {
    showToast('请输入手机号');
    return;
  }
  if (!prizeInfo.value.province || !prizeInfo.value.city || !prizeInfo.value.county) {
    showToast('请选择地址');
    return;
  }
  if (!prizeInfo.value.address) {
    showToast('请输入详细地址');
    return;
  }
  if (!/^1[3456789]\d{9}$/.test(prizeInfo.value.mobile)) {
    showToast('请输入正确的手机号');
    return;
  }
  try {
    await httpRequest.post('/member/starbucks/pay/member/renovation/saveAddress/h5', {
      ...prizeInfo.value,
    });
    copyPopup.value = false;
    showToast('保存地址成功');
  } catch (e) {
    showToast('保存地址失败，请联系客服');
  }
};

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const getImg = () => {
  const item = prizeInfo.value;
  if (item.prizeType === 7) {
    const result = item.missReason;
    if (result.cardPassword === result.cardNumber) {
      type.value = 1;
      return img1;
    }
    if (result.cardPassword !== result.cardNumber) {
      type.value = 2;
      return img2;
    }
  }
  if (item.prizeType === 3) {
    type.value = 3;
    return img3;
  }
  return '';
};
</script>

<style scoped lang="scss">
.record-bk {
  padding: 0.25rem;
  .item {
    position: relative;
    background-color: #fff;
    border-radius: 0.15rem;
    padding: 0.3rem;
    margin-bottom: 0.25rem;
    overflow: hidden;
    .title {
      font-size: 0.3rem;
      color: #000;
      margin-bottom: 0.3rem;
    }
  }
  .content {
    font-size: 0.2rem;
    color: #7f8080;
    div {
      margin-bottom: 0.1rem;
    }
  }
  .footer {
    font-size: 0.24rem;
    color: #040000;
    margin-top: 0.25rem;
    .sendNum {
      color: #7f8080;
      span {
        color: #040000;
        margin-right: 0.5rem;
      }
    }
  }
  .type {
    position: absolute;
    top: 0;
    right: 0;
    height: 0.43rem;
    width: 1.5rem;
    text-align: center;
    line-height: 0.43rem;
    border-bottom-left-radius: 0.15rem;
    background-image: linear-gradient(0deg, #642516 0%, #842c20 100%);
    font-size: 0.24rem;
    color: #fff;
  }
}
.no-data {
  font-size: 0.28rem;
  color: #7f8080;
  text-align: center;
  padding-top: 30vh;
}
.textOverflow {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  line-break: anywhere;
  -webkit-box-orient: vertical;
}
.copy-bk {
  width: 6.7rem;
  background-color: transparent;
  position: relative;
  border-radius: 0.15rem;
  .prizeImg {
    width: 100%;
    display: block;
  }
  .card1 {
    .card-code {
      position: absolute;
      bottom: 0.95rem;
      left: 0.4rem;
      width: 2.7rem;
      @extend .textOverflow;
    }
    .copy-btn {
      position: absolute;
      bottom: 0.95rem;
      right: 0.5rem;
      width: 2.7rem;
      height: .5rem;
    }
  }
  .card2 {
    display: flex;
    flex-direction: column;
    //gap: 0.35rem;
    position: absolute;
    bottom: 0.95rem;
    left: 0.4rem;

    > div {
      display: flex;
      justify-content: space-around;
      &:not(:last-child) {
        margin-bottom: 0.35rem;
      }

      > div {
        flex: 1
      }
      .card-code {
        width: 5.2rem;
        @extend .textOverflow;
      }
    }
  }
  .address {
    position: absolute;
    display: flex;
    left: 2.3rem;
    top: 1.15rem;
    flex-direction: column;
    .input {
      background: transparent;
      height: 0.77rem;
      width: 4rem;
      display: flex;
      align-items: center;
      &:not(:last-child) {
        margin-bottom: 0.3rem;
      }
    }
    .save-btn {
      position: absolute;
      bottom: -1.6rem;
      right: 1.3rem;
      width: 3.4rem;
      height: 0.8rem;
    }
  }

}
</style>
