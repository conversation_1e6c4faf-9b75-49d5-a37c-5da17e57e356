<template>
  <div class="main" :style="{ backgroundImage: `url(${decoData.bg})` }">
    <div class="content">
      <div class="prize-box" :style="{ backgroundImage: `url(${decoData.skuItemBg})` }" v-for="(item, index) in popupData.skuList" :key="index" @click="gotoSkuPage(item.skuId)">
        <img :src="item.skuMainPicture" alt="" />
        <div class="skuName" :style="{ color: decoData.skuNameColor }">{{ item.skuName }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { decorationInfo, popupData } from '../DataHooks';
import { gotoSkuPage } from '@/utils/platforms/jump';

const props = defineProps(['decoData']);
</script>

<style scoped lang="scss">
.main {
  width: 6.23rem;
  height: 7.67rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/237775/38/27478/9852/6729802aF1a43215c/a115080b032362cb.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 1rem 0.5rem 0.02rem;
  .content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-content: flex-start;
    .prize-box {
      width: 2.34rem;
      height: 3.2rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/245158/32/22762/12815/6729802aF8a028512/a2407a4a2bc42696.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-bottom: 0.2rem;
      img {
        width: 1.8rem;
        height: 1.8rem;
        margin: 0.1rem auto 0.02rem;
      }
      .skuName {
        text-align: center;
        font-size: 0.18rem;
        color: #333;
        // 文字最多2行
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        white-space: normal;
        height: 0.4rem;
        line-height: 0.2rem;
        padding: 0 0.2rem;
      }
    }
  }
}
</style>
