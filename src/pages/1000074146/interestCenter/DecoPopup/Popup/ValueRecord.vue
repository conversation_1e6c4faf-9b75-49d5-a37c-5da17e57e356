<template>
  <div class="main" :style="{ backgroundImage: `url(${decoData.bg})` }">
    <div class="content" :style="{ color: `${decoData.color} ` }">
      <div v-for="(item, index) in recordList" :key="index" class="prize">
        <div class="cell">{{ item.orderId }}</div>
        <div class="cell">{{ item.num }}</div>
        <div class="cell">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
      </div>
      <div class="no-data" v-if="!recordList.length">暂无数据~</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { preview } from '../../Utils';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';

const props = defineProps(['decoData']);

const recordList = ref<any[]>([]);

const prizeRecordList = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
      message: '加载中...',
    });
    const { data } = await httpRequest.post('/swisseMedal/luxuryValueRecord');
    // const data = [
    //   {
    //     num: 0,
    //     orderId: '111',
    //     receiveTime: '2021-09-01',
    //   },
    //   {
    //     num: 0,
    //     orderId: '111',
    //     receiveTime: '2021-09-01',
    //   },
    // ];
    recordList.value = data;
    closeToast();
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
};

onMounted(() => {
  !preview && prizeRecordList();
});
</script>

<style scoped lang="scss">
.main {
  width: 6.23rem;
  height: 7rem;
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 2.3rem 0.6rem 2.6rem;
  //background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/102840/1/47972/94897/672ae002Fa680e55b/09e595a77da94913.png');
  .content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    .prize {
      display: flex;
      align-items: center;
      margin-bottom: 0.1rem;
      .cell {
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 0.24rem;
        &:nth-child(1) {
          width: 2.2rem;
          text-align: left;
        }
        &:nth-child(2) {
          width: 1.2rem;
        }
        &:nth-child(3) {
          text-align: right;
          flex: 1;
        }
      }
      .btn {
        width: 1.84rem;
        height: 0.53rem;
        line-height: 0.53rem;
        border-color: #412812;
        border-width: 0.02rem;
        border-style: solid;
        border-radius: 0.06rem;
        margin: 0 auto;
      }
      .btn2 {
        width: 1.84rem;
        height: 0.53rem;
        line-height: 0.53rem;
        border-radius: 0.06rem;
        background-image: linear-gradient(-55deg, #1e1007 1%, #16100d 48%, #381d0b 100%);
        color: #fff;
        margin: 0 auto;
      }
    }
  }
  .no-data {
    font-size: 0.24rem;
    text-align: center;
    padding-top: 0.8rem;
  }
}
</style>
