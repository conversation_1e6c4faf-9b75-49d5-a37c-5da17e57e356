<template>
  <div class="main" :style="{ backgroundImage: `url(${decoData.bg})` }">
    <div class="content" v-if="prizeData.prizeType !== 0" :style="{ color: decoData.color }">
      <div class="title">恭喜您！领取成功</div>
      <div class="subtitle">{{ prizeData.prizeName }}</div>
      <img class="prize-img" :src="prizeData.prizeImg" alt="" />
      <div class="btn">
        <img v-if="prizeData.prizeType === 3" :src="decoData?.addressBtn ?? '//img10.360buyimg.com/imgzone/jfs/t1/196793/9/49111/18129/6719bf33F51bc13e5/f4c9fbaf6b1d83dc.png'" alt="" @click="saveAddress" />
        <img v-else-if="prizeData.prizeType === 1" :src="decoData?.toShopBtn ? decoData.toShopBtn : '//img10.360buyimg.com/imgzone/jfs/t1/245084/20/21087/17644/6719bf33Fc59ac39b/19c4e36196b1fbd4.png'" alt="" @click="handleGoShop" />
        <img v-else-if="prizeData.prizeType === 7" :src="decoData?.toCopyBtn ? decoData.toCopyBtn : '//img10.360buyimg.com/imgzone/jfs/t1/245184/7/22289/2525/6720a2b3F3a24112a/c7574ea19b0a1bd5.png'" alt="" @click="handleOpenCard()" />
        <img v-else :src="decoData?.confirmBtn ? decoData?.confirmBtn : '//img10.360buyimg.com/imgzone/jfs/t1/196793/9/49111/18129/6719bf33F51bc13e5/f4c9fbaf6b1d83dc.png'" alt="" @click="handelClose" />
      </div>
      <div class="tip">{{ tips[prizeData.prizeType] ?? '' }}</div>
    </div>
    <div v-else>
      <div class="content" :style="{ color: decoData.color }">
        <div class="title">谢谢参与</div>
        <div class="subtitle">感谢您的参与，祝您好运</div>
        <img class="prize-img" src="//img10.360buyimg.com/imgzone/jfs/t1/137169/6/49599/8323/672231deFa5012064/d9b4f29bba57468d.png" alt="" />
        <div class="btn">
          <img :src="decoData?.confirmBtn ?? '//img10.360buyimg.com/imgzone/jfs/t1/196793/9/49111/18129/6719bf33F51bc13e5/f4c9fbaf6b1d83dc.png'" alt="" @click="handelClose" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject } from 'vue';
import { preview } from '../../Utils';
import { hidePopup, popupData, showPopup } from '../DataHooks';
import { gotoShopPage } from '@/utils/platforms/jump';

const props = defineProps(['decoData']);
const prizeData = computed(() => popupData.award);

const handleGoShop = () => {
  gotoShopPage('1000074146');
};
const handleOpenCard = () => {
  showPopup('copyPassword', { num: prizeData.value.result.cardNumber, password: prizeData.value.result.cardPassword, tipImg: '' });
  hidePopup('award');
};
const saveAddress = () => {
  showPopup('saveAddress', { activityPrizeId: prizeData.value.userPrizeId });
  hidePopup('award');
};
const handelClose = () => {
  hidePopup('award');
};
const tips: any = {
  6: '红包查看请前往“我的-我的钱包-红包”中查看详情',
  1: `您的优惠券已自动发放到您的账户
如需查看可点击“我的卡券”查看详情`,
  3: '地址需在24小时之内填写，如超24小时没填写需联系客服',
};
</script>

<style scoped lang="scss">
.main {
  width: 6.23rem;
  height: 7.82rem;
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 0.83rem 0.02rem 0.02rem;
  .content {
    .title {
      font-size: 0.5rem;
      text-align: center;
      margin-bottom: 0.15rem;
    }
    .subtitle {
      font-size: 0.3rem;
      text-align: center;
    }
    .prize-img {
      width: 5.5rem;
      height: 2.75rem;
      object-fit: contain;
      margin: 0.3rem auto;
    }
    .btn {
      padding-top: 0.65rem;
      padding-bottom: 0.46rem;
      img {
        width: 3.6rem;
        margin: 0 auto;
      }
    }
    .tip {
      font-size: 0.18rem;
      text-align: center;
    }
  }
}
</style>
