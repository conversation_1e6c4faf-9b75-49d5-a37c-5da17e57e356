<template>
  <div class="main">
    <div class="nick-name" :style="{ color: Number(checkMemberInfo.black) === 2 ? decoData.pageTextColor : decoData.blackPageTextColor }">
      <div>尊敬的</div>
      <div>{{ userInfo?.nickname ?? '用户昵称' }}</div>
    </div>
    <div class="bar-box" :style="{ color: Number(checkMemberInfo.black) === 2 ? decoData.pageTextColor : decoData.blackPageTextColor }">
      <div class="join-point">
        <div>您目前奢享值为：{{ checkMemberInfo?.num ?? 'xx' }}</div>
        <div @click="showRule">奢享值规则说明></div>
      </div>
      <div class="progress-bar progress-bar-top" :style="{ backgroundImage: `url(${Number(checkMemberInfo.black) === 2 ? decoData.progressBarBg : decoData.blackProgressBarBg})` }">
        <div class="progress-content">
          <img :src="Number(checkMemberInfo.black) === 2 ? decoData.progressBar : decoData.blackProgressBar" alt="" :style="{ right: progressBarNum }" />
        </div>
      </div>
      <div class="lack-can" :style="{ color: Number(checkMemberInfo.black) === 2 ? decoData.pageImTextColor : decoData.blackPageImTextColor }">再得{{ checkMemberInfo?.lackCan ?? 'xx' }}奢享值即可享超多权益</div>
    </div>
    <HotZone :width="7.5" :data="Number(checkMemberInfo.black) === 2 ? decoData.hotZoneSetting : decoData.blackHotZoneSetting" @hotClick="hotClick"></HotZone>
  </div>

  <!--  用户须知-->
  <VanPopup v-model:show="acccordWithShow1">
    <div class="accord-with-box" :style="{ backgroundImage: `url(${popupDeco.agreement.bg})` }">
      <div class="rule">
        <img class="text-img" :src="popupDeco.agreement.textBg" />
        <!--        <div class="text" :style="{ color: popupDeco.agreement.color }">{{ decoData.rule }}</div>-->
      </div>
      <div class="agree-box">
        <VanCheckbox v-model="agreeCheck" shape="square" checkedColor="#1e1308"></VanCheckbox>
        <div :style="{ color: popupDeco.agreement.agreeTextColor }">我已阅读并同意</div>
      </div>
      <div class="btn" @click="join"></div>
    </div>
  </VanPopup>
  <VanPopup v-model:show="notAccordWithShow2">
    <div class="notAccord-with-box" :style="{ backgroundImage: `url(${bkImg})` }">
      <!-- <div class="text" :style="{ color: textColor }" v-if="actTab === 1">
        <p>
          再购买<span>{{ checkMemberInfo.lackCan }}瓶</span>PLUS产品即可进阶
        </p>
        <p><span>无门槛惊喜</span>福利等你来领</p>
      </div> -->
      <div class="text1" :style="{ color: textColor }">您目前奢享值为：{{ checkMemberInfo.num }}</div>
      <div class="progress-bar" :style="{ backgroundImage: `url(${popupProgressBarBg})` }">
        <div class="progress-content">
          <img :src="popupProgressBar" alt="" :style="{ right: progressBarNum }" />
        </div>
      </div>
      <div class="text2" :style="{ color: textColor }">还差{{ checkMemberInfo.lackCan }}奢享值即可加入高阶权益中心</div>
      <div class="btn" @click="jumpLink"></div>
    </div>
  </VanPopup>
  <!--  开卡-->
  <VanPopup v-model:show="joinPopup">
    <div class="join-with-box" :style="{ backgroundImage: `url(${popupDeco.joinMember.bg})` }" @click="showJoinPopup">
      <div class="btn"></div>
    </div>
  </VanPopup>
  <VanPopup v-model:show="rulePopup">
    <div class="rule-img">
      <img :src="Number(checkMemberInfo.black) === 2 ? popupDeco.notMemberRule.ruleImg : popupDeco.notMemberRule.blackRuleImg" alt="" />
    </div>
  </VanPopup>
</template>
<script setup lang="ts">
import HotZone from '../../Components/HotZone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { preview } from '../../Utils';
import { computed, inject, onMounted, ref } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { UserInfo } from '@/utils/products/types/UserInfo';
import { checkMemberInfo, popupDecorationInfo } from '../DataHooks';
import { gotoShopPage } from '@/utils/platforms/jump';

const MemberInfo = inject('memberInfo') as any;
const userInfo = inject('userInfo') as UserInfo;
const pathParams = inject('pathParams') as any;

const actTab = ref(checkMemberInfo.value.windowType);

const props = defineProps(['decorationInfo']);
const decoData = computed(() => props.decorationInfo);

const progressBarNum = computed(() => {
  if (preview) {
    return '30%';
  }
  const num = checkMemberInfo.value.num / (checkMemberInfo.value.num + checkMemberInfo.value.lackCan);
  return `${(1 - num) * 100}%`;
});

const popupDeco = computed(() => popupDecorationInfo.value.thresholdPage);
const bkImg = computed(() => {
  if (checkMemberInfo.value.windowType === 1) {
    return popupDeco.value.notMember.bg1;
  }
  if (checkMemberInfo.value.windowType === 2) {
    return popupDeco.value.notMember.bg2;
  }
  if (checkMemberInfo.value.windowType === 3) {
    return popupDeco.value.notMember.bg3;
  }
  return popupDeco.value.notMember.bg1;
});

const textColor = computed(() => {
  if (checkMemberInfo.value.windowType === 1) {
    return popupDeco.value.notMember.color1;
  }
  if (checkMemberInfo.value.windowType === 2) {
    return popupDeco.value.notMember.color2;
  }
  if (checkMemberInfo.value.windowType === 3) {
    return popupDeco.value.notMember.color3;
  }
  return popupDeco.value.notMember.color1;
});

const popupProgressBar = computed(() => {
  if (checkMemberInfo.value.windowType === 1) {
    return popupDeco.value.notMember.progressBar1;
  }
  if (checkMemberInfo.value.windowType === 2) {
    return popupDeco.value.notMember.progressBar2;
  }
  if (checkMemberInfo.value.windowType === 3) {
    return popupDeco.value.notMember.progressBar3;
  }
  return popupDeco.value.notMember.progressBar1;
});

const popupProgressBarBg = computed(() => {
  if (checkMemberInfo.value.windowType === 1) {
    return popupDeco.value.notMember.progressBarBg1;
  }
  if (checkMemberInfo.value.windowType === 2) {
    return popupDeco.value.notMember.progressBarBg2;
  }
  if (checkMemberInfo.value.windowType === 3) {
    return popupDeco.value.notMember.progressBarBg3;
  }
  return popupDeco.value.notMember.progressBarBg1;
});

const agreeCheck = ref(false);

const rulePopup = ref(false);
const acccordWithShow1 = ref(false);
const notAccordWithShow2 = ref(false);
const joinPopup = ref(false);
const hasJoinPageUrl = `${process.env.VUE_APP_HOST}1000074146/interestCenter/hasJoinPage/`;

const showJoinPopup = () => {
  joinPopup.value = false;
  acccordWithShow1.value = true;
};

const join = async () => {
  if (!agreeCheck.value) {
    showToast('请先勾选已阅读并同意协议');
    return;
  }
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/swisseNoMember/joinMember', { channel: pathParams.adsource });
    closeToast();
    window.location.href = hasJoinPageUrl;
  } catch (error: any) {
    console.error(error);
    showToast(error.message);
  }
};

const checkMember = async () => {
  if (checkMemberInfo.value.isReceive === 1) {
    joinPopup.value = true;
  } else {
    notAccordWithShow2.value = true;
  }
};

const showRule = () => {
  if (preview) {
    return;
  }
  rulePopup.value = true;
};

const hotClick = (eventName: string) => {
  if (preview) {
    return;
  }
  if (eventName === 'join') {
    checkMember();
  } else if (eventName === 'showRule') {
    showRule();
  }
};

const jumpLink = () => {
  if (preview) {
    return;
  }
  if (checkMemberInfo.value.windowType === 1) {
    window.location.href = popupDeco.value.notMember.url1;
  } else if (checkMemberInfo.value.windowType === 2) {
    window.location.href = popupDeco.value.notMember.url2;
  } else if (checkMemberInfo.value.windowType === 3) {
    window.location.href = popupDeco.value.notMember.url3;
  } else {
    window.location.href = popupDeco.value.notMember.url3;
  }
};
onMounted(() => {
  if (checkMemberInfo.value.isReceive === 1) {
    joinPopup.value = true;
  }
});
</script>
<style scoped lang="scss">
.main {
  position: relative;
}
.nick-name {
  position: absolute;
  top: 3rem;
  left: 0.7rem;
  right: 0.7rem;
  font-size: 0.3rem;
  line-height: 0.4rem;
  color: #fff;
  z-index: 20;
}
.bar-box {
  position: absolute;
  bottom: 4.3rem;
  left: 50%;
  transform: translate(-50%);
  z-index: 100;
  .join-point {
    font-size: 0.2rem;
    //margin-top: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.05rem;
  }
  .progress-bar-top {
    margin: 0.2rem 0;
  }
  .lack-can {
    font-size: 0.2rem;
    color: #e9b37d;
    margin-top: 0.1rem;
  }
}
.progress-bar {
  width: 6rem;
  height: 0.53rem;
  background-size: 100%, 100%;
  background-repeat: no-repeat;
  padding: 0.06rem;
  .progress-content {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    border-radius: 0.3rem;
    img {
      position: absolute;
      top: 0;
      right: 100%;
      width: 100%;
      height: 100%;
    }
  }
}
.notAccord-with-box {
  width: 6.3rem;
  height: 7.08rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/14087/10/22169/41006/671b51acFe861bec9/598ad6de116bf3a0.png');
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.3rem;
  .text {
    text-align: center;
    font-size: 0.24rem;
    line-height: 0.3rem;
    span {
      font-weight: bold;
    }
  }
  .text1 {
    text-align: center;
    font-size: 0.38rem;
    color: #e9b37d;
    margin-bottom: 0.1rem;
  }
  .progress-bar {
    transform: scale(0.75);
  }
  .text2 {
    text-align: center;
    font-size: 0.3rem;
    color: #e9b37d;
    margin-top: 0.1rem;
  }
  .btn {
    position: absolute;
    bottom: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4.5rem;
    height: 0.9rem;
  }
}
.accord-with-box {
  width: 6.3rem;
  height: 7.08rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/185663/39/50294/33272/671b51aeF4eaad760/4cf23fd0a0aac803.png');
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
  padding: 1.92rem 0.7rem 0;
  .rule {
    width: 100%;
    height: 1.9rem;
    overflow-y: auto;
    scrollbar-width: none;
    .text-img {
      width: 100%;
    }
  }
  .agree-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 0.1rem;
    div {
      padding-left: 0.2rem;
      font-size: 0.24rem;
      color: #d71721;
    }
  }
  .btn {
    position: absolute;
    bottom: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4.5rem;
    height: 0.9rem;
  }
}
.join-with-box {
  width: 6.3rem;
  height: 7.08rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/200826/4/47388/44191/671b51adF4ceb8948/d3bf2cedbfef3e0d.png');
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
  .btn {
    position: absolute;
    bottom: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4.5rem;
    height: 0.9rem;
  }
}
.rule-img {
  width: 6.3rem;
  img {
    width: 100%;
  }
}
</style>
<style>
.accord-with-box .van-checkbox__icon .van-icon {
  border-color: #000;
  background: #fff;
}
</style>
