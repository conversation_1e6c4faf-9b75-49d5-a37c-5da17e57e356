<template>
  <HotZone :data="hotZoneListSetting" @hotClick="hotClick"></HotZone>
</template>
<script setup lang='ts'>
import { ref } from 'vue';
import HotZone from '../../Components/HotZone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

const hotZoneListSetting = ref([
  {
    bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/220661/8/39026/41209/662f856bF8d7839b0/2c021f235dceb0b9.png',
    hotZoneList: [
      {
        left: 78,
        top: 37,
        width: 233,
        height: 67.9375,
        url: '',
        rectId: '1726046950739',
        containerWidth: 375,
        containerHeight: 273,
      },
      {
        left: 104,
        top: 146,
        width: 189,
        height: 44.9375,
        url: '',
        rectId: '1726046951218',
        containerWidth: 375,
        containerHeight: 273,
      },
    ],
  },
]);
const hotClick = (hotZone: any) => {

  if (hotZone.url) {
    lzReportClick(`hotZoneUrl${hotZone.url}`);
    // window.location.href = hotZone.url;
  }
};
</script>
