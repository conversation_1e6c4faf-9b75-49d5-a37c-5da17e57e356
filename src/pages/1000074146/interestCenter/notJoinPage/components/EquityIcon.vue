<template>
  <HotZone :data="hotZoneListSetting" @hotClick="hotClick"></HotZone>
</template>
<script setup lang='ts'>
import { ref } from 'vue';
import HotZone from '../../Components/HotZone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

const hotZoneListSetting = ref([
  {
    bgColor: '#1e3932',
    bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/243805/37/17382/14942/66de8cbcF13b98b13/4c72415803967e5f.png',
    hotZoneList: [
      {
        left: 84,
        top: 24,
        width: 215,
        height: 48.9375,
        url: '',
        rectId: '1726046935179',
        containerWidth: 375,
        containerHeight: 273,
      },
      {
        left: 99,
        top: 101,
        width: 240,
        height: 44.9375,
        url: '',
        rectId: '1726046936251',
        containerWidth: 375,
        containerHeight: 273,
      },
    ],
  },
]);
const hotClick = (hotZone: any) => {

  if (hotZone.url) {
    lzReportClick(`hotZoneUrl${hotZone.url}`);
    // window.location.href = hotZone.url;
  }
};
</script>
