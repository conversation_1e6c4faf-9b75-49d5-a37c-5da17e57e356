<template>
  <div class="main-box">
    <div class="sku-list-swiper swiper-container">
      <div class="swiper-wrapper">
        <div class="swiper-slide sku-item" v-for="(item, index) in skuList" :key="index">
          <div class="sku-item-content" @click="gotoSkuPage(item.skuId)">
            <img :src="item.skuMainPicture" alt="" class="sku-img" />
            <div class="btn"></div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/333971/11/18333/2396/68da2e9eF0446ec77/fa9295530de1a41c.png" alt="" class="prev" @click="toPrev" />
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/334259/20/18471/2433/68da3063F6cd9278f/37292443fb07402b.png" alt="" class="next" @click="toNext" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import { getSkuList, Sku } from '../hooks';
import { gotoSkuPage } from '@/utils/platforms/jump';

Swiper.use([Autoplay]);

const skuList = ref<Sku[]>([]);

let skuSwiper: Swiper;
const toPrev = () => {
  if (skuSwiper) {
    skuSwiper.slidePrev();
  }
};
const toNext = () => {
  if (skuSwiper) {
    skuSwiper.slideNext();
  }
};
const initSwiper = () => {
  nextTick(() => {
    skuSwiper = new Swiper('.sku-list-swiper', {
      slidesPerView: 3,
      autoplay: {
        delay: 2000,
        stopOnLastSlide: false,
        disableOnInteraction: false,
      },
    });
  });
};
onMounted(async () => {
  skuList.value = await getSkuList(1);
  initSwiper();
});
</script>

<style scoped lang="scss">
.main-box {
  width: 7.19rem;
  height: 4.83rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/331831/40/22152/31535/68eca802F3367abd0/40a78ee011816241.png');
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 1.45rem 0.12rem 0rem 0.13rem;
  position: relative;
  margin: 0 auto;
  .sku-list-swiper {
    width: 6.4rem;
    overflow: hidden;
    margin: 0 auto;
    .sku-item-content {
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/333599/1/22315/4654/68eca802F6ea3681b/78a2f371cb7e4ee2.png');
      background-size: 100%;
      background-repeat: no-repeat;
      width: 1.96rem;
      height: 2.62rem;
      margin: 0 auto;
      padding-top: 0.09rem;
      .sku-img {
        width: 1.79rem;
        height: 1.79rem;
        object-fit: cover;
        margin: 0 auto;
        border-radius: 0.07rem;
      }
    }
  }
  .prev {
    width: 0.48rem;
    position: absolute;
    top: 2.55rem;
    left: -0.13rem;
    z-index: 20;
  }
  .next {
    width: 0.48rem;
    position: absolute;
    top: 2.55rem;
    right: -0.13rem;
    z-index: 20;
  }
}
</style>
