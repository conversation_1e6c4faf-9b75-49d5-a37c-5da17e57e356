<template>
  <div class="main-box">
    <!-- 订阅提醒 -->
    <div class="subscribe-reminder">
      <img v-if="isSubscribe" src="//img10.360buyimg.com/imgzone/jfs/t1/347599/13/9506/3125/68ece976F17edc2bc/daa79457c5af5646.png" alt="" @click="closeSubscribeActivity" />
      <img v-else src="//img10.360buyimg.com/imgzone/jfs/t1/341828/4/12241/2989/68ece975F41c1c5ff/2a7324100be6aedb.png" alt="" @click="subscribeActivity" />
    </div>
    <!-- 可补签次数 -->
    <div class="supplement-count">{{ activityInfo.userMakeUpSignNum }}次</div>
    <div class="get-supplement-count-btn" @click="getMakeUpSignNum"></div>
    <!-- 累计签到天数 -->
    <div class="total-days">{{ totalSignInDays }}</div>
    <div class="calendar-box">
      <div class="progress-bg"></div>
      <div class="calendar-list-swiper swiper-container">
        <div class="swiper-wrapper">
          <div class="swiper-slide calendar-item" v-for="(item, index) in signCalendarList" :key="index">
            <div class="calendar-day">
              <div class="calendar-day-icon">
                <img v-if="item.signStatus === 0 || item.signStatus === 4" src="//img10.360buyimg.com/imgzone/jfs/t1/328394/2/25027/1013/68da7660F64d375f7/ee77fa88dff3a152.png" alt="" class="icon" />
                <img v-else-if="item.signStatus === 1" src="//img10.360buyimg.com/imgzone/jfs/t1/343488/16/8525/3729/68da7660Fb9633323/7548cc7f081141ad.png" alt="" class="icon" />
                <img v-else-if="item.signStatus === 2 || item.signStatus === 3" src="//img10.360buyimg.com/imgzone/jfs/t1/327781/16/25326/3622/68da7660Fb79ceac4/5de3d7fa898ec8d1.png" alt="" class="icon" />
                <div class="line" v-if="item.signStatus !== 4" :class="{ now: dayjs(item.recordDate).format('MM-DD') === dayjs().format('MM-DD') }"></div>
                <div class="day">{{ dayjs(item.recordDate).format('MM-DD') }}</div>
              </div>
              <div class="calendar-bg" :style="{ backgroundImage: `url(${getCalendarImg(item)})` }">
                <div class="prize" :class="{ 'has-sign': item.signStatus === 1, 'opacity-0': promotionDates.includes(dayjs(item.recordDate).format('MM-DD')) }"><img src="//img10.360buyimg.com/imgzone/jfs/t1/349015/25/8467/2357/68d9f0c0Fc3715457/f8be545359ee7ca2.png" alt="" /><span>+15</span></div>
                <img v-if="item.signStatus === 0" src="//img10.360buyimg.com/imgzone/jfs/t1/344775/27/8650/4305/68da835aFfdc08d85/5e36af7cb2b3ae23.png" alt="" class="btn" @click="signIn" />
                <img v-else-if="item.signStatus === 2" src="//img10.360buyimg.com/imgzone/jfs/t1/329130/32/18679/4274/68da835aFf2840da9/745293564acb922e.png" alt="" class="btn" @click="makeUpSign(item)" />
                <img v-else-if="item.signStatus === 3" src="//img10.360buyimg.com/imgzone/jfs/t1/329130/32/18679/4274/68da835aFf2840da9/745293564acb922e.png" alt="" class="btn gary" @click="showToast('抱歉，首次参与活动前的天数无法补签哦～')" />
                <img v-else-if="item.signStatus === 4" src="//img10.360buyimg.com/imgzone/jfs/t1/333150/36/21093/2464/68e89d59F589fc67e/23915604246859b0.png" alt="" class="btn" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/333971/11/18333/2396/68da2e9eF0446ec77/fa9295530de1a41c.png" alt="" class="prev" @click="toPrev" />
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/334259/20/18471/2433/68da3063F6cd9278f/37292443fb07402b.png" alt="" class="next" @click="toNext" />
    </div>
    <div>
      <div class="get-prize-time">活动时间：{{ dayjs(bigDayPrizeInfo.startTime).format('MM月DD日HH点') }}-{{ dayjs(bigDayPrizeInfo.endTime).format('MM月DD日HH点') }}下单</div>
      <img v-if="bigDayPrizeInfo.btnStatus === 2" src="//img10.360buyimg.com/imgzone/jfs/t1/339100/36/15762/4674/68d9f0c4F7cdc8a5f/fc480c344d34f6fb.png" alt="" class="get-prize-btn" />
      <img v-else-if="bigDayPrizeInfo.btnStatus === 3" src="//img10.360buyimg.com/imgzone/jfs/t1/350298/25/7534/4126/68d9f0c4F696c9eca/110e159ae530d75b.png" alt="" class="get-prize-btn" />
      <img v-else-if="bigDayPrizeInfo.btnStatus === 1" src="//img10.360buyimg.com/imgzone/jfs/t1/336112/37/15897/5285/68d9f0c2F4c611cd0/321e8ddee42d9229.png" alt="" class="get-prize-btn" @click="getBigDayPrize" />
      <img v-else src="//img10.360buyimg.com/imgzone/jfs/t1/336112/37/15897/5285/68d9f0c2F4c611cd0/321e8ddee42d9229.png" alt="" class="get-prize-btn gary" />
      <div class="prize-num">（限量500件，先到先得）</div>
    </div>
  </div>

  <VanPopup v-model:show="awardPrizePop" teleport="body" :close-on-click-overlay="false">
    <AwardPrize @close="awardPrizePop = false" @toSaveAddress="toSaveAddress" :prize-info="awardPrizeInfo"></AwardPrize>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, ref, watch } from 'vue';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import AwardPrize from '../Pop/AwardPrize.vue';
import { httpRequest } from '@/utils/service';
import { signCalendarList, activityInfo, signDayTask, makeUpSignTask, checkActStatus } from '../hooks';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

// 大促日期 10/14	10/15 10/18 10/21 10/25 10/30	10/31	11/1
const promotionDates = ['10-14', '10-15', '10-18', '10-21', '10-25', '10-30', '10-31', '11-01'];

Swiper.use([Autoplay]);

const emits = defineEmits(['getMakeUpSignNum', 'signSuccess', 'toSaveAddress']);

// 是否订阅活动
const isSubscribe = ref(false);

// 开启订阅活动
const subscribeActivity = () => {
  if (!checkActStatus()) {
    return;
  }
  if (!window.jmfe.isApp('jd')) {
    showToast('请在京东app内操作');
    return;
  }
  const needSubscribeDays = signCalendarList.value.filter((item) => item.signStatus === 4);
  if (needSubscribeDays.length === 0) {
    showToast('活动即将结束，无法订阅');
    return;
  }
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  for (let i = 0; i < needSubscribeDays.length; i++) {
    const item = needSubscribeDays[i];
    window.jmfe
      .addReminder({
        id: baseInfo.activityMainId, //可以使用相关活动id、页面id、广告组id或广告id等
        tag: '活动',
        time: dayjs(item.recordDate).add(10, 'hour').valueOf(), //可以使用Date对象的getTime方法获取，例如：new Date().getTime()
        title: baseInfo.activityName,
        type: 'HUODONG',
        url: window.location.href, //支持h5页面地址或jump协议对象
      })
      .then(({ appointed }: any) => {
        if (appointed) {
          console.log('添加订阅成功');
        } else {
          console.log('添加订阅失败');
        }
      });
  }
  closeToast();
  showToast('添加订阅成功');
  isSubscribe.value = true;
};

// 关闭订阅活动
const closeSubscribeActivity = () => {
  if (!checkActStatus()) {
    return;
  }
  if (!window.jmfe.isApp('jd')) {
    showToast('请在京东app内操作');
    return;
  }
  // 需要删除的预约列表
  const needDeleteDays = signCalendarList.value.filter((item) => item.signStatus === 4 || dayjs(item.recordDate).valueOf() === dayjs().valueOf());
  for (let i = 0; i < needDeleteDays.length; i++) {
    const item = needDeleteDays[i];
    window.jmfe
      .deleteReminder({
        id: baseInfo.activityMainId, //可以使用相关活动id、页面id、广告组id或广告id等
        time: dayjs(item.recordDate).add(10, 'hour').valueOf(), //可以使用Date对象的getTime方法获取，例如：new Date().getTime()
        type: 'HUODONG',
      })
      .then(({ appointed }: any) => {
        if (appointed) {
          console.log('取消订阅成功');
        } else {
          console.log('取消订阅失败');
        }
      });
  }

  showToast('取消订阅成功');
  isSubscribe.value = false;
};

const getMakeUpSignNum = () => {
  if (!checkActStatus()) {
    return;
  }
  emits('getMakeUpSignNum');
};

const getCalendarImg = (item: any) => {
  switch (item.signStatus) {
    case 0:
      return item.signImg;
    case 1:
      return item.signedImg;
    case 2:
    case 3:
      return item.notSignImg;
    case 4:
      return item.notSignTimeImg;
    default:
      return '';
  }
};

const awardPrizePop = ref(false);
const totalSignInDays = computed(() => signCalendarList.value.filter((item) => item.signStatus === 1).length);

const signIn = async () => {
  if (!checkActStatus()) {
    return;
  }
  try {
    const data = await signDayTask();
    emits('signSuccess', data);
  } catch (error) {
    showToast('签到失败');
  }
};

const makeUpSign = async (item: any) => {
  if (!checkActStatus()) {
    return;
  }
  if (activityInfo.userMakeUpSignNum <= 0) {
    getMakeUpSignNum();
    return;
  }
  try {
    const data = await makeUpSignTask(dayjs(item.recordDate).format('YYYY-MM-DD 00:00:00'));
    emits('signSuccess', data);
  } catch (error) {
    showToast('补签失败');
  }
};

let calendarSwiper: Swiper;
const toPrev = () => {
  if (calendarSwiper) {
    calendarSwiper.slidePrev();
  }
};
const toNext = () => {
  if (calendarSwiper) {
    calendarSwiper.slideNext();
  }
};
const initSwiper = () => {
  nextTick(() => {
    if (calendarSwiper) {
      calendarSwiper.destroy();
    }
    calendarSwiper = new Swiper('.calendar-list-swiper', {
      slidesPerView: 5,
    });
    let nowIndex = signCalendarList.value.findIndex((item) => dayjs(item.recordDate).format('MM-DD') === dayjs().format('MM-DD')) - 2;
    if (nowIndex < 0) {
      nowIndex = 0;
    }
    if (nowIndex > signCalendarList.value.length - 1) {
      nowIndex = signCalendarList.value.length - 1;
    }
    calendarSwiper.slideTo(nowIndex);
  });
};
watch(
  () => signCalendarList.value,
  (newVal, oldVal) => {
    if (newVal.length !== oldVal.length) {
      initSwiper();
      window.jmfe
        .queryReminder({
          id: baseInfo.activityMainId,
          time: dayjs().startOf('day').add(1, 'day').add(10, 'hour').valueOf(),
          type: 'HUODONG',
        })
        .then(({ appointed }: any) => {
          isSubscribe.value = !!appointed;
          if (appointed) {
            console.log('已预约');
          } else {
            console.log('未预约');
          }
        })
        .catch((error: any) => {
          console.error(error);
        });
    }
  },
);

const bigDayPrizeInfo = ref({
  // 奖项主键
  id: 0,
  // 奖品名称
  prizeName: '',
  // 奖品图片
  prizeImg: '',
  // 奖品剩余库存
  prizeRemainStock: 0,
  // 领取开始时间
  startTime: '',
  // 领取结束时间
  endTime: '',
  // 按钮状态 0-不符合条件；1-立即领取；2-已领取；3-已兑完
  btnStatus: 0,
});

const getBigDayPrizeInfo = async () => {
  try {
    const { data } = await httpRequest.post('/94007/getPrizeInfo');
    bigDayPrizeInfo.value = data;
  } catch (error) {
    console.error(error);
  }
};
getBigDayPrizeInfo();

const awardPrizeInfo = ref<any>();
const getBigDayPrize = async () => {
  if (!checkActStatus()) {
    return;
  }
  try {
    showLoadingToast({
      message: '领取中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/94007/receiveBigDayPrize', {
      prizeId: bigDayPrizeInfo.value.id,
    });
    getBigDayPrizeInfo();
    closeToast();
    awardPrizeInfo.value = data;
    awardPrizePop.value = true;
  } catch (error: any) {
    closeToast();
    showToast(error.message);
    console.error(error);
  }
};

const toSaveAddress = (id: string) => {
  awardPrizePop.value = false;
  emits('toSaveAddress', id);
};
</script>

<style scoped lang="scss">
.main-box {
  width: 7.19rem;
  height: 8.63rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/336610/33/19057/72150/68ea174dFd15b6cb7/be51db7b5b87aee2.png');
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 1.82rem 0.12rem 0rem 0.13rem;
  position: relative;
  margin: 0 auto 0.36rem;
  .subscribe-reminder {
    position: absolute;
    top: 0.29rem;
    right: 0;
    img {
      width: auto;
      height: 0.42rem;
    }
  }
  .supplement-count {
    width: 0.59rem;
    height: 0.3rem;
    position: absolute;
    top: 1.2rem;
    left: 2.16rem;
    text-align: center;
    font-size: 0.238rem;
    color: #fff;
    line-height: 0.3rem;
  }
  .get-supplement-count-btn {
    width: 1.3rem;
    height: 0.3rem;
    position: absolute;
    top: 1.2rem;
    left: 2.9rem;
  }
  .total-days {
    width: 0.47rem;
    height: 0.3rem;
    position: absolute;
    top: 1.2rem;
    right: 0.63rem;
    text-align: center;
    font-size: 0.238rem;
    color: #fff;
    line-height: 0.3rem;
  }
  .calendar-box {
    position: relative;
    width: 6.5rem;
    margin: 0 auto;
  }
  .progress-bg {
    position: absolute;
    left: -0.1rem;
    top: 0.02rem;
    width: 6.7rem;
    height: 0.32rem;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/339915/12/15875/1826/68d9f0c2Fd3eaef7b/7010e991fc2fc20d.png') no-repeat;
    background-size: 100%;
  }
  .calendar-list-swiper {
    position: relative;
    overflow: hidden;

    .calendar-day {
      position: relative;
      .calendar-day-icon {
        .icon {
          position: relative;
          width: 0.34rem;
          margin: 0 auto;
          z-index: 10;
        }
        .line {
          width: 100%;
          height: 0.08rem;
          background-color: #d11413;
          position: absolute;
          left: 0;
          top: 0.13rem;
          z-index: 9;
        }
        .now {
          width: 50%;
        }
        .day {
          margin-top: 0.1rem;
          margin-bottom: 0.3rem;
          font-size: 0.232rem;
          line-height: 0.232rem;
          text-align: center;
        }
      }
      .calendar-bg {
        background-size: 100%;
        background-repeat: no-repeat;
        width: 1.15rem;
        height: 1.8rem;
        margin: 0 auto;
        padding-top: 0.87rem;
      }
      .prize {
        height: 0.35rem;
        text-align: center;
        font-size: 0.31rem;
        line-height: 0.35rem;
        color: #c61a31;
        margin-bottom: 0.2rem;
        img {
          height: 0.35rem;
          display: inline-block;
          vertical-align: middle;
          margin-right: -0.08rem;
        }
      }
      .has-sign {
        padding-top: 0.4rem;
      }
      .opacity-0 {
        opacity: 0;
      }
      .btn {
        width: 0.97rem;
        margin: 0 auto;
      }
    }
  }
  .prev {
    width: 0.48rem;
    position: absolute;
    top: 3.35rem;
    left: -0.13rem;
    z-index: 20;
  }
  .next {
    width: 0.48rem;
    position: absolute;
    top: 3.35rem;
    right: -0.13rem;
    z-index: 20;
  }
  .get-prize-time {
    width: 4.45rem;
    height: 0.3rem;
    position: absolute;
    top: 5.74rem;
    left: 1.41rem;
    font-size: 0.204rem;
    text-align: center;
    color: #fff;
    line-height: 0.3rem;
  }
  .get-prize-btn {
    width: 1.92rem;
    position: absolute;
    top: 7.16rem;
    left: 4.14rem;
  }
  .prize-num {
    width: 3rem;
    position: absolute;
    top: 7.9rem;
    right: 0.62rem;
    font-size: 0.204rem;
    text-align: center;
    color: #5d5d5d;
    line-height: 0.23rem;
  }
}
.gary {
  filter: grayscale(100%);
}
</style>
