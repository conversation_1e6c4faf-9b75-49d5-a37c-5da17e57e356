<template>
  <div class="bk" v-if="loadingFinish">
    <div :class="showData.className" v-if="pageType !== 5">
      <img :src="showData.pageImg" alt="" class="bk-img" />
      <div v-for="(item, index) in showData.hotZoneList" :key="index" :style="item.style" v-click-track="showData.clickCode" @click="toLink(item.url)"></div>
    </div>
    <div class="sku-list" v-else>
      <div class="list">
        <div v-for="item in skuList" :key="item" class="item" v-click-track="item">
          <img class="sku-img" :src="item.skuMainPicture" alt="" />
          <div class="sku-name">{{ item.skuName }}</div>
          <img class="btn" :src="showData.goSkuBtn" alt="" @click="toSku(item.skuId)" v-click-track="item.skuId" />
        </div>
      </div>
      <img :src="showData.moreBtn" alt="" class="more-btn" v-click-track="'gd'" @click="toLink(showData.moreLink)" />
    </div>
  </div>

  <VanPopup v-model:show="memberPopup">
    <div class="member-bk">
      <img :src="memberBk" alt="" class="bk-img" />
      <div :style="memberStyle" @click="toOpenCard" v-click-track="'ljrh'"></div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, inject } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast } from 'vant';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const decoData = inject('decoData') as any;

const styleData = [
  {
    calssName: 'first',
    clickCode: 'sg',
    pageType: 1,
  },
  {
    calssName: 'lock-right',
    clickCode: 'sq',
    pageType: 2,
  },
  {
    calssName: 'koi',
    clickCode: 'jl',
    pageType: 3,
  },
  {
    calssName: 'repurchase',
    clickCode: 'fg',
    pageType: 4,
  },
];

const showData = ref<any>({});
const memberBk = ref('');
const memberStyle = ref<any>({
  width: '0',
  height: '0',
  position: 'absolute',
  top: '0',
  left: '0',
});
const openCardLink = ref('');

const loadingFinish = ref(false);
const pageType = ref(0);
const memberPopup = ref(false);
const skuList = ref<any[]>();
let liveId = '';
const getRoomUrl = (): string => `https://lives.jd.com/#/${liveId}?origin=3&appid=jdzb&id=${liveId}`;

const toLink = (url: string) => {
  window.jmfe.toAny(url);
};

const toSku = (sku: number) => {
  window.jmfe.toSku(String(sku));
};

const toOpenCard = () => {
  window.jmfe.toAny(`${openCardLink.value}&returnUrl=${encodeURIComponent(liveId ? getRoomUrl() : '')}`);
};

const getExposureSkuList = async () => {
  try {
    showLoadingToast({ message: '加载中...', forbidClick: true });
    const res = await httpRequest.post('/swisseLive/getExposureSkuList');
    closeToast();
    skuList.value = res.data;
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

/**
 * 合并当前展示数据等
 */
const matchData = () => {
  if (pageType.value !== 0) {
    let combinedData = {};
    // 查找并合并 styleData 中匹配的项
    styleData.find((item) => {
      if (item.pageType === pageType.value) {
        combinedData = { ...combinedData, ...item };
        return combinedData;
      }
      return false;
    });
    // 查找并合并 decoData 中匹配的项
    decoData.map((item:any) => {
      if (item.prizeType === pageType.value) {
        combinedData = { ...combinedData, ...item };
        return combinedData;
      }
      return false;
    });
    // 将合并后的数据赋值给 showData.value
    showData.value = combinedData;
    // 热区部分样式赋值
    const style:any = {};
    showData.value.hotZoneList.forEach((item: any) => {
      style.width = `${(item.width * 2) / 100}rem`;
      style.height = `${(item.height * 2) / 100}rem`;
      style.position = 'absolute';
      style.top = `${(item.top * 2) / 100}rem`;
      style.left = `${(item.left * 2) / 100}rem`;
      item.style = style;
    });
    console.log('show', showData.value);
  }
};

const getActInfo = async () => {
  try {
    showLoadingToast({ message: '加载中...', forbidClick: true });
    const res = await httpRequest.post('/swisseLive/getUserType');
    closeToast();
    pageType.value = res.data.type;
    loadingFinish.value = true;
    if (res.data.type >= 5 || res.data.type <= 0) {
      await getExposureSkuList();
    }
    matchData();
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
const getLiveId = async () => {
  const { data } = await httpRequest.post('/live/getLiveId');
  liveId = data.liveId;
};
getLiveId();

if (!baseInfo.memberLevel) {
  // 给非会员弹窗图赋值
  decoData.forEach((item:any) => {
    if (item.prizeType === 6) {
      memberBk.value = item.pageImg;
      memberStyle.value = {
        width: `${(item.hotZoneList[0].width * 1.605) / 100}rem`,
        height: `${(item.hotZoneList[0].height * 1.605) / 100}rem`,
        position: 'absolute',
        top: `${(item.hotZoneList[0].top * 1.605) / 100}rem`,
        left: `${(item.hotZoneList[0].left * 1.605) / 100}rem`,
      };
      openCardLink.value = item.hotZoneList[0].url;
    }
  });
  memberPopup.value = true;
  loadingFinish.value = true;
  getExposureSkuList();
} else {
  getActInfo();
}
</script>

<style scoped lang="scss">
.bk {
  min-height: 100vh;
  position: relative;
  .first {
    position: relative;
    min-height: 100vh;
    background-color: #760001;
    .btn {
      position: absolute;
      top: 11.5rem;
      left: 1.8rem;
      width: 4.1rem;
      height: 0.9rem;
    }
  }
  .repurchase {
    position: relative;
    min-height: 100vh;
    background-color: #760001;
    .btn {
      position: absolute;
      top: 12rem;
      left: 1.8rem;
      width: 4.1rem;
      height: 0.9rem;
    }
  }
  .lock-right {
    position: relative;
    min-height: 100vh;
    background-color: #760001;
    .btn {
      position: absolute;
      top: 10.7rem;
      left: 1.8rem;
      width: 4.1rem;
      height: 0.9rem;
    }
  }
  .koi {
    position: relative;
    min-height: 100vh;
    background-color: #cbcdd1;
    .btn {
      position: absolute;
      top: 10.3rem;
      left: 0.5rem;
      width: 6.4rem;
      height: 0.9rem;
    }
  }
  .sku-list {
    min-height: 100vh;
    background-color: #8f0208;
    position: relative;
    padding: 0.44rem 0.24rem 0.4rem;
    .list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .item {
        width: 3.4rem;
        background-color: #c8111a;
        border-radius: 0.3rem;
        border: solid 0.06rem #ffffff;
        overflow: hidden;
        margin-bottom: 0.2rem;
        .sku-img {
          width: 3.4rem;
          height: 3.4rem;
          margin-bottom: 0.2rem;
        }
        .sku-name {
          width: 3rem;
          margin: 0 auto;
          font-size: 0.24rem;
          color: #ffffff;
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 0.6rem;
          border-top: 1px solid rgb(251 244 232 / 30%);
        }
        .btn {
          width: 2.47rem;
          margin: 0 auto;
        }
      }
    }
    .more-btn {
      width: 4.2rem;
      margin: 0 auto;
    }
  }
}
.bk-img {
  width: 100%;
}
.member-bk {
  width: 6rem;
  //.join {
  //  position: absolute;
  //  top: 5rem;
  //  left: 1.6rem;
  //  width: 2.9rem;
  //  height: 0.7rem;
  //}
}
</style>
