<template>
  <div class="not-pop">
    <div class="pop-bk">
     <div class="auth-box">
       <h3>开启摇一摇功能</h3>
       <p>需要您授权访问设备的运动传感器，才能使用摇一摇抽奖功能</p>
       <div class="btn-box">
         <div class="auth-btn confirm" @click="requestAuth">立即开启</div>
       </div>
     </div>
    </div>
    <img src="//img10.360buyimg.com/imgzone/jfs/t1/334827/7/20907/1554/68e72719F615303f9/48f9b5cfa337bf82.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
const emits = defineEmits(['close','requestAuth']);

const close = () => {
  emits('close');
};
const requestAuth = () => {
  close() ;
  emits('requestAuth');
};
</script>

<style scoped lang="scss">
.not-pop {
  .pop-bk {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/329463/17/23419/7940/68f096f4F4cc59c9a/f2b06c7955e47bb0.png') no-repeat;
    background-size: 100%;
    width: 5.31rem;
    height: 5.68rem;
    position: relative;
    padding: 0.6rem 0.4rem 0;
    .auth-box {
      height: 4rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      .btn-box {
        display: flex;
        gap: 0.2rem;
        .auth-btn {
          width: 2rem;
          height: 0.6rem;
          line-height: 0.6rem;
          text-align: center;
          border-radius: 0.2rem;
        }
        .cancel {
          background: #f5f5f5;
          color: #333;
        }
        .confirm {
          background: #ffa500;
          color: #fff;
        }
      }
    }
  }
  .close {
    width: 0.52rem;
    margin: 0.2rem auto;
  }
}

</style>
