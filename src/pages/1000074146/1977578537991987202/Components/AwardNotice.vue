<template>
  <div class="award-notice" v-if="awardList.length > 0">
    <div class="award-swiper swiper-container">
      <div class="swiper-wrapper">
        <div class="swiper-slide award-item" v-for="(item, index) in awardList" :key="index">
          <div class="award-content">
            <div class="award-text">
              <span >恭喜{{ item.nickName }}获得{{ item.prizeName }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';

Swiper.use([Autoplay]);

interface AwardItem {
  userName?: string;
  avatar?: string;
  award: string;
}

const props = defineProps<{
  awardList: [];
}>();



let awardSwiper: Swiper | null = null;

const initSwiper = () => {
  nextTick(() => {
    // 销毁之前的实例，避免重复初始化
    if (awardSwiper) {
      awardSwiper.destroy(true, true);
    }

    // 创建新的Swiper实例
    awardSwiper = new Swiper('.award-swiper', {
      direction: 'vertical', // 垂直方向
      loop: true, // 循环模式
      autoplay:  {
        delay: 1000,
        disableOnInteraction: false, // 用户交互后不停止自动轮播
      } ,
      speed: 800, // 过渡速度
      observer: true, // 监视DOM变化
      observeParents: true, // 监视父元素变化
    });
  });
};

// 监听awardList变化，重新初始化swiper
watch(() => props.awardList, () => {
  if (props.awardList && props.awardList.length > 1) {
    initSwiper();
  }
}, { deep: true });

onMounted(() => {
  if (props.awardList && props.awardList.length > 1) {
    initSwiper();
  }
});

onUnmounted(() => {
  // 组件卸载时销毁swiper实例
  if (awardSwiper) {
    awardSwiper.destroy(true, true);
    awardSwiper = null;
  }
});
</script>

<style scoped lang="scss">
.award-notice {
  position:absolute;
  top: 0.8rem;
  left: 0.75rem;
  z-index: 2;
  height: 0.45rem;
  overflow: hidden;
  width:6rem;
  margin: 0.2rem auto 0;
  background-color: #000;
  border-radius: 0.4rem;
  .award-swiper {
    width: 100%;
    height: 100%;
    .award-item {
      height: 0.45rem;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 0.3rem;
      .award-content {
        .award-text {
          font-size: 0.24rem;
          line-height: 1.2;
          overflow: hidden;
          color: #fff;
        }
      }
    }
  }
}
</style>
