<template>
  <div class="rule-bk">
    <div class="close" @click="close"></div>
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="type">{{ prizeType[item.prizeType] }}</div>
        <div class="info">
          <img :src="item.prizeImg" alt="" class="show-img" />
          <div class="detail">
            <div class="name">{{ item.prizeName }}</div>
            <div class="time">获奖时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div class="status" v-if="item.prizeType === 3">
            <div class="orange" v-if="!item.deliveryStatus && item.status !== 2">待发货</div>
            <div class="red" v-if="item.status === 2">已取消</div>
            <div class="green" v-if="item.deliveryStatus && item.status !== 2">已发货</div>
            <div class="blue" v-if="!item.deliveryStatus && item.status !== 2 && !item.realName" @click="changAddress(item)">填写地址</div>
            <div class="blue" v-else @click="changAddress(item)">查看地址</div>
<!--            <div class="blue" @click="item.showOrder = !item.showOrder">{{ item.showOrder ? '收起订单' : '关联订单' }}</div>-->
          </div>
          <div class="status" v-else-if="item.prizeType === 7">
            <div class="orange" v-if="item.status === 1">待发放</div>
            <div class="green" v-else-if="item.status === 3">已发放</div>
            <div class="red" v-else-if="item.status === 2">已取消</div>
            <div class="blue" @click="showCardNum(item)" v-if="item.status === 3">如何兑换</div>
<!--            <div class="blue" @click="item.showOrder = !item.showOrder">{{ item.showOrder ? '收起订单' : '关联订单' }}</div>-->
          </div>
          <div class="status" v-else-if="item.prizeType === 9 || item.prizeType === 10">
            <div class="orange" v-if="item.status === 1">待发放</div>
            <div class="green" v-else-if="item.status === 3">已发放</div>
            <div class="red" v-else-if="item.status === 2">已取消</div>
            <div class="blue" @click="exchangePlusOrAiqiyi" v-if="item.status === 3">立即兑换</div>
<!--            <div class="blue" @click="item.showOrder = !item.showOrder">{{ item.showOrder ? '收起订单' : '关联订单' }}</div>-->
          </div>
          <div class="status" v-else-if="item.prizeType === 12">
            <div class="orange" v-if="item.status === 1">待发放</div>
            <div class="green" v-else-if="item.status === 3">已发放</div>
            <div class="red" v-else-if="item.status === 2">已取消</div>
            <div class="blue" @click="savePhone(item)" v-if="item.isFuLuWaitingReceive && item.status !== 2">填写信息</div>
<!--            <div class="blue" @click="item.showOrder = !item.showOrder">{{ item.showOrder ? '收起订单' : '关联订单' }}</div>-->
          </div>
          <div class="status" v-else>
            <div class="orange" v-if="item.status === 1">待发放</div>
            <div class="green" v-else-if="item.status === 3">已发放</div>
            <div class="red" v-else-if="item.status === 2">已取消</div>
<!--            <div class="blue" @click="item.showOrder = !item.showOrder">{{ item.showOrder ? '收起订单' : '关联订单' }}</div>-->
          </div>
        </div>
        <div v-if="item.showOrder" class="order-list">
          <div v-for="item1 in item.orderList" :key="item1.orderId" class="order-info">
            <div class="order-id">订单号：{{ item1.orderId }}</div>
            <div class="order-status">订单状态：{{ item1.orderStatus }}</div>
            <div class="order-price">订单金额：{{ item1.orderPrice }}</div>
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :userReceiveRecordId="userReceiveRecordId" :activityPrizeId="activityPrizeId" :echoData="echoData" @close="closeSaveAddress"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';

import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  userReceiveRecordId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  status: number;
  showOrder: boolean;
  orderList: {
    orderId: string;
    orderStatus: string;
    orderPrice: string;
  }[];
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/80077/getReceive');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
    prizes.forEach((item) => {
      item.showOrder = false;
    });
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const showSaveAddress = ref(false);
const userReceiveRecordId = ref('');
const activityPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 修改地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast('活动已结束~');
    return;
  }
  userReceiveRecordId.value = item.userReceiveRecordId;
  activityPrizeId.value = item.userPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits('savePhone', item.userReceiveRecordId, prizeContent.result.planDesc);
};
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/161727/17/39326/346153/64c899cfF24b2efd1/aaedb735aae58d6b.png) no-repeat;
  background-size: 100%;
  width: 100vw;

  .close {
    width: 0.35rem;
    height: 0.35rem;
    position: relative;
    left: 6.7rem;
    top: 0.3rem;
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/163849/34/37594/298/64c87c0cF3e6aab57/00fa23dd4bece777.png");
    background-repeat: no-repeat;
    background-size: 100%;
  }

  .content {
    height: 7rem;
    width: 7.5rem;
    margin: 0.7rem auto 0;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .prize {
      width: 6.9rem;
      background-color: #ffffff;
      margin: 0 auto 0.1rem auto;
      padding-bottom: 0.24rem;
      border-radius: 0.16rem;

      .flex {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.08rem 0.22rem;
      }

      .type {
        color: #999999;
        font-size: 0.2rem;
        text-align: left;
        border-bottom: #eee 0.02rem dashed;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding: 0.1rem 0;
      }
      .time {
        color: #999999;
        font-size: 0.2rem;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 0.9rem;
        margin-left: 0.22rem;
        margin-right: 0.22rem;

        .show-img {
          width: 0.6rem;
          height: 0.6rem;
          border-radius: 50%;
        }

        .detail {
          flex: 1;
          padding-left: 0.28rem;

          .name {
            font-size: 0.28rem;
            color: #000000;
          }
        }

        .status {
          font-size: 0.24rem;
          text-align: right;
          .gray {
            color: #333333;
          }
          .green {
            color: #6bce98;
          }

          .orange {
            color: #ff9900;
          }

          .blue {
            color: #0083ff;
          }

          .red {
            color: #ff3333;
          }
        }
      }
      .order-list {
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        margin-top: 0.2rem;
        border-top: 0.02rem dashed #eee;
      }
      .order-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 0.2rem;
        font-size: 0.2rem;
        color: #999999;

        .order-id {
          flex: 1.5;
        }
        .order-status {
          flex: 1;
        }
        .order-price {
          flex: 1.1;
        }
      }
    }

    .no-data {
      height: 100%;
      font-size: 0.24rem;
      color: #8c8c8c;
      //background: url(//img10.360buyimg.com/imgzone/jfs/t1/232560/34/9110/4825/65854f01Fdebcf9a2/a37203c75a9d6936.png) no-repeat;
      background-size: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
