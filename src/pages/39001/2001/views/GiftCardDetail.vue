<template>
  <div class="gift-card-bg">
    <img alt="" :src="giftCardInfo.rightsImg" class="gift-card-img"/>
    <div class="card-info-box">
      <div class="table-box">
         <div class="card-list-title-line">
          <div class="title-1">序号</div>
          <div class="title-2">卡号</div>
          <div class="title-3">卡密</div>
        </div>
        <div class="card-list-info-line"  v-for="(item, index) in giftCardInfo.giftCardList" :key="index">
          <div class="title-1">{{ index+1 }}</div>
          <div class="title-2 info-box2">
            <div class="card-number">{{item.cardNumber}}</div>
            <div class="copy-btn" :copy-text="item.cardNumber">复制</div>
          </div>
          <div class="title-3 info-box3">
            <div class="card-pass-word">{{item.cardPassword}}</div>
            <div class="copy-btn" :copy-text="item.cardPassword">复制</div>
          </div>
        </div>
      </div>
    </div>
    <div class="gift-tips-box">
      <div class="tips-title">礼品卡说明</div>
      <div class="tips-info">{{giftCardInfo.cardDesc}}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref } from 'vue';
import Clipboard from 'clipboard';
import { useRoute } from 'vue-router';

const route = useRoute();
const giftCardInfo = ref([]);
const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
const getGiftCardInfo = async () => {
  try {
    const { data } = await httpRequest.post('/39001/getGiftCardInfo', { id: route.query.id });
    giftCardInfo.value = data;
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};
const init = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await getGiftCardInfo();
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
.gift-card-bg {
  background-color: #F2F2F2;
  height: 100vh;
  .gift-card-img {
    margin: 0.3rem;
    width: 6.91rem;
    border-radius: 0.05rem;
  }
  .card-info-box {
    margin-bottom: 0.93rem;
    padding: 0.22rem  0.16rem  0.7rem ;
    background-color: #fff;
    width: 7.5rem;
  }
  .table-box {
    border-radius: 0.01rem;
    border-top: solid 0.01rem #4b4b4b;
    border-left: solid 0.01rem #4b4b4b;
    text-align: center;
  }
  .card-list-title-line {
    display: flex;
    height: 0.42rem;
    line-height: 0.42rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.27rem;
    color: #333333;
  }
  .card-list-info-line {
    display: flex;
    height: 0.57rem;
    line-height: 0.57rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.27rem;
    color: #333333;
  }
  .title-1 {
    flex: 1;
  }
  .title-2 {
    flex: 3.3;
  }
  .title-3 {
    flex: 3.3;
  }
  .title-1,.title-2,.title-3 {
    border-right: solid 0.01rem #4b4b4b;
    border-bottom: solid 0.01rem #4b4b4b;
  }
  .info-box2,.info-box3 {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .card-number,.card-pass-word {
    margin-left: 0.15rem;
    width: 1.95rem;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    white-space: nowrap;
  }
  .copy-btn {
    margin-right: 0.15rem;
    width: 0.82rem;
    height: 0.29rem;
    line-height: 0.29rem;
    background-color: #0083ff;
    border-radius: 0.144rem;
    font-family: MiSans-Regular;
    font-size: 0.198rem;
    color: #ffffff;
    text-align: center;
  }
  .gift-tips-box {
    padding: 0rem 0.29rem;
    .tips-title {
      margin-bottom: 0.25rem;
      font-family: MiSans-Bold;
      font-size: 0.3rem;
      color: #333333;
    }
    .tips-info {
      width: 6.66rem;
      font-family: MiSans-Medium;
      font-size: 0.24rem;
      line-height: 0.36rem;
      color: #666666;
    }
  }
}
</style>
