<template>
    <div class="main-view">
        <!-- 弹幕 -->
        <div>
            <div class="scroll-wrapper">
                <div class="scroll-box" :style="{transform: `${translate}`}">
                <div class="data-list-part c-row">
                    <div class="data-list">
                        <div class="item" v-for="(item, index) in articleName1" :key="index">
                        {{item}}
                        </div>
                        <div class="item" v-for="(item, index) in articleName1" :key="index">
                        {{item}}
                        </div>
                    </div>
                </div>
                </div>
            </div>
            <div class="scroll-wrapper">
                <div class="scroll-box" :style="{transform: `${translate1}`}">
                <div class="data-list-part c-row">
                    <div class="data-list">
                    <div class="item" v-for="(item, index) in articleName2" :key="index">
                        {{item}}
                    </div>
                    <div class="item" v-for="(item, index) in articleName2" :key="index">
                        {{item}}
                    </div>
                    </div>
                </div>
                </div>
            </div>
            <div class="scroll-wrapper">
                <div class="scroll-box" :style="{transform: `${translate}`}">
                <div class="data-list-part c-row">
                    <div class="data-list">
                        <div class="item" v-for="(item, index) in articleName3" :key="index">
                        {{item}}
                        </div>
                        <div class="item" v-for="(item, index) in articleName3" :key="index">
                        {{item}}
                        </div>
                    </div>
                </div>
                </div>
            </div>
            <!-- <div class="scroll-wrapper">
                <div class="scroll-box" :style="{transform: `${translate1}`}">
                <div class="data-list-part c-row">
                    <div class="data-list">
                    <div class="item" v-for="(item, index) in articleName2" :key="index">
                        {{item}}
                    </div>
                    <div class="item" v-for="(item, index) in articleName2" :key="index">
                        {{item}}
                    </div>
                    </div>
                </div>
                </div>
            </div> -->
        </div>
        <div class="right-box">
            <div class="right-btn" @click="showRulePopup = true" style="background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/268591/33/7518/1478/6777fa92Fdedada6a/c0f6c15b1167f0e1.png')"></div>
            <div class="right-btn" @click="userPrizes" style="background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/255961/38/8009/1492/6777fa92Fb04ca5f2/a0b104c1990651f0.png')"></div>
        </div>
      <div class="top-tab">
          <img alt="" class="tab-item" v-for="(item, index) in tabList" :key="index" :src="selectTab == item.id ? item.categoryRemark : item.categoryImageUrl" @click="handleSelect(item.id)">
      </div>
      <div class="search-box">
          <input type="text" v-model="searchText" placeholder="搜索标题名称" maxlength="10" />
          <div class="search-btn" @click="handleSearch"></div>
      </div>
      <div class="type-tab">
          <div :class="pageType == '最新'? 'select-tab':''" @click="changePageType('最新')">最新</div>
          <div :class="pageType == '最热'? 'select-tab':''" @click="changePageType('最热')">最热</div>
      </div>
      <div class="home-phto-box" id="worksBox1" ref="scrollableContent" v-show="pageType == '最新'">
          <div style="width: 3.6rem;" class="photo-padding" >
            <photo v-for="(item, index) in pageNew0" :photoInfo="item" :photoStyle="index%2== 0 ? 0:1" :key="index" @handleOpenPhotoPage="handleOpenPhotoPage" @handleTaskLove="getMainFn"></photo>
          </div>
          <div style="width: 3.6rem;">
            <photo v-for="(item, index) in pageNew1" :photoInfo="item" :photoStyle="index%2== 0 ? 1:0" :key="index" @handleOpenPhotoPage="handleOpenPhotoPage" @handleTaskLove="getMainFn"></photo>
          </div>
      </div>
      <div class="home-phto-box" id="worksBox2" ref="scrollableContent2" v-show="pageType == '最热'">
          <div style="width: 3.6rem;">
            <photo v-for="(item, index) in pageHot0" :photoInfo="item" :photoStyle="index%2== 0 ? 1:0" :key="index" @handleOpenPhotoPage="handleOpenPhotoPage"></photo>
          </div>
          <div style="width: 3.6rem;" class="photo-padding">
            <photo v-for="(item, index) in pageHot1" :photoInfo="item" :photoStyle="index%2== 0 ? 0:1" :key="index" @handleOpenPhotoPage="handleOpenPhotoPage"></photo>
          </div>
      </div>
      <img src="https://img10.360buyimg.com/imgzone/jfs/t1/257478/21/8335/1150/677b4cdaFa7b7ed14/5d1615eb9c9874b5.png" class="scroll-tips"/>
      <div class="prize-view">
          我的积分： {{score}}
          <div class="bottom-view">
          <img class="bottom-item" @click="lotteryDraw" src="https://img10.360buyimg.com/imgzone/jfs/t1/261698/13/10134/7945/677f6d9dF703324fb/5c6e49e3d59d347a.png">
          <!-- <img class="bottom-item" @click="showTaskPopup = true" src="https://img10.360buyimg.com/imgzone/jfs/t1/268423/22/7388/3906/6777848dFb6028372/bce6811b6b8f3130.png"> -->
      </div>
      </div>
      <div class="task-popup">
          <div class="title-img-box">
              <img class="title-img" src="https://img20.360buyimg.com/imgzone/jfs/t1/261788/39/10063/2843/677f6c1bFe68e8b07/f06295d4d971af29.png">
          </div>
        <!-- <div class="close-btn" @click="showTaskPopup = false"></div> -->
        <!-- <div class="point-view">
            我的积分： {{score}}
        </div> -->
        <div class="task-item" v-for="(item, index) in taskList" :key="index" :style="{backgroundImage: `url(${JSON.parse(item.taskDescription)['imgUrl']})`}">
            <div class="task-act-btn" v-if="item.status" @click="doTask(item)">{{JSON.parse(item.taskDescription).actText}}</div>
            <div class="task-gary-btn" v-else>{{JSON.parse(item.taskDescription).garyText}}</div>
        </div>
    </div>
    <VanPopup teleport="body" v-model:show="showTaskPopup" position="center" :close-on-click-overlay="isCloseOverlay">
        <div class="popop-view">
            <div class="task-popup">
                <div class="close-btn" @click="showTaskPopup = false"></div>
                <div class="point-view">
                    我的积分： {{score}}
                </div>
                <div class="task-item" v-for="(item, index) in taskList" :key="index" :style="{backgroundImage: `url(${JSON.parse(item.taskDescription)['imgUrl']})`}">
                    <div class="task-act-btn" v-if="item.status" @click="doTask(item)">{{JSON.parse(item.taskDescription).actText}}</div>
                    <div class="task-gary-btn" v-else>{{JSON.parse(item.taskDescription).garyText}}</div>
                </div>
            </div>
        </div>
    </VanPopup>
    <!-- 活动规则 -->
    <VanPopup teleport="body" v-model:show="showRulePopup" position="center" :close-on-click-overlay="isCloseOverlay">
        <div style="width: 6.5rem;height: 8rem;position:relative;">
            <div class="rule-popup">
                <div class="title">活动规则</div>
                <div class="rule-content" v-html="ruleFormat(rules)">
                </div>
            </div>
            <div class="close-btn" style="bottom: 0rem;top:unset;left: 2.95rem;" @click="showRulePopup = false"></div>
        </div>
    </VanPopup>
    <!-- 我的奖品 -->
    <VanPopup teleport="body" v-model:show="showMyPrizePopup" position="center" :close-on-click-overlay="isCloseOverlay">
        <div style="width: 6.5rem;height: 8rem;position:relative;">
            <div class="rule-popup">
                <div class="title">我的奖品</div>
                <div class="my-prize-popup">
                    <div class="my-prize-item">
                        <div style="flex: 1;display: flex;justify-content: center;"><div class="my-prize-title">时间</div></div>
                        <div style="flex: 1;display: flex;justify-content: center;"><div class="my-prize-title">奖品</div></div>
                        <div style="flex: 1;display: flex;justify-content: center;"><div class="my-prize-title">领取</div></div>
                    </div>
                    <div class="my-prize-item" v-for="(item, index) in myPrizeList" :key="index">
                        <div style="flex: 1;display: flex;justify-content: center;">{{formatDate(new Date(item.createTime))}}</div>
                        <div style="flex: 1;display: flex;justify-content: center;"><div class="my-prize--name">{{item.rightsName}}</div></div>
                        <div style="flex: 1;display: flex;justify-content: center;" v-if="item.recordType == 1">
                            <div class="my-prize-btn" @click="openRankAddressPopup(item)">{{item.address ? '查看地址': '填写地址'}}</div>
                        </div>
                        <div style="flex: 1;display: flex;justify-content: center;" v-else>
                            <div class="my-prize-btn" v-if="item.rightsType == 3" @click="openAddressPopup(item)">{{item.address ? '查看地址': '填写地址'}}</div>
                            <div v-else>已发放</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="close-btn" style="bottom: 0rem;top:unset;left: 2.95rem;" @click="showMyPrizePopup = false"></div>
        </div>
    </VanPopup>
    <!-- 未中奖 -->
    <VanPopup teleport="body" v-model:show="showNoPrize" position="center" :close-on-click-overlay="isCloseOverlay">
        <div style="width: 6.5rem;height: 8rem;position:relative;">
            <div class="no-prize" @click="showNoPrize = false"></div>
            <div class="close-btn" style="bottom: 0rem;top:unset;left: 2.95rem;" @click="showNoPrize = false"></div>
        </div>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showPrize" position="center" :close-on-click-overlay="isCloseOverlay">
        <div style="width: 6.5rem;height: 8rem;position:relative;">
            <div class="prize-popup">
                <div class="prize-img" :style="{backgroundImage: `url(${prizeResult.prizeImg})`}"></div>
                <div class="prize-name">{{prizeResult.prizeName}}</div>
                <div class="prize-tips" v-if="prizeResult.prizeType == 1">优惠券稍后将放入您的账户中</div>
                <div class="prize-tips" v-if="prizeResult.prizeType == 3">请在活动时间内填写完毕收货信息，逾期未填写</div>
                <div class="prize-tips" v-if="prizeResult.prizeType == 3">将视为用户主动放弃该奖品权益</div>
                <div class="prize-tips" v-if="prizeResult.prizeType == 2">京豆将放入您的账户，届时可在个人中心-京豆中查看</div>
                <div class="prize-tips" v-if="prizeResult.prizeType == 6">红包稍后将放入您的账户中</div>
                <div class="prize-btn" v-if="prizeResult.prizeType == 3" @click="showAddressPop = true">填写地址</div>
                <div class="prize-btn" v-else @click="showPrize = false">确认</div>
            </div>
            <div class="close-btn" style="bottom: 0rem;top:unset;left: 2.95rem;" @click="showPrize = false"></div>
        </div>
    </VanPopup>
    <!-- 加购商品 -->
    <VanPopup teleport="body" v-model:show="showAddPopup" position="center" :close-on-click-overlay="isCloseOverlay">
        <div class="popop-view">
            <div class="product-popup">
                <div class="close-btn" @click="showAddPopup = false"></div>
                <div class="title">加购商品</div>
                <div class="product-view" style="height: 6.5rem;">
                    <div class="product-item" style="height: 2.6rem;" v-for="(item, index) in addSku" :key="index">
                        <img class="product-img" :src="item.imagePath">
                        <div class="product-name">{{item.name}}</div>
                        <!-- <div class="product-act-btn" v-if="item.status == '1'" @click="addCarSku(item)">去加购</div>
                        <div class="product-gary-btn" v-else>已加购</div> -->
                    </div>
                </div>
                <img style="width: 7.5rem;height: 0.75rem;margin-top: 0.4rem;" @click="addCarSku(item)" src="//img10.360buyimg.com/imgzone/jfs/t1/264428/10/8805/1091/677b9de3F4af9ae09/dcd46c51ef29134a.png">
            </div>
        </div>
    </VanPopup>
    <!-- 浏览商品 -->
    <VanPopup teleport="body" v-model:show="showLinkPopup" position="center" :close-on-click-overlay="isCloseOverlay">
        <div class="popop-view">
            <div class="product-popup">
                <div class="close-btn" @click="showLinkPopup = false"></div>
                <div class="title">浏览商品</div>
                <div class="product-view">
                    <div class="product-item" v-for="(item, index) in linkSku" :key="index">
                        <img class="product-img" :src="item.imagePath">
                        <div class="product-name">{{item.name}}</div>
                        <div class="product-act-btn" v-if="!item.browse" @click="lingSku(item)">去浏览</div>
                        <div class="product-gary-btn" v-else>已浏览</div>
                    </div>
                </div>
            </div>
        </div>
    </VanPopup>
    <!-- 入会 -->
    <VanPopup teleport="body" v-model:show="showOpenCardPopup" position="center" :close-on-click-overlay="isCloseOverlay">
        <div class="popop-view">
            <div class="product-popup">
                <div class="close-btn" @click="showOpenCardPopup = false"></div>
                <div class="title">加入会员</div>
                <div class="product-view">
                    <div v-for="(item, index) in shopList" :key="index" class="member-item" :style="{backgroundImage: `url(${item.logoUrl})`}">
                        <div v-if="item.openStatus" class="member-act-btn" @click="openCard(item.openUrl)">去入会</div>
                        <div v-else class="member-gary-btn">已入会</div>
                    </div>
                </div>
            </div>
        </div>
    </VanPopup>
    <!-- 进入店铺 -->
    <VanPopup teleport="body" v-model:show="showOrderPopup" position="center" :close-on-click-overlay="isCloseOverlay">
        <div class="popop-view">
            <div class="product-popup">
                <div class="close-btn" @click="showOrderPopup = false"></div>
                <div class="title">进入店铺下单</div>
                <div class="product-view">
                    <div v-for="(item, index) in shopList" :key="index" class="member-item" :style="{backgroundImage: `url(${item.logoUrl})`}">
                        <div class="member-act-btn" @click="gotoShopPage(item.shopId)">进入店铺</div>
                        <!-- <div v-else class="member-gary-btn">已入会</div> -->
                    </div>
                </div>
            </div>
        </div>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showAddressPop" position="center">
        <AddressPop :activityPrizeId="activityPrizeId" :addressId="addressId" :addressData="addressData" @closePop="showAddressPop = false;userPrizes()"></AddressPop>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showRankReplayAddressPop" position="center">
        <RankReplayAddressPop :activityPrizeId="activityPrizeId" :addressId="addressId" :addressData="addressData" @closePop="showRankReplayAddressPop = false;userPrizes()"></RankReplayAddressPop>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showPhotoInfo" position="center">
        <div style="width: 6.7rem;height: auto;position:relative;">
            <photoInfo :photoInfo="infoWork" :photoStyle="0" :key="index" @handleOpenPhotoPage="()=>{}"></photoInfo>
            <div class="close-btn"  @click="showPhotoInfo = false"></div>
        </div>
    </VanPopup>
</div>
</template>
<script lang="ts" setup>
/* eslint-disable */
import { ref, Ref, reactive, onBeforeMount, inject, onMounted, onUnmounted } from 'vue';
import { emojiRegex, isValidChineseId, ruleFormat, formatDate, shareUuid, shareWork } from '../common';
import { httpRequest } from '@/utils/service';
import { showLoadingToast, showToast, closeToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import photo from '../components/photo';
import photoInfo from '../components/photoInfo';
import { callShare } from '@/utils/platforms/share';
import AddressPop from '../components/AddressPop.vue';
import RankReplayAddressPop from '../components/RankReplayAddressPop.vue';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const baseUserInfo: any = inject('baseUserInfo');
const pathParams: any = inject('pathParams');
const category: any = inject('category');
const toggleComponent = inject('toggleComponent');
const isCloseOverlay = ref(false);
const showTaskPopup = ref(false);
const showOrderPopup = ref(false);
const showAddPopup = ref(false);
const showLinkPopup = ref(false);
const showOpenCardPopup = ref(false);
const showRulePopup = ref(false);
const showMyPrizePopup = ref(false);
const showAddressPop = ref(false);
const showRankReplayAddressPop = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const addressData = ref({});

const showPrize = ref(false);
const showNoPrize = ref(false);

const infoWork = ref({});
const showPhotoInfo = ref(false);

const rules = inject('rules');

type DefineEmits = {
  (e: 'toggle-component', componentName: string): void;
};
const emits = defineEmits<DefineEmits>();

const photoInfoPopup = ref(false);
const handleOpenPhotoPage = (info):void => {
    showPhotoInfo.value = true;
    infoWork.value = info;
};

const searchText = ref('');
const tabList = ref([]);
const getCategory = async () => {
    console.log(category.value);

    const data = JSON.parse(JSON.stringify(category.value));
    const all = {
        categorySort: 0,
        categoryImageUrl: 'https://img10.360buyimg.com/imgzone/jfs/t1/260670/29/8604/2275/677b5110F9c5fea8c/478278966c6a2664.png',
        categoryRemark: 'https://img10.360buyimg.com/imgzone/jfs/t1/225099/12/29484/2242/677b5110F807473c8/7b4eed77891bb335.png',
        id: ''
    }
    data.unshift(all);
    tabList.value = data;
}
const shopList = reactive([{
    shopName: 'vivo京东自营旗舰店',
    shopId: 1000085868,
    openUrl: 'https://shopmember.m.jd.com/shopcard?venderId=1000085868&channel=401',
    venderId: 1000085868,
    logoUrl: 'https://img10.360buyimg.com/imgzone/jfs/t1/254845/23/9931/9074/677c99fdF06c2ba53/2e552c2ce6b6fff8.png',
    openStatus: false
},{
    shopName: 'vivo官方旗舰店',
    shopId: 770171,
    openUrl: 'https://shopmember.m.jd.com/shopcard?venderId=774276&channel=401',
    venderId: 774276,
    logoUrl: 'https://img10.360buyimg.com/imgzone/jfs/t1/262295/1/9039/8617/677c99fcFe449f5ef/a6bbd1ca87b12223.png',
    openStatus: false
},{
    shopName: 'a2海外京东自营旗舰店',
    shopId: 1000015026,
    openUrl: 'https://shopmember.m.jd.com/shopcard?venderId=1000015026&channel=401',
    venderId: 1000015026,
    logoUrl: 'https://img10.360buyimg.com/imgzone/jfs/t1/258800/39/7514/21785/6777d21dF78200a14/c405cd0d8481b5df.png',
    openStatus: true
},{
    shopName: '三只松鼠京东自营旗舰店',
    shopId: 1000015268,
    openUrl: 'https://shopmember.m.jd.com/shopcard?venderId=100000000000466&channel=80144130',
    venderId: 100000000000466,
    logoUrl: 'https://img10.360buyimg.com/imgzone/jfs/t1/261281/13/7397/31784/6777d234Fe7477b77/1d40f76d2609d2fd.png',
    openStatus: false
}])
const taskList = ref([]);

const getTaskList = async () => {
    const formdata = {}
    const { data, message } = await httpRequest.get('/dz/1874673979168907266/taskList', formdata, { isLoading: true });
    if (data) {
        taskList.value = data;
    } else {
        showToast(message)
    }
}
const getStoreList = async () => {
    const {data, message} = await httpRequest.get('/dz/1874673979168907266/storeList', {}, {isLoading: true});
    if (data) {
        shopList.forEach(element => {
            const shop = data.filter((item)=>item.shopId == element.shopId);
            element.openStatus = !shop[0].open
        });
        showOpenCardPopup.value = true;
        console.log(data);
    } else {
        showToast(message)
    }
}
const openCard = (openUrl) => {
    // console.log(`${encodeURIComponent(`${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?adSource=${pathParams.adsource}&shareUuid=${shareUuid}`)}`);
  window.location.href = `${openUrl}&returnUrl=${encodeURIComponent(`${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?adSource=${pathParams.adsource}&shareUuid=${shareUuid}`)}`;
};
// 分享
const handleShare = () => {

  const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
  console.log(`${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?shareId=${shareConfig.shareId}`);
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
    shareUrl: `${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?shareId=${shareConfig.shareId}`,
    afterShare: () => { },
  });
};
//浏览商品
const linkSku = ref([]);
const addSku = ref([]);
const showSkuList = async (type) => {
    try {
    const {data, message} = await httpRequest.post('/dz/1874673979168907266/showSkuList', {type}, {isLoading: true});
    if (data) {
        console.log(data);
        if (type == 1) {
            linkSku.value = data;
            showLinkPopup.value = true;
            showTaskPopup.value = false;
        }
        if (type == 2) {
            addSku.value = data;
            showAddPopup.value = true;
            showTaskPopup.value = false;
        }
    } else {
        showToast(message)
    }
} catch (error: any) {
    showToast(error.message);
    }
};
const pageType = ref('最新');
const changePageType = (type) => {
    const worksBox1 = document.getElementById('worksBox1');
    worksBox1?.scrollTo(0, 0);
    const worksBox2 = document.getElementById('worksBox2');
    worksBox2?.scrollTo(0, 0);
    pageType.value = type;
};
const pageNewList = ref([]);
const pageNew0 = ref();
const pageNew1 = ref();
// console.log(page0, page1);
const pageHotList = ref([]);
const pageHot0 = ref();
const pageHot1 = ref();
const pageNo = ref(1);
const pageNo1 = ref(1);

const selectTab = ref('');
// 最新作品
const getPageNewList = async (isSearch:any) => {
    const formdata = {
      categoryId: selectTab.value,
      content: searchText.value,
      pageNo: pageNo.value,
      pageSize: 10,
      sortType: 1
    }
    if (isSearch) {
        // 第一次点查询按钮，后续加载应该不走这里的重置
        pageNo.value = 1;
        formdata.pageNo = 1
    }
    try {
    const { data, message } = await httpRequest.post('/dz/1874673979168907266/worksPage', formdata, { isLoading: true });
    if (data) {
        // console.log(data);
        let temp = []
        if (isSearch) {
            temp = [];
            data.records.forEach(element => {
                temp.push(element);
            });
            pageNewList.value = temp;
            pageNew0.value = pageNewList.value.filter((item, index) => index%2 == 0);
            pageNew1.value = pageNewList.value.filter((item, index) => index%2 == 1);
        } else {
            temp = JSON.parse(JSON.stringify(pageNewList.value));
            data.records.forEach(element => {
                temp.push(element);
            });
            pageNewList.value = temp;
            pageNew0.value = pageNewList.value.filter((item, index) => index%2 == 0);
            pageNew1.value = pageNewList.value.filter((item, index) => index%2 == 1);
        }
    } else {
        showToast(message)
    }
} catch (error: any) {
    showToast(error.message);
    }
}
// 最热作品
const getPageHotList = async (isSearch:any) => {
    const formdata = {
      categoryId: selectTab.value,
      content: searchText.value,
      pageNo: pageNo1.value,
      pageSize: 10,
      sortType: 0
    }
    if (isSearch) {
        // 第一次点查询按钮，后续加载应该不走这里的重置
        pageNo1.value = 1;
        formdata.pageNo = 1
    }
    try {
    const { data, message } = await httpRequest.post('/dz/1874673979168907266/worksPage', formdata, { isLoading: true });
    if (data) {
        // console.log(data);
        let temp = []
        if (isSearch) {
            temp = [];
            data.records.forEach(element => {
                temp.push(element);
            });
            pageHotList.value = temp;
            pageHot0.value = pageHotList.value.filter((item, index) => index%2 == 0);
            pageHot1.value = pageHotList.value.filter((item, index) => index%2 == 1);
        } else {
            temp = JSON.parse(JSON.stringify(pageHotList.value));
            data.records.forEach(element => {
                temp.push(element);
            });
            pageHotList.value = temp;
            pageHot0.value = pageHotList.value.filter((item, index) => index%2 == 0);
            pageHot1.value = pageHotList.value.filter((item, index) => index%2 == 1);
        }
    } else {
        showToast(message)
    }
} catch (error: any) {
    showToast(error.message);
    }
}
const handleSelect = (id) => {
    selectTab.value = id;
    pageNewList.value = [];
    pageHotList.value = [];
    getPageNewList(true);
    getPageHotList(true);
}
const handleSearch = (params:type) => {
    // console.log(searchText.value);
    pageNewList.value = [];
    pageHotList.value = [];
    getPageNewList(true);
    getPageHotList(true);
}
const scrollableContent = ref(null);
const scrollableContent2 = ref(null);
const screen = window.innerWidth / 750;
const handleScroll = (event) => {
  const scrollTop = event.target.scrollTop;
  console.log(scrollTop/screen);
  if (scrollTop/screen >= 700 * pageNo.value) {
    // 在这里调用你想要执行的方法
    pageNo.value += 1;
    getPageNewList();
  }
}
const handleScroll1 = (event) => {
  const scrollTop = event.target.scrollTop;
  if (scrollTop/screen >= 700 * pageNo1.value) {
    pageNo1.value += 1;
    getPageHotList();
  }
}
const articleName1 = ref([]);
const articleName2 = ref([]);
const articleName3 = ref([]);
const translate = ref('translateX(0rem)');
const translate1 = ref('translateX(0rem)');
const transObj = reactive({
    data: {
        _realWidth: 0,
        _realHeight: 0,
        _xPos: 0,
        _yPos: 0,
        _xPos1: 0,
        _yPos1: 0,
        timer: null,
    }
});
// 初始化轮播参数
const _initOptions = (realWidth) => {
    console.log('123');
    transObj.data._realWidth = realWidth;
  };
// 更新效果
const _updateStyle = () => {
    translate.value = `translateX(${transObj.data._xPos/100}rem)`;
  };
const _updateStyle1 = () => {
    translate1.value = `translateX(${transObj.data._xPos1/100}rem)`;
  };
const  _moving = () => {
    transObj.data.timer = setInterval(() => {
      // console.log('*************');
      let { _realWidth } = transObj.data;
      if (Math.abs(transObj.data._xPos) >= _realWidth) {
        transObj.data._xPos = 0;
      }
      transObj.data._xPos -= 3;
      _updateStyle();
    }, 20);
  };
const  _moving1 = () => {
    transObj.data.timer1 = setInterval(() => {
    //   console.log('*************');
      let { _realWidth } = transObj.data;
      if (Math.abs(transObj.data._xPos1) >= _realWidth) {
        transObj.data._xPos1 = 0;
      }
      transObj.data._xPos1 -= 3;
      _updateStyle1();
    }, 40);
  };
  const shullfe = (arr) => {
    let i = arr.length;
    while (i) {
      let j = Math.floor(Math.random() * i--);
      [arr[j], arr[i]] = [arr[i], arr[j]];
    }
    return arr;
  };
  const fetchData = async () => {
  const { data } = await httpRequest.post('/common/getActivityConfig', {}, { isLoading: false });
  const barrage = JSON.parse(data).barrage;
  const barrage1 = JSON.parse(data).barrage;
  const barrage3 = JSON.parse(data).barrage;
  articleName1.value = shullfe(barrage);
  articleName2.value = shullfe(barrage1);
  articleName3.value = shullfe(barrage3);
  const articleNameStr = barrage.join('');
  _initOptions(Math.floor(barrage.length * 50 + articleNameStr.length * 24));

    setTimeout(() => {
      _moving();
      _moving1();
    }, 1000);
};
const score = ref(0);
const getMainFn = async () => {
    try {
        console.log(shareWork);
    const { data, message } = await httpRequest.post('/dz/1874673979168907266/main', {workId: shareWork},{ isLoading: true });
    console.log(data);
    if (data) {
        score.value = data.score;
        getTaskList();
    } else {
        showToast(message)
    }
} catch (error: any) {
    showToast(error.message);
    }
};
const getMian = async () => {
    showLoadingToast({})
    try {
        console.log(shareWork);
    const { data, message } = await httpRequest.post('/dz/1874673979168907266/main', {workId: shareWork},{ isLoading: true });
    console.log(data);
    if (data) {
        score.value = data.score;
        pageNo.value = 1;
        pageNo1.value = 1;
        getPageNewList(true);
        getPageHotList(true);
        getTaskList();
        getCategory();
    } else {
        showToast(message)
    }
} catch (error: any) {
    showToast(error.message);
    } finally {
        closeToast();
    }
};
const lingSku = async (sku) => {
    try {
    const {data, message} = await httpRequest.post('/dz/1874673979168907266/skuBrowseEvent', {skuId:sku.skuId}, {isLoading: true});
    if (message) {
        showToast(message)
    } else {
        window.location.href = sku.url;
        showSkuList(1)
        getMainFn();
    }
} catch (error: any) {
    showToast(error.message);
    }
};
const addCarSku = async () => {
    showLoadingToast({})
    try{
    const {data, message} = await httpRequest.get('/dz/1874673979168907266/addCart', {}, {isLoading: true});
    if (message) {
        showToast(message)
    } else {
        showAddPopup.value = false;
        // showSkuList(2)
        getMainFn();
    }
    } catch (error: any) {
        showToast(error.message);
    } finally {
        closeToast();
    }
};
const prizeResult = ref();
const openAddressPopup = (info) => {
    console.log(info);
    activityPrizeId.value = info.id;
    // addressId.value = info.id;
    addressData.value = info;
    showAddressPop.value = true;
}
const openRankAddressPopup = (info) => {
    console.log(info);
    activityPrizeId.value = info.id;
    // addressId.value = info.id;
    addressData.value = info;
    showRankReplayAddressPop.value = true;
}
const lotteryDraw = async () => {
    showLoadingToast({})
    try {
    const {data, message} = await httpRequest.post('/dz/1874673979168907266/lotteryDraw', {}, {isLoading: true});
    if (message) {
        console.log(message);
        showToast(message)
    } else {
        if (data.result) {
            prizeResult.value = data;
            showPrize.value = true;
            console.log(data, '中奖结果');
            if (data.prizeType == 3) {
                console.log(data, '中奖结果');
                activityPrizeId.value = data.userPrizeId;
                // addressId.value = data.result.result;
                addressData.value = {};
            }
        } else {
            showNoPrize.value = true;
        }
        getMainFn();
        console.log(data);
    }
    } catch (error: any) {
        showToast(error.message);
    } finally {
        closeToast();
    }
};
const myPrizeList = ref([]);
const userPrizes = async () => {
    showLoadingToast({})
    try {
    const {data, message} = await httpRequest.get('/dz/1874673979168907266/userPrizes', {}, {isLoading: true});
        if (message) {
            showToast(message)
        } else {
            myPrizeList.value = data;
            showMyPrizePopup.value = true;
            console.log(data);
        }
    } catch (error: any) {
     showToast(error.message);
    } finally {
        closeToast();
    }
};
const doTask = async (taskInfo) => {
    switch (taskInfo.taskType) {
        case 1:
            // 入会
            getStoreList();
            break;
        case 2:
            // 浏览商品
            showSkuList(1);
            break;
        case 3:
            // 加购商品
            // addCarSku();
            showSkuList(2);
            break;
        case 4:
            // 下单商品
            showOrderPopup.value = true;
            // gotoShopPage(baseInfo.shopId);
            break;
        case 5:
            // 邀请
            emits('toggle-component', 'Info');
            break;
        case 6:
            // 分享
            handleShare();
            try {
                const {data, message} = await httpRequest.get('/dz/1874673979168907266/share', {}, {isLoading: true});
                if (message) {
                    showToast(message)
                } else {
                    getMainFn();
                }
            } catch (error: any) {
                showToast(error.message);
            }

            break;
        case 7:
            // 上传作品
            emits('toggle-component', 'CreatePage');
            break;
        case 8:
            // 点赞
            showTaskPopup.value = false;
            window.scrollTo({top: 300,behavior: 'smooth'});
            break;
        default:
            break;
    }
}
const getWorkDetail = async () => {
    try {
        const {data, message} = await httpRequest.post('/dz/1874673979168907266/workDetail', {workId: shareWork}, {isLoading: true});
        if (message) {
            showToast(message)
        } else {
            handleOpenPhotoPage(data);
        }
    } catch (error: any) {
    showToast(error.message);
    }
};
onMounted(() => {
  scrollableContent.value.addEventListener('scroll', handleScroll);
  scrollableContent2.value.addEventListener('scroll', handleScroll1);
  fetchData();
  getMian();
  if (shareWork && toggleComponent.value == 0) {
    getWorkDetail()
  }
});

onUnmounted(() => {
    if (scrollableContent.value) {
        scrollableContent.value.removeEventListener('scroll', handleScroll);
        scrollableContent2.value.removeEventListener('scroll', handleScroll1);
    }
});
</script>
<style lang="scss">
.main-view {
    width: 7.5rem;
    height: 43rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/259665/5/14945/1163955/6791f56eFb3f5636f/b1de4467711b3ad2.png');
    background-repeat: no-repeat;
    background-position: top;
    background-size: 7.5rem auto;
    padding-top: 3.25rem;
    margin-bottom: 1.3rem;
}
.right-box {
    width: 1.2rem;
    margin-left: 6.3rem;
    margin-top: 0.75rem;
    margin-bottom: 4.5rem;
}
.right-btn {
    width: 1.2rem;
    height: 0.38rem;
    background-repeat: no-repeat;
    background-position: left;
    background-size: cover;
    margin-bottom: 0.05rem;
}
.top-tab {
    width: 7.5rem;
    height: auto;
    overflow-x: scroll;
    display: flex;
    padding-left: 0.1rem;
    padding-right: 0.1rem;
    box-sizing: border-box;
    margin-bottom: 0.35rem;
    .tab-item {
        flex: none;
        width: 1.42rem;
        height: 0.9rem;
        margin-left: 0.1rem;
        margin-right: 0.1rem;
    }
}
.search-box {
    width: 6.93rem;
    height: 0.74rem;
    background-image: url('https://img20.360buyimg.com/imgzone/jfs/t1/265164/8/7271/2270/67775ce8Fef663da9/6ff7cb2bfa065257.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    align-items: center;
    padding-left: 0.4rem;
    input {
        flex: 1;
        background: unset;
        border: unset;
        font-size: 0.26rem;
    }
    .search-btn {
        flex: none;
        width: 0.74rem;
        height: 0.74rem;
        margin-left: 0.2rem;
    }
    margin-bottom: 0.35rem;
}
.scrollable-content {
    height: 200px;
    overflow: auto;
}
.type-tab {
    width: 7.5rem;
    height: auto;
    display: flex;
    align-items: flex-start;
    justify-content: space-around;
    font-size: 0.3rem;
    color: #fff;
    .select-tab {
        font-size:0.34rem;
        color: #ffe2a8;
        text-decoration: underline;
        text-decoration-color: #fb792b;
        text-decoration-thickness: 0.1rem;
    }
}
.home-phto-box {
    width: 7.5rem;
    height: 12rem;
    margin-top: 0.15rem;
    display: flex;
    justify-content: space-between;
    overflow-y: scroll;
    overflow-x: hidden;
    box-sizing: border-box;
    padding-left: 0.2rem;
    padding-right: 0.2rem;
}
.photo-padding {
    box-sizing: border-box;
    padding-top: 0.33rem;
}
.phto-0 {
    width: 3.4rem;
    height: 3.8rem;
    border-radius: 0.2rem;
    background-color: #fff;
    margin: 0.1rem;
}
.phto-1 {
    width: 3.4rem;
    height: 5.12rem;
    border-radius: 0.2rem;
    background-color: #fff;
    margin: 0.1rem;
}
.scroll-tips {
    width: 0.23rem;
    height: 3.12rem;
    position: absolute;
    top: 19rem;
    right: 0;

}
.prize-view {
    width: 7.2rem;
    margin-left: 0.15rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    height: 6.52rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/262427/35/10114/103382/677f7736F1b8a8c1f/0ff4c0fc63cf3114.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    padding-top: 4.95rem;
    text-align: center;
    color: #ffe8bd;
    font-size: 0.2rem;
}
.bottom-view {
    width: 7.5rem;
    display: flex;
    justify-content: center;
    margin-top: 0.3rem;
    .bottom-item {
        width: 3.31rem;
        height: 0.71rem;
        margin-left: 0.28rem;
        margin-right: 0.28rem;
    }
}
.popop-view {
    width: auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
}
.product-popup {
    width: 7.5rem;
    height: 8.8rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/233659/27/36120/85043/6777c7ebFee90a720/618a2fae13adf6ff.png');
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: contain;
    position: relative;
    padding-top: 0.2rem;
    .title {
        width: 7.5rem;
        text-align: center;
        font-size: 0.4rem;
        color: #fff;
        font-weight: bold;
        margin-bottom: 0.35rem;
    }
}
.product-view {
    width: 6.85rem;
    height: 7.5rem;
    margin-left: auto;
    margin-right: auto;
    overflow-y: scroll;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .member-item {
        width: 3.4rem;
        height: 3.2rem;
        background-position: bottom;
        background-repeat: no-repeat;
        background-size: contain;
        position: relative;
        padding-left: 0.3rem;
        padding-right: 0.3rem;
        padding-top: 2.35rem;
    }
    .member-act-btn {
        width: 100%;
        height: 0.58rem;
        border-radius: 0.29rem;
        background-color: #e75543;
        text-align: center;
        font-size: 0.32rem;
        line-height: 0.58rem;
        color: #fff;
    }
    .member-gary-btn {
        width: 100%;
        height: 0.58rem;
        border-radius: 0.29rem;
        background-color: #b8b8b8;
        text-align: center;
        font-size: 0.32rem;
        line-height: 0.58rem;
        color: #fff;
    }
    .product-item {
        width: 2.15rem;
        height: 2.92rem;
        margin-bottom: 0.1rem;
        border-radius: 0.2rem;
        background: #fff;
        padding: 0.1rem;
        box-sizing: border-box;
    }
    .product-img {
        width: 1.95rem;
        height: 1.95rem;
        margin-bottom: 0.1rem;
    }
    .product-name {
        width: 100%;
        font-size: 0.16rem;
        line-height: 0.18rem;
        height: 0.34rem;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
    }
    .product-act-btn {
            width: 100%;
            height: 0.36rem;
            border-radius: 0.18rem;
            background-color: #e75543;
            text-align: center;
            font-size: 0.2rem;
            line-height: 0.36rem;
            color: #ffe8bd;
        }
    .product-gary-btn {
        width: 100%;
        height: 0.36rem;
            border-radius: 0.18rem;
        background-color: #b8b8b8;
        text-align: center;
        font-size: 0.2rem;
            line-height: 0.36rem;
        color: #fff;
    }
}
.task-popup {
    width: 7.5rem;
    height: 8.8rem;
    // background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/258678/28/7013/134717/6777bec2Fdc768185/17eeb8f80ac5dd20.png');
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: contain;
    position: relative;
    // padding-top: 0.97rem;
    .title-img-box {
        width: 7.5rem;
        height: 0.97rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .title-img {
        width: 3.8rem;
        height: 0.58rem;
    }
    .point-view {
        width: 7.5rem;
        text-align: center;
        color: #ffe8bd;
        font-size: 0.2rem;
        margin-bottom: 0.3rem;
    }
    .task-item {
        width: 7.11rem;
        height: 0.71rem;
        margin: 0 auto;
        margin-bottom: 0.1rem;
        background-position: bottom;
        background-repeat: no-repeat;
        background-size: contain;
        position: relative;
        .task-act-btn {
            width: 1.54rem;
            height: 0.44rem;
            border-radius: 0.22rem;
            background-color: #e75543;
            text-align: center;
            font-size: 0.24rem;
            line-height: 0.44rem;
            color: #ffe8bd;

            position: absolute;
            top: 0.135rem;
            right: 0.1rem;
        }
        .task-gary-btn {
            width: 1.54rem;
            height: 0.44rem;
            border-radius: 0.22rem;
            background-color: #b8b8b8;
            text-align: center;
            font-size: 0.24rem;
            line-height: 0.44rem;
            color: #fff;
            position: absolute;
            top: 0.135rem;
            right: 0.1rem;
        }
    }
}
.no-prize {
    width: 6.5rem;
    height: 7.15rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/262539/21/8075/105618/67791e5cF24208bb1/7c4ae80dcc33cef9.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}
.prize-popup {
    width: 6.5rem;
    height: 7.15rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/253752/35/9125/97862/67791879Fb8ca57de/5296703dc7e55b0d.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    padding-top: 2rem;
    .prize-img {
        width: 6.5rem;
        height: 2.5rem;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
    }
    .prize-name {
        width: 6.5rem;
        margin-top: 0.3rem;
        margin-bottom: 0.3rem;
        text-align: center;
        font-size: 0.38rem;
        color: #fff;
    }
    .prize-tips {
        width: 6.5rem;
        text-align: center;
        font-size: 0.23rem;
        color: #fff;
    }
    .prize-btn {
        width: 3.9rem;
        margin-left: 1.3rem;
        margin-top: 0.1rem;
        height: 0.64rem;
        border-radius: 0.19rem;
        background-color: #ffe2a8;
        color: #e64330;
        text-align: center;
        line-height: 0.64rem;
    }
}
.rule-popup {
    width: 6.5rem;
    height: 7.15rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/263773/16/7543/62031/6777d6a9Fb25963df/e52ac400bdfa34b8.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    padding-top: 0.2rem;
    .title {
        width: 6.5rem;
        text-align: center;
        font-size: 0.4rem;
        color: #fff;
        font-weight: bold;
        margin-bottom: 0.35rem;
    }
    .rule-content {
        width: 6rem;
        margin-left: 0.25rem;
        max-height: 5.75rem;
        overflow-y: scroll;
        font-size: 0.25rem;
        color: #fff;
    }
}
.my-prize-popup {
    width: 5.5rem;
    margin-left: 0.5rem;
    max-height: 5.75rem;
    overflow-y: scroll;
    font-size: 0.21rem;
    color: #fff;
}
.my-prize-item {
    width: 5.5rem;
    height: 0.5rem;
    display: flex;
    align-items: center;
}
.my-prize-title {
    width: 1.2rem;
    height: 0.38rem;
    border-radius: 0.19rem;
    background-color: #ffe2a8;
    text-align: center;
    color: #e64330;
    line-height: 0.38rem;
}
.my-prize--name {
  width: 2rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}
.my-prize-btn {
    width: 1.1rem;
    height: 0.39rem;
    border-radius: 0.1rem;
    background-color: #ffcf70;
    color: #e64330;
    line-height: 0.39rem;
    text-align: center;
}
.close-btn {
        width: 0.6rem;
        height: 0.6rem;
        position: absolute;
        right: 0.2rem;
        top: 0.2rem;
        background-image: url('https://img20.360buyimg.com/imgzone/jfs/t1/267350/1/7467/2070/6777bec0F8f9d1714/f769e18aa6aae4cc.png');
        background-position: bottom;
        background-repeat: no-repeat;
        background-size: contain;
    }
.scroll-wrapper {
  overflow: hidden;
  width: 7.5rem;
  height: 0.6rem;
  margin-top: 0.11rem;
  .scroll-box {
    display: flex;
    // transition: transform 10ms;
    .data-list-part {
      display: flex;
      .data-list {
        display: flex;
        .item {
          flex: none;
          width: auto;
          height: 0.5rem;
          margin: 0.05rem;
          margin-left: 1.5rem;
          border-radius: 0.25rem;
          background-color: #fff1f1;
          color: #ec5d39;
          font-size: 0.24rem;
          line-height: 0.5rem;
          box-sizing: border-box;
          padding-left: 0.2rem;
          padding-right: 0.2rem;
        }
      }
    }
    .c-row {
      flex-direction: row;
    }
  }
}
</style>
