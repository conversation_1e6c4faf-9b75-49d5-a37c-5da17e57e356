<template>
  <div id="page">
    <div class="container">
      <!-- 我的奖品 -->
      <div class="my-prize-button" @click="onClickMyPrize"></div>
      <!-- 活动规则 -->
      <div class="rule-button" @click="onClickRule"></div>
      <!-- 五张卡片的展示 -->
      <div class="card-list">
        <div class="card-item" :class="{ active: index === activeCardIndex }" v-for="(item, index) in mainData.cards.filter((c) => !c.isSuperCard)" :key="index">
          <div class="card-image" :style="{ backgroundImage: `url(${item.cardPreview})` }"></div>
        </div>
      </div>
      <!-- 抽卡按钮区 -->
      <div class="draw-card-container">
        <!-- 抽卡记录 -->
        <div class="draw-card-record-button" @click="onClickCardRecord"></div>
        <!-- 点击抽卡按钮 -->
        <div class="draw-card-button" @click="drawCard"></div>
        <!-- 做任务获得更多集卡机会 -->
        <div class="show-task-button" @click="onClickShowTask"></div>
      </div>
      <p class="draw-card-chance">{{ `剩余抽卡机会：${mainData.remainingDrawCardTimes} ` }}次</p>
      <!-- 已收集卡片 -->
      <div class="collected-card-list">
        <div class="collected-card-item" v-for="(item, index) in mainData.cards" :key="index" @click="onClickCollectedCard(item)">
          <div
            class="collected-card-image"
            :class="{ 'is-synthesis': item.isSynthesis }"
            :style="{
              backgroundImage: `url(${item.cardNum > 0 ? item.cardOnIcon : item.cardOffIcon})`,
            }"></div>
          <span class="collected-card-num">{{ item.cardNum }}</span>
        </div>
      </div>
      <p class="collected-card-tips">
        {{ `*每集齐“多彩小直屏”五张卡，即可合成一张S卡；\n每张S卡可换取一次抽奖机会。` }}
      </p>
      <!-- 签到区 -->
      <div class="sign-in-container">
        <p class="sign-in-tips">{{ `每签到一天，购vivo S30系列三丽鸥家族限定礼盒\n返${mainData.signInRewardBeans}京豆` }}</p>
        <div class="sign-in-container-inner">
          <p class="sign-in-num-container">
            已累计签到<span class="sign-in-num">{{ mainData.signInTotalDays }}天</span>，购机预计返<span class="sign-in-num">{{ mainData.signInTtlBeans }}京豆</span>
          </p>
          <!-- 签到按钮 -->
          <div class="sign-in-button" :class="{ 'btn-disabled': mainData.signInStatus == 1 }" @click="onClickTask({ taskType: 1 })"></div>
        </div>
        <!-- 签到日期 -->
        <div class="sign-in-date-list">
          <div class="sign-in-date-item" v-for="(item, index) in mainData.signInRecords" :key="index">
            <div class="sign-in-icon" :class="{ 'is-sign': item.signInStatus + '' === '0' }"></div>
            <span class="sign-date">{{ dayjs(item.signInDate).format('M月D日') }}</span>
          </div>
        </div>
      </div>
      <!-- 抽奖区 -->
      <div class="draw-lottery-container">
        <!-- 奖品轮播 -->
        <van-swipe class="draw-lottery-swiper" :autoplay="lotterySwiperSpeed" indicator-color="#46b7f3">
          <van-swipe-item class="draw-lottery-swiper-item" v-for="(item, index) in mainData.drawPrizes">
            <img class="prize-img" :src="item.prizeImage" />
          </van-swipe-item>
        </van-swipe>
        <!-- 抽奖按钮 -->
        <button class="draw-lottery-button" @click="handleDrawLottery"></button>
        <!-- 剩余抽奖次数 -->
        <p class="draw-lottery-chance">剩余抽奖次数：{{ mainData.remainingDrawPrizeTimes }}次</p>
      </div>
      <!-- 图片视频区 -->
      <div class="img-video-container">
        <div class="img-video-title"></div>
        <div class="img-video-list-container" v-if="decoData?.imageVideoList?.length > 0">
          <div class="img-video-list">
            <div class="img-video-item" v-for="(item, index) in decoData.imageVideoList" :key="index" @click="onClickImageVideo(item)">
              <img class="img-video-img" :src="item.img" />
              <p class="img-video-tips text-overflow-2-line">{{ item.tips }}</p>
            </div>
          </div>
        </div>
      </div>
      <!-- 底部商标 -->
      <div class="footer-logo-container"></div>
    </div>
  </div>
  <!-- 合成S卡弹窗 -->
  <van-popup class="my-dialog" :show="showComposeCardDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showComposeCardDialog,
        animate__zoomOut: !showComposeCardDialog,
      }"
      @click.stop>
      <div class="compose-card-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showComposeCardDialog = false"></div>
        <div class="compose-card-dialog-content">
          <!-- 五张卡片的展示 -->
          <div class="compose-card-list">
            <div class="compose-card-item" v-for="(item, index) in mainData.cards.filter((c) => !c.isSuperCard)" :key="index">
              <div class="compose-card-image" :style="{ backgroundImage: `url(${item.cardImage})` }"></div>
              <span class="compose-card-num">{{ item.cardNum }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 立即合成按钮 -->
      <div class="compose-card-button" @click="onClickComposeCard"></div>
    </div>
  </van-popup>
  <!-- 已合成S卡弹窗 -->
  <van-popup class="my-dialog" :show="showSuperCardDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showSuperCardDialog,
        animate__zoomOut: !showSuperCardDialog,
      }"
      @click.stop>
      <div class="super-card-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showSuperCardDialog = false"></div>
        <img class="super-card-image" :src="mainData.cards.find((c) => c.isSuperCard)?.cardImage" />
      </div>
      <!-- 收下S卡按钮 -->
      <div class="receive-super-card-button" @click="showSuperCardDialog = false"></div>
    </div>
  </van-popup>
  <!-- 抽卡结果弹窗 -->
  <van-popup class="my-dialog" :show="showDrawCardDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showDrawCardDialog,
        animate__zoomOut: !showDrawCardDialog,
      }"
      @click.stop>
      <div class="draw-card-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showDrawCardDialog = false"></div>
        <img class="draw-card-image" :src="drawCardResultImage" />
      </div>
      <!-- 收下S卡按钮 -->
      <div class="receive-draw-card-button" @click="showDrawCardDialog = false"></div>
    </div>
  </van-popup>
  <!-- 中奖弹窗 -->
  <van-popup class="my-dialog" :show="showPrizeResDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showPrizeResDialog,
        animate__zoomOut: !showPrizeResDialog,
      }"
      @click.stop>
      <div class="prize-res-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showPrizeResDialog = false"></div>
        <!-- 奖品图 -->
        <div class="prize-img-container">
          <img class="prize-img" :src="drawRes?.prizeImg" />
        </div>
        <!-- 奖品名称 -->
        <div class="prize-name" v-if="drawRes?.prizeType === 3">
          {{ `恭喜你，成功抽中${drawRes?.prizeName}!\n请在1小时内填写地址信息，\n奖品将在活动结束后7个工作日发放` }}
        </div>
        <div class="prize-name" v-if="drawRes?.prizeType === 2">
          {{ `恭喜你，成功抽中${drawRes?.prizeName}!\n奖品已发放至账户，\n请前往我的-京豆查看。` }}
        </div>
      </div>
      <!-- 填写地址按钮 -->
      <div class="fill-address-button" v-if="drawRes?.prizeType === 3" @click="handleFillAddress"></div>
      <!-- 如果是京豆，显示【好的】 -->
      <div class="ok-button" v-if="drawRes?.prizeType === 2" @click="showPrizeResDialog = false"></div>
    </div>
  </van-popup>
  <!-- 未中奖弹窗 -->
  <van-popup class="my-dialog" :show="showPrizeNoResDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showPrizeNoResDialog,
        animate__zoomOut: !showPrizeNoResDialog,
      }"
      @click.stop>
      <div class="no-prize-res-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showPrizeNoResDialog = false"></div>
      </div>
    </div>
  </van-popup>
  <!-- 抽卡记录弹窗 -->
  <van-popup class="my-dialog" :show="showCardRecordDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showCardRecordDialog,
        animate__zoomOut: !showCardRecordDialog,
      }"
      @click.stop>
      <div class="card-record-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showCardRecordDialog = false"></div>

        <div class="card-record-container">
          <!-- Tab 切换 -->
          <div class="card-record-tab-header">
            <div class="card-record-tab-item" :class="{ active: cardRecordTab === 'card' }" @click="cardRecordTab = 'card'">卡片获取记录</div>
            <div class="card-record-tab-item" :class="{ active: cardRecordTab === 'chance' }" @click="cardRecordTab = 'chance'">机会获取记录</div>
          </div>

          <!-- Tab 内容区域 -->
          <div class="tab-body">
            <!-- 卡片获取记录 -->
            <div v-if="cardRecordTab === 'card'" class="card-record-list">
              <span v-if="cardRecordList.length === 0" class="empty-text">暂无记录</span>
              <div v-else class="card-record-item" v-for="(item, index) in cardRecordList" :key="index">
                <span class="card-record-name">{{ item.cardName }}卡</span>
                <span class="card-record-date">{{ dayjs(item.drawCardDate).format('YYYY年MM月DD日') }}</span>
              </div>
            </div>
            <!-- 机会记录内容 -->
            <div class="chance-record-list" v-else>
              <span v-if="cardChanceRecordList.length === 0" class="empty-text">暂无记录</span>
              <div v-else class="chance-record-item" v-for="(item, index) in cardChanceRecordList" :key="index">
                <span class="chance-record-date">{{ dayjs(item.doTaskDate).format('YYYY.M.D') }}</span>
                <span class="chance-record-task">{{ item.taskName }}</span>
                <span class="chance-record-info" v-if="item.relationInfo">{{ item.relationInfo }}</span>
                <span class="chance-record-plus">+{{ item.rewardTimes }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
  <!-- 我的奖品弹窗 -->
  <van-popup class="my-dialog" :show="showMyPrizeDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showMyPrizeDialog,
        animate__zoomOut: !showMyPrizeDialog,
      }"
      @click.stop>
      <div class="my-prize-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showMyPrizeDialog = false"></div>

        <div class="my-prize-container">
          <span v-if="myPrizeList.length === 0" class="empty-text">暂无记录</span>
          <div v-else class="my-prize-item" v-for="(item, index) in myPrizeList" :key="index">
            <span class="col-time">{{ dayjs(item.drawPrizeDate).format('YYYY年MM月DD日') }}</span>
            <span class="col-name">{{ item.prizeName }}</span>
            <span class="col-status">
              <button v-if="item.prizeStatus == 1" class="btn blue" @click="fillAddress(item)">填写地址</button>
              <button v-else-if="item.prizeStatus == 3" class="btn gray">发放失败</button>
              <button v-else-if="item.prizeStatus == 2" class="btn gray">已发放</button>
              <button v-else-if="item.prizeStatus == 0" class="btn blue" @click="lookAddress(item)">查看地址</button>
            </span>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
  <!-- 规则弹窗 -->
  <van-popup class="my-dialog" :show="showRuleDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showRuleDialog,
        animate__zoomOut: !showRuleDialog,
      }"
      @click.stop>
      <div class="rule-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showRuleDialog = false"></div>
        <div class="rule-dialog-container">
          <PerfectScrollbar class="rule-scroll">
            <p class="rule-text">{{ ruleText }}</p>
          </PerfectScrollbar>
        </div>
      </div>
    </div>
  </van-popup>
  <!-- 集卡任务弹窗 -->
  <van-popup class="my-dialog" :show="showCardTaskDialog">
    <div
      class="dialog-container card-task-dialog-container animate__animated animate__faster"
      :class="{
        animate__fadeInUp: showCardTaskDialog,
        animate__fadeOutDown: !showCardTaskDialog,
      }"
      @click.stop>
      <div class="card-task-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showCardTaskDialog = false"></div>
        <div class="card-task-list">
          <div class="card-task-item" v-for="(item, index) in cardTaskList" :key="index">
            <div class="task-info">
              <div class="task-name" :class="`task-type-${item.taskType}`">{{ item.taskNotes }}</div>
              <div class="task-tips">{{ item.taskTips }}</div>
            </div>
            <button
              class="task-btn"
              :class="{
                disabled: item.taskBtnStatus === '1',
              }"
              :disabled="item.taskBtnStatus === '1'"
              @click="onClickTask(item)">
              {{ item.taskBtnStatus === '1' ? '已完成' : item.taskBtnName }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
  <!-- 下单商品弹窗 -->
  <van-popup class="my-dialog" :show="orderSkuDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: orderSkuDialog,
        animate__zoomOut: !orderSkuDialog,
      }"
      @click.stop>
      <div class="order-sku-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="orderSkuDialog = false"></div>
        <!-- 商品列表 -->
        <div class="order-sku-list">
          <div class="order-sku-item" v-for="(item, index) in orderSkuList" :key="index">
            <div class="sku-item">
              <div class="sku-img" :style="{ backgroundImage: `url(${item.skuImage})` }"></div>
              <div class="sku-name-container">
                <p class="sku-name text-overflow-line">{{ item.skuName }}</p>
              </div>
            </div>
            <button class="order-sku-button" @click="handleOrderSkuButtonClick(item)"></button>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
  <!-- 浏览商品弹窗 -->
  <van-popup class="my-dialog" :show="showViewSkuDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showViewSkuDialog,
        animate__zoomOut: !showViewSkuDialog,
      }"
      @click.stop>
      <div class="view-sku-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showViewSkuDialog = false"></div>
        <!-- 商品列表 -->
        <div class="view-sku-list">
          <div class="view-sku-item" v-for="(item, index) in viewSkuList" :key="index">
            <div class="sku-item">
              <div class="sku-img" :style="{ backgroundImage: `url(${item.skuImage})` }"></div>
              <div class="sku-name-container">
                <p class="sku-name text-overflow-line">{{ item.skuName }}</p>
              </div>
            </div>
            <button class="view-sku-button" @click="handleViewSku(item)"></button>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
  <!-- 加购商品弹窗 -->
  <van-popup class="my-dialog" :show="showAddCartDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showAddCartDialog,
        animate__zoomOut: !showAddCartDialog,
      }"
      @click.stop>
      <div class="add-cart-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showAddCartDialog = false"></div>
        <!-- 商品列表 -->
        <div class="add-cart-list">
          <div class="sku-item" v-for="(item, index) in addCartSkuList" :key="index">
            <div class="sku-img" :style="{ backgroundImage: `url(${item.skuImage})` }"></div>
            <div class="sku-name-container">
              <p class="sku-name text-overflow-line">{{ item.skuName }}</p>
            </div>
          </div>
        </div>
        <!-- 一键加购按钮 -->
        <div class="add-cart-button" @click="handleAddCart"></div>
      </div>
    </div>
  </van-popup>
  <!-- 填写地址弹窗 -->
  <van-popup class="my-dialog" :show="showAddressDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showAddressDialog,
        animate__zoomOut: !showAddressDialog,
      }">
      <div class="address-dialog">
        <div class="dialog-close-btn" @click="showAddressDialog = false"></div>

        <div class="address-form">
          <van-field v-model="addressFormData.realName" :readonly="isViewAddress" required label="姓名：" placeholder="请输入姓名" class="address-form-field" :maxlength="10" />
          <van-field v-model="addressFormData.mobile" :readonly="isViewAddress" required label="电话：" placeholder="收货人手机号" class="address-form-field" type="tel" :maxlength="11" />
          <van-field v-model="fullAddress" required label="地区：" placeholder="选择省/市/区" class="address-form-field" readonly clickable right-icon="arrow-down" @click="addressInputClick" />
          <van-field v-model="addressFormData.address" :readonly="isViewAddress" required label="详细地址：" placeholder="街道门牌号" class="address-form-field" />
        </div>
      </div>
      <!-- 提交地址按钮 -->
      <div class="submit-address-button" @click="onSubmitAddress" v-if="!isViewAddress"></div>
    </div>
  </van-popup>
  <!-- 选择省市区弹窗 -->
  <van-popup class="my-dialog" position="bottom" :show="showAreaDialog">
    <div class="choose-area-dialog">
      <van-area :area-list="areaList" :value="addressFormData.postalCode" @confirm="onAreaConfirm" @cancel="onAreaCancel" />
    </div>
  </van-popup>
  <!-- 图片弹窗 -->
  <van-popup class="my-dialog" :show="showImageDialog" :destroyOnClose="true">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showImageDialog,
        animate__zoomOut: !showImageDialog,
      }"
      @click.stop>
      <div class="image-dialog">
        <!-- 关闭按钮 -->
        <div class="dialog-close-btn" @click="showImageDialog = false"></div>
        <!-- 图片轮播 -->
        <van-swipe class="image-dialog-swiper" :autoplay="3000" indicator-color="#f088c3">
          <van-swipe-item class="image-dialog-swiper-item" v-for="(item, index) in imageDialogPicList">
            <div class="image-dialog-img" :style="{ backgroundImage: `url(${item.img})` }"></div>
          </van-swipe-item>
        </van-swipe>
        <!-- 宣传文案 -->
        <p class="image-dialog-text">{{ imageDialogText }}</p>
      </div>
    </div>
  </van-popup>
  <!-- 视频弹窗 -->
  <van-popup class="my-dialog" :show="showVideoDialog">
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: showVideoDialog,
        animate__zoomOut: !showVideoDialog,
      }"
      @click="clickVideoOverlay">
      <div class="video-dialog">
        <video-player class="video-player" :style="{ height: videoHeight }" :sources="videoSources" :poster="videoPoster" playsinline controls />
      </div>
    </div>
  </van-popup>
</template>

<script lang="ts" setup>
/* eslint-disable */
import { inject, reactive, provide, computed, ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { closeToast, showLoadingToast, showToast, Swipe as VanSwipe, SwipeItem as VanSwipeItem, Popup as VanPopup } from 'vant';
import { debounce } from 'lodash';
import dayjs from 'dayjs';
import Macy from 'macy';
import { areaList } from '@vant/area-data';

import { PerfectScrollbar } from 'vue3-perfect-scrollbar';
import 'vue3-perfect-scrollbar/dist/style.css';

import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { gotoShopPage, addSkuToCart, gotoSkuPage } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import openCard from '@/utils/openCard';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('baseInfo', baseInfo);

const pathParams: any = inject('pathParams');
console.log('pathParams', pathParams);

const baseUserInfo: any = inject('baseUserInfo');
console.log('baseUserInfo', baseUserInfo);

const decoData: any = inject('decoData');
console.log('decoData', decoData);

//toast提示方法
const delayToast = async (e: string, time?: number) => {
  setTimeout(() => {
    showToast(e);
  }, time || 1000);
};

const mainData = reactive({
  cards: [],
  drawPrizes: [],
  remainingDrawCardTimes: 0, // 剩余抽卡次数
  remainingDrawPrizeTimes: 0, // 剩余抽奖次数
  signInRecords: [],
  signInRewardBeans: 0, // 签到奖励京豆数量
  signInStatus: 0, // 签到按钮状态：0.可用；1.禁用
  signInTotalDays: 0, // 累计签到天数
  signInTtlBeans: 0, // 赠送京豆
});

// 合成S卡弹窗
const showComposeCardDialog = ref(false);

// 已合成S卡弹窗
const showSuperCardDialog = ref(false);

// 抽卡结果弹窗
const showDrawCardDialog = ref(false);

// 中奖弹窗
const showPrizeResDialog = ref(false);
// 未中奖弹窗
const showPrizeNoResDialog = ref(false);

// 抽卡记录弹窗
const showCardRecordDialog = ref(false);
// 抽卡记录tab
const cardRecordTab = ref<'card' | 'chance'>('card');
// 卡片获取记录
const cardRecordList = ref([]);
// 抽卡机会获取记录
const cardChanceRecordList = ref([]);

// 我的奖品弹窗
const showMyPrizeDialog = ref(false);
// 我的奖品列表
const myPrizeList = ref([]);
// 点击我的奖品按钮
const onClickMyPrize = async () => {
  try {
    showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
    const res = await httpRequest.post('/vivo/1914959045256859650/query/prizes');
    myPrizeList.value = res.data || [];
    showMyPrizeDialog.value = true;
  } catch (e: any) {
    showToast(e.message || '获取失败');
  } finally {
    closeToast();
  }
};

// 规则弹窗
const showRuleDialog = ref(false);
const ruleText = ref('');
// 点击活动规则按钮
const onClickRule = async () => {
  try {
    showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
    const res = await httpRequest.get('/common/getRule');
    ruleText.value = res.data || '';
    showRuleDialog.value = true;
  } catch (e: any) {
    showToast(e.message || '获取失败');
  } finally {
    closeToast();
  }
};

// 集卡任务弹窗
const showCardTaskDialog = ref(false);
// 集卡任务列表
const cardTaskList = ref([]);

// 加购商品弹窗
const showAddCartDialog = ref(false);
// 加购商品列表
const addCartSkuList = ref([]);
// 加购商品任务ID
const addCartTaskId = ref('');

// 下单商品弹窗
const orderSkuDialog = ref(false);
// 下单商品列表
const orderSkuList = ref([]);
// 下单逻辑
const handleOrderSkuButtonClick = async (item) => {
  gotoSkuPage(item.skuId);
  sessionStorage.setItem('fromOrderSku', 'true');
};
const handleOrderSku = async () => {
  // 如果是点击【去下单】返回
  if (sessionStorage.getItem('fromOrderSku') === 'true') {
    console.log('点击【去下单】返回');
    sessionStorage.removeItem('fromOrderSku');
    // 刷新任务列表
    const { data: tasksData } = await httpRequest.post('/vivo/1914959045256859650/query/tasks');
    cardTaskList.value = tasksData || [];
    // 刷新主数据
    await getHomeData({ showLoading: false });
  }
};

// 浏览商品弹窗
const showViewSkuDialog = ref(false);
// 浏览商品列表
const viewSkuList = ref([]);

// 是否是查看地址
const isViewAddress = ref(false);
// 填写地址弹窗
const showAddressDialog = ref(false);
// 选择省市区弹窗
const showAreaDialog = ref(false);
// 地址表单数据
const addressFormData = ref({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
  postalCode: '',
});
// 组合地区文本
const fullAddress = computed(() => {
  if (!addressFormData.value.province) return '';
  return `${addressFormData.value.province}/${addressFormData.value.city}/${addressFormData.value.county}`;
});
// 点击地址输入框
const addressInputClick = () => {
  if (isViewAddress.value) return;
  showAreaDialog.value = true;
};
// 提交地址
const onSubmitAddress = async () => {
  if (isViewAddress.value) return showToast('查看地址不能修改');

  console.log('提交地址', addressFormData.value);

  const iosFace = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
  const androidFace = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
  const telValidator = (str: string) => {
    return /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[01456879])|(?:5[0-3,5-9])|(?:6[2567])|(?:7[0-8])|(?:8[\d])|(?:9[0-3,5-9]))\d{8}$/.test(str);
  };
  const telValidator2 = (str: string) => {
    return /^1[3456789]\d{9}$/.test(str);
  };

  const { realName, mobile, province, city, county, address, postalCode } = addressFormData.value;

  if (!realName) {
    showToast('请填写收货人姓名');
    return;
  }

  if (realName.length > 10) {
    showToast('收货人姓名长度不得超过10个字符');
    return;
  }

  if (iosFace.test(realName) || androidFace.test(realName)) {
    showToast('收货人姓名不能含有表情');
    return;
  }

  if (!mobile) {
    showToast('请填写手机号');
    return;
  }

  if (!telValidator2(mobile) || !telValidator(mobile)) {
    showToast('请填写正确的手机号');
    return;
  }

  if (!province || !city || !county) {
    showToast('请选择省市区');
    return;
  }

  if (!address) {
    showToast('请填写详细地址');
    return;
  }

  if (address.length > 150) {
    showToast('收货地址长度超出限制');
    return;
  }

  if (iosFace.test(address) || androidFace.test(address)) {
    showToast('收货地址不能含有表情');
    return;
  }

  const submitData = {
    realName,
    mobile,
    province,
    city,
    county,
    address,
    postalCode,
  };
  console.log('🚀 ~ onSubmitAddress ~ submitData:', submitData);

  const formatSubmitData = {
    realName,
    mobile,
    province,
    city,
    county,
    address,
    postalCode,
    areaCode: postalCode,
    grantId: drawRes.value.result.grantId,
  };
  console.log('🚀 ~ onSubmitAddress ~ formatSubmitData:', formatSubmitData);
  try {
    showLoadingToast({ message: '正在提交...', forbidClick: true, duration: 0 });
    const res = await httpRequest.post('/vivo/1914959045256859650/address/modify', formatSubmitData);
    if (res.code === 200) {
      showToast('提交成功');
      showAddressDialog.value = false;
    }
  } catch (e: any) {
    showToast(e.message || '获取失败');
  } finally {
    closeToast();
  }
};
// 省市区弹窗-确认
const onAreaConfirm = (selected) => {
  console.log('🚀 ~ 省市区弹窗-确认', selected);
  const { selectedOptions } = selected;
  addressFormData.value.province = selectedOptions[0]?.text || '';
  addressFormData.value.city = selectedOptions[1]?.text || '';
  addressFormData.value.county = selectedOptions[2]?.text || '';
  addressFormData.value.postalCode = selectedOptions[2]?.value || '';
  showAreaDialog.value = false;
};
// 隐藏选择省市区弹窗
const onAreaCancel = () => {
  showAreaDialog.value = false;
};
// 我的奖品弹窗：查看地址
const lookAddress = async (item) => {
  addressFormData.value = {
    realName: item.realName || '',
    mobile: item.mobile || '',
    province: item.province || '',
    city: item.city || '',
    county: item.county || '',
    address: item.address || '',
    postalCode: item.postalCode || '',
  };
  isViewAddress.value = true; // 设置为只读模式
  showAddressDialog.value = true; // 展示地址弹窗
};
// 我的奖品弹窗：填写地址
const fillAddress = (item) => {
  addressFormData.value = {
    realName: item.realName || '',
    mobile: item.mobile || '',
    province: item.province || '',
    city: item.city || '',
    county: item.county || '',
    address: item.address || '',
    postalCode: item.postalCode || '',
  };
  drawRes.value = { result: { grantId: item.grantId } }; // 用于提交时带 grantId
  isViewAddress.value = false; // 设置为可编辑模式
  showAddressDialog.value = true; // 展示地址弹窗
  showMyPrizeDialog.value = false; // 隐藏我的奖品弹窗
};

// 图片弹窗
const showImageDialog = ref(false);
// 图片弹窗中的轮播
const imageDialogPicList = ref([]);
const imageDialogText = ref(``);

// 视频弹窗
const showVideoDialog = ref(false);
const videoPoster = ref();
const videoSources = reactive([
  {
    src: '',
    type: 'video/mp4',
  },
]);
const videoHeight = ref('4.2rem');
const clickVideoOverlay = () => {
  showVideoDialog.value = false;
  setTimeout(() => {
    stopVideo();
  }, 300);
};
const stopVideo = () => {
  const videoEl = document.querySelector('.video-player video') as HTMLVideoElement;
  if (videoEl) {
    videoEl.pause();
    videoEl.currentTime = 0;
  }
};
// 点击图片或视频
const onClickImageVideo = (item) => {
  if (item.type === 'video') {
    videoPoster.value = item.img;
    videoSources[0].src = item.videoUrl;
    videoHeight.value = item.videoHeight;
    setTimeout(() => {
      showVideoDialog.value = true;
    }, 300);
  }
  if (item.type === 'image') {
    imageDialogPicList.value = item.imgList;
    imageDialogText.value = item.tips;
    showImageDialog.value = true;
  }
};
// 获取活动主接口返回信息
const getHomeData = async (options = { showLoading: true }) => {
  const { showLoading = true } = options;

  try {
    if (showLoading) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
    }

    if (!pathParams.shareId) {
      // 被分享人进入页面
    }

    const { data } = await httpRequest.post('/vivo/1914959045256859650/loading/main', {
      shareId: pathParams.shareId,
    });

    mainData.cards = data.cards || [];
    mainData.drawPrizes = data.drawPrizes || [];
    mainData.remainingDrawCardTimes = data.remainingDrawCardTimes ?? 0;
    mainData.remainingDrawPrizeTimes = data.remainingDrawPrizeTimes ?? 0;
    mainData.signInRecords = data.signInRecords || [];
    mainData.signInRewardBeans = data.signInRewardBeans ?? 0;
    mainData.signInStatus = data.signInStatus ?? 0;
    mainData.signInTotalDays = data.signInTotalDays ?? 0;
    mainData.signInTtlBeans = data.signInTtlBeans ?? 0;
    // 签到任务id
    mainData.signTaskId = data.taskId;

    // 助力提示信息
    if (data.toastMsg && showLoading) {
      delayToast(data.toastMsg);
    }
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  } finally {
    closeToast();
  }
};

// 抽卡
const activeCardIndex = ref(-1); // 当前高亮的卡片下标
let drawAnimationTimer: number | null = null;
const drawAnimationInProgress = ref(false); // 防止重复点击
const drawCardResultImage = ref(''); // 设置抽卡结果展示图
const drawCard = async () => {
  if (mainData.remainingDrawCardTimes <= 0) {
    showToast('暂无抽卡机会');
    return;
  }

  if (drawAnimationInProgress.value) return;
  drawAnimationInProgress.value = true;

  mainData.remainingDrawCardTimes = mainData.remainingDrawCardTimes - 1;

  // showLoadingToast({
  //   message: '抽卡中...',
  //   forbidClick: true,
  //   duration: 0,
  // });

  let totalRounds = 4;
  let cardPool = mainData.cards.filter((c) => !c.isSuperCard);
  let totalSteps = totalRounds * cardPool.length;
  let currentStep = 0;
  const baseDelay = 50;
  const maxDelay = 300;

  // 请求接口
  const { data } = await httpRequest.post('/vivo/1914959045256859650/draw/card');
  const prizeCardName = data.prizeName?.trim();
  const prizeImg = data.prizeImg || '';
  drawCardResultImage.value = prizeImg;

  // 找到目标卡片
  let targetIndex = cardPool.findIndex((c) => c.cardName === prizeCardName);
  if (targetIndex === -1) targetIndex = 0;

  totalSteps += targetIndex;

  const animateStep = () => {
    activeCardIndex.value = currentStep % cardPool.length;
    currentStep++;

    if (currentStep <= totalSteps) {
      const progress = currentStep / totalSteps;
      const delay = baseDelay + (maxDelay - baseDelay) * Math.pow(progress, 2);
      drawAnimationTimer = window.setTimeout(animateStep, delay);
    } else {
      // 停留 1 秒展示最后的高亮，再弹窗
      setTimeout(() => {
        showDrawCardDialog.value = true;
        activeCardIndex.value = -1; // 清除高亮
        drawAnimationInProgress.value = false;
        drawAnimationTimer = null;
        closeToast();
        getHomeData({ showLoading: false }); // 刷新页面数据
      }, 1500);
    }
  };

  animateStep();
};

/**
 * 点击已收集卡片的处理函数
 * 如果点击的是超级卡（isSuperCard 为 true）且可合成（isSynthesis 为 true），则弹出合成 S 卡弹窗
 */
const onClickCollectedCard = (item) => {
  if (item.isSuperCard && item.isSynthesis) {
    // 显示合成 S 卡弹窗
    showComposeCardDialog.value = true;
  }
};

// 合成S卡操作
const onClickComposeCard = async () => {
  try {
    showLoadingToast({ message: '合成中...', forbidClick: true, duration: 0 });
    await httpRequest.post('/vivo/1914959045256859650/synthesis/super');
    // 合成成功，关闭合成弹窗，显示成功弹窗
    showComposeCardDialog.value = false;
    showSuperCardDialog.value = true;
    getHomeData({ showLoading: false }); // 刷新数据
  } catch (e: any) {
    showToast(e.message || '合成失败，请稍后重试');
  } finally {
    closeToast();
  }
};

// 点击抽卡记录
const onClickCardRecord = async () => {
  try {
    showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
    const { data } = await httpRequest.post('/vivo/1914959045256859650/query/cards');
    cardRecordList.value = data || [];
    cardRecordTab.value = 'card'; // 默认切换到“卡片记录”Tab
    showCardRecordDialog.value = true;
  } catch (e: any) {
    showToast(e.message || '获取抽卡记录失败');
  } finally {
    closeToast();
  }
};

// 监听抽奖记录弹窗tab切换
watch(cardRecordTab, async (val) => {
  if (val === 'chance') {
    try {
      showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
      const { data } = await httpRequest.post('/vivo/1914959045256859650/query/task/records');
      cardChanceRecordList.value = data || [];
    } catch (e: any) {
      showToast(e.message || '获取机会记录失败');
    } finally {
      closeToast();
    }
  } else if (val === 'card') {
    try {
      showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
      const { data } = await httpRequest.post('/vivo/1914959045256859650/query/cards');
      cardRecordList.value = data || [];
    } catch (e: any) {
      showToast(e.message || '获取卡片记录失败');
    } finally {
      closeToast();
    }
  }
});

// 点击 做任务获得更多集卡机会
const onClickShowTask = async () => {
  try {
    showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
    const { data } = await httpRequest.post('/vivo/1914959045256859650/query/tasks');
    cardTaskList.value = data || [];
    showCardTaskDialog.value = true;
  } catch (e: any) {
    showToast(e.message || '获取任务失败');
  } finally {
    closeToast();
  }
};

// 点击任务按钮（包含签到）
const onClickTask = async (taskItem) => {
  const { taskType, taskId, taskBtnStatus } = taskItem;
  if (taskBtnStatus === '1') return;

  if (taskType === 1) {
    // 签到
    try {
      showLoadingToast({ message: '签到中...', forbidClick: true, duration: 0 });
      await httpRequest.post('/vivo/1914959045256859650/submit/task', {
        taskId: mainData.signTaskId,
      });
      delayToast('签到成功');
      // 刷新主数据
      await getHomeData({ showLoading: false });
    } catch (e: any) {
      delayToast(e.message || '签到失败');
    } finally {
      closeToast();
    }
  } else if (taskType === 2) {
    // 预约任务
    try {
      showLoadingToast({ message: '预约中...', forbidClick: true, duration: 0 });
      await httpRequest.post('/vivo/1914959045256859650/submit/task', {
        taskId,
      });
      delayToast('预约成功');

      // 刷新任务列表
      const { data: tasksData } = await httpRequest.post('/vivo/1914959045256859650/query/tasks');
      cardTaskList.value = tasksData || [];
      // 刷新主数据
      await getHomeData({ showLoading: false });
    } catch (e: any) {
      showToast(e.message || '提交失败');
    } finally {
      closeToast();
    }
  } else if (taskType === 7) {
    // 下单任务
    try {
      showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
      const { data } = await httpRequest.post('/vivo/1914959045256859650/query/task/goods', {
        taskId,
      });
      orderSkuList.value = data || [];
      orderSkuDialog.value = true;
    } catch (e: any) {
      showToast(e.message || '获取下单商品失败');
    } finally {
      closeToast();
    }
  } else if (taskType === 9) {
    // 浏览会场任务
    window.jmfe.toAny(taskItem.link);
    sessionStorage.setItem('fromViewVenue', 'true');
    sessionStorage.setItem('fromViewVenueTaskId', taskId);
    const now = Date.now();
    sessionStorage.setItem('ViewVenueEnterTime', String(now)); // 记录进入时间
  } else if (taskType === 3) {
    // 入会任务，跳转京东店铺入会页
    handleOpenCard(taskItem.link);
    // 存一下入会的taskId
    sessionStorage.setItem('openCardTaskId', taskId);
  } else if (taskType === 4) {
    // 浏览商品任务
    try {
      showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
      const { data } = await httpRequest.post('/vivo/1914959045256859650/query/task/goods', {
        taskId,
      });
      data.forEach((item) => {
        item.taskId = taskId;
      });
      viewSkuList.value = data || [];
      showViewSkuDialog.value = true;
    } catch (e: any) {
      showToast(e.message || '获取浏览商品失败');
    } finally {
      closeToast();
    }
  } else if (taskType === 5) {
    // 加购任务
    try {
      showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 });
      const { data } = await httpRequest.post('/vivo/1914959045256859650/query/task/goods', {
        taskId,
      });
      addCartSkuList.value = data || [];
      showAddCartDialog.value = true;
      addCartTaskId.value = taskId;
    } catch (e: any) {
      showToast(e.message || '获取加购商品失败');
    } finally {
      closeToast();
    }
  } else if (taskType === 6) {
    // 分享任务
    console.log('邀请好友');
    const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
    callShare({
      title: shareConfig.shareTitle,
      content: shareConfig.shareContent,
      imageUrl: shareConfig.shareImage,
    });
  }
};

// 入会
const handleOpenCard = async (openCardUrl) => {
  openCard(`${openCardUrl}&returnUrl=${`${window.location.href}&isJoin=1`}`);
};

// 入会任务处理方法
const handleJoinTask = async () => {
  try {
    // showLoadingToast({ message: '提交入会任务中...', forbidClick: true, duration: 0 });
    const taskId = sessionStorage.getItem('openCardTaskId');
    await httpRequest.post('/vivo/1914959045256859650/submit/task', {
      taskId,
    });
    delayToast('入会任务完成');

    // 刷新任务列表
    const { data: tasksData } = await httpRequest.post('/vivo/1914959045256859650/query/tasks');
    cardTaskList.value = tasksData || [];
    // 刷新主数据
    await getHomeData({ showLoading: false });
  } catch (e: any) {
    showToast(e.message || '入会任务提交失败');
  } finally {
    closeToast();
  }
};

// 浏览商品逻辑
const handleViewSku = async (item) => {
  gotoSkuPage(item.skuId);
  // 标记来自 sku 页面
  sessionStorage.setItem('fromSkuPage', 'true');
  // 保存当前 skuId
  sessionStorage.setItem('skuId', item.skuId);
  // 保存taskId
  sessionStorage.setItem('viewSkuTaskId', item.taskId);

  const now = Date.now();
  sessionStorage.setItem('skuEnterTime', String(now)); // 记录进入时间
};

// 判断是否从 sku 页面返回，完成浏览任务
const handleViewSkuBack = async () => {
  if (sessionStorage.getItem('fromSkuPage') === 'true') {
    const skuId = sessionStorage.getItem('skuId');
    const taskId = sessionStorage.getItem('viewSkuTaskId');
    const enterTime = Number(sessionStorage.getItem('skuEnterTime') || 0);
    const now = Date.now();

    const stayDuration = now - enterTime;
    const minSeconds = 15;

    if (!skuId || !taskId || !enterTime) return;

    if (stayDuration >= minSeconds * 1000) {
      await httpRequest.post('/vivo/1914959045256859650/submit/task', {
        skuId,
        taskId,
      });
      delayToast('浏览商品任务完成');

      // 刷新任务列表
      const { data: tasksData } = await httpRequest.post('/vivo/1914959045256859650/query/tasks');
      cardTaskList.value = tasksData || [];
      // 刷新主数据
      await getHomeData({ showLoading: false });
    } else {
      delayToast(`浏览时间未满 ${minSeconds} 秒`);
    }

    // 清除标记
    sessionStorage.removeItem('fromSkuPage');
    sessionStorage.removeItem('skuId');
    sessionStorage.removeItem('skuEnterTime');
    sessionStorage.removeItem('viewSkuTaskId');
  }
};

// 判断从会场返回，完成浏览会场任务
const handleViewVenue = async () => {
  if (sessionStorage.getItem('fromViewVenue') === 'true') {
    const taskId = sessionStorage.getItem('fromViewVenueTaskId');
    const enterTime = Number(sessionStorage.getItem('ViewVenueEnterTime') || 0);
    const now = Date.now();

    const stayDuration = now - enterTime;
    const minSeconds = 15;

    if (!taskId || !enterTime) return;

    if (stayDuration >= minSeconds * 1000) {
      try {
        await httpRequest.post('/vivo/1914959045256859650/submit/task', {
          taskId,
        });
        delayToast('浏览会场任务完成');

        // 刷新任务列表
        const { data: tasksData } = await httpRequest.post('/vivo/1914959045256859650/query/tasks');
        cardTaskList.value = tasksData || [];
        // 刷新主数据
        await getHomeData({ showLoading: false });
      } catch (e: any) {
        showToast(e.message);
      }
    } else {
      delayToast(`浏览时间未满 ${minSeconds} 秒`);
    }

    // 清除标记
    sessionStorage.removeItem('fromViewVenue');
    sessionStorage.removeItem('fromViewVenueTaskId');
    sessionStorage.removeItem('ViewVenueEnterTime');
  }
};

// 加购商品逻辑
const handleAddCart = async () => {
  if (!window.jmfe.isApp('jd')) {
    showToast('请在京东APP中进行加购');
    return;
  }

  const skuIds = addCartSkuList.value.map((sku) => sku.skuId);
  console.log(skuIds);
  addSkuToCart(skuIds);

  sessionStorage.setItem('fromAddSkuToCart', 'true');
};
// 加购任务，跳转到购物车，回到页面的时候，做任务
const handleAddCartTask = async () => {
  if (sessionStorage.getItem('fromAddSkuToCart') === 'true') {
    try {
      await httpRequest.post('/vivo/1914959045256859650/submit/task', {
        taskId: addCartTaskId.value,
      });
      delayToast('加购商品任务完成');
      // 刷新任务列表
      const { data: tasksData } = await httpRequest.post('/vivo/1914959045256859650/query/tasks');
      cardTaskList.value = tasksData || [];
      // 刷新主数据
      getHomeData({ showLoading: false });
      sessionStorage.removeItem('fromAddSkuToCart');
      showAddCartDialog.value = false;
    } catch (error: any) {
      showToast(error.message);
      console.error(error);
    } finally {
      closeToast();
    }
  }
};

const lotterySwiperSpeed = ref(3000);
// 中奖信息
const drawRes = ref(null);
// 抽奖逻辑
const handleDrawLottery = async () => {
  if (mainData.remainingDrawPrizeTimes <= 0) {
    showToast('暂无抽奖机会');
    return;
  }

  mainData.remainingDrawPrizeTimes = mainData.remainingDrawPrizeTimes - 1;

  // 设置轮播为快速滚动
  lotterySwiperSpeed.value = 250;

  try {
    // showLoadingToast({ message: '正在抽奖', forbidClick: true, duration: 0 });

    // 等待 1.5 秒，让轮播快速跑几圈
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const { data } = await httpRequest.post('/vivo/1914959045256859650/draw/prize');

    // 还原正常速度
    lotterySwiperSpeed.value = 3000;

    // 未中奖
    if (data.status === 0) {
      showPrizeNoResDialog.value = true;
    } else {
      drawRes.value = data;
      showPrizeResDialog.value = true;
    }

    getHomeData({ showLoading: false });
  } catch (e: any) {
    showToast(e.message);
  } finally {
    closeToast();
  }
};

// 填写地址
const handleFillAddress = async () => {
  showPrizeResDialog.value = false;
  showAddressDialog.value = true;
};

onMounted(async () => {
  await getHomeData();

  // 如果是从入会跳转回来（判断 pathParams 中是否带有 isJoin=1）
  if (pathParams?.isJoin === '1') {
    await handleJoinTask();
  }

  // 页面首次加载也要检查是否从商品页回跳（网页刷新场景）
  await handleViewSkuBack();
  await handleViewVenue();
  await handleOrderSku();

  // App 内跳转回来才会触发 visibilitychange，处理浏览任务回跳
  const handleVisibility = () => {
    if (document.visibilityState === 'visible') {
      handleViewSkuBack();
      handleViewVenue();
      handleAddCartTask();
      handleOrderSku();
    }
  };
  document.addEventListener('visibilitychange', handleVisibility);

  nextTick(() => {
    // 把签到定位到当前日期
    const scrollContainer = document.querySelector('.sign-in-date-list') as HTMLElement;
    const itemEls = document.querySelectorAll('.sign-in-date-item');
    const currentIndex = mainData.signInRecords.findIndex((item) => item.isCurrentDay);
    const currentItem = itemEls[currentIndex] as HTMLElement;

    if (scrollContainer && currentItem) {
      const offsetLeft = currentItem.offsetLeft;
      const containerWidth = scrollContainer.offsetWidth;
      const itemWidth = currentItem.offsetWidth;
      const paddingLeft = parseFloat(window.getComputedStyle(scrollContainer).paddingLeft);

      const scrollTo = offsetLeft - (containerWidth - itemWidth) / 2 - paddingLeft - 6;

      scrollContainer.scrollTo({
        left: scrollTo,
        behavior: 'smooth',
      });
    }

    // 图片和视频列表，瀑布流布局
    if (decoData.imageVideoList?.length > 0) {
      Macy({
        container: '.img-video-list',
        trueOrder: false, // 是否保持渲染顺序（false 会更“自然”）
        waitForImages: true,
        margin: 8,
        columns: 2,
        breakAt: {},
      });
    }
  });

  // 滚动监听
  const scrollKey = 'page-scroll-top';
  const pageEl = document.getElementById('page');
  if (pageEl) {
    const handleScroll = debounce(() => {
      sessionStorage.setItem(scrollKey, String(pageEl.scrollTop));
    }, 200);
    pageEl.addEventListener('scroll', handleScroll);

    // 恢复滚动位置
    const savedTop = sessionStorage.getItem(scrollKey);
    pageEl.scrollTop = Number(savedTop);
  }
});
</script>

<style lang="scss" scoped>
#page {
  width: 100vw;
  height: 100vh;
  overflow: auto;
}

.container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  // max-height: 37.2rem;
  padding-top: 4.25rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/312164/18/2411/1099464/682be4f5F3e7b628b/ead08e07bfae2e77.png');
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
}

/* 活动规则 */
.rule-button {
  position: absolute;
  top: 2.89rem;
  right: 0;
  width: 1.2rem;
  height: 0.5rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/296757/22/1144/11848/68109831F67bbc6d4/c28fdcd37d3e9453.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 我的奖品 */
.my-prize-button {
  position: absolute;
  top: 3.54rem;
  right: 0;
  width: 1.2rem;
  height: 0.5rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/282156/39/28663/11929/68109831F09a48b22/fa9f4c8b46ecbd60.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 五张卡片的展示 */
.card-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  min-height: 6.7rem;

  .card-item {
    position: relative;
    width: (669 / (3 * 100)) * 1rem;
    height: (1012 / (3 * 100)) * 1rem;
    margin: 0 0.05rem;

    &.active {
      &::before {
        position: absolute;
        top: 50%;
        left: 50%;
        display: block;
        width: (625 / (3 * 100)) * 1rem;
        height: (958 / (3 * 100)) * 1rem;
        background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/275422/24/28319/44762/68109831Fe6b13c68/ee55ba7c099348b7.png');
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 100% 100%;
        transform: translate(-50%, -50%);
        content: '';
      }
    }

    .card-image {
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100% 100%;
    }
  }
}

/* 抽卡按钮区 */
.draw-card-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.58rem;

  .draw-card-record-button {
    width: (483 / (3 * 100)) * 1rem;
    height: (186 / (3 * 100)) * 1rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/272752/23/29362/3856/6810a136Fb3317126/e3387c0880845618.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }

  .draw-card-button {
    width: (1089 / (3 * 100)) * 1rem;
    height: (266 / (3 * 100)) * 1rem;
    margin: 0 0.2rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/296480/3/1409/93209/6810a136F4a0430c2/48ddfcd827024812.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }

  .show-task-button {
    width: (483 / (3 * 100)) * 1rem;
    height: (186 / (3 * 100)) * 1rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/321136/31/1105/7689/6826d2c9F5141e9e0/dbbf1a22b14eaca5.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }
}

/* 剩余抽奖机会 */
.draw-card-chance {
  margin: 0.15rem 0 0.5rem;
  font-size: 0.22rem;
  text-align: center;
}

/* 已收集卡片 */
.collected-card-list {
  display: flex;
  justify-content: center;
  min-height: 1rem;

  .collected-card-item {
    position: relative;
    width: 1rem;
    height: 1rem;
    margin: 0 0.08rem;
  }

  .collected-card-image {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    transform: translate(-50%, -50%);

    &.is-synthesis {
      &::before {
        position: absolute;
        top: 50%;
        left: 50%;
        display: block;
        width: (360 / (3 * 100)) * 1rem;
        height: (360 / (3 * 100)) * 1rem;
        background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/319910/23/581/28424/682494ecF6a864a06/99cad979926bcd09.png');
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 100% 100%;
        transform: translate(-50%, -50%);
        animation: breath-light 1.6s infinite ease-in-out;
        content: '';
        pointer-events: none; // 避免伪元素挡住点击
      }

      &::after {
        position: absolute;
        bottom: 0.13rem;
        left: 50%;
        display: block;
        width: (234 / (3 * 100)) * 1rem;
        height: (104 / (3 * 100)) * 1rem;
        background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/317180/15/2160/6762/682be4f4F2a0be76b/9e22769bec48e692.png');
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 100% 100%;
        transform: translateX(-50%);
        content: '';
        pointer-events: none; // 避免伪元素挡住点击
      }
    }
  }

  @keyframes breath-light {
    0% {
      opacity: 0.2;
      filter: brightness(1.2);
    }

    50% {
      opacity: 1;
      filter: brightness(2);
    }

    100% {
      opacity: 0.2;
      filter: brightness(1.2);
    }
  }

  .collected-card-num {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 0.3rem;
    height: 0.3rem;
    color: #fff;
    font-size: 0.25rem;
    background-color: #ffa7d0;
    border-radius: 50%;
    transform: translate(40%, -40%);
  }
}

.collected-card-tips {
  margin: 0.25rem 0 0.4rem;
  color: #000;
  font-size: 0.2rem;
  line-height: 1.5;
  white-space: break-spaces;
  text-align: center;
}

/* 签到区 */
.sign-in-container {
  display: none;
  // display: flex;
  flex-direction: column;
  align-items: center;
  width: (1426 / (2 * 100)) * 1rem;
  height: (814 / (2 * 100)) * 1rem;
  padding-top: 1.15rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/272767/34/29289/49912/6810c73aF8ce4a9e3/0f3cf109fbfba8ef.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .sign-in-tips {
    color: #000;
    font-size: 0.2rem;
    line-height: 0.33rem;
    white-space: break-spaces;
    text-align: center;
  }

  .sign-in-container-inner {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0.3rem 0.3rem 0.3rem 0.4rem;
  }

  .sign-in-num-container {
    display: flex;
    align-items: center;
    color: #000;
    font-size: 0.2rem;
    letter-spacing: 0.01rem;

    .sign-in-num {
      color: #ff78b7;
    }
  }

  .sign-in-button {
    width: (465 / (3 * 100)) * 1rem;
    height: (156 / (3 * 100)) * 1rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/301433/40/316/16018/6810d154F3f3b2d6e/a623207112e415d8.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;

    &.btn-disabled {
      background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/279922/2/28402/6164/6810d1beF84b7420b/94cb77bfdeaf8f28.png');
      pointer-events: none;
    }
  }

  .sign-in-date-list {
    display: flex;
    width: 95%;
    padding-bottom: 0.4rem;
    padding-left: 0.23rem;
    overflow: auto hidden;
    scroll-behavior: smooth;
  }

  .sign-in-date-item {
    position: relative;
    flex-shrink: 0;
    margin-right: 0.58rem;

    &::after {
      position: absolute;
      top: 0;
      left: 100%;
      display: block;
      width: 0.58rem;
      height: 100%;
      background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/297887/9/1622/160/681181c3Ffc3fa627/d1e3148282090a8b.png');
      background-repeat: no-repeat;
      background-position: center center;
      background-size: (124 / (3 * 100)) * 1rem (4 / (3 * 100)) * 1rem;
      content: '';
    }

    &:last-child {
      margin-right: 0;

      &::after {
        display: none;
      }
    }
  }

  .sign-in-icon {
    width: (126 / (3 * 100)) * 1rem;
    height: (126 / (3 * 100)) * 1rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/292735/24/1354/1763/68117d03Fc2260b82/a35baa69c62b853b.png');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: (100 / (3 * 100)) * 1rem (100 / (3 * 100)) * 1rem;

    &.is-sign {
      background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/300023/35/1037/3022/68117d03Fdee3abc9/00a3f177976a952e.png');
      background-size: 100% 100%;
    }
  }

  .sign-date {
    position: absolute;
    bottom: -0.3rem;
    left: 50%;
    color: #a4d4fc;
    font-size: 0.18rem;
    word-break: keep-all;
    transform: translate(-50%, 0);
  }
}

/* 抽奖区 */
.draw-lottery-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1426 / (2 * 100)) * 1rem;
  height: (1632 / (2 * 100)) * 1rem;
  padding-top: 2.1rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/303658/40/1438/221597/6822bc34F5450a8af/cac45647bd02b76b.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .draw-lottery-swiper {
    width: 6.1rem;
    height: 3.24rem;
    border-radius: 0.45rem;

    --van-swipe-indicator-inactive-background: #afdff2;
    --van-swipe-indicator-inactive-opacity: 1;

    :deep(.van-swipe__indicators) {
      bottom: 0.15rem;
    }

    .draw-lottery-swiper-item {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      border-radius: 0.45rem;
    }

    .prize-img {
      height: 97%;
    }
  }

  .draw-lottery-button {
    width: (1347 / (3 * 100)) * 1rem;
    height: (291 / (3 * 100)) * 1rem;
    margin-top: 1rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/291885/39/1274/83979/68118d85Fdf5fe2c3/534fb3e785015ebc.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    transition: all 50ms;

    &:active {
      transform: scale(0.98);
      transform-origin: center;
    }
  }

  .draw-lottery-chance {
    padding-top: 0.4em;
    color: #a62d65;
    font-size: 0.2rem;
  }
}

/* 图片视频区 */
.img-video-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.45rem 0 0;

  .img-video-title {
    width: (1164 / (2 * 100)) * 1rem;
    height: (90 / (2 * 100)) * 1rem;
    margin-bottom: 0.3rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/287680/27/1344/21708/6811b602Fbd80bf92/51ad316fff823696.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }

  .img-video-list-container {
    width: (1426 / (2 * 100)) * 1rem;
    max-height: (2252 / (2 * 100)) * 1rem;
    padding: 0.3rem 0.25rem;
    overflow-y: auto;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/276011/13/28614/15017/6811b602Fe6978299/0ee3cb1ddab92d53.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }

  .img-video-list {
    width: 100%;
  }

  .img-video-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.1rem 0.1rem 0;
    background-color: #fff;
    border-radius: 0.14rem;
    box-shadow: 0 0.08rem 0.06rem 0 rgba(211, 197, 217, 20%);
  }

  .img-video-img {
    width: 100%;
    border-radius: 0.13rem;
  }

  .img-video-tips {
    margin: 0.2rem 0.22rem 0.25rem;
    color: #7c7c7c;
    font-size: 0.24rem;
    line-height: 1.2;
  }
}

/* 底部商标 */
.footer-logo-container {
  width: (1117 / (3 * 100)) * 1rem;
  height: (96 / (3 * 100)) * 1rem;
  margin: 0.3rem 0;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/312943/18/2496/23548/682be4f5Fcaee8ed5/89be352a59d2249b.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 弹窗 */
.my-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 7.5rem;
  overflow-y: unset;
  background: none;

  &.van-popup--center {
    max-width: 100%;
  }
}

.dialog-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;

  &.card-task-dialog-container {
    justify-content: flex-end;
  }
}

/* 关闭弹窗icon */
.dialog-close-btn {
  position: absolute;
  top: 0.4rem;
  right: 0.4rem;
  width: (93 / (2 * 100)) * 1rem;
  height: (93 / (2 * 100)) * 1rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/302804/12/1081/1407/6811d24aFfa69628a/461707c7f0a64240.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 合成S卡弹窗 */
.compose-card-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1404 / (2 * 100)) * 1rem;
  height: (1658 / (2 * 100)) * 1rem;
  padding-top: 1.3rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/287182/11/2722/40637/6819c1ffF43a0d902/7a9541001ddc2cf8.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .compose-card-dialog-content {
    display: flex;
    align-items: center;
    width: 6.25rem;
    height: 6.6rem;
    border-radius: 0.26rem;
  }

  .compose-card-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;

    .compose-card-item {
      position: relative;
      width: (669 / (3 * 100)) * 1rem * 0.85;
      height: (1012 / (3 * 100)) * 1rem * 0.85;
      margin: 0 0.05rem 0.2rem;

      .compose-card-image {
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% 100%;
      }
    }

    .compose-card-num {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 0.3rem;
      height: 0.3rem;
      color: #fff;
      font-size: 0.25rem;
      background-color: #f66fae;
      border-radius: 50%;
    }
  }
}

/* 立即合成按钮 */
.compose-card-button {
  width: (729 / (2 * 100)) * 1rem;
  height: (177 / (2 * 100)) * 1rem;
  margin-top: 0.5rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/295773/6/3514/46878/6819c1feF57297eae/184babc7df566a0b.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 已合成S卡弹窗 */
.super-card-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1279 / (2 * 100)) * 1rem;
  height: (1800 / (2 * 100)) * 1rem;
  padding-top: 2rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/281100/36/29147/41800/6819ca05Fe9338bf5/2bea94fca210622a.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .super-card-image {
    width: 4.17rem;
  }

  .dialog-close-btn {
    top: 0.3rem;
    right: 0.35rem;
  }
}

.receive-super-card-button {
  width: (729 / (2 * 100)) * 1rem;
  height: (177 / (2 * 100)) * 1rem;
  margin-top: 0.5rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/292601/19/3186/45463/6819ca05F14e0a273/83e657611c0189a7.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 抽卡结果弹窗 */
.draw-card-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1279 / (2 * 100)) * 1rem;
  height: (1800 / (2 * 100)) * 1rem;
  padding-top: 2rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/287532/16/3475/39256/6819d5aeF1b4ae022/4b15e9d55f2155dc.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .draw-card-image {
    width: 4.17rem;
  }

  .dialog-close-btn {
    top: 0.3rem;
    right: 0.35rem;
  }
}

.receive-draw-card-button {
  width: (729 / (2 * 100)) * 1rem;
  height: (177 / (2 * 100)) * 1rem;
  margin-top: 0.5rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/292601/19/3186/45463/6819ca05F14e0a273/83e657611c0189a7.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 中奖弹窗 */
.prize-res-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1279 / (2 * 100)) * 1rem;
  height: (1800 / (2 * 100)) * 1rem;
  padding-top: 1.95rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/280512/33/26356/62794/6811d24aF44467d2e/6fc2d35a39b3c274.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .prize-img-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4.3rem;
    height: 4.3rem;

    .prize-img {
      height: 90%;
    }
  }

  .prize-name {
    position: absolute;
    bottom: 0.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.9rem;
    padding: 0 0.4rem;
    color: #ac598a;
    font-size: 0.26rem;
    line-height: 1.6;
    letter-spacing: 0.02rem;
    white-space: break-spaces;
    text-align: center;
  }
}

/* 填写地址按钮 */
.fill-address-button {
  width: (729 / (2 * 100)) * 1rem;
  height: (177 / (2 * 100)) * 1rem;
  margin-top: 0.5rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/283671/26/26346/46215/6811d24aFbc30cedf/170b84374be70f01.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 好的按钮 */
.ok-button {
  width: (729 / (2 * 100)) * 1rem;
  height: (177 / (2 * 100)) * 1rem;
  margin-top: 0.5rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/299789/10/1682/46369/6811d249Ff7c425ff/c1a75a34ed2bb987.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 未中奖弹窗 */
.no-prize-res-dialog {
  position: relative;
  width: (1279 / (2 * 100)) * 1rem;
  height: (1580 / (2 * 100)) * 1rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/293010/5/1479/95429/6811d249F8da2f9f1/e1a2bfbb9e490fa3.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .dialog-close-btn {
    top: 0.32rem;
    right: 0.35rem;
  }
}

/* 抽卡记录弹窗 */
.card-record-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1279 / (2 * 100)) * 1rem;
  height: (1760 / (2 * 100)) * 1rem;
  padding-top: 1.25rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/289664/11/1757/43846/6811e999F915c5bad/10cacbb5d2b6736a.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .card-record-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 5.33rem;
    height: 6.98rem;
    border-radius: 0.28rem;
  }

  .card-record-tab-header {
    display: flex;
    width: 5.1rem;
    height: 0.79rem;
    margin-top: 0.12rem;
    background-color: #fff;
    border-bottom: 0.02rem solid #e677ad;

    .card-record-tab-item {
      position: relative;
      z-index: 1;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 0.32rem;
      background: #fff;
      border: solid 0.01rem #fff;
      border-radius: 0.19rem 0.19rem 0.02rem 0.02rem;

      &.active {
        color: #e677ad;
        background-image: linear-gradient(-30deg, #ffdbf0 0%, #ffe9f8 100%);
        border: solid 0.01rem #f9a7cf;
      }
    }
  }

  .tab-body {
    flex: 1;
    width: 100%;
    margin: 0 0.05rem;
    padding: 0.23rem;
    overflow-y: auto;
  }

  .card-record-list {
    width: 100%;

    .card-record-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.35rem 0.2rem;
      border-bottom: 0.01rem solid #ededed;

      .card-record-name {
        color: #5d5d5d;
        font-size: 0.28rem;
      }

      .card-record-date {
        color: #acacac;
        font-size: 0.2rem;
      }
    }
  }

  .chance-record-list {
    position: relative;
    width: 100%;
    min-height: 100%;

    .chance-record-item {
      display: flex;
      align-items: center;
      padding: 0.35rem 0.2rem;
      color: #8d8d8d;
      font-weight: normal;
      font-size: 0.2rem;
      font-stretch: normal;
      letter-spacing: 0.01rem;
      border-bottom: 0.01rem solid #ededed;

      .chance-record-date {
        flex-shrink: 0;
        width: 1.1rem;
      }

      .chance-record-task {
        flex: 1;
        margin: 0 0.2rem;
        word-break: break-all;
      }

      .chance-record-info {
        flex-shrink: 0;
        width: 1.5rem;
        margin-right: 0.2rem;
        word-break: break-all;
      }

      .chance-record-plus {
        flex-shrink: 0;
      }
    }
  }
}

/* 我的奖品弹窗 */
.my-prize-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1279 / (2 * 100)) * 1rem;
  height: (1760 / (2 * 100)) * 1rem;
  padding-top: 2.1rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/290686/22/3016/56320/681979adFe5564377/0a7e31f52360913e.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .my-prize-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 5.33rem;
    height: 6rem;
    padding: 0.1rem 0.2rem 0;
    overflow-y: auto;
    border-radius: 0.28rem;
  }

  .my-prize-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.24rem 0;
    border-bottom: 0.01rem solid #ededed;

    &:last-child {
      border-bottom: none;
    }
  }

  .col-time {
    flex: 0 0 1.83rem;
    padding-left: 0.1rem;
    color: #5d5d5d;
    font-size: 0.18rem;
  }

  .col-name {
    display: flex;
    flex: 1;
    justify-content: center;
    color: #5d5d5d;
    font-size: 0.24rem;
  }

  .col-status {
    display: flex;
    flex: 0 0 1.5rem;
    justify-content: flex-end;
  }

  .btn {
    width: 1.25rem;
    height: 0.48rem;
    font-size: 0.2rem;
    border-radius: 0.25rem;
  }

  .btn.blue {
    color: #fff;
    background-image: linear-gradient(90deg, #88caff 0%, #7ec5fe 71%, #74c0fd 100%);
  }

  .btn.gray {
    color: #8e8e8e;
    background-color: #e3e3e3;
  }
}

/* 规则弹窗 */
.rule-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1279 / (2 * 100)) * 1rem;
  height: (2018 / (2 * 100)) * 1rem;
  padding-top: 1.25rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/300082/15/2956/44164/68199cecF7e7b69a1/ddf75c6262912040.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  :deep(.ps__thumb-y) {
    width: 0.08rem;
    background-color: #de85ba !important;
    border-radius: 0.04rem;
  }

  :deep(.ps__rail-y) {
    right: 0;
    width: 0.08rem;
    background-color: transparent;
  }

  :deep(.ps__rail-x),
  :deep(.ps__thumb-x) {
    display: none !important;
  }

  .rule-dialog-container {
    width: 5.33rem;
    height: 8.28rem;
    padding: 0.5rem 0;
    border-radius: 0.28rem;

    .rule-scroll {
      width: 100%;
      height: 100%;
      padding: 0 0.4rem;
    }

    .rule-text {
      color: #6c6c6c;
      font-size: 0.24rem;
      line-height: 2;
      white-space: pre-line;
      text-align: justify;
      word-break: break-all;
    }
  }
}

/* 集卡任务弹窗 */
.card-task-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1500 / (2 * 100)) * 1rem;
  height: (1726 / (2 * 100)) * 1rem;
  padding: 1.5rem 0.2rem 0;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/317848/6/524/102699/68243286F7da4c2f2/72def116a297dd95.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .dialog-close-btn {
    top: 0.5rem;
    right: 0.3rem;
  }

  .card-task-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: auto;

    .card-task-item {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 1.3rem;
      margin-bottom: 0.1rem;
      padding: 0 0.28rem 0 0.3rem;
      background-color: #fff;
      border-radius: 0.24rem;
    }

    .task-info {
      display: flex;
      flex-direction: column;
    }

    .task-name {
      margin-bottom: 0.13rem;
      color: #505050;
      font-size: 0.3rem;
      letter-spacing: -1px;

      &.task-type-9 {
        font-size: 0.27rem;
      }
    }

    .task-tips {
      color: #7b7b7b;
      font-size: 0.2rem;
      letter-spacing: -1px;
    }

    .task-btn {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 1.45rem;
      height: 0.55rem;
      color: #fff;
      font-size: 0.24rem;
      white-space: nowrap;
      background-image: linear-gradient(90deg, #add7ff 4%, #60c4f9 100%);
      border-radius: 0.3rem;

      &.disabled {
        color: #8e8e8e;
        background-color: #e3e3e3;
        background-image: none;
      }
    }
  }
}

/* 加购商品弹窗 */
.add-cart-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1279 / (2 * 100)) * 1rem;
  height: (1820 / (2 * 100)) * 1rem;
  padding-top: 1.5rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/291618/24/3221/84399/6819d8e5Ff8098d23/620e5620e0d5b93f.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .add-cart-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 5.25rem;
    height: 5.8rem;
    overflow: auto;
  }

  .add-cart-button {
    width: (729 / (2 * 100)) * 1rem;
    height: (177 / (2 * 100)) * 1rem;
    margin-top: 0.35rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/299748/26/3756/46072/6819d8e5Fc81d7bbe/9a697253ba053f4f.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }
}

/* 浏览商品弹窗 */
.view-sku-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1279 / (2 * 100)) * 1rem;
  height: (1920 / (2 * 100)) * 1rem;
  padding-top: 1.5rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/295064/32/2855/92551/681a0252F5ce5bcb4/cf9d1fe35480d982.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .view-sku-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 5.25rem;
    height: 7.5rem;
    overflow: auto;
  }

  .view-sku-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 0.15rem;
  }

  .view-sku-button {
    width: (588 / (3 * 100)) * 1rem;
    height: (189 / (3 * 100)) * 1rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/284238/2/29565/39746/681a0252Fe880c885/a02e599d76d706d2.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }
}

/* 下单商品弹窗 */
.order-sku-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1279 / (2 * 100)) * 1rem;
  height: (1920 / (2 * 100)) * 1rem;
  padding-top: 1.5rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/305862/31/1830/93964/68244586Ffb268b6d/456d01f4be6268bf.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .order-sku-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 5.25rem;
    height: 7.5rem;
    overflow: auto;
  }

  .order-sku-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 0.15rem;
  }

  .order-sku-button {
    width: (588 / (3 * 100)) * 1rem;
    height: (189 / (3 * 100)) * 1rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/313563/21/752/39379/68244585F4a6537e1/7e9891e61e759a24.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }
}

/* 弹窗中sku展示 */
.sku-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (506 / (2 * 100)) * 1rem;
  height: (568 / (2 * 100)) * 1rem;
  margin-bottom: 0.15rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/284076/26/29233/3251/6819d8e5F0d630ce6/c6e5ed63f0013ea6.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .sku-img {
    width: (506 / (2 * 100)) * 1rem;
    height: 2.34rem;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% auto;
    border-radius: 0.28rem;
  }

  .sku-name-container {
    display: flex;
    flex: 1;
    align-items: center;
    width: 100%;
    padding: 0 0.2rem 0.05rem;
  }

  .sku-name {
    color: #191919;
    font-size: 0.22rem;
  }
}

/* 填写地址弹窗 */
.address-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1279 / (2 * 100)) * 1rem;
  height: (1480 / (2 * 100)) * 1rem;
  padding-top: 1.4rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/280876/16/29114/100756/681a087eF393e8852/2cb76b9450cb3482.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .address-form-field {
    width: 5.33rem;
    height: 0.9rem;
    margin-bottom: 0.2rem;
    padding: 0 0.2rem 0 0;
    font-size: 0.24rem;
    background-color: #fff;
    border-radius: 0.21rem;

    :deep(.van-field__label) {
      display: flex;
      align-items: center;
      width: 1.4rem;
      padding-left: 0.2rem;
      color: #5d5d5d;
      word-break: keep-all;
    }

    :deep(.van-field__label--required::before) {
      color: #f22626;
    }

    :deep(.van-field__value) {
      display: flex;
      align-items: center;
      color: #5d5d5d;

      .van-field__body {
        width: 100%;
      }
    }
  }
}

/* 提交地址按钮 */
.submit-address-button {
  width: (729 / (2 * 100)) * 1rem;
  height: (177 / (2 * 100)) * 1rem;
  margin-top: 0.3rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/300869/3/3242/45553/681a087eF7561ee2c/bd65c1b780132286.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/* 选择省市区弹窗 */
.choose-area-dialog {
  width: 100vw;

  :deep(.van-picker) {
    width: 100%;
  }
}

/* 图片弹窗 */
.image-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (1279 / (2 * 100)) * 1rem;
  height: (1800 / (2 * 100)) * 1rem;
  padding-top: 1rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/302723/20/3257/26805/681ab7d9Fcfc26d3d/ebd84e68884b4c6d.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .dialog-close-btn {
    top: 0.33rem;
    right: 0.33rem;
  }

  .image-dialog-swiper {
    width: 4.39rem;
    height: 6rem;
    border-radius: 0.13rem;

    --van-swipe-indicator-inactive-background: #f2def2;
    --van-swipe-indicator-inactive-opacity: 1;

    :deep(.van-swipe__indicators) {
      bottom: 0.15rem;
    }

    .image-dialog-swiper-item {
      height: 5.54rem;
      border-radius: 0.13rem;
    }

    .image-dialog-img {
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100% auto;
      border-radius: 0.13rem;
    }
  }

  .image-dialog-text {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    margin-top: 0.2rem;
    margin-bottom: 0.3rem;
    color: #ac598a;
    font-size: 0.26rem;
    white-space: break-spaces;
  }
}

/* 视频弹窗 */
.video-dialog {
  position: relative;
  width: 100vw;

  .video-player {
    width: 100vw;
    height: 4.2rem;
    max-height: 100vh;
    background: transparent;

    :deep(.vjs-poster) {
      background-color: transparent;
    }

    :deep(.vjs-big-play-button) {
      top: 50%;
      left: 50%;
      margin: 0;
      transform: translate(-50%, -50%);

      .vjs-icon-placeholder {
        &::before {
          top: 50%;
          left: 50%;
          width: auto;
          height: auto;
          transform: translate(-50%, -50%);
        }
      }
    }
  }
}

.empty-text {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #ac598a;
  font-size: 0.26rem;
  transform: translate(-50%, -50%);
}
</style>

<style lang="scss">
* {
  line-height: 1;

  ::-webkit-scrollbar {
    display: none;
  }
}

// 禁止页面回弹行为
html,
body {
  overscroll-behavior: none none;
}

button {
  margin: 0;
  padding: 0;
  background-color: transparent;
  border: 0;
  outline: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0%); /* 透明色，禁用默认高亮效果 */
}

.text-overflow-line {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-overflow-2-line {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>
