import { showLoadingToast, showToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';

// 活动主接口
export const mainActivity = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/doMain');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return message;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 签到详情
export const signDetail = async () => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    // });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/signDetail');
    if (code !== 200) {
      showToast(message);
      return message;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 点击签到
export const sign = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const res = await httpRequest.post('/dz/1963890232061632513/sign');
    closeToast();
    if (res.code !== 200) {
      showToast(res.message);
      return false;
    }
    return res;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 获取活动规则
export const getRule = async () => {
  try {
    const { data, code, message } = await httpRequest.get('/common/getRule');
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 任务列表
export const taskList = async () => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    // });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/task');
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 预约商品
export const appointSku = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/appointSku');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return code;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 浏览商品
export const browseSku = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/browseSku');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return code;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 浏览会场
export const browseArea = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/browseArea');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return code;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 浏览新品搜索页
export const browseSearchPage = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/browseSearchPage');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return code;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 卡片列表
export const cardList = async () => {
  try {
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/card');
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 卡片列表
export const lotteryDrawCard = async (groupId: any) => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/lotteryDrawCard', { groupId });
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 兑换卡片
export const exchangeCard = async (cardId: any, exchangeCardId: any) => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/exchangeCard', { cardId, exchangeCardId });
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return code;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 拼图获取记录
export const myCardTimeList = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.get('/dz/1963890232061632513/myCardTimeList');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 抽奖
export const draw = async (groupId: any) => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/lotteryDraw', { groupId });
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 填写地址
export const fillAddress = async (address: any) => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/writeAddress', { ...address });
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return code;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 我的奖品
export const getPrize = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.get('/dz/1963890232061632513/myPrizes');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 获取加购商品
export const getAddSkus = async (taskType: any) => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/taskSku', { taskType });
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 加购商品
export const addSku = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/addSku');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return code;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 获取奖品列表
export const lotteryPrize = async () => {
  try {
    const { data, code, message } = await httpRequest.get('/dz/1963890232061632513/lotteryPrize');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 查询积分兑换奖品列表
export const pointPrize = async () => {
  try {
    const { data, code, message } = await httpRequest.get('/dz/1963890232061632513/pointPrize');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 老客返积分领取
export const olderPoint = async () => {
  try {
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/olderPoint');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
export const olderPointDraw = async () => {
  try {
    const data = await httpRequest.post('/dz/1963890232061632513/olderPointDraw');
    closeToast();
    if (data.code !== 200) {
      showToast(data.message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 会员日返积分状态
export const memberDayPoint = async () => {
  try {
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/memberDayPoint');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
export const memberDayPointDraw = async () => {
  try {
    const data = await httpRequest.post('/dz/1963890232061632513/memberDayPointDraw');
    closeToast();
    if (data.code !== 200) {
      showToast(data.message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 积分兑换奖品
export const pointExchange = async (prizeId: any) => {
  try {
    const data = await httpRequest.post('/dz/1963890232061632513/pointExchange', { prizeId });
    closeToast();
    if (data.code !== 200) {
      showToast(data.message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
