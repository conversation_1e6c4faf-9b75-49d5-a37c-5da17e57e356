<template>
  <VanPopup v-model:show="show">
    <div class="popup-container">
      <div class="card">
        <img class="cardImg" :src="chooseCardItem.BCardImg" alt="" />
      </div>
      <div class="tips">注：5个重复碎片可兑换任意1个新碎片</div>
      <div class="btn-group">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/349114/40/2484/3157/68c383ceF473a165b/762f809183590774.png" alt="" @click="close" />
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/324375/4/19181/6797/68c383ceF51b0992d/af03d28526882ab2.png" alt="" @click="Exchange()" />
      </div>
      <img class="close" src="//img10.360buyimg.com/imgzone/jfs/t1/336699/33/7877/1518/68becc65F83003840/ce3f7cefdf3ea220.png" alt="" @click="close" />
    </div>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
const props = defineProps({
  showPopup: {
    type: Boolean,
    default: false,
  },
  chooseCardItem: {
    type: Object,
    default: () => {},
  },
});
const show = computed(() => props.showPopup);
const Exchange = () => {
  emit('Exchange', props.chooseCardItem);
};
const close = () => {
  emit('close');
};

const emit = defineEmits(['close', 'Exchange']);
</script>
<style scoped lang="scss">
.popup-container {
  width: 6.5rem;
  height: 9.2rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/326846/22/20787/12716/68c90564F6ed79936/4efca7bdce67684b.png') no-repeat;
  background-size: 100%;
  position: relative;
  padding-top: 1.3rem;
  display: flex;
  justify-content: center;
  .card {
    width: 3rem;
    height: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    .cardImg {
      width: 2rem;
    }
  }
  .tips {
    width: 100%;
    text-align: center;
    font-size: 0.18rem;
    color: #666566;
    position: absolute;
    top: 6rem;
  }
  .btn-group {
    width: 5rem;
    position: absolute;
    top: 6.9rem;
    display: flex;
    justify-content: space-around;
    left: 50%;
    transform: translateX(-50%);
    img {
      width: 2.2rem;
      /* height: 0.5rem; */
    }
  }
  .close {
    width: 0.5rem;
    height: 0.5rem;
    position: absolute;
    bottom: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
