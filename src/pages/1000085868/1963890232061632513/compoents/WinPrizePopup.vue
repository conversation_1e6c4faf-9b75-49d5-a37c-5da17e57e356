<template>
  <VanPopup v-model:show="show">
    <div class="popup-container">
      <div class="prize" v-if="prizeInfo.prizeType === 3">
        <img class="prizeImg" :src="prizeInfo.prizeImg" alt="" />
        <div class="text">恭喜您成功抽中</div>
        <div class="prizeName">{{ prizeInfo.prizeName }}</div>
      </div>
      <div class="prize" v-if="prizeInfo.prizeType === 7">
        <img class="prizeImg" :src="prizeInfo.prizeImg" alt="" />
        <div class="text">恭喜您成功抽中</div>
        <div class="prizeName">{{ prizeInfo.prizeName }}</div>
        <div class="copy">
          <div class="title">卡密:</div>
          <input type="text" readonly :value="prizeInfo.result ? prizeInfo.result.cardNumber : prizeInfo.cardNumber" /> <img class="product-card-btn copy-button" :copy-text="prizeInfo.result ? prizeInfo.result.cardNumber : prizeInfo.cardNumber" src="//img10.360buyimg.com/imgzone/jfs/t1/339608/39/9920/1655/68c3c6c3F64230be2/5cb45c8e199dc935.png" alt="" />
        </div>
      </div>
      <div class="prize" v-if="prizeInfo.prizeType === 8">
        <img class="prizeImg" :src="prizeInfo.prizeImg" alt="" />
        <div class="prizeName">获得{{ prizeInfo.prizeName }}</div>
        <div class="text">可在 我的-钱包 中查看</div>
      </div>
      <div class="prize" v-if="prizeInfo.prizeType === 4">
        <img class="prizeImg" :src="prizeInfo.prizeImg" alt="" />
        <div class="prizeName">获得{{ prizeInfo.prizeName }}</div>
        <div class="text">活动积分可在本次活动中兑换奖品</div>
      </div>
      <div class="prize" v-if="prizeInfo.prizeType === 2">
        <img class="prizeImg" :src="prizeInfo.prizeImg" alt="" />
        <div class="prizeName">获得{{ prizeInfo.prizeName }}</div>
        <div class="text">可在 我的-京豆 中查看</div>
      </div>
      <img v-if="prizeInfo.prizeType === 3" class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/329596/27/12340/4817/68c3be77F92e4090e/7fa58a8f0d7ee29b.png" alt="" @click="fill()" />
      <img v-else class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/326841/39/18961/5011/68c3c086Fee928763/2966027c486cca24.png" alt="" @click="close()" />
      <img class="close" src="//img10.360buyimg.com/imgzone/jfs/t1/336699/33/7877/1518/68becc65F83003840/ce3f7cefdf3ea220.png" alt="" @click="close" />
    </div>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import Clipboard from 'clipboard';
import { showToast } from 'vant';

const props = defineProps({
  showPopup: {
    type: Boolean,
    default: false,
  },
  prizeInfo: {
    type: Object,
    default: () => {},
  },
});
const show = computed(() => props.showPopup);
const close = () => {
  emit('close');
};
const clipboard = new Clipboard('.copy-button', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
const fill = () => {
  emit('fill');
};
const emit = defineEmits(['close', 'fill']);
watch(
  () => props.prizeInfo,
  (newVal) => {
    if (newVal) {
      // 使用新传入的值，确保组件内部状态与props同步
      console.log('接收到新的奖品信息:', newVal);
      // 这里可以添加其他需要处理的逻辑
    }
  },
  { deep: true, immediate: true },
);
</script>
<style scoped lang="scss">
.popup-container {
  width: 6.5rem;
  height: 9.2rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/333372/29/12402/10008/68c3be77Fae12bf7f/13ecbc173e940ae6.png') no-repeat;
  background-size: 100%;
  position: relative;
  padding-top: 1.8rem;
  display: flex;
  justify-content: center;
  .prize {
    width: 5rem;
    height: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .prizeImg {
      width: 3rem;
      object-fit: contain;
      margin: 0 auto;
    }
    .text {
      width: 100%;
      text-align: center;
      font-size: 0.24rem;
      margin-top: 0.1rem;
      color: #333333;
    }
    .prizeName {
      width: 100%;
      text-align: center;
      font-size: 0.3rem;
      margin-top: 0.1rem;
      color: #333333;
      font-weight: bold;
    }
    .copy {
      width: 100%;
      height: 1rem;
      border: solid 1px #000;
      border-radius: 0.3rem;
      display: flex;
      align-items: center;
      padding: 0.1rem 0.2rem;
      margin-top: 0.2rem;
      input {
        border: none;
        background: none;
        width: 3rem;
        height: 100%;
        font-size: 0.24rem;
        margin-left: 0.1rem;
      }
      .title {
        font-size: 0.24rem;
        width: 0.5rem;
        white-space: nowrap;
      }
      .copy-button {
        width: 1rem;
      }
    }
  }

  .btn {
    width: 3.5rem;
    position: absolute;
    top: 6.6rem;
    left: 50%;
    transform: translateX(-50%);
  }
  .close {
    width: 0.5rem;
    height: 0.5rem;
    position: absolute;
    bottom: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
