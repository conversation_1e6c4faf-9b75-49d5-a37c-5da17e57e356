import{e,a5 as a,_ as t,A as o,B as s,a0 as c,f as n,s as d}from"./index-b897ff36.js";import{c as l}from"./init.6dfa4376.js";const i=e=>{var a=new Date(e);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(r(a.getDate())+" ")},r=e=>(e=e.toString())[1]?e:"0"+e,m=e=>({channelId:"TM"==e?"aptamil-baby-tmall-h5":"aptamil-baby-jd-h5",channelSecret:"TM"==e?"I8YjhCY":"XMu9E8P"}),u=e=>{var a=new RegExp("(^|&)"+e+"=([^&]*)(&|$)");let t=window.location.href.split("#")[0].split("?")[1];if(t){var o=t.substring(0).match(a);return null!==o?decodeURIComponent(o[2]):null}return null},g="/test/cc/custom/api";var h=!1;const p=function(a){return new Promise(((o,s)=>{h||(h=!0,setTimeout((async()=>{t({url:g+"/1000002668/aigc/dz/login",method:"post",header:{"Content-Type":"application/json","Pin-Token":e(l.LZ_PIN_TOKEN)||""},data:{openid:n.state.uniqueId,channel:m(n.state.channel).channelId},async success(a){const t=a;t.data=t.data.data,0==t.data.code&&(d("access_token",t.data.data.token),t.data.data.memberLevel&&d("member_level",t.data.data.memberLevel),e("member_level")&&e("member_level")===n.state.memberLevel||await k(),h=!1,o(t))},fail(e){h=!1,s(e)}})}),a||0))}))},k=()=>new Promise(((a,o)=>{t({url:g+"/1000002668/aigc/dz/userUpdate",method:"post",header:{"Content-Type":"application/json","X-Access-Token":e("access_token"),"Pin-Token":e(l.LZ_PIN_TOKEN)||""},data:{accessToken:e("access_token"),mobile:n.state.mobile,memberLevel:n.state.memberLevel},success(e){d("member_level",n.state.memberLevel),console.log("用户信息同步成功~"),a(e.data)},fail(e){o(e)}})})),b=async n=>{e("access_token")||h||await p(1e3);let d=n.url,i=n.method||"get",r=n.data;r.accessToken=e("access_token"),console.log(r);let m={"X-Access-Token":e("access_token")||"","Content-Type":"application/json","Pin-Token":e(l.LZ_PIN_TOKEN)||""};return"upload"==i?(m={"X-Access-Token":e("access_token")||"","Pin-Token":e(l.LZ_PIN_TOKEN)||""},new Promise(((e,t)=>{a({url:g+d,filePath:r.imageFile,header:m,name:"imageFile",formData:r,success:async a=>{a.data=JSON.parse(a.data),a.data=a.data.data;const t=a;if(200==t.statusCode)if(20==t.data.code||21==t.data.code){await p();const a=await b(n);e(JSON.parse(a))}else e(t.data);else switch(t.statusCode){case 401:case 404:console.log(t)}}})})).catch((e=>{}))):new Promise(((e,a)=>{setTimeout((()=>{t({url:g+d,method:i,header:m,timeout:6e5,data:r,success:async a=>{const t=a;if(t.data=t.data.data,200==t.statusCode)if(20==t.data.code||21==t.data.code){await p();const a=await b(n);e(a)}else e(t.data);else switch(t.statusCode){case 401:case 404:console.log(t)}},fail(e){console.log(e),-1!==e.errMsg.indexOf("request:fail")?o({title:"网络异常",icon:"error",duration:2e3}):o({title:"未知异常",duration:2e3}),a(e)},complete(){s(),c()}})}),h?1e3:0)}))},f={userLogin:e=>b({url:"/hub-tools/login",method:"post",data:e}),userRecordList:e=>b({url:"/1000002668/aigc/dz/recordList",method:"get",data:e}),faceAnticipate:e=>b({url:"/1000002668/aigc/dz/createRecord",method:"upload",data:e}),faceAnticipateImg:e=>b({url:"/1000002668/aigc/dz/generate",method:"get",data:e}),unlockImage:e=>b({url:"/1000002668/aigc/dz/unlockImage",method:"post",data:e}),isQwFriend:e=>b({url:"/hub-tools/wework/is-friend",method:"get",data:e}),getUnlockWay:e=>b({url:"/1000002668/aigc/dz/unlockWay",method:"get",data:e}),recordDetail:e=>b({url:"/1000002668/aigc/dz/recordDetail",method:"get",data:e}),updateRecordDetail:e=>b({url:"/1000002668/aigc/dz/recordUpdate",method:"post",data:e})};export{f as A,m as a,u as g,i as t};
