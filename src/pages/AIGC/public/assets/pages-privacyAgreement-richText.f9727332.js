import{o as t,p as n,a6 as f,h as s,w as i,k as e,u as o,E as p}from"./index-b897ff36.js";import{_ as l,i as a}from"./init.6dfa4376.js";const y=l({mixins:[a],components:{text1:l({props:[],data:()=>({}),computed:{},methods:{}},[["render",function(s,i,e,o,p,l){return t(),n("div",null,[f("p",{style:{"background-color":"#ffffff",margin:"6pt 0pt","text-align":"center"}},[f("span",{style:{color:"#333333","font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"达能"),f("span",{style:{color:"#333333","font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"儿童"),f("span",{style:{color:"#333333","font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"个人信息保护规则")]),f("p",{style:{margin:"6pt 0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}}," ")]),f("p",{style:{margin:"6pt 0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"更新日期："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"2"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"02"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"3"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"年"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"3"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"月"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"1"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"6"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"日")]),f("p",{style:{margin:"6pt 0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}}," ")]),f("p",{style:{margin:"6pt 0pt",orphans:"0","text-align":"center",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"提示条款")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"纽迪希亚生命早期营养品管理"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"（上海）有限公司"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"以下简称“我们”）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"深知儿童（指未满十四周岁的未成年人）个人信息和"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"隐私"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"安全的重要性。"),f("span",{style:{"background-color":"#ffffff",color:"#333333","font-family":"微软雅黑","font-size":"9pt"}},"为保障"),f("span",{style:{"background-color":"#ffffff",color:"#333333","font-family":"微软雅黑","font-size":"9pt"}},"儿童"),f("span",{style:{"background-color":"#ffffff",color:"#333333","font-family":"微软雅黑","font-size":"9pt"}},"个人信息及隐私安全，我们在"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"A"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"ptaclub"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"喂自由"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"隐私政策》"),f("span",{style:{"background-color":"#ffffff",color:"#333333","font-family":"微软雅黑","font-size":"9pt"}},"的基础之上，专门制定了"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"达能"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"儿童个人信息保护规则》（以下简称“本"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"”）"),f("span",{style:{"background-color":"#ffffff",color:"#333333","font-family":"微软雅黑","font-size":"9pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们希望通过"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"本规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"向您说明"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，我们在收集和使用儿童个人信息时对应的处理规则等相关事宜。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"在适用"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"本规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"的情况下，如"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"本规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"与"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"Aptaclub喂自由"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"和具体产品/服务的隐私政策（如有）的条款规定存在不一致的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，以"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"本规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为准；"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"本规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"未载明之处，请参照"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"Aptaclub喂自由"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"或"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"具体产品/服务的隐私政策（如有）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"监护人特别说明：")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"如果您是儿童"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"个人信息主体"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"的监护人，请您务必仔细阅读并透彻理解"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"本规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"，在确认充分理解并同意"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"本规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"的全部条款后再使用我们提供的产品/服务。"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"如果您不同意"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"本规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"的内容，请您立即停止访问"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"、"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"使用我们的产品"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"/"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"服务"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"这可能会导致您无法正常使用部分产品"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"/服务，但不影响您使用我们提供的其他"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"产品"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"/服务"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold","text-decoration":"underline"}},"。")]),f("p",{style:{margin:"6pt 0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}}," ")]),f("p",{style:{margin:"6pt 0pt","text-align":"center"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"儿童个人"),f("span",{style:{color:"#333333","font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"保护规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"条款")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"本规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"将帮助您了解以下内容：")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify","text-indent":"0pt",widows:"0"}},[f("span",{style:{"font-family":"Wingdings","font-size":"9pt"}},"◼"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"           "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们如何"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"收集、使用"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"儿童"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"个人信息")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify","text-indent":"0pt",widows:"0"}},[f("span",{style:{"font-family":"Wingdings","font-size":"9pt"}},"◼"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"           "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们如何委托其他方处理、向其他个人信息处理者提供、转移、公开披露"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"儿童"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"个人信息")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify","text-indent":"0pt",widows:"0"}},[f("span",{style:{"font-family":"Wingdings","font-size":"9pt"}},"◼"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"           "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们如何"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"保护"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"儿童个人信息")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify","text-indent":"0pt",widows:"0"}},[f("span",{style:{"font-family":"Wingdings","font-size":"9pt"}},"◼"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"           "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们如何"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"存储"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"儿童个人信息")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify","text-indent":"0pt",widows:"0"}},[f("span",{style:{"font-family":"Wingdings","font-size":"9pt"}},"◼"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"           "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"如何"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"管理儿童"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"个人信息")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify","text-indent":"0pt",widows:"0"}},[f("span",{style:{"font-family":"Wingdings","font-size":"9pt"}},"◼"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"           "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"本"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"如何更新")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify","text-indent":"0pt",widows:"0"}},[f("span",{style:{"font-family":"Wingdings","font-size":"9pt"}},"◼"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"           "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"如何联系我们")]),f("p",{style:{margin:"6pt 0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}}," ")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"一、我们如何收集和使用"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"儿童"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"个人信息")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"当您作为父母或其他监护人为儿童选择使用"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"相关服务时，我们可能需要向您收集被监护的儿童个人信息，用于向您履行相关服务之必要。在具体服务中需要向您收集儿童个人信息的，我们会事先取得您的单独同意，并告知您收集的目的和用途和其他依法应告知的事项。如果您不提供前述信息，您将无法享受我们提供的相关服务。")]),f("p",{style:{margin:"6pt 0pt 6pt 36pt","text-align":"justify","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（一）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"      "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"建档")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"当您选择在本平台建档时，您需要提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"以下儿童个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝的生日"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"如果"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您不"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"同意"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"这些信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，您将无法"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"完成建档，也无法"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"使用部分会员服务。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"当您选择完善您的宝宝信息设置时，您需要提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"以下儿童个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝头像、宝宝昵称、宝宝生日"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"如果您不同意提供这些信息，您将无法完"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"善宝宝信息设置"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。")]),f("p",{style:{margin:"6pt 0pt 6pt 36pt","text-align":"justify","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（二）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"      "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"喂养小工具")]),f("ul",{type:"disc",style:{margin:"0pt","padding-left":"0pt"}},[f("li",{style:{"font-family":"serif","font-size":"9pt",margin:"6pt 0pt 6pt 9.14pt","padding-left":"8.86pt","text-align":"justify","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了向您提供记喂奶服务，您需要提供以下儿童个人信息（包括文字/语音记录形式）："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝的生日、宝宝的喂奶信息（喂奶方式、喂奶时间、喂奶时长、喂奶量、喂奶种类）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。如果您不同意提供这些信息，我们将无法提供记喂奶服务。")]),f("li",{style:{"font-family":"serif","font-size":"9pt",margin:"6pt 0pt 6pt 9.14pt","padding-left":"8.86pt","text-align":"justify","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了向您提供记睡眠服务，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您需要提供以下儿童个人信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"/语音记录形式）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝的生日、宝宝的睡眠信息（宝宝睡下时间、宝宝睡眠时长）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"如果您不同意提供这些信息，我们将无法提供记睡眠服务。")]),f("li",{style:{"font-family":"serif","font-size":"9pt",margin:"6pt 0pt 6pt 9.14pt","padding-left":"8.86pt","text-align":"justify","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了向您提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"记"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"辅食服务，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您需要提供以下儿童个人信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"/语音记录形式）："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝的生日、宝宝性别、宝宝喂养方式、宝宝的辅食信息（辅食时间、辅食类型、辅食食材、辅食重量）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。如果您不同意提供这些信息，我们将无法提供记"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"辅食"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"服务。")]),f("li",{style:{"font-family":"serif","font-size":"9pt",margin:"6pt 0pt 6pt 9.14pt","padding-left":"8.86pt","text-align":"justify","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了向您提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"宝宝"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"便便"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"记录"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"服务，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您需要提供以下儿童个人信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"/"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"图片"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"）："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝的生日、宝宝便便的照片、宝宝的便便信息（便便时间、尿布状态、便便颜色、便便形状）、宝宝喂养方式"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"如果您不同意提供这些信息，我们将无法提供便便记录服务。"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了优化宝宝便便解析的计算方式，增强用户体验，我们后续需要对收集的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝便便的照片"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"进行去标识化处理后再进行研究分析及统计。如果您不同意我们进行前述处理，您可以通过客服或者"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"本规则所列联系方式"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"与我们联系。")]),f("li",{style:{"font-family":"serif","font-size":"9pt",margin:"6pt 0pt 6pt 9.14pt","padding-left":"8.86pt","text-align":"justify","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了向您提供个性化营养评估服务，您需要提供以下儿童个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝的生日"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，或者您的预产期。如果"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您不同意提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"这些"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"信息，我们将无法提供个性化营养评估试用服务。")]),f("li",{style:{"font-family":"serif","font-size":"9pt",margin:"6pt 0pt 6pt 9.14pt","padding-left":"8.86pt","text-align":"justify","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了向您提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"宝宝体型分析服务（成长曲线）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您需要提供以下儿童个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝的生日、宝宝性别、"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"喂养方式、宝宝身高、宝宝体重"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"如果您不同意提供这些信息，我们将无法提供宝宝体型分析服务"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。")]),f("li",{style:{"font-family":"serif","font-size":"9pt",margin:"6pt 0pt 6pt 9.14pt","padding-left":"8.86pt","text-align":"justify","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了向您提供宝宝拍照身高识别服务"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（成长曲线"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您需要提供以下儿童个人信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"/图片）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝的生日、宝宝照片（宝宝面容及隐私部位进行遮盖处理）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"如果您不同意提供这些信息，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"将"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"无法提供宝宝拍照身高识别服务。为了优化宝宝拍照身高识别的计算方式，增强用户体验，我们后续需要对收集的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝照片（宝宝面容及隐私部位已进行遮盖处理）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"进行去标识化处理后再进行研究分析及统计。如果您不同意我们进行前述处理，您可以通过客服或者"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"本规则所列联系方式"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"与我们联系。")]),f("li",{style:{"font-family":"serif","font-size":"9pt",margin:"6pt 0pt 6pt 9.14pt","padding-left":"8.86pt","text-align":"justify","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了向您提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"AI四维彩超长相预测服务，您需要提供以下"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"儿童"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝（胎儿）四维彩超照片"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"或者"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"宝宝照片"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，并同意我们与"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","text-decoration":"underline"}},"厦门美图之家科技有限公司（“美图”）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"合作进行图片处理，生成预测图片。"),f("a",{name:"_Hlk161327592"}),f("a",{name:"_Hlk161327580"},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们会保存您提供的照片及生成的预测图片一年，供您在【"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"历史记录"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"】页面查看，到期后，我们会对照片和图片做删除或匿名化处理。美图仅提供即时图片生成服务，其不会保存您提供照片及生成的预测图片。如果您不同意前述处理，则我们无法为您提供宝宝未来长相预测服务"),f("span",{style:{"-aw-bookmark-end":"_Hlk161327592"}}),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。")])])]),f("p",{style:{margin:"6pt 0pt 6pt 36pt","text-align":"justify","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（三）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"      "),f("span",{style:{"-aw-bookmark-end":"_Hlk161327580"}}),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"产品咨询")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"向您提供产品咨询服务，您需要授权我们收集您与我们客服的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"通讯信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"/图片/音视频/通话记录形式）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"中"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"与您的需求相关联的必要"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"儿童个人"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。如果您拒绝授权，则我们及我们的客服无法为您提供产品咨询服务。")]),f("p",{style:{margin:"6pt 0pt 6pt 36pt","text-align":"justify","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（四）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"      "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"喂养顾问")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"向您提供喂养顾问服务，您需要授权我们收集您与我们客服的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"通讯信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"/图片/音视频/通话记录形式）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"中与您的需求相关联的必要儿童个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。如果您拒绝授权，则我们及我们的客服无法为您提供喂养顾问服务。")]),f("p",{style:{margin:"6pt 0pt 6pt 36pt","text-align":"justify","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（五）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"      "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"1v1企业微信咨询小助手")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为通过企微向您提供活动咨询客服支持、喂养咨询客服支持服务，您需要授权我们收集您与我们企微小助手的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"通讯信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"/图片/音视频/通话记录形式）中与您的需求相关联的必要"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"儿童"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。如果您拒绝授权，则我们及我们的企微小助手无法为您提供企微活动咨询客服支持或者喂养咨询客服支持服务。")]),f("p",{style:{margin:"6pt 0pt 6pt 36pt","text-align":"justify","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（六）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"      "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"其他"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"信息")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了向您"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"服务及改进服务质量"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，您需要授权我们收集以下儿童个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"您与客服联系时提供的儿童个人信息、您参与我们问卷调查时提供的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"儿童"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"个人信息、您同意向我们的关联方和合作伙伴提供的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"儿童"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。如果您"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"拒绝授权"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们可能无法"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为您提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"相关"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"服务"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，或者可能影响您的用户体验"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。")]),f("p",{style:{margin:"6pt 0pt 6pt 36pt","text-align":"justify","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（七）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"      "),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"数据分析")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为了进一步改善服务及优化用户体验，您需要授权我们对"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"收集的儿童个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"进行数据分析和研究。如果您拒绝授权，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们可能无法进行服务优化。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}}," ")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您在使用我们的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"产品/服务"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"过程中，我们可能还会收集和使用其他儿童个人信息，有关具体的使用目的、收集个人信息的范围以及拒绝提供相应信息的后果请您查阅"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"Aptaclub喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"“我们如何收集"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"和"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"使用您的个人信息”章节进行详细了解。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"此外，我们也会通过Cookies、SDK等同类技术自动收集您或您孩子的个人信息，具体请您查阅"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"Aptaclub喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"“"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们如何使用"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}}," Cookie 和同类技术"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"”"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"以及“"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"第三方SDK"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"”"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"部分进行详细了解。如我们需要超出上述"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"目的和范围"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"使用儿童个人信息，我们将再次征得监护人的同意。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"请您"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"注意，绝大多数情形下我们无法识别且不会判断收集和处理的个人信息是否属于儿童个人信息，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"这种"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"情形下我们"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"将按照"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"Aptaclub喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"收集和处理用户的个人信息。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"根据相关法律法规的规定，在特定情形下，我们收集儿童个人信息时无需征得儿童监护人的授权同意，具体请您查阅"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"Aptaclub喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"“我们如何收集"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"和"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"使用您的个人信息”部分进行详细了解。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}}," ")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"二、我们如何委托其他方处理、向其他个人信息处理者提供、转移和公开披露儿童个人信息")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们承诺对儿童个人信息进行严格保密，仅在您明确授权同意和本规则说明"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"下述"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"情况下对外共享儿童个人信息：")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"1、向监护人提供。监护人可以访问和管理被监护儿童的个人信息。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"2、"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"Aptaclub喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"“"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们如何委托其他方处理、向其他个人信息处理者提供、转移"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"、"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"公开披露您的个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"”"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"章节"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"描述的相关"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"情形。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"3、基于"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"Aptaclub喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"和本规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"所述"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"目的，我们可能委托"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们的合作伙伴"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"处理儿童信息。我们会对受托"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"方"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"及委托行为等进行安全评估，在书面委托协议中明确双方责任、处理事项、处理期限、处理性质和目的等，禁止受托"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"方"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"超出授权范围使用"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"儿童个人"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"信息，并要求"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"受托方"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"依法履行以下义务：")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（1）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"按照法律、行政法规的规定和我们的要求处理儿童信息；")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（2）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"协助我们回应儿童监护人提出的申请；")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（3）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"采取措施保障信息安全，并在发生儿童信息泄露安全事件时，及时向我们反馈；")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（4）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"委托关系解除时及时删除儿童信息；")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（5）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"不得转委托；")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（6）"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"其他依法应当履行的儿童信息保护义务。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}}," ")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"三、我们如何"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"保护儿童个人信息")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们非常重视儿童的个人信息安全，并采取一切合理可行的措施，保护儿童个人信息：")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们会遵循正当必要、知情同意、目的明确、安全保障、依法利用的原则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"仅"),f("span",{style:{"background-color":"#ffffff","font-family":"微软雅黑","font-size":"9pt"}},"收集与我们提供的产品/服务相关的儿童个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们会严格控制儿童个人信息的访问权限，对可能接触到儿童个人信息的工作人员采取最小够用授权原则，并对工作人员"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"访问"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"儿童个人信息的行为进行记录，避免违法复制、下载儿童个人信息。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"当我们发现儿童个人信息发生或者可能发生泄露、毁损、丢失的，我们会按照法律法规的要求，启动应急预案、采取补救措施，及时向有关主管部门报告，并将事件相关情况以邮件、信函、电话、推送通知等方式告知受影响的儿童及其监护人，难以逐一告知的，我们会采取合理、有效的方式发布相关警示信息。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"如您希望了解更多，请查阅"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"Aptaclub喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"“我们如何"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"保护、存储"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"个人"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"信息”章节。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}}," ")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"四、我们如何存储儿童个人信息")]),f("p",{style:{margin:"6pt 0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"对于我们在中华人民共和国境内收集和产生的儿童个人信息，原则上我们将存储在中华人民共和国境内。我们存储儿童信息不会超过"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"与您约定的期限；如无特殊约定，我们存储儿童信息不会超过"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"实现信息收集、使用目的所必需的期限"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"到期后我们会对儿童个人信息进行删除或匿名化处理。请查阅"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"Aptaclub喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"“"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您的个人信息如何在全球范围转移"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"”章节，详细了解我们如何存储儿童个人信息。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}}," ")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"五、如何管理儿童个人信息")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"当您发现我们处理的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您的孩子"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"的个人信息不准确或不完整时，您有权要求我们作出更正。"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您可以"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"通过登录个人中心"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"自行更正"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，您也可联系我们要求更正"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"相应的儿童"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"个人"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"下列情形下，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您或您的孩子"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"可联系我们要求删除相应的儿童"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"个人"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"信息：")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（1）我们违反法律、行政法规的规定或者双方的约定收集、存储、使用、转移、披露儿童个人信息的；")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（2）超出目的范围或者必要期限收集、存储、使用、转移、披露儿童信息的；")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（3）儿童监护人撤回同意的；")]),f("p",{style:{margin:"0pt 0pt 6pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"（4）儿童或者其监护人通过注销等方式终止使用产品或者服务的。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"更多"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"管理个人信息的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"权利"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"行使途径和方式，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"请查阅"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"Aptaclub喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"“"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您的权利"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"”章节"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}}," ")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"六、本规则如何更新")]),f("p",{style:{margin:"6pt 0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"为给您和您的孩子提供更好的服务以及随着我们业务的发展，本规则也会随之适时变更"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。但"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"未经您明确同意，我们不会削减您和您的孩子依据本"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"所应享有的权利。我们会通过在我们网站、移动端上发出更新版本或以其他方式提醒您相关内容的更新，也请您访问我们以便及时了解最新的隐私政策。在前述情况下，若您继续使用我们的服务，即表示同意接受修订后的本"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"并受之约束。")]),f("p",{style:{margin:"6pt 0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}}," ")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt","font-weight":"bold"}},"七、如何联系我们")]),f("p",{style:{margin:"6pt 0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"如果您对本"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"有任何疑问、意见"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"、"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"建议"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"或投诉"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"，"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"您"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"可以通过在线客服或"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"发送邮件"),f("span",{style:{"font-family":"'Times New Roman'","font-size":"10.5pt"}},"<EMAIL>"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"我们联系"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"请监护人理解，为了保障"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"儿童个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"安全，在响应监护人反馈的问题前，我们可能需要监护人提供相关材料证明其监护人身份。我们将在收到反馈并验证监护人身份后的十五"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"个工作日"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"内回复监护人的"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"相关"),f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}},"请求。")]),f("p",{style:{margin:"6pt 0pt","text-align":"justify"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"9pt"}}," ")])])}]]),text2:l({props:[],data:()=>({}),computed:{},methods:{}},[["render",function(s,i,e,o,p,l){return t(),n("div",null,[f("p",{style:{margin:"0pt",orphans:"0","text-align":"center",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"达能"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"敏感个人信息处理规则")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"center",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}}," ")]),f("p",{style:{margin:"0pt",orphans:"0",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"更新日期：202"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"3"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"年 "),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"3"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"月 "),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"1"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"6"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"日")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}}," ")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"纽迪希亚生命早期营养品管理（上海）有限公司"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（以下统称为"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"“达能”"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"或"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"“我们”"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"非常重视用户的隐私和个人信息保护。您在使用我们的产品与"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"/或服务时，我们可能会"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"收集、存储、使用、加工、传输、提供（以下统称"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"“处理”"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"）您的敏感个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"我们希望通过"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"《敏感个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"处理"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"规则》"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（以下简称"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"“本规则”"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"向您说明我们"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"如何"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"处理您的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"敏感"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"个人信息。")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}}," ")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"本"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"规则为"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"Aptaclub"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"隐私政策"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"》"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"的补充，本规则未尽事宜，以"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"Aptaclub"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为准。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"在使用我们的产品或服务前，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"请您务必认真阅读并充分理解"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"本规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"，在确认充分理解并同意后再开始使用"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"。")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}}," ")]),f("p",{style:{margin:"0pt 0pt 0pt 21pt",orphans:"0","text-align":"justify","text-indent":"-21pt",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"一"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"        "),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"敏感个人信息的处理目的、处理方式及种类")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}}," ")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"在向您提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"我们的产品或服务"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"的过程中，我们可能"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"会"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"处理"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"您的敏感个人信息。我们深知，如对这些"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"敏感"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"个人信息处理不当，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"可能会"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"导致您的人格尊严受到侵害或者人身、财产安全受到危害。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"为了保障您的合法权益，我们仅"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"处理"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"normal"}},"向您提供下述服务所必要的敏感个人信息。我们将采取严格的保护措施，保障您敏感个人信息的安全。")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}}," ")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"请注意，如果您不同意我们"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"处理"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"您的相关敏感个人信息，我们可能无法向您提供部分或全部产品或服务。")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}}," ")]),f("p",{style:{margin:"0pt 0pt 8pt 36pt","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（一）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"    "),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"建档")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"当您选择在本平台建档时，您需要提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"以下"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"敏感"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"您的预产期"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"或"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝的生日"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"如果"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"您不"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"同意"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"这些信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"，您将无法"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"完成建档，也无法"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"使用部分会员服务。")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"当您选择完善您的宝宝信息设置时，您需要提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"以下"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"敏感"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝头像、宝宝昵称、宝宝生日"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"如果您不同意提供这些信息，您将无法完善宝宝信息设置。")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"当您选择邀请家人、将您与您家人的账号连接时，您需要提供以下敏感个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"您选择"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"的微信联系人"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果您不同意提供这些信息，我们将无法为您提供邀请家人服务。")]),f("p",{style:{margin:"0pt 0pt 8pt 36pt","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（二）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"    "),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"喂养小工具")]),f("ul",{type:"disc",style:{margin:"0pt","padding-left":"0pt"}},[f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 8pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供记喂奶服务，您需要提供以下敏感个人信息（包括文字/语音记录形式）："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝的生日、宝宝的喂奶信息（喂奶方式、喂奶时间、喂奶时长、喂奶量、喂奶种类）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果您不同意提供这些信息，我们将无法提供记喂奶服务。")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 8pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供记睡眠服务，您需要提供以下敏感个人信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"/语音记录形式）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝的生日、宝宝的睡眠信息（宝宝睡下时间、宝宝睡眠时长）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果您不同意提供这些信息，我们将无法提供记睡眠服务。")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 8pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供记辅食服务，您需要提供以下敏感个人信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"/语音记录形式）："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝的生日、宝宝性别、宝宝喂养方式、宝宝的辅食信息（辅食时间、辅食类型、辅食"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"食"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"材、辅食重量）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果您不同意提供这些信息，我们将无法提供记"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"辅食"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"服务。")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 8pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供宝宝便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"记录服务，您需要提供以下敏感个人信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"/"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"图片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"）："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝的生日、宝宝便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"的照片、宝宝的便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"信息（便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"时间、尿布状态、便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"颜色、便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"形状）、宝宝喂养方式"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果您不同意提供这些信息，我们将无法提供便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"记录服务。为了优化宝宝便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"解析的计算方式，增强用户体验，我们后续需要对收集的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"的照片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"进行去标识化处理后再进行研究分析及统计。如果您不同意我们进行前述处理，您可以通过客"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"服或者"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"本规则所列联系方式"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"与我们联系。")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 8pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供个性化营养评估服务，您需要提供以下敏感个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"您的个人状态（孕期"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"/哺乳期）、个人当前身高体重、宝宝的生日或您的预产期、孕前体重、食物过敏及"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"不"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"耐受情况、饮食偏好及运动水平"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果您不同意"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供这些"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"信息，我们"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"将"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"无法提供个性化营养评估试用服务。")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 8pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供宝宝体型分析服务（成长曲线），您需要提供以下敏感个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝的生日、宝宝性别、宝宝喂养方式、宝宝身高、宝宝体重"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果您不同意提供这些信息，我们将无法提供宝宝体型分析服务。")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 8pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供宝宝拍照身高识别服务（成长曲线"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"，您需要提供以下敏感个人信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"/图片）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝的生日、宝宝照片（宝宝面容及隐私部位进行遮盖处理）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果您不同意提供这些信息，我们将无法提供宝宝拍照身高识别服务。为了优化宝宝拍照身高识别的计算方式，增强用户体验，我们后续需要对收集的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝照片（宝宝面容及隐私部位已进行遮盖处理）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"进行去标识化处理后再进行研究分析及统计。如果您不同意我们进行前述处理，您可以通过客"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"服或者"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"本规则所列联系方式"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"与我们联系。")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 8pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"AI"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"四维彩超"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"长相"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"预测"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"服务，您需要"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"以下敏感个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝（"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"胎儿"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"四维彩超"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"照片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"或者"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝照片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"，并同意"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"我们与"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","text-decoration":"underline"}},"厦门美图之家科技有限公司（“美图”）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"合作进行图片处理，生成预测图片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"我们会"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"保存您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供的照片及生成的预测图片一年，供您在【我的】页面查看，到期后，我们会对照片和图片做删除或匿名化处理。美图仅提供即时图片生成服务，其不会"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"保存您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供照片及生成的预测图片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"如果您不同意"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"前述处理，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"则我们无法为您提供宝宝未来长相"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"预测"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"服务。")])]),f("p",{style:{margin:"0pt 0pt 8pt 36pt","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（三）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"    "),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"产品咨询")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供产品咨询服务，您需要授权我们收集您与我们客服的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"通讯信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"/图片/音视频/通话记录形式）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"中"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"与您的需求相关联的必要"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"敏感"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"个人"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"您拒绝"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"授权，则我们及我们的客服无法为您提供产品咨询服务。")]),f("p",{style:{margin:"0pt 0pt 8pt 36pt","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（四）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"    "),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"喂养顾问")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供喂养顾问服务，您需要授权我们收集您与我们客服的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"通讯信息（包括文字"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"/图片/音视频/通话记录形式）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"中与您的需求相关联的必要"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"敏感"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"您拒绝"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"授权，则我们及我们的客服无法为您提供喂养顾问服务。")]),f("p",{style:{margin:"0pt 0pt 8pt 36pt","text-indent":"-36pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（五）"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"    "),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"1"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"v1"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"企业微信咨询小助手")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"通过企微向"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"您提供活动咨询客服支持、喂养咨询客"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"服支持"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"服务，您需要授权我们收集您与我们"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"企微小助手的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"通讯信息（包括文字/图片/音视频/通话记录形式）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"中"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"与您的需求相关联的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"必要敏感个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"您拒绝"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"授权，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"则"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"我们及我们的企微小助手无法为您提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"企微"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"活动"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"咨询客"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"服支持"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"或者喂养咨询客"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"服支持"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"服务。")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（七）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"营销推广活动")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供营销推广服务，您需要授权我们记录以下敏感个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"优惠福利记录"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。如果"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"您拒绝"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"授权，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"您将"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"无法"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"参加本平台组织的优惠福利活动"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（八）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"个性化推荐")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了向您提供个性化推荐服务，您需要授权我们收集以下敏感个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"优惠福利记录、"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"设置偏好、"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"IP地址、"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"转引U"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"R"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"L、"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"个人行为标签信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"、"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"网页浏览记录、"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"浏览行为信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"如果"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"拒绝"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"授权，则"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"我们将无法提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"个性化推荐"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"服务。")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（九）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"其他"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"信息")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"向您提供服务及改进服务"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"质量"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"，您需要授权我们收集以下敏感个人信息："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"您与客"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"服联系"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"时提供的敏感个人信息、您参与我们问卷调查时提供的敏感个人信息、您同意向我们的关联方和合作伙伴提供的敏感个人信息、您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"从喂自由微信公众号"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"链接到本平台时"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"转引URL、"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"浏览行为信息、"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"网页浏览记录"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"如果"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"拒绝"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"授权，则"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"我们"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"可能"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"无法"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为您提供相关服务，或者可能影响您的用户体验"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（十）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"数据分析")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了进一步改善服务及优化用户体验，您需要授权我们对"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"收集的敏感个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"进行数据分析和研究。如果"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"您拒绝"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"授权，则我们可能无法进行服务优化。")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（十一）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"第三方SDK")]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为了"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"实现"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"本平台"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"承诺的功能及服务，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"以及应用的安全稳定运行，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"您需要授权我们接入"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"第三方"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"S"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"DK"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"对"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"以下敏感个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"进行处理"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"：")]),f("ul",{type:"disc",style:{margin:"0pt","padding-left":"0pt"}},[f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 3pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"讯飞语音识别"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"S"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"DK"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（由科大讯飞股份有限公司"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"识别"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"的语音记录文件"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"中"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"可能"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"含有的敏感个人信息，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"用于实现"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"语音搜索"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"、语音记录功能；")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 3pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"阿里云敏感图片检测"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"SDK"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"由"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"阿里"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"云计算"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"有限公司"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"）识别、"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"检测"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"上传"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"照片（"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"照片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"、宝宝便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"便"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"照片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"是否含有敏感或不良信息；")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 3pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"阿拉丁统计S"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"DK"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"由"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"北京阿拉丁未来科技有限责任公司"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"统计、"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"分析"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"个人"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"上网记录"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"用于实现数据分析、优化服务；")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 3pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"竹间自然语音语义识别S"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"DK"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"由"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"竹间智能科技（上海）有限公司"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"识别"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"与"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"客服"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"的通讯信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"中可能含有的敏感个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"语义"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"用于实现"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"智能"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"问"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"答；")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 3pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"GrowingIO"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"用户行为数据采集SDK（由北京易数科技有限公司提供）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"和火山用户行为数据采集"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"SDK"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（由北京火山引擎有限公司提供）采集"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"个人上网记录（包括本平台浏览记录、本平"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"台使用记录、点击记录、转引"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"URL）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"用于"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"实现个性化服务、"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"优化用户的使用体验"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"；")]),f("li",{style:{"font-family":"serif","font-size":"10pt",margin:"0pt 0pt 8pt 9.6pt","padding-left":"8.4pt","text-indent":"0pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"美图"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"AI图片生成"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"S"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"DK"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"（由"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"厦门美图网科技有限公司"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供）"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"采集您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"四维彩超"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"照片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"或者"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"宝宝照片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"，用于"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"生成宝宝"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"未来"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"长相"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"预测"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"图片。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"美图"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"仅提供即时图片生成服务，其"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"不会"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"保存您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"照片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"及生成的预测图片"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"我们会"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"保存您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供的照片及生成的预测图片一年，供您在【历史记录】页面查看，到期后，我们会对照片和图片做删除或匿名化处理。美图仅提供即时图片生成服务，其不会"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"保存您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"提供照片及生成的预测图片。如果您不同意前述处理，则我们无法为您提供宝宝未来长相预测服务。")])]),f("p",{style:{margin:"0pt 0pt 8pt"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"如果您不同意"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"我们接入这些第三方SDK用于上述处理"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"，您将无法使用本平台服务。")]),f("p",{style:{"font-size":"10.5pt","line-height":"150%",margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"'Times New Roman'","font-size":"10.5pt"}}," ")]),f("p",{style:{margin:"0pt 0pt 0pt 21pt",orphans:"0","text-align":"justify","text-indent":"-21pt",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"二"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"        "),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"存储"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"期限"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"及"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"同意处理"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"敏感个人信息的期限")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}}," ")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"我们会在"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"与您约定的期限内"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"存储您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"的敏感个人信息；如无特别约定，我们仅会在"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"为实现以上各项业务功能所必需的最短时间内"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"存储您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"的个人信息"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"超出该期限后，我们会对您的敏感个人信息进行删除或者匿名化处理，但法律法规另有规定或监管部门另有要求的除外。")]),f("p",{style:{margin:"0pt 0pt 0pt 21pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}}," ")]),f("p",{style:{margin:"0pt 0pt 0pt 21pt",orphans:"0","text-align":"justify","text-indent":"-21pt",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"三"),f("span",{style:{font:"7.0pt 'Times New Roman'"}},"        "),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}},"其他事项")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}}," ")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"关于我们如何"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"处理和"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"保护您"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"敏感"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"个人信息、您的相关权利"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"等详细"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"内容，"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"请访问"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"我们的"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"《"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"Aptaclub"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"喂自由"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"隐私政策》"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"。")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}}," ")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"如果您对本"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"规则"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"有任何疑问、意见或建议，您可以通过以下方式与我们联系：")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"电子邮箱："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"<EMAIL>")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"地址："),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"上海市"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"浦东新区芳"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"甸"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"路"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"1155"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"号"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"3"),f("span",{style:{"font-family":"微软雅黑","font-size":"10pt"}},"楼")]),f("p",{style:{margin:"0pt",orphans:"0","text-align":"justify",widows:"0"}},[f("span",{style:{"font-family":"微软雅黑","font-size":"10pt","font-weight":"bold"}}," ")])])}]])},data:()=>({type:0}),onLoad(t){this.type=t.type??0},onShow(){},methods:{}},[["render",function(n,f,l,a,y,m){const z=p("text1"),g=e;return t(),s(g,{class:"text-bk"},{default:i((()=>[1==y.type?(t(),s(z,{key:0})):2==y.type?(t(),s(z,{key:1})):o("",!0)])),_:1})}],["__scopeId","data-v-3aa30feb"]]);export{y as default};
