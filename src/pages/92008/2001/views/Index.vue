<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <img :src="furnish.pageBg" alt="" class="page-bg" />
    <div class="content">
      <img :src="furnish.ruleBtn" alt="" class="rule-btn" @click="showRulePopup" />
      <img :src="furnish.myPrizeBtn" alt="" class="my-prize-btn" @click="showMyPrize = true" />
      <div class="wheel-bg">
        <lucky-grid ref="myLucky" width="85.33vw" height="85.33vw" :blocks="furnishStyles.params.value.blocks" :prizes="furnishStyles.params.value.prizes" :buttons="furnishStyles.params.value.buttons" @start="startCallback" @end="endCallback" :defaultConfig="furnishStyles.params.value.defaultConfig" />
      </div>
    </div>
  </div>
  <Threshold v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
    <OrderRecordPopup v-if="showOrderRecord" @close="showOrderRecord = false" :orderRestrainStatus="orderRestrainStatus"></OrderRecordPopup>
  </VanPopup>
  <!--抽奖记录弹窗-->
  <VanPopup teleport="body" v-model:show="showDrawRecord" position="bottom">
    <DrawRecordPopup v-if="showDrawRecord" @close="showDrawRecord = false"></DrawRecordPopup>
  </VanPopup>
  <!-- 活动商品弹窗-->
  <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
    <GoodsPopup :data="[]" @close="showGoods = false" :orderSkuisExposure="orderSkuisExposure"></GoodsPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import GoodsPopup from '../components/GoodsPopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import Threshold from '../components/Threshold.vue';
import vueDanmaku from 'vue3-danmaku';
import { gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import DrawRecordPopup from '../components/DrawRecordPopup.vue';
import { Handler } from '@/utils/handle';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import LzLuckyGrid from '@/components/LzLuckyDraw/LzLuckyGrid.vue';
import LzLuckyGacha from '@/components/LzLuckyDraw/LzLuckyGacha.vue';
import LzLuckyCurettage from '@/components/LzLuckyDraw/LzLuckyCurettage.vue';

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
// 0-全部商品 1-指定商品  2-排除
const orderSkuisExposure = ref(0);
const shopName = ref(baseInfo.shopName);

const showLimit = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const checkLimitDialog = async (draw = false) => {
  if (baseInfo.status === 1) {
    showToast('活动未开始！');
    return;
  }
  if (baseInfo.status === 3) {
    showToast('活动已结束！');
    return;
  }
  if (baseInfo.thresholdResponseList.findIndex((item) => item.thresholdCode === 4 || item.thresholdCode === 5 || item.thresholdCode === 8) !== -1) {
    showLimit.value = true;
    return;
  }
  if (baseInfo.thresholdResponseList.findIndex((item) => item.thresholdCode === 920082) !== -1 && draw) {
    showToast('您的抽奖次数已用完');
  }
};

// 抽奖次数
const chanceNum = ref(0);
// 参与人数
const joinNum = ref(0);
// 订单状态
const orderRestrainStatus = ref(0);

const showMyPrize = ref(false);

const showGoods = ref(false);
const showOrderRecord = ref(false);
type Sku = {
  skuName: string;
  skuMainPicture: string;
  skuId: number;
  jdPrice: string;
};

const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const pagesAll = ref(0);

const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});
const showDrawRecord = ref(false);
// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      showRulePopup();
    },
  },
  {
    name: '我的奖品',
    event: () => {
      showMyPrize.value = true;
    },
  },
  {
    name: '活动商品',
    event: () => {
      showGoods.value = true;
    },
  },
  {
    name: '我的订单',
    event: () => {
      showOrderRecord.value = true;
    },
  },
  {
    name: '抽奖记录',
    event: () => {
      showDrawRecord.value = true;
    },
  },
];

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  showGoods.value = false;
  showOrderRecord.value = false;
  savePhonePopup.value = true;
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

// 不在活动范围呢刷新页面
const unStart = (partakeStartTime: string) => {
  const now = dayjs().format('YYYY-MM-DD');
  let time = 0;
  if (partakeStartTime > dayjs().format('HH:mm:ss')) {
    time = dayjs(`${now} ${partakeStartTime}`).valueOf() - dayjs().valueOf();
  } else {
    time = dayjs(`${now} ${partakeStartTime}`).add(1, 'day').valueOf() - dayjs().valueOf();
  }
  setTimeout(() => {
    window.location.reload();
  }, time);
};
const handler = Handler.getInstance();

// 获取客抽奖次数
const getChanceNum = async () => {
  try {
    const { data } = await httpRequest.post('/92008/chanceNum');
    chanceNum.value = data.chanceNum;
    joinNum.value = data.joinNum;
    orderRestrainStatus.value = data.orderRestrainStatus;
    orderSkuisExposure.value = data.orderSkuisExposure;

    const thresholdLength = await handler.trigger('onThresholdIf');
    const thresholdData = await handler.trigger('onThresholdData');
    if (thresholdLength) {
      const threshold = thresholdData.find((item: any) => item.thresholdCode === 201);
      if (threshold) {
        unStart(data.partakeStartTime);
      }
    }
  } catch (error) {
    console.error(error);
  }
};

// 获取中奖名单
const getWinners = async () => {
  try {
    const res = await httpRequest.post('/92008/winners');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container', {
        autoplay: activityGiftRecords.length > 5 ? { delay: 1000, stopOnLastSlide: false, disableOnInteraction: false } : false,
        direction: 'vertical',
        loop: activityGiftRecords.length > 5,
        slidesPerView: 5,
        loopedSlides: 7,
        spaceBetween: 10,
      });
    });
  } catch (error) {
    console.error(error);
  }
};

const myLucky = ref();
// 抽奖接口
const lotteryDraw = async () => {
  try {
    const res = await httpRequest.post('/92008/lotteryDraw');
    if (res.data.prizeType) {
      award.value = {
        prizeType: res.data.prizeType,
        prizeName: res.data.prizeName,
        showImg: res.data.prizeImg,
        result: res.data.result ?? '',
        activityPrizeId: res.data.activityPrizeId ?? '',
        userPrizeId: res.data.userPrizeId,
      };
      const index = prizeInfo.findIndex((item) => item.index === res.data.sortId);
      myLucky.value.stop(index);
    } else {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };

      const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
      myLucky.value.stop(index);
    }
  } catch (error) {
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
    myLucky.value.stop(index);
    console.error(error);
  }
  getChanceNum();
  getWinners();
};
const startCallback = async () => {
  if (baseInfo.thresholdResponseList.length) {
    checkLimitDialog(true);
    return;
  }
  lzReportClick('kscj');
  if (chanceNum.value <= 0) {
    showToast('您的抽奖次数已用完');
    return;
  }
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  lotteryDraw();
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};

// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/92008/getPrizes');
    prizeInfo.splice(0);
    prizeInfo.push(...data);
  } catch (error) {
    console.error(error);
  }
};
// 获取曝光商品
const getSkuList = async () => {
  try {
    const res = await httpRequest.post('/92008/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    if (res.code === 200) {
      skuList.value.push(...res.data.records);
      pagesAll.value = res.data.pages;
    }
  } catch (error) {
    console.error(error);
  }
};
const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getChanceNum(), getPrizes(), getWinners(), getSkuList()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
  checkLimitDialog();
  if (baseInfo.status === 1) {
    const time = baseInfo.startTime - dayjs().valueOf();
    setTimeout(() => {
      window.location.reload();
    }, time);
  }
};
init();
</script>
<style lang="scss">
@font-face {
  font-family: 'SourceHanSansCN';
  src: url('https://lzcdn.dianpusoft.cn/fonts/SourceHanSansCN/SourceHanSansCN-Regular.otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'GucciSans';
  src: url('https://lzcdn.dianpusoft.cn/fonts/GucciSans/GucciSans-Bold.otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'GucciSans', 'SourceHanSansCN';
}
</style>
<style scoped lang="scss">
.bg {
  position: relative;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}
.page-bg {
  width: 100%;
}
.content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding-top: 3rem;
  .rule-btn {
    position: absolute;
    top: 1.04rem;
    right: 0;
    width: 1.05rem;
  }
  .my-prize-btn {
    position: absolute;
    top: 1.84rem;
    right: 0;
    width: 1.05rem;
  }
  .wheel-bg {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
