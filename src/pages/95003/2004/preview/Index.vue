<template>
  <div class="bg" :style="furnishStyles.actBgColor.value"  v-if="isLoadingFinish">
    <div class="header-kv select-hover" >
      <img :src="furnish.pageBg" alt=""/>
    </div>
      <div class="header-btn-all" :class="{ 'create-img': isCreateImg }">
        <div class="header-btn" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event">
          <div>{{ btn.name }}</div>
        </div>
      </div>
    <div class="select-hover wheelClass" >
      <div class="wheel">
        <lz-lucky-wheel ref="myLucky" width="90vw" height="90vw" :blocks="furnishStyles.params.value.blocks" :prizes="furnishStyles.params.value.prizes" :buttons="furnishStyles.params.value.buttons" @start="startCallback" @end="endCallback" :defaultConfig="furnishStyles.params.value.defaultConfig" />
      </div>
      <div class="chance">
        *有效消费金额 0 元，共获得 0 次抽奖机会
      </div>
      <div class="figure">
        <div class="draws-num" >您今天还有<span>0</span>次抽奖机会</div>
      </div>
    </div>

    <div class="prizeBox" v-if="prizeList.length>0">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/208127/5/42656/9188/668ce2a9Fc24685e2/35510c2e4d00be7a.png" alt=""/>
      <div class="box">
        <div class="swiper-container-prize" ref="swiperRef" >
          <div class="swiper-wrapper " >
            <div class="swiper-slide prizeItem" v-for="item in prizeList" :key="item">
              <div class="prizeItem" v-if="item.prizeName">
                <img :src="item.prizeImg || '//img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png' " alt=""/>
                <div>{{item.prizeName || 'xx积分'}}</div>
              </div>
            </div>
          </div>
          <div v-if="prizeList.length>2" class="swiper-button-prev" @click="prevSwiper"></div>
          <div v-if="prizeList.length>2" class="swiper-button-next" @click="nextSwiper"></div>
        </div>
      </div>

    </div>
    <div class="reciveBox">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/2059/20/23071/9887/668ce2a9F33b931d8/928a95bd1e6da0cc.png" alt=""/>
      <div class="content">
        暂无记录
      </div>

    </div>

  </div>
  <div v-if="!isCreateImg">
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!--    我的订单弹窗-->
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup @close="showOrderRecord = false" :orderRestrainStatus="orderRestrainStatus"></OrderRecordPopup>
    </VanPopup>
    <!--抽奖记录弹窗-->
    <VanPopup teleport="body" v-model:show="showDrawRecord" position="bottom">
      <DrawRecordPopup @close="showDrawRecord = false" />
    </VanPopup>
    <!--    活动商品弹窗-->
    <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
      <GoodsPopup :data="orderSkuList" @close="showGoods = false"></GoodsPopup>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :userReceiveRecordId="addressId" :activityPrizeId="''" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import html2canvas from 'html2canvas';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import GoodsPopup from '../components/GoodsPopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import vueDanmaku from 'vue3-danmaku';
import DrawRecordPopup from '../components/DrawRecordPopup.vue';
import Swiper from 'swiper';
import 'swiper/swiper.min.css';
import { Handler } from '@/utils/handle';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import LzLuckyGrid from '@/components/LzLuckyDraw/LzLuckyGrid.vue';
import LzLuckyGacha from '@/components/LzLuckyDraw/LzLuckyGacha.vue';
import LzLuckyCurettage from '@/components/LzLuckyDraw/LzLuckyCurettage.vue';

const prizeList = ref<any>([]);
const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
console.log(activityData);
let prizeSwiper: Swiper;
const shopName = ref('xxx自营旗舰店');

const isLoadingFinish = ref(false);
const showDrawRecord = ref(false);
const showRule = ref(false);
const ruleTest = ref('');

const showMyPrize = ref(false);
const showGoods = ref(false);
const showOrderRecord = ref(false);

// 订单状态
const orderRestrainStatus = ref(0);

const times = ref(0);

type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const isExposure = ref(1);
const skuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);

// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则>',
    event: () => {
      showRule.value = true;
    },
  },
  {
    name: '我的奖品>',
    event: () => {
      showMyPrize.value = true;
    },
  },
];

const activityGiftRecords = reactive([
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
]);

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const myLucky = ref();
const startCallback = async () => {
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  // 模拟调用接口异步抽奖
  setTimeout(() => {
    // 假设后端返回的中奖索引是0
    const index = Math.floor(Math.random() * 8);
    const _award = prizeInfo[index];
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    // 调用stop停止旋转并传递中奖索引
    myLucky.value.stop(index);
  }, 2000);
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};
const prevSwiper = () => {
  prizeSwiper.slidePrev();
};
const nextSwiper = () => {
  prizeSwiper.slideNext();
};
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showGoods.value = false;
  showAward.value = false;
  showSaveAddress.value = false;

  showSelect.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    showSelect.value = true;
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    if (data.prizeList.length) {
      prizeInfo.splice(0);
      prizeInfo.push(...data.prizeList);
      prizeList.value.splice(0);
      prizeList.value.push(...data.prizeList.filter((item: any) => item.prizeType !== 0));
    }
    console.log('c', data);

    isExposure.value = data.isExposure;
    skuList.value = data.skuList;
    orderSkuList.value = data.orderSkuList;
    ruleTest.value = data.rules;
    orderRestrainStatus.value = data.orderRestrainStatus;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'shop') {
    shopName.value = data;
  }
  nextTick(() => {
    prizeSwiper = new Swiper('.swiper-container-prize', {
      direction: 'horizontal',
      loop: prizeList.value.length > 2,
      slidesPerView: 2 || 'auto',
      spaceBetween: prizeList.value.length > 2 ? 30 : 0,
      centeredSlides: prizeList.value.length !== 2,
      navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' },
    });
  });
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    prizeInfo.splice(0);
    prizeInfo.push(...activityData.prizeList);
    prizeList.value.splice(0);
    prizeList.value.push(...activityData.prizeList.filter((item: any) => item.prizeType !== 0));
    isExposure.value = activityData.isExposure;
    skuList.value.splice(0);
    skuList.value.push(...activityData.skuList);
    ruleTest.value = activityData.rules;
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
  nextTick(() => {
    prizeSwiper = new Swiper('.swiper-container-prize', {
      direction: 'horizontal',
      loop: prizeList.value.length > 2,
      slidesPerView: 2 || 'auto',
      spaceBetween: prizeList.value.length > 2 ? 30 : 0,
      centeredSlides: [1, 3].includes(prizeList.value.length),
      navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' },
    });
  });
  if (activityGiftRecords.length > 4) {
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container', {
        autoplay: {
          delay: 1000,
          stopOnLastSlide: false,
          disableOnInteraction: false,
        },
        direction: 'vertical',
        loop: true,
        slidesPerView: 5,
        loopedSlides: 8,
        centeredSlides: true,
      });
    });
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  .kv-img {
    width: 100%;
  }

}
.header-btn-all{
  display: flex;
  justify-content: space-around;
  .header-btn {
    width: 2rem;
    height: 0.56rem;
    background: #fff;
    border-radius: 0.35rem;
    border: 1px solid #f6fbf7;
    font-size: 0.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.wheelClass{
  position: relative;
  .wheel {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    .wheel-img {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      object-fit: contain;
    }
  }
}
.chance {
  margin-top: .5rem;
  color: #69b17d;
  font-size: 0.24rem;
  text-align: center;
}
.prizeBox {
  font-size: 0.3rem;
  color: #fff;
  overflow: hidden;
  position: relative;
  .box {
    width: 80%;
    margin:0 auto;
    overflow: hidden;
  }
  img {
    width: 4rem;
    margin: 0.2rem auto;
  }
  .prizeItem {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    img {
      width: 1.5rem;
      background: #fff;
    }
  }}
.reciveBox {
  margin:1rem auto 0;
  font-size: 0.3rem;
  color: #000;
  img {
    width: 4rem;
    margin: 0.2rem auto;
  }
  .content {
    width: 6rem;
    height: 4rem;
    background: #fff;
    border-radius: 0.3rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
  }
}
.draws-num {
  width: 5.94rem;
  height: 0.66rem;
  display: flex;
  background-size: 100% 100%;
  border-radius: 0.35rem;
  background-repeat: no-repeat;
  margin: 0.36rem 0.8rem 0;
  align-items: center;
  justify-content: center;
  padding: 0.06rem 0;
  font-size: 0.4rem;
  font-weight: bold;
  color: #fff;
  span {
    padding: 0.04rem 0.08rem;
    color: #e4c783;;
  }
}
.swiper-button-prev {
  position: absolute;
  left: 0;
  top: 70%;
  transform: translateY(-50%);
  width: 0.28rem;
  height: 0.52rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/180230/35/37580/745/66755088F83e4bb06/fe2cefbfd6ece6c5.png') no-repeat;
  background-size: 100%;
  z-index: 1;
}
.swiper-button-next {
  z-index: 1;
  position: absolute;
  right: 0;
  top: 70%;
  transform: translateY(-50%);
  width: 0.28rem;
  height: 0.52rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/233542/28/20707/755/66755089Fd2d6dab8/628521ff436b49fa.png') no-repeat;
  background-size: 100%;
}
.bottom-div {
  padding-top: 0.8rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #fff;
  text-align: center;
}
</style>
