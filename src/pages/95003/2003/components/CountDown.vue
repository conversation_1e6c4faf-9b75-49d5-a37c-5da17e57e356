<template>
  <div class="count-down-time" >
    <span>{{ getCountdownTitle() }}</span>
    <van-count-down :time="getTimeFun()" format="DD:HH:mm:ss"  @finish="countDownFinish"  v-if="getTimeFun()>0">
      <template #default="timeData">
        <div class="contentSpan">
          <div class="acblockStyleStyle" >{{ timeData.days }}</div>
          <span>天</span>
          <div class="acblockStyleStyle" >{{ timeData.hours }}</div>
          <span>时</span>
          <div class="acblockStyleStyle" >{{ timeData.minutes }}</div>
          <span>分</span>
          <div class="acblockStyleStyle" >{{ timeData.seconds }}</div>
          <span>秒</span>
        </div>
      </template>
    </van-count-down>
  </div>
</template>

<script setup lang="ts">
import { BaseInfo } from '@/types/BaseInfo';
import { inject } from 'vue';

const props = defineProps({
  startTime: {
    type: Number,
    default: 0,
    required: true,
  },
  endTime: {
    type: Number,
    default: 0,
    required: true,
  },
});

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

// 倒计时文案
const getCountdownTitle = () => {
  if (props.startTime > new Date().getTime()) {
    return '距离抽奖开始';
  }
  if (props.endTime > new Date().getTime()) {
    return '距离抽奖结束';
  }
  return '抽奖已结束';
};

// 倒计时时间
const getTimeFun = () => {
  if (props.startTime > new Date().getTime()) {
    return props.startTime - new Date().getTime();
  }
  if (props.endTime > new Date().getTime()) {
    return props.endTime - new Date().getTime();
  }
  return 0;
};
const countDownFinish = () => {
  console.log('倒计时结束');
  window.location.reload();
};
</script>

<style scoped lang="scss">
.count-down-time {
  position: relative;
  width: 6rem;
  margin: 0 auto;
  font-size: 0.25rem;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  .contentSpan {
    display: flex;
    position: absolute;
    top: -0.04rem;
    left: 2.8rem;

    .acblockStyleStyle {
      color: #fff;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
    }
    span {
      width: 0.4rem;
      height: 0.44rem;
      color: #fff;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
    }
  }
}
.count-down-finish {
  font-size: 0.25rem;
  color: #fff;
  text-align: center;
}
</style>
