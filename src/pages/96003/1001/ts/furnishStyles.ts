import { computed, reactive } from 'vue';

export const furnish = reactive({
// 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 按钮
  btnColor: '',
  btnBg: '',
  btnBorderColor: '',
  // 活动规则
  ruleBg: '',
  // 我的奖品
  myPrizeBg: '',
  // 我的订单
  myOrderBg: '',
  // 倒计时背景
  cutDownBg: '',
  cutDownColor: '',
  cutDownNumBg: '',
  cutDownNumColor: '',
  // 奖励背景图
  prizeBg: '',
  // 奖品名称颜色
  prizeNameColor: '',
  // 领奖按钮
  getPrizeBtn: '',
  // 参与活动商品背景
  showSkuBg: '',
  // 获奖名单背景图
  winnersBg: '',
  // 进店逛逛按钮
  btnToShop: '',
  // 是否能关闭入会弹窗
  canNotCloseJoinPopup: 1,
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  disableShopName: 0,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const prizeNameColor = computed(() => ({
  color: furnish.prizeNameColor ?? '',
}));

const headerBtn = computed(() => ({
  backgroundImage: furnish.ruleBg ? `url("${furnish.ruleBg}")` : '',
  color: furnish.btnColor ?? '',
}));

const cutDownColor = computed(() => ({
  color: furnish.cutDownColor ?? '',
}));

const cutDownNumColor = computed(() => ({
  color: furnish.cutDownNumColor ?? '',
  backgroundColor: furnish.cutDownNumBg ?? '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const winnersBg = computed(() => ({
  backgroundImage: furnish.winnersBg ? `url(${furnish.winnersBg})` : '',
}));

const btnToShop = computed(() => ({
  backgroundImage: furnish.btnToShop ? `url("${furnish.btnToShop}")` : '',
}));

export default {
  pageBg,
  shopNameColor,
  headerBtn,
  cutDownColor,
  cutDownNumColor,
  prizeNameColor,
  winnersBg,
  btnToShop,
};
