<template>
  <div class="pop-success-all">
    <div class="pop-success-bk1" v-if="drawResultData && drawResultData.prizeType > 0">
      <!-- <div class="prizeImg">
      <img :src="drawResultData.prizeImg" alt="" />
    </div> -->
      <div class="btnDivAll">
        <div class="addressDiv" @click="addressClick()">填写地址</div>
        <div class="saveImageDiv" @click="saveImage()">保存图片</div>
      </div>
      <div class="closeDiv" @click="closeClick()"></div>
    </div>
    <div class="pop-success-bk2" v-else>
      <div class="knowDiv" @click="closeClick()">我知道了</div>
      <div class="closeDiv" @click="closeClick()"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick } from "process";
import useHtmlToCanvas from "@/hooks/useHtmlToCanvas";
import html2canvas from "html2canvas";
import { closeToast, showLoadingToast, showToast } from "vant";
import { compressImage } from "@/utils/ImageUtils";
import { httpRequest } from "@/utils/service";
const emits = defineEmits(["close", "saveAddress"]);

const props = defineProps({
  drawResultData: {
    type: Object,
    default: null,
  },
});
console.log(props.drawResultData, "==============");
const closeClick = () => {
  emits("close");
};
// 填写地址
const addressClick = async () => {
  emits(
    "saveAddress",
    props.drawResultData.result.result,
    props.drawResultData.userPrizeId
  );
};

const uploadPosterImage = async (blob: Blob) => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    // 创建 FormData 实例
    const file = await compressImage(blob, { quality: 0.5 });
    const formData = new FormData();
    formData.append("pictureCateId", "0");
    formData.append("file", file, "poster.png"); // 'poster.png' 是文件名
    const config = {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };
    // 使用 httpRequest 上传文件
    const res = await httpRequest.post("/common/uploadImg", formData, config);
    console.log("res=========", res, res.data);
    closeToast();
    const params = {
      url: res.data,
    };
    window.jmfe.saveImage(params).then(({ status }) => {
      if (status === "0") {
        //保存成功
        console.log("保存成功");
        showToast("保存图片成功");
        closeToast();
      } else {
        //保存失败
        console.log("保存失败");
        showToast("保存图片失败");
        closeToast();
      }
    });
  } catch (error) {
    closeToast();
    console.log(error, "error========");
  }
};
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(",");
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};
// 保存图片
const saveImage = () => {
  console.log("保存图片");
  const params = {
    url:
      "https://img10.360buyimg.com/imgzone/jfs/t1/288445/29/12247/213936/683ee0e5F52ab10a7/d12b53f18154e936.png",
  };
  window.jmfe.saveImage(params).then(({ status }) => {
    if (status === "0") {
      //保存成功
      console.log("保存成功");
      showToast("保存图片成功");
      // closeToast();
    } else {
      //保存失败
      console.log("保存失败");
      showToast("保存图片失败");
      // closeToast();
    }
  });
};
</script>

<style scoped lang="scss">
.pop-success-all {
  //  background-color: rgba($color: #000000, $alpha: 0.8);
  //   position: fixed;
  //   top: 0;
  //   width: 100%;
  //   height: 100%;
}
.pop-success-bk1 {
  // padding-top: 25%;
  // margin-left: 50%;
  // margin-top: 50%;
  // transform: translateX(-50%) translateY(-25%);
  // background-image: url('../assets/failBk1.png');
  position: relative;
  background: {
    image: url("../assets/drawPopBg.png");
    repeat: no-repeat;
    size: 100% 100%;
  }
  width: 6.8rem;
  height: 8rem;
  .btnDivAll {
    display: flex;
    position: absolute;
    bottom: 1.6rem;
    left: 50%;
    transform: translateX(-50%);
    .addressDiv {
      width: 2rem;
      height: 0.68rem;
      // background-color: greenyellow;
      font-size: 0;
      // left: 50%;
      // transform: translateX(-50%);
    }
    .saveImageDiv {
      width: 2rem;
      height: 0.68rem;
      // background-color: red;
      font-size: 0;
      margin-left: 0.3rem;
      // left: 50%;
      // transform: translateX(-50%);
    }
  }
}

.pop-success-bk2 {
  position: relative;
  // background-image: url('../assets/failBk1.png');
  background: {
    image: url("../assets/drawFail.png");
    repeat: no-repeat;
    size: 100% 100%;
  }
  width: 6.8rem;
  height: 8rem;
  .knowDiv {
    width: 2rem;
    height: 0.68rem;
    position: absolute;
    bottom: 1.9rem;
    left: 50%;
    transform: translateX(-50%);
    // background-color: red;
    font-size: 0;
  }
}
.closeDiv {
  width: 0.8rem;
  height: 0.8rem;
  position: absolute;
  bottom: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  // background-color: red;
}
</style>
