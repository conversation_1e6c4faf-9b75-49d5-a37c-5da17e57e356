<template>
  <div class="pop-success-bk1">
    <div class="prizeImg">
      <img :src="exchangePrize.prizeImg" alt="" />
    </div>
    <div class="btnDivAll">
      <div class="goToshop" @click="gotoSkuPage(exchangePrize.skuId)">去下单</div>
      <div class="drawDiv" @click="drawClick()">参与抽奖</div>
    </div>
    <div class="closeDiv" @click="closeClick()"></div>
  </div>
</template>

<script lang="ts" setup>
import { gotoSkuPage } from "@/utils/platforms/jump";
import { computed, ref } from "vue";

const emits = defineEmits(["close", 'selectPrize']);

const props = defineProps({
  exchangePrize: {
    type: Object,
    default: null,
  },
});
const closeClick = () => {
  emits("close");
};
// 抽奖
const drawClick = () => {
  emits('selectPrize');
};
</script>

<style scoped lang="scss">
.pop-success-bk1 {
  position: relative;
  // background-image: url('../assets/failBk1.png');
  background: {
    image: url("../assets/exchangeSuccess.png");
    repeat: no-repeat;
    size: 100% 100%;
  }
  width: 6.8rem;
  height: 8rem;
  padding-top: 2.2rem;
}
.prizeImg {
  width: 2.7rem;
  margin-left: 50%;
  transform: translateX(-50%);
  img {
     width:2.7rem;
  }
}
.btnDivAll {
  display: flex;
  position: absolute;
  bottom: 1.6rem;
  font-size: 0;
  width: 100%;
  padding: 0 0.32rem;
  justify-content: center;
  .goToshop {
    width: 2rem;
    height: 0.68rem;
    // background-color: red;
    margin-right: 0.2rem;
  }
  .drawDiv{
    width: 2rem;
    height: 0.68rem;
    // background-color: greenyellow;
    margin-left: 0.2rem;
  }
}
.closeDiv {
  width: 0.8rem;
  height: 0.8rem;
  position: absolute;
  bottom: 0.3rem;
  left: 50%;
  transform: translateX(-50%);
  // background-color: red;
}
</style>
