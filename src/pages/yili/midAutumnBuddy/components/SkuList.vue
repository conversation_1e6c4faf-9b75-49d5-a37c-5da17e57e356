<template>
  <div class="com-bg">
    <div class="yili-list" @scroll="handleContainerScroll">
      <div class="sku-item" v-for="item in skuList" :key="item.skuId" @click="toSku(item.skuId)">
        <img :src="item.skuImg" alt="" class="sku-img" />
        <div class="sku-name-box">
          <div class="sku-name">{{ item.skuName }}</div>
        </div>
        <img src="https://img10.360buyimg.com/imgzone/jfs/t1/325204/27/10729/2870/68abc550Fb454a875/6740f09482d111a2.png" alt="" class="btn" />
      </div>
    </div>
    <div class="mooncake-list">
      <div class="sku-item" v-for="item in otherShopSkuList" :key="item.skuId" @click="toSku(item.skuId)">
        <img :src="item.skuImg" alt="" class="sku-img" />
        <div class="sku-name-box">
          <div class="sku-name">{{ item.skuName }}</div>
        </div>
        <img src="https://img10.360buyimg.com/imgzone/jfs/t1/325204/27/10729/2870/68abc550Fb454a875/6740f09482d111a2.png" alt="" class="btn" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { gotoSkuPage } from '@/utils/platforms/jump';
import { httpRequest } from '@/utils/service';
import { ref } from 'vue';
import { checkActStatus } from '../hooks';

interface SKU {
  skuId: number;
  skuName: string;
  skuImg: string;
  sortId: number;
  shopId: number;
}

const isLoading = ref(false);
const isEnd = ref(false);
const pageInfo = ref({
  pageNum: 1,
  pageSize: 16,
});
const skuList = ref<SKU[]>([]);

const toSku = (skuId: number) => {
  checkActStatus();
  gotoSkuPage(skuId);
};

const getShowAndCartSkuList = async () => {
  try {
    isLoading.value = true;
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getShowAndCartSkuList', {
      type: 2,
      ...pageInfo.value,
    });
    skuList.value = [...skuList.value, ...data.records];
    isEnd.value = data.records.length < pageInfo.value.pageSize;
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

const handleContainerScroll = (e: any) => {
  const container = e.target;
  const scrollBottom = container.scrollHeight - container.clientHeight - container.scrollTop;
  if (scrollBottom < 100 && !isLoading.value && !isEnd.value) {
    pageInfo.value.pageNum += 1;
    getShowAndCartSkuList();
  }
};

const otherShopSkuList = ref<SKU[]>([]);
const getOtherShopShowSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getOtherShopShowSkuList', {
      type: 2,
    });
    otherShopSkuList.value = data.records;
  } catch (error) {
    console.error(error);
  }
};

getShowAndCartSkuList();
getOtherShopShowSkuList();
</script>

<style scoped lang="scss">
.com-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/337795/15/6665/269041/68b93a87F1ee5a345/5662a78d6988f521.png') no-repeat;
  background-size: 100%;
  height: 13.55rem;
  position: relative;
  display: flex;
  justify-content: space-between;
  padding: 2.633rem 0.467rem 0 0.427rem;
  margin-bottom: 0.13rem;
  .yili-list {
    width: 3.75rem;
    height: 9.034rem;
    overflow-y: auto;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-content: flex-start;
  }
  .mooncake-list {
    width: 1.75rem;
    height: 9.034rem;
    overflow-y: auto;
  }
}
.sku-item {
  width: 1.75rem;
  padding: 0.06rem 0.06rem 0;
  background-color: #fff;
  border-radius: 0.2rem;
  height: fit-content;
  margin-bottom: 0.1rem;
  .sku-img {
    width: 1.6rem;
    height: 1.5rem;
    border-radius: 0.2rem;
  }
  .sku-name-box {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 0.32rem;
    .sku-name {
      font-size: 0.11rem;
      line-height: 0.16rem;
      color: #ec6c40;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-all;
      text-align: center;
    }
  }
  .btn {
    width: 0.83rem;
    height: 0.29rem;
    object-fit: cover;
    object-position: top;
    margin: 0 auto;
  }
}
</style>
