<template>
  <div class="pop-bg">
    <div class="content">
      <div style="width: 100%; overflow: hidden">
        {{ ruleTest }}
      </div>
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { ref } from 'vue';

const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};

const ruleTest = ref('');

const getRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    ruleTest.value = data;
  } catch (error) {
    console.error();
  }
};
getRule();
</script>

<style scoped lang="scss">
.pop-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/324425/18/10744/84619/68ac35e1Ff5b4e25c/2d4d086b797c5ea0.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.2rem;
  position: relative;
  padding: 2rem 0.5rem 0 0.7rem;
  .content {
    height: 3.2rem;
    overflow-y: auto;
    padding-top: 0.2rem;
    white-space: pre-wrap;
    word-break: break-all;
    color: #d94f26;
    font-size: 0.24rem;
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
  }
}
</style>
