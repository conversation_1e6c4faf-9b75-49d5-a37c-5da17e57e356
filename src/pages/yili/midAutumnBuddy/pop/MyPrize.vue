<template>
  <div class="pop-bg">
    <div class="content">
      <div class="row" v-for="item in prizeRecordList" :key="item.id">
        <div>{{ dayjs(item.createTime).format('MM-DD') }}</div>
        <div>
          <p>{{ item.prizeType === 99 ? `${item.freeMultiple}倍免单` : item.prizeName }}</p>
          <p v-if="item.prizeType === 99 && item.prizePrice">（{{ item.prizePrice }}元）</p>
        </div>
        <div :class="{ order: item.chanceSource === 1 }" @click="showChildOrder(item)">{{ item.chanceSource === 1 || item.chanceSource === 4 ? item.parentOrderId : '/' }}</div>
        <div>
          <div class="btn" v-if="item.status === 0">待发放</div>
          <div class="btn" v-else-if="item.status === 1 && item.prizeType === 1" @click="toMyCoupon">去使用</div>
          <div class="btn" v-else-if="item.status === 1 && item.prizeType === 7" @click="showCopyCard(item)">查看卡密</div>
          <div class="btn" v-else-if="item.status === 1 && item.prizeType === 99" @click="toJDjingrong">去查看</div>
          <div class="btn" v-else-if="item.status === 1">已发放</div>
          <div class="btn" v-else-if="item.status === 2 && item.prizeType === 99" @click="toastMsg(item)">已取消</div>
          <div class="btn" v-else-if="item.status === 2 && item.prizeType === 7 && (item.chanceSource === 1 || item.chanceSource === 4)" @click="toastMsg(item)">已取消</div>
          <div class="btn" v-else-if="item.status === 2">已取消</div>
        </div>
      </div>
      <div class="no-data" v-if="prizeRecordList.length === 0">暂无数据~</div>
    </div>
    <div class="tip">
      <span>免单/E卡将于2025年10月13日后且无退款退货行为，统一进行发放</span>
    </div>
    <div class="tip">
      <span>点击订单号，可展示查看该订单下的全部子订单</span>
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>

  <VanPopup v-model:show="childOrderPop" teleport="body" :close-on-click-overlay="false">
    <ChildOrder v-if="childOrderPop" @close="childOrderPop = false" :parent-order-id="parentOrderId" />
  </VanPopup>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { onMounted, ref } from 'vue';
import ChildOrder from './ChildOrder.vue';

const emits = defineEmits(['close', 'showCopyCard']);
const close = () => {
  emits('close');
};

const childOrderPop = ref(false);
const prizeRecordList = ref<any[]>([]);

const getUserPrizeRecordList = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getUserPrizeRecordList');
    data.forEach((item: any) => {
      if (data.prizeType === 99 && item.prizePrice && item.prizePrice > 5000) {
        item.prizePrice = 5000;
      }
    });
    prizeRecordList.value = data;
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

const toMyCoupon = () => {
  window.jmfe.toMyCoupon();
};

const toastMsg = (item: any) => {
  showToast(`订单${item.parentOrderId}存在退款，故取消奖品发放`);
};

const showCopyCard = (item: any) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits('showCopyCard', { ...prizeContent, prizeName: item.prizeName });
};

const toJDjingrong = () => {
  window.location.href = 'https://web-pay.jdpay.com/m-wallet/balance/home?style=jd_yili';
};

const parentOrderId = ref('');
const showChildOrder = (item: any) => {
  if (item.chanceSource === 1) {
    parentOrderId.value = item.parentOrderId;
    childOrderPop.value = true;
  }
};

onMounted(() => {
  getUserPrizeRecordList();
});
</script>

<style scoped lang="scss">
.pop-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/324758/39/10954/83041/68ac35e1F2af953b3/4a2d21783bd105cc.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.2rem;
  position: relative;
  padding: 2.6rem 0.5rem 0 0.7rem;
  .content {
    height: 2.4rem;
    overflow-y: auto;
    padding-top: 0.2rem;
    .row {
      display: flex;
      align-items: center;
      margin-bottom: 0.25rem;
      div {
        width: 25%;
        text-align: center;
        color: #d23f1d;
        font-size: 0.2rem;
        word-break: break-all;
      }
      .order {
        color: #2b00e7;
        text-decoration: underline;
      }
      .btn {
        width: 1rem;
        height: 0.35rem;
        line-height: 0.35rem;
        border-radius: 0.175rem;
        background: #ff6233;
        color: #fff;
        font-size: 0.2rem;
        margin-left: 0.07rem;
      }
    }
    .no-data {
      text-align: center;
      color: #fdf2ab;
      font-size: 0.26rem;
      text-shadow: 0.01rem 0 0 #ab0001, -0.01rem 0 0 #ab0001, 0 0.01rem 0 #ab0001, 0 -0.01rem 0 #ab0001;
      font-style: italic;
      margin-top: 1rem;
    }
  }
  .tip {
    font-size: 0.17rem;
    text-align: center;
    white-space: nowrap;
    span {
      background-color: #e99062;
      color: #fff;
      line-height: 0.3rem;
      display: inline-block;
    }
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
  }
}
</style>
