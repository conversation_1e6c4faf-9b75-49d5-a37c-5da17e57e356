<template>
  <div class="pop-bg-order">
    <div class="content">
      <div v-for="item in orderList" :key="item" class="order-item">{{ item }}</div>
      <div class="no-data" v-if="orderList.length === 0">暂无子订单</div>
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast } from 'vant';
import { ref } from 'vue';

const props = defineProps({
  parentOrderId: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};

const orderList = ref<any[]>([]);

const getSonOrderIdByParentId = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getSonOrderIdByParentId', {
      parentId: props.parentOrderId,
    });
    orderList.value = data;
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
getSonOrderIdByParentId();
</script>

<style scoped lang="scss">
.pop-bg-order {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/238813/12/29965/77873/68d0b7eeF6d53b617/f4100ea01bbc13c6.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.2rem;
  position: relative;
  padding: 2rem 0.5rem 0 0.7rem;
  .content {
    height: 3.2rem;
    overflow-y: auto;
    .order-item {
      width: 4.7rem;
      height: 0.47rem;
      border-radius: 0.235rem;
      line-height: 0.47rem;
      font-size: 0.24rem;
      color: #c32412;
      text-align: center;
      background-color: #e1723f;
      margin: 0.2rem auto 0;
    }
    .no-data {
      text-align: center;
      color: #fdf2ab;
      font-size: 0.26rem;
      text-shadow: 0.01rem 0 0 #ab0001, -0.01rem 0 0 #ab0001, 0 0.01rem 0 #ab0001, 0 -0.01rem 0 #ab0001;
      font-style: italic;
      margin-top: 1.5rem;
    }
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
  }
}
</style>
