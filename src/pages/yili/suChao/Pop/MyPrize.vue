<template>
  <div class="pop-bk">
    <div class="content">
      <div class="row" v-for="(item, index) in prizeList" :key="index">
        <div class="item">
          {{ dayjs(item.createTime).format('YYYY-MM-DD') }}
          <br />
          {{ dayjs(item.createTime).format('HH:mm:ss') }}
        </div>
        <div class="item">{{ item.prizeName }}</div>
        <div class="item">
          <div class="btn" v-if="!item.realName && item.prizeType === 3 && item.winningType !== 1" @click="toSaveAddress(item)">填写地址</div>
          <div class="btn" v-else-if="item.realName && item.prizeType === 3 && item.winningType !== 1">已填地址</div>
          <div class="btn" v-else-if="!item.idCardRealName && item.prizeType === 3 && item.winningType === 1" @click="toSaveInformation(item)">填写信息</div>
          <div class="btn" v-else-if="item.idCardRealName && item.prizeType === 3 && item.winningType === 1">已填信息</div>
          <div class="btn" v-else-if="item.prizeType === 7" @click="toShowPassword(item)">查看卡密</div>
          <div class="btn" v-else>已发放</div>
        </div>
      </div>
      <div class="no-data" v-if="prizeList.length === 0">暂无数据～</div>
    </div>
    <div class="close" @click="close"></div>

    <VanPopup teleport="body" v-model:show="saveAddressPop">
      <SaveAddress v-if="saveAddressPop" :address-id="prizeInfo?.addressId" @close="saveAddressPop = false" @success="saveAddressSuccess" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="saveInformationPop">
      <SavePersonalInformation v-if="saveInformationPop" @close="saveInformationPop = false" @success="saveInformationSuccess" :saveUserId="prizeInfo?.addressId" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="copyPasswordPop">
      <CopyPassword :prize-info="prizeInfo?.prizeContent" @close="copyPasswordPop = false" />
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { ref } from 'vue';
import SaveAddress from './SaveAddress.vue';
import SavePersonalInformation from './SavePersonalInformation.vue';
import dayjs from 'dayjs';
import CopyPassword from './CopyPassword.vue';
import { showToast } from 'vant';

const saveAddressPop = ref(false);
const saveInformationPop = ref(false);
const copyPasswordPop = ref(false);

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
interface Prize {
  /** 奖品名称 */
  prizeName: string;
  /** 中奖时间 */
  createTime: Date;
  /** 资产类型 */
  prizeType: number;
  /** 中奖类型：1-兑换门票奖品2-抽奖奖品3-竞猜京豆；4-竞猜猜中场次奖品；5-竞猜猜中场次消费最高奖品 */
  winningType: number;
  /** 发货状态 0:未发货 1:已发货 */
  deliveryStatus: number;
  /** 是否填写信息 */
  isFillInfo: boolean;
  /** 收货人 */
  realName: string;
  /** 手机号 */
  mobile: string;
  /** 所在省份 */
  province: string;
  /** 所在城市 */
  city: string;
  /** 所在县区 */
  county: string;
  /** 地址字符串 */
  address: string;
  /** 资产发放内容 */
  prizeContent: any;
  /** 地址ID */
  addressId: string;
  /** 身份证号 */
  idCard: string;
  /** 门票收货人 */
  idCardRealName: string;
  /** 门票手机号 */
  idCardMobile: string;
}
const prizeList = ref<Prize[]>([]);
const getPrizeList = async () => {
  try {
    const { data } = await httpRequest.post('/brand/yiLiSuChao/getUserPrizeRecordList');
    prizeList.value = data;
  } catch (error) {
    console.log(error);
  }
};
getPrizeList();

const prizeInfo = ref<Prize | null>(null);

const toSaveAddress = (item: Prize) => {
  if (dayjs(item.createTime).add(1, 'hour').isBefore(dayjs())) {
    showToast('抱歉已超过指定填写时间');
    return;
  }
  prizeInfo.value = item;
  saveAddressPop.value = true;
};
const saveAddressSuccess = () => {
  prizeInfo.value = null;
  saveAddressPop.value = false;
  getPrizeList();
};
const toSaveInformation = (item: Prize) => {
  if (dayjs(item.createTime).add(1, 'hour').isBefore(dayjs())) {
    showToast('抱歉已超过指定填写时间');
    return;
  }
  prizeInfo.value = item;
  saveInformationPop.value = true;
};
const saveInformationSuccess = () => {
  prizeInfo.value = null;
  saveInformationPop.value = false;
  getPrizeList();
};
const toShowPassword = (item: Prize) => {
  if (typeof item.prizeContent === 'string') {
    item.prizeContent = JSON.parse(item.prizeContent);
  }
  prizeInfo.value = item;
  copyPasswordPop.value = true;
};
</script>

<style scoped lang="scss">
.pop-bk {
  position: relative;
  width: 6.6rem;
  height: 6.23rem;
  background: url('../assets/pop/myPrizePop.png') no-repeat;
  background-size: 100%;
  padding-top: 1.54rem;
  .content {
    width: 5.7rem;
    height: 3.6rem;
    margin-left: 0.55rem;
    overflow-y: auto;
    .row {
      display: flex;
      align-items: center;
      padding: 0.1rem 0;
      &:last-child {
        padding-bottom: 0.6rem;
      }
      .item {
        flex: 0.33;
        text-align: center;
        color: #fff;
        font-size: 0.26rem;
        font-family: 'FZY4JW';
      }
      .btn {
        width: 1.44rem;
        height: 0.4rem;
        background-color: #ffa200;
        border-radius: 0.2rem;
        margin: 0 auto;
        line-height: 0.4rem;
      }
    }
    .no-data {
      padding-top: 1.4rem;
      color: #fff;
      font-size: 0.33rem;
      font-family: 'FZY4JW';
      text-align: center;
      font-style: italic;
    }
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.8rem;
    height: 0.8rem;
  }
}
</style>
