<template>
  <div class="pop-bk" v-if="prizeInfo?.prizeType !== 0">
    <img :src="prizeInfo?.prizeImg" alt="" class="prize-img" />
    <div class="prize-name">
      恭喜您成功抽中<br />
      【{{ prizeInfo?.prizeName }}】
    </div>
    <div class="btn">
      <img src="../assets/pop/saveAddressBtn.png" alt="" v-if="prizeInfo?.prizeType === 3" @click="emits('saveAddress')" />
      <img src="../assets/pop/showPasswordBtn.png" alt="" v-else-if="prizeInfo?.prizeType === 7" @click="emits('showPassword')" />
      <img src="../assets/pop/prizeBtn.png" alt="" v-else @click="close" />
    </div>
    <div class="tip">
      <div v-if="prizeInfo?.prizeType === 3">请在<span>中奖后1小时</span>内填写地址，过期视作放弃奖品</div>
      <div v-else-if="prizeInfo?.prizeType === 2">京豆已发放到您的账户中，可在<span>个人中心-京豆</span>中查看</div>
      <div v-else-if="prizeInfo?.prizeType === 6">红包已发放到您的账户中，可在<span>个人中心-红包</span>中查看</div>
      <div v-else-if="prizeInfo?.prizeType === 1">优惠券已放到您的账户中，可在<span>个人中心-优惠券</span>中查看</div>
    </div>
    <div class="close" @click="close"></div>
  </div>
  <div v-else class="no-win">
    <div class="btn" @click="close"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  prizeInfo: any;
}>();

const emits = defineEmits(['close', 'saveAddress', 'showPassword']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.pop-bk {
  position: relative;
  width: 6.6rem;
  height: 6.57rem;
  background: url('../assets/pop/winPop.png') no-repeat;
  background-size: 100%;
  padding-top: 1.25rem;
  .prize-img {
    width: 4.5rem;
    height: 2rem;
    object-fit: contain;
    margin: 0 auto 0.25rem;
  }
  .prize-name {
    font-family: 'FZY4JW';
    color: #3374b0;
    font-size: 0.49rem;
    line-height: 0.55rem;
    text-align: center;
    text-shadow: -0.02rem -0.02rem 0 #fff, 0.02rem -0.02rem 0 #fff, -0.02rem 0.02rem 0 #fff, 0.02rem 0.02rem 0 #fff;
    margin-bottom: 0.1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .btn img {
    width: 2.27rem;
    margin: 0 auto 0.1rem;
  }
  .tip {
    text-align: center;
    font-size: 0.18rem;
    font-family: 'FZY4JW';
    line-height: 0.18rem;
    color: #fff;
    padding-left: 0.4rem;
    span {
      color: #ff0000;
    }
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.8rem;
    height: 0.8rem;
  }
}
.no-win {
  position: relative;
  width: 6.6rem;
  height: 6.26rem;
  background: url('../assets/pop/noWinPop.png') no-repeat;
  background-size: 100%;
  padding-top: 4.7rem;
  .btn {
    width: 2.27rem;
    height: 0.7rem;
    margin: 0 auto;
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.8rem;
    height: 0.8rem;
  }
}
</style>
