<template>
  <div class="pop-bk">
    <div class="content">
      <div class="row" v-for="item in guessRecordList" :key="item.guessTime">
        <div class="item">{{ dayjs(item.guessTime).format('YYYY-MM-DD') }}</div>
        <div class="item">{{ dayjs(item.competitionTime).format('YYYY-MM-DD') }}</div>
        <div class="item">{{ item.teamName }}</div>
        <div class="item">{{ guessResultString(item.guessResult) }}</div>
      </div>
      <div class="no-data" v-if="guessRecordList.length === 0">暂无数据～</div>
    </div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { ref } from 'vue';

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

interface GuessRecord {
  /** 竞猜时间 */
  guessTime: string;
  /** 赛事日期 */
  competitionTime: string;
  /** 竞猜类型：0-平局；1-队伍1获胜；2-队伍2获胜 */
  guessType: 0 | 1 | 2;
  /** 竞猜队伍名称 */
  teamName: string;
  /** 竞猜结果：0-未开奖；1-猜中；2-未猜中 */
  guessResult: 0 | 1 | 2;
}

const guessResultString = (guessResult: number) => {
  if (guessResult === 0) {
    return '待结算';
  }
  if (guessResult === 1) {
    return '猜中';
  }
  return '未猜中';
};

const guessRecordList = ref<GuessRecord[]>([]);

const getUserGuessRecordList = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/brand/yiLiSuChao/getUserGuessRecordList');
    closeToast();
    guessRecordList.value = data;
  } catch (error) {
    closeToast();
  }
};
getUserGuessRecordList();
</script>

<style scoped lang="scss">
.pop-bk {
  position: relative;
  width: 6.6rem;
  height: 6.27rem;
  background: url('../assets/pop/predictionRecords​Pop.png') no-repeat;
  background-size: 100%;
  padding-top: 1.6rem;
  .content {
    width: 5.7rem;
    height: 3.6rem;
    margin-left: 0.5rem;
    overflow-y: auto;
    .row {
      display: flex;
      align-items: center;
      padding: 0.1rem 0;
      &:last-child {
        padding-bottom: 0.6rem;
      }
      .item {
        flex: 0.25;
        text-align: center;
        color: #fff;
        font-size: 0.2rem;
        font-family: 'FZY4JW';
      }
    }
    .no-data {
      padding-top: 1.4rem;
      color: #fff;
      font-size: 0.33rem;
      font-family: 'FZY4JW';
      text-align: center;
      font-style: italic;
    }
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.8rem;
    height: 0.8rem;
  }
}
</style>
