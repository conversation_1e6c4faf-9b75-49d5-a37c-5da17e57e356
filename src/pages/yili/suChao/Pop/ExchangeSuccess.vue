<template>
  <div class="pop-bk">
    <img :src="ticketInfo.prizeImgExt" alt="" class="prize-img" />
    <div class="prize-name">
      恭喜您成功兑换<br />
      【{{ ticketInfo.prizeName }}】
    </div>
    <div class="btn" @click="emit('saveInformation')"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const props = defineProps<{
  ticketInfo: any;
}>();
const emit = defineEmits(['close', 'saveInformation']);

const close = () => {
  emit('close');
};
</script>

<style scoped lang="scss">
.pop-bk {
  position: relative;
  width: 6.6rem;
  height: 6.57rem;
  background: url('../assets/pop/exchangeSuccessPop.png') no-repeat;
  background-size: 100%;
  padding-top: 1.24rem;
  .prize-img {
    width: 3.5rem;
    height: 2rem;
    object-fit: cover;
    margin: 0 auto 0.25rem;
  }
  .prize-name {
    font-family: 'FZY4JW';
    color: #3374b0;
    font-size: 0.49rem;
    line-height: 0.55rem;
    text-align: center;
    text-shadow: -0.02rem -0.02rem 0 #fff, 0.02rem -0.02rem 0 #fff, -0.02rem 0.02rem 0 #fff, 0.02rem 0.02rem 0 #fff;
    margin-bottom: 0.1rem;
  }
  .btn {
    width: 2.27rem;
    height: 0.7rem;
    margin: 0 auto 0.1rem;
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.8rem;
    height: 0.8rem;
  }
}
</style>
