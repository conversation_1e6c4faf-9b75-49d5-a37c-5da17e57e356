import { httpRequest } from '@/utils/service';
import { reactive, ref } from 'vue';

export const yiLiShopList = [
  '1000013402', // 伊利牛奶京东自营旗舰店
  '1000556465', // 舒化牛奶京东自营旗舰店
  '112175', // 伊利牛奶官方旗舰店
  '13140728', // 伊利牛奶旗舰店
  '11572021', // 金典 SATINE官方旗舰店
];

export const userInfo = ref({
  skuNum: 0, // 用户当前的sku件数
  drawNum: 0, // 用户当前的抽奖次数
  guessNum: 0, // 用户当前的竞猜次数
  isAddCart: false, // 今日是否加购
  isAddCartThreeDays: false, // 是否加购三天
  exchangeSkuNumLimit: 0, // 累计下单满xx件，可参与兑换门票一次
  addDrawNum: 0, // 新增抽奖次数
  addGuessNum: 0, // 新增竞猜次数
});
export const decoData = ref<any>({});
// 获取用户信息
export const getUserInfo = async () => {
  try {
    const { data } = await httpRequest.post('/brand/yiLiSuChao/getUserInfo');
    userInfo.value = data;
    userInfo.value.skuNum = data.skuNum >= 0 ? data.skuNum : 0;
    userInfo.value.drawNum = data.drawNum >= 0 ? data.drawNum : 0;
    userInfo.value.guessNum = data.guessNum >= 0 ? data.guessNum : 0;
    userInfo.value.addDrawNum = data.addDrawNum >= 0 ? data.addDrawNum : 0;
    userInfo.value.addGuessNum = data.addGuessNum >= 0 ? data.addGuessNum : 0;
    decoData.value = JSON.parse(data.kv);
  } catch (error) {
    console.log(error);
  }
};
export interface Prize {
  prizeId: number; // 主键
  prizeKey: string; // 资产id
  prizeType: number; // 资产类型
  prizeImg: string; // 资产封面图
  prizeName: string; // 资产名称
  sortId: number; // 排序
  remainNum: number; // 奖品剩余数量
  isWin: boolean; // 是否领取
  userGuessMatch: number; // 用户猜中场次
  userCanWin: boolean; // 用户是否可领
  guessNum: number; // 竞猜猜中场次
}
export const exChangePrizeList = ref<Prize[]>([]);
export const drawPrizeList = ref<Prize[]>([]);
export const guessPrizeList = ref<Prize[]>([]);
export const guessMatchPrizeList = ref<Prize[]>([]);
export const guessMatchConsumePrizeList = ref<Prize[]>([]);
// 获取奖品列表 奖品配置类型:1-兑换门票奖品  2-抽奖奖品  3-竞猜京豆；4-竞猜猜中场次奖品；5-竞猜猜中场次消费最高奖品
export const getPrizeList = async (configType: number) => {
  try {
    const { data }: { data: Prize[] } = await httpRequest.post('/brand/yiLiSuChao/getPrizeList', {
      configType,
    });
    data.forEach((item) => {
      item.remainNum = item.remainNum >= 0 ? item.remainNum : 0;
    });
    data.sort((a, b) => a.sortId - b.sortId);
    if (configType === 1) {
      exChangePrizeList.value = data;
    } else if (configType === 2) {
      drawPrizeList.value = data;
    } else if (configType === 3) {
      guessPrizeList.value = data;
    } else if (configType === 4) {
      guessMatchPrizeList.value = data;
    } else if (configType === 5) {
      guessMatchConsumePrizeList.value = data;
    }
  } catch (error) {
    console.log(error);
  }
};

export interface Guess {
  guessId: number; // 主键
  competitionTime: string; // 赛事日期
  teamOneName: string; // 队伍1名称
  teamOneImg: string; // 队伍1图标
  teamTwoName: string; // 队伍2名称
  teamTwoImg: string; // 队伍2图标
  guessOptions: string; // 竞猜选项
  competitionResult: 0 | 1 | 2 | 3; // 结果公示：0-平局；1-队伍1获胜；2-队伍2获胜；3-未开奖
  guessType: 0 | 1 | 2 | 3; // 用户竞猜的类型：0-平局；1-队伍1获胜；2-队伍2获胜;3-未竞猜
  guessResult: 0 | 1 | 2; // 竞猜结果：0-未开奖；1-猜中；2-未猜中
  teamName: string; // 竞猜队伍名称
}
export const guessList = ref<Guess[]>([]);
// 获取竞猜列表
export const getGuessList = async () => {
  try {
    const { data }: { data: Guess[] } = await httpRequest.post('/brand/yiLiSuChao/getGuessList');
    guessList.value = data;
  } catch (error) {
    console.log(error);
  }
};

export const yiliShopListSort = ref<string[]>([]);
const getShopConfig = async () => {
  try {
    const { data } = await httpRequest.post('/brand/yiLiSuChao/getShopConfig');
    yiliShopListSort.value = data.map((item: any) => item.shopId);
  } catch (error) {
    console.log(error);
  }
};

export const initActive = async () => {
  try {
    await Promise.all([getUserInfo(), getPrizeList(1), getPrizeList(2), getPrizeList(3), getPrizeList(4), getPrizeList(5), getGuessList(), getShopConfig()]);
  } catch (error) {
    console.log(error);
  }
};

export interface Sku {
  /**
   * 店铺id
   */
  shopId: number;

  /**
   * sku编码
   */
  skuId: number;

  /**
   * 商品价格，单位分
   */
  jdPrice: string;

  /**
   * 商品图片地址
   */
  skuMainPicture: string;

  /**
   * 商品名称
   */
  skuName: string;

  /**
   * 排序
   */
  sortId: number;
}
