<template>
  <div class="pop-bg">
    <div class="content" @scroll="handleContainerScroll">
      <div class="sku-item" v-for="item in skuList" :key="item.skuId">
        <img :src="item.skuImg" alt="" class="sku-img" />
        <div class="sku-name-box">
          <div class="sku-name">{{ item.skuName }}</div>
        </div>
        <img src="https://img10.360buyimg.com/imgzone/jfs/t1/324025/28/14721/2276/68b689a8F7889ac12/43e96e0e15199b0c.png" alt="" class="btn" @click="addSku(item)" />
      </div>
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { onMounted, ref } from 'vue';
import { addSkuToCart } from '@/utils/platforms/jump';
import { closeToast, showLoadingToast } from 'vant';
import { getActivityInfo } from '../hooks';

const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};

const isLoading = ref(false);
const isEnd = ref(false);
const pageInfo = ref({
  pageNum: 1,
  pageSize: 16,
});
const skuList = ref<any[]>([]);

const getShowAndCartSkuList = async () => {
  try {
    isLoading.value = true;
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getShowAndCartSkuList', {
      type: 1,
      ...pageInfo.value,
    });
    skuList.value = [...skuList.value, ...data.records];
    isEnd.value = data.records.length < pageInfo.value.pageSize;
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

const handleContainerScroll = (e: any) => {
  const container = e.target;
  const scrollBottom = container.scrollHeight - container.clientHeight - container.scrollTop;
  if (scrollBottom < 100 && !isLoading.value && !isEnd.value) {
    pageInfo.value.pageNum += 1;
    getShowAndCartSkuList();
  }
};

const addSku = async (item: any) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    await httpRequest.post('/brand/yiLiMidAutumn/addCartTask', {
      skuId: item.skuId,
      shopId: item.shopId,
    });
    getActivityInfo();
    closeToast();
    addSkuToCart(item.skuId);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

onMounted(async () => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  await getShowAndCartSkuList();
  closeToast();
});
</script>

<style scoped lang="scss">
.pop-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/331299/33/7823/84365/68b689a8Fe3c73429/bafebaa664117036.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.75rem;
  position: relative;
  padding: 2.05rem 0.7rem 0 0.9rem;
  .content {
    height: 3.9rem;
    overflow-y: auto;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-content: flex-start;
    .sku-item {
      width: 2.35rem;
      padding: 0.06rem 0.06rem 0;
      background-color: #fff;
      border-radius: 0.2rem;
      height: fit-content;
      margin-bottom: 0.1rem;
      .sku-img {
        width: 2.2rem;
        height: 2.1rem;
        border-radius: 0.2rem;
        margin-bottom: 0.05rem;
      }
      .sku-name-box {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 0.28rem;
        .sku-name {
          font-size: 0.13rem;
          line-height: 0.14rem;
          color: #ec6c40;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          word-break: break-all;
          text-align: center;
        }
      }
      .btn {
        width: 0.83rem;
        object-fit: cover;
        object-position: top;
        margin: 0 auto;
      }
    }
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
  }
}
</style>
