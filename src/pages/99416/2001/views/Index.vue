<template>
  <div id="page">
    <div class="activity-container">
      <!-- 我的奖品按钮 -->
      <div class="my-prize-btn" @click="onClickMyPrize"><span>我的奖品</span></div>
      <!-- 创建宝宝信息表单 -->
      <CreateBabyPage v-if="homeData.babies.length === 0" :homeData="homeData" @submit="createBaby" :checkActivityTime="checkActivityTime" />
      <!-- 宝宝列表 -->
      <BabyListPage v-else :babies="homeData.babies" @editBaby="editBaby" @addBaby="addBaby" :checkActivityTime="checkActivityTime" />
    </div>
  </div>
  <!-- 填写地址弹窗 -->
  <AddressDialog v-model:show="showAddressDialog" @submitSuccess="onSubmitAddressSuccess" @submit="onSubmitAddress" />
  <!-- 抽奖结果弹窗 -->
  <PrizeResultDialog v-model:show="showPrizeDialog" :prizes="prizeList" />
  <!-- 宝宝编辑or再次增加 -->
  <CreateBabyPage v-if="showEditDialog" :homeData="homeData" :initData="editFormData" @submit="onBabySubmit" @close="showEditDialog = false" @remove="onBabyRemove" :checkActivityTime="checkActivityTime" />
  <!-- 我的奖品弹窗 -->
  <MyPrizeDialog v-model:show="showMyPrizeDialog" :my-prize-list="myPrizeList" />
</template>

<script lang="ts" setup>
/* eslint-disable */
import { inject, reactive, provide, computed, ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { closeToast, showLoadingToast, showToast, Swipe as VanSwipe, SwipeItem as VanSwipeItem, Popup as VanPopup, CountDown as VanCountDown } from 'vant';
import { debounce } from 'lodash';
import dayjs from 'dayjs';
import { areaList } from '@vant/area-data';

import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { gotoShopPage, addSkuToCart, gotoSkuPage } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import openCard from '@/utils/openCard';

import CreateBabyPage from '../components/CreateBabyPage.vue';
import BabyListPage from '../components/BabyListPage.vue';
import AddressDialog from '../components/AddressDialog.vue';
import PrizeResultDialog from '../components/PrizeResultDialog.vue';
import MyPrizeDialog from '../components/MyPrizeDialog.vue';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('baseInfo', baseInfo);

const pathParams: any = inject('pathParams');
console.log('pathParams', pathParams);

const baseUserInfo: any = inject('baseUserInfo');
console.log('baseUserInfo', baseUserInfo);

const decoData: any = inject('decoData');
console.log('decoData', decoData);

const delayToast = (e: string, time = 1000): Promise<void> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      showToast(e);
      resolve();
    }, time);
  });
};

function checkActivityTime(): Promise<void> {
  return new Promise((resolve, reject) => {
    const now = dayjs();
    const start = dayjs(baseInfo.startTime);
    const end = dayjs(baseInfo.endTime);

    if (now.isBefore(start)) {
      showToast('活动未开始');
      reject(new Error('活动未开始'));
      return;
    }
    if (now.isAfter(end)) {
      showToast('活动已结束');
      reject(new Error('活动已结束'));
      return;
    }
    resolve();
  });
}

const homeData = ref({
  babies: [],
  hasStock: false,
  hasGoods: false, // 是否包含实物奖品
  hasLimit: false, // 是否到达宝宝上限
  modifyTimes: 1, // 宝宝信息可修改次数
});

const isSaving = ref(false);

const getHomeData = async (options = { showLoading: true }) => {
  const { showLoading = true } = options;

  try {
    if (showLoading) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
    }

    // 查询用户宝宝列表
    const { data } = await httpRequest.post('/99416/main');
    homeData.value = data;
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  } finally {
    if (showLoading) {
      closeToast();
    }
  }
};

onMounted(async () => {
  await getHomeData();
});

const babyFormData = ref({});
const createBaby = async (babyInfo) => {
  if (isSaving.value) return; // 防连击
  console.log('宝宝表单:', babyInfo);
  if (!homeData.value.hasStock) return showToast('奖品无库存，请联系客服');
  babyFormData.value = babyInfo;
  if (homeData.value.hasGoods) {
    showAddressDialog.value = true;
  } else {
    try {
      isSaving.value = true;
      showLoadingToast({ message: '提交中...', forbidClick: true, duration: 0 });
      const params = { baby: babyInfo };
      const { data } = await httpRequest.post('/99416/save', params);
      if (data && data.length > 0) {
        showPrizeDialog.value = true;
        prizeList.value = data;
        await getHomeData({ showLoading: false });
      }
    } catch (error: any) {
      showToast(error.message);
      console.error(error);
    } finally {
      isSaving.value = false;
      closeToast();
    }
  }
};
// 抽奖结果弹窗
const showPrizeDialog = ref(false);
const prizeList = ref([]);

// 填写地址弹窗
const showAddressDialog = ref(false);
const onSubmitAddressSuccess = async () => {};
const onSubmitAddress = async (address) => {
  if (isSaving.value) return; // 防连击
  try {
    isSaving.value = true;
    showLoadingToast({ message: '提交中...', forbidClick: true, duration: 0 });
    const params = { address, baby: babyFormData.value };
    const { data } = await httpRequest.post('/99416/save', params);
    if (data && data.length > 0) {
      showAddressDialog.value = false;
      showPrizeDialog.value = true;
      prizeList.value = data;
      await getHomeData();
    }
  } catch (error: any) {
    showAddressDialog.value = false;
    showToast(error.message);
    console.error(error);
    await getHomeData({ showLoading: false });
  } finally {
    isSaving.value = false;
    closeToast();
  }
};

const showEditDialog = ref(false); // 控制弹窗显示
const editFormData = ref<any>(null); // 需要回显的宝宝信息

const editBaby = async (babyInfo) => {
  editFormData.value = { ...babyInfo };
  showEditDialog.value = true;
};

// 添加宝宝
const addBaby = () => {
  editFormData.value = null;
  showEditDialog.value = true;
};

// 保存回调
const onBabySubmit = async (babyInfo) => {
  if (isSaving.value) return; // 防连击
  try {
    isSaving.value = true;
    showLoadingToast({ message: '提交中...', forbidClick: true, duration: 0 });
    const params = {
      baby: {
        ...editFormData.value,
        ...babyInfo,
      },
    };
    const { data } = await httpRequest.post('/99416/save', params);
    showEditDialog.value = false;
    showToast(editFormData.value ? '修改成功' : '新增成功');
    await getHomeData({ showLoading: false });
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  } finally {
    isSaving.value = false;
    closeToast();
  }
};
// 我的奖品弹窗
const showMyPrizeDialog = ref(false);
const myPrizeList = ref([]);
const onClickMyPrize = async () => {
  try {
    showLoadingToast({ message: '正在查询...', forbidClick: true, duration: 0 });
    const { data } = await httpRequest.post('/99416/prize/query');
    myPrizeList.value = data;
    showMyPrizeDialog.value = true;
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  } finally {
    closeToast();
  }
};

// 删除宝宝
const onBabyRemove = async (babyInfo) => {
  console.log('🚀 ~ onBabyRemove ~ babyInfo:', babyInfo);
  try {
    showLoadingToast({ message: '删除中...', forbidClick: true, duration: 0 });
    const params = { baby: { ...babyInfo } };
    await httpRequest.post('/99416/remove', params);
    showToast('删除成功');
    showEditDialog.value = false;
    await getHomeData({ showLoading: false });
  } catch (error: any) {
    showToast(error.message || '删除失败');
    console.error(error);
  } finally {
    closeToast();
  }
};
</script>

<style lang="scss" scoped>
#page {
  width: 100vw;
  height: 100vh;
  overflow: auto;
}

.activity-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  min-height: 100vh;
}

.my-prize-btn {
  position: fixed;
  top: 1rem;
  right: 0;
  z-index: 9;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.38rem;
  height: 0.53rem;
  padding-left: 0.1rem;
  font-size: 0.21rem;
  background-image: linear-gradient(108deg, #d2a161 0%, #e0bd79 100%);
  border-radius: 0.27rem 0 0 0.27rem;
}
</style>
