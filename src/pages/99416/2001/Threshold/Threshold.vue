<template>
  <VanPopup v-model:show="limitPopup" :closeOnClickOverlay="false" z-index="9999">
    <div class="common-limit" :style="furnish.dialogPopupBg ? { backgroundImage: 'url(' + furnish.dialogPopupBg + ')' } : ''">
      <div class="common-limit-title">
        <div>很遗憾</div>
        <div>您当前暂不满足活动参与条件，原因如下：</div>
      </div>
      <div class="common-limit-content">
        <div v-for="(item, index) in data" :key="index" class="common-limit-item">
          <div class="common-limit-item-text">{{ item.thresholdContent }}</div>
          <div v-if="item.btnContent && item.thresholdStatus === 0" @click="eventClick(item.type)" class="common-limit-btn">{{ item.btnContent }}</div>
        </div>
      </div>
    </div>
    <img src="//img10.360buyimg.com/imgzone/jfs/t1/19991/28/20752/1150/659bdc16Fb8182d8d/bd77364aab8a284a.png" class="common-limit-close-btn" @click="close" />
  </VanPopup>
</template>
<script setup lang="ts">
import { defineEmits, defineProps, inject, ref, watch, onMounted } from 'vue';
import { gotoShopPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { Handler } from '@/utils/handle';
import useThreshold from '@/hooks/useThreshold';
import openCard from '@/utils/openCard';
import { furnish } from '../ts/furnishStyles';

const baseInfo: any = inject('baseInfo') as BaseInfo;
const handler = Handler.getInstance();

const props = defineProps(['data', 'show']);
const emits = defineEmits(['update:show']);

const data = baseInfo.thresholdResponseList;

const limitPopup = ref(props.show);
const close = () => {
  limitPopup.value = false;
  emits('update:show', false);
};

watch(
  () => props.show,
  (val) => {
    limitPopup.value = val;
  },
);

const fellowShop = async () => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    await httpRequest.post('/common/followShop');
    showToast({
      message: '关注成功',
      forbidClick: true,
    });
    return true;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
    return false;
  }
};
const eventClick = async (type: number) => {
  switch (type) {
    case 1:
      // 去开卡
      openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
      // window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;`;
      break;
    case 2:
      // 关注店铺
      await fellowShop();
      setTimeout(() => {
        window.location.reload();
      }, 1500);
      break;
    case 3:
      // 等级不满足，进入店铺
      await gotoShopPage(baseInfo.shopId);
      break;
    case 4:
      // 关注店铺并立即入会
      await fellowShop();
      openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
      // window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;`;
      break;
    default:
      console.log('~');
  }
};

const openThreshold = () => {
  limitPopup.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList,
    className: 'common-message-cpb',
  });
  return baseInfo.thresholdResponseList.length;
};

onMounted(() => {
  handler.on('onThresholdOpen', () => openThreshold());
  handler.on('onThresholdIf', () => baseInfo.thresholdResponseList.length);
});
</script>
<style lang="scss" scoped>
.common-limit {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 6.52rem;
  height: 9.42rem;
  padding: 1rem 0.5rem 0.5rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/236810/34/9907/10566/65969c00F90424d43/759a43f2c99a47cb.png') no-repeat;
  background-size: 100% 100%;

  .common-limit-title {
    height: 1.3rem;
    color: #8d6a35;
    text-align: center;

    div:nth-child(1) {
      font-size: 0.4rem;
    }

    div:nth-child(2) {
      margin-top: 0.1rem;
      font-size: 0.24rem;
    }
  }

  .common-limit-content {
    width: 100%;
    min-height: 2.5rem;

    .common-limit-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 0.3rem;
      border: 0.02rem solid #8d6a35;
      border-radius: 0.2rem;

      &:not(:last-child) {
        margin-bottom: 0.1rem;
      }

      .icon {
        width: 0.8rem;
        height: 0.8rem;
        margin-right: 0.2rem;
        object-fit: contain;
      }

      .common-limit-item-text {
        flex: 1;
        padding-right: 0.2rem;
        color: #8d6a35;
        font-size: 0.2rem;
      }

      .common-limit-btn {
        width: 1.6rem;
        height: 0.5rem;
        color: #fff;
        font-size: 0.24rem;
        line-height: 0.5rem;
        text-align: center;
        background-image: linear-gradient(270deg, #785334 0%, #a17d59 27%, #caa67d 54%, #785334 100%);
        background-size: 100%;
        border-radius: 0.25rem;
      }
    }
  }
}

.common-limit-close-btn {
  width: 0.4rem;
  margin: 0.2rem auto 0;
}
</style>
