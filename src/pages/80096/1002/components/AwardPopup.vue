<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <img :src="prize.prizeImg" alt="" class="prize-img" />
    <div class="content">
      <p class="prize-name">{{ prize.prizeName }}</p>
      <div>
        <div class="p3" v-if="prize.prizeType === PrizeTypeEnum.Physical">
          您已经成功领取奖品，在完成订单付款，并最终确认收货后，在领取记录中填写地址。未完成付款或未确认收货的将无法获得礼品
        </div>
        <p class="p3" v-else-if="prize.prizeType === PrizeTypeEnum.Rights">
          1:权益非自动发放,需首先填写权益领取信息 <br />
          2:如放弃领取权益,活动结束权益不予补发
        </p>
        <div class="p3" v-else>
          <p>已锁定奖品名额，虚拟礼品将在您完成订单付款，并最终确认收货后自动发放，未完成付款或未确认收货的将无法获得礼品</p>
        </div>
      </div>
      <div class="btn-list">
        <img
          class="btn"
          src="//img10.360buyimg.com/imgzone/jfs/t1/231531/16/610/12698/65375e31F919ce8ce/93b2a6852a5cdeb7.png"
          alt=""
          v-if="prize.prizeType === PrizeTypeEnum.Physical"
          @click="saveAddress" />
        <img
          class="btn"
          src="//img10.360buyimg.com/imgzone/jfs/t1/192487/3/37587/5851/65019640F0825b3e3/136411a41743e85c.png"
          alt=""
          v-else-if="prize.prizeType === PrizeTypeEnum.Rights"
          @click="savePhone" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/235471/4/6102/13218/65715f25F7bdb6111/5871c21c56f6e2c8.png" alt="" @click="close" />
      </div>
    </div>
    <div class="close" @click="close"></div>
  </div>
  <div class="thanks-join" v-else>
    <div class="close" @click="close"></div>
    <div class="btn" @click="gotoShopPage"></div>
  </div>
</template>
<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import { PropType, inject } from 'vue';
// import { gotoShopPage } from '@/utils/platforms/jump';
// 定义枚举类型
enum PrizeTypeEnum {
  // 实物
  Physical = 3,
  // 京元宝
  Rights = 12,
}

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  prizeImg: string;
  result: any;
  activityPrizeId: string;
  userReceiveRecordId: string;
  prizeContent: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'savePhone', 'openShowGoShop']);

const gotoShopPage = () => {
  emits('openShowGoShop');
};

const close = () => {
  emits('close');
};

const saveAddress = () => {
  emits('saveAddress', props.prize.userReceiveRecordId);
};

const savePhone = () => {
  const prizeContent = props.prize.prizeContent ? JSON.parse(props.prize.prizeContent) : { result: { planDesc: '-' } };
  emits('savePhone', props.prize.userReceiveRecordId, prizeContent.result.planDesc);
};
</script>

<style scoped lang="scss">
.bk {
  height: 7.89rem;
  width: 6rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/166908/32/40589/98464/65019763Fff6d6346/af37c0263c52fd05.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 1.3rem;
  .prize-img {
    height: 2rem;
    width: 2rem;
    //border-radius: 50%;
    margin: 0 auto;
  }
  .content {
    padding: 0 0.3rem;
    .p1 {
      display: block;
      color: #262626;
      font-size: 0.24rem;
      font-weight: 500;
    }
    .prize-name {
      font-size: 0.4rem;
      font-weight: bold;
      margin: 0.2rem 0 0;
      text-align: center;
      color: #fff;
    }
    .p3 {
      font-size: 0.22rem;
      color: #fff;
      text-align: center;
      height: 1.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        color: #fff;
      }
    }
    .btn-list {
      display: flex;
      justify-content: space-around;
      padding: 0 0.3rem;
      margin-top: 0.2rem;
      .btn {
        width: 2.1rem;
      }
      .btn-left {
        background: linear-gradient(to right, #f2270c, #ff6320);
      }
      .btn-right {
        background: #ff9900;
      }
    }
  }
}
.thanks-join {
  width: 6rem;
  height: 7.89rem;
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/98130/39/37445/93706/65019837Fca4f3e8e/02538ed5f33a63ec.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 5.7rem;
  .btn {
    display: block;
    margin: 0 auto;
    width: 2.6rem;
    height: 0.76rem;
  }
}
.close {
  height: 0.6rem;
  width: 0.6rem;
  position: absolute;
  bottom: 0;
  left: 2.7rem;
}
</style>
