<template>
  <div class="relative" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="h-screen overflow-auto">
      <div class="relative">
        <div class="box-border flex flex-row justify-between z-10 absolute w-full top-3 px-3">
          <div class="text-black text-sm" :style="furnishStyles.shopNameColor.value">
            <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
          </div>
          <div class="flex flex-col gap-y-2">
            <div
              class="text-xs text-red-500 bg-white border-[1px] border-[#ffc92e] rounded-2xl px-2.5 py-0.5"
              v-for="(btn, index) in btnList"
              :key="index"
              @click="btn.event">
              {{ btn.name }}
            </div>
          </div>
        </div>
        <img class="w-full" :src="furnish.actBg" alt="" />
      </div>
      <div class="py-3.5">
        <div class="mx-2 relative">
          <img class="w-full" :src="IMAGE_MAP.COUNT_DOWN_BG" alt="" />
          <div class="text-white text-xs absolute top-3.5 left-9 leading-[0.32rem]">距活动结束剩余：</div>
          <div class="count-down">{{ countdownTime }}</div>
        </div>
        <div class="px-14 mt-3.5" @click="toast">
          <img :src="IMAGE_MAP.IMMEDIATELY_PAY" alt="" />
        </div>
        <div class="pl-2 mt-3.5 pr-3">
          <div class="flex flex-row items-center">
            <div class="relative">
              <img class="h-4 mr-2.5" :src="IMAGE_MAP.STEP_ICON" alt="" />
              <ul class="process" v-if="prizeList.length">
                <li v-for="item in 9" :key="item"></li>
              </ul>
            </div>
            <div class="bg-white w-full rounded px-5 py-1 chat relative">
              <div class="text-xs font-bold mb-0.5">
                您当前累计消费 <span class="text-red-500 mx-0.5">0</span> 元，距离下一个大奖还差<span class="text-red-500 mx-0.5">{{
                  nextStateAmount.toFixed(2)
                }}</span
                >元
              </div>
              <div class="text-xs" :style="{ color: '#CF1110' }">(确认收货后才会发放对应礼品)</div>
            </div>
          </div>
          <div class="flex flex-row items-center mt-3" v-for="(item, index) in prizeList" :key="index">
            <div class="relative">
              <div class="circle mr-2.5 ml-0.5"></div>
              <ul class="process" v-if="index !== prizeList.length - 1">
                <li v-for="item in 14" :key="item"></li>
              </ul>
            </div>
            <div class="bg-white w-full rounded p-2 chat relative">
              <div class="flex flex-row relative">
                <div>
                  <img class="border-2 w-24 border-red-500 rounded-lg" :src="item.prizeImg" alt="" />
                  <img class="bottom-0 absolute w-24" :src="IMAGE_MAP.PRIZE_BORDER" alt="" />
                  <div class="absolute bottom-0 text-center w-24 text-white text-xs">{{ prizeType[item.prizeType] }}</div>
                </div>
                <div class="flex-1 ml-3 mr-1 flex flex-col justify-around">
                  <div class="font-bold tracking-widest text-base">{{ item.prizeName }}</div>
                  <div class="text-xs font-bold">满{{ item.stepAmount || 'x' }}元可领奖品</div>
                  <div class="flex flex-row text-xs justify-between items-center">
                    <div class="text-gray-800">奖品剩余：{{ item.remainCount || item.sendTotalCount || 'xx' }}份</div>
                    <div class="receive-btn" @click="toast">报名领取</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="p-3.5 relative" v-if="skuList.length">
        <img :src="IMAGE_MAP.HOT_ITEM_BG" alt="" />
        <div class="grid grid-cols-2 gap-1.5 absolute top-[1.376rem] pl-4 pr-7 max-h-[9.44rem] overflow-auto sku-box">
          <div v-for="(item, index) in skuList" class="bg-white py-2 px-3.5" :key="index">
            <div class="flex justify-center">
              <img class="w-32 h-32" :src="item.skuMainPicture" alt="" />
            </div>
            <div class="text-xs mt-5 lz-multi-ellipsis--l2">{{ item.skuName }}</div>
            <div class="text-red-500 text-xs mt-3">¥ {{ item.jdPrice }}</div>
          </div>
          <!-- <div class="w-[6.34rem] text-gray-400 text-xs text-center my-1" v-if="skuList.length > 4">—— 没有更多了 ——</div> -->
        </div>
        <div class="load-more" @click="handleLoadMore">加载更多</div>
      </div>
    </div>
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showReceiveRecord" position="bottom">
      <ReceiveRecordPopup @close="showReceiveRecord = false"></ReceiveRecordPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
      <GoodsPopup :data="orderSkuList" :isAllOrderSku="isAllOrderSku" @close="showGoods = false"></GoodsPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList, IMAGE_MAP, prizeType } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import ReceiveRecordPopup from '../components/ReceiveRecordPopup.vue';
import GoodsPopup from '../components/GoodsPopup.vue';
import AwardPopup from '../components/AwardPopup.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useCountdown from '@/hooks/useCountdown';
import { showToast } from 'vant';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();
// 默认设置结束时间戳为1小时后
const endTime = ref(dayjs().add(30, 'day').valueOf());
const countdownTime = useCountdown(endTime);

const isLoadingFinish = ref(false);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
};
const prizeList = ref<Prize[]>(defaultStateList);
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);
const nextStateAmount = ref(0);
const isAllOrderSku = ref(1);
const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
const showOrderRecord = ref(false);
const showReceiveRecord = ref(false);
const showGoods = ref(false);
const showAward = ref(false);

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});
// 加载更多
const handleLoadMore = () => {
  showToast('活动预览，仅供查看');
};
// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};
const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      showRule.value = true;
    },
  },
  {
    name: '订单记录',
    event: () => {
      showOrderRecord.value = true;
    },
  },
  {
    name: '领奖记录',
    event: () => {
      showReceiveRecord.value = true;
    },
  },
  {
    name: '活动商品',
    event: () => {
      showGoods.value = true;
    },
  },
];

const close = () => {
  showLimit.value = false;
};
const createImg = async () => {
  showRule.value = false;
  showGoods.value = false;
  showOrderRecord.value = false;
  showOrderRecord.value = false;
  showLimit.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  const efficientPrizeList = data.prizeList.filter((e: { prizeType: number }) => e.prizeType);
  if (efficientPrizeList.length) {
    prizeList.value = efficientPrizeList;
    nextStateAmount.value = prizeList.value[0].stepAmount || 0;
  } else {
    prizeList.value = defaultStateList;
  }
  if (data.skuList) {
    skuList.value = data.skuList;
  }
  if (data.orderSkuList) {
    orderSkuList.value = data.orderSkuList;
  }
  ruleTest.value = data.rules;
  isAllOrderSku.value = data.isAllOrderSku;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    prizeList.value = activityData.prizeList;
    ruleTest.value = activityData.rules;
    orderSkuList.value = activityData.orderSkuList;
    endTime.value = dayjs(activityData.endTime).valueOf();
    nextStateAmount.value = prizeList.value[0].stepAmount || 0;
    shopName.value = activityData.shopName;
    skuList.value = activityData.skuList;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style scoped lang="scss">
.sku-box{
  height: 8.8rem;
}
  .load-more {
    width: 3rem;
    height: 0.6rem;
    line-height: 0.6rem;
    text-align: center;
    background: #ff7b77;
    border-radius: 0.2rem;
    color: white;
    position: absolute;
    bottom: 0.4rem;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 600;
  }
</style>
