<template>
  <CommonDrawer title="活动商品" @close="emits('close')">
    <div class="h-[40vh] px-4 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="text-gray-400 text-sm flex justify-center pb-4 sku-list-box" v-if="skuList.length || data?.length">
        <div class="grid grid-cols-2 gap-2">
          <div v-for="(item, index) in skuList.length ? skuList : data" class="bg-white py-2 px-3.5" :key="index" @click="gotoSkuPage(item.skuId)">
            <div class="flex justify-center">
              <img class="w-32 h-32" :src="item.skuMainPicture" alt="" />
            </div>
            <div class="text-xs mt-5 lz-multi-ellipsis--l2" v-text="item.skuName"></div>
            <div class="text-red-500 text-xs mt-3">¥ <span v-text="item.jdPrice"></span></div>
          </div>
          <!-- <div class="w-[6.34rem] text-gray-400 text-xs text-center my-1" v-if="skuList.length > 4 || data.length > 4">—— 没有更多了 ——</div> -->
        </div>
        <div class="load-more" @click="handleLoadMore">加载更多</div>
      </div>
      <div v-else class="text-gray-400 text-sm h-[80%] flex justify-center items-center">
        {{ isPreview && isAllOrderSku ? '您当前选择的是全部商品，活动开始时将跳转至店铺首页' : '暂无活动商品哦～' }}
      </div>
    </div>
  </CommonDrawer>
</template>

<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import { gotoSkuPage } from '@/utils/platforms/jump';
import { ref, defineEmits, defineProps, watch } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';

const props = defineProps(['data', 'isAllOrderSku']);
const emits = defineEmits(['close']);
const skuList = ref<any[]>([]);
const pageNum = ref(1);

// 获取曝光商品
const getSkuList = async () => {
  const params = {
    pageNum: pageNum.value,
    pageSize: 20,
    type: 1,
  };
  try {
    const { data } = await httpRequest.post('/80096/getExposureSku', params);
    console.log(data);
    skuList.value = data.records as any[];
  } catch (error: any) {
    console.error(error);
  }
};
// 加载更多
const handleLoadMore = async () => {
  if (isPreview) {
    showToast('活动预览，仅供查看');
    return;
  }
  pageNum.value++;
  const params = {
    pageNum: pageNum.value,
    pageSize: 20,
    type: 1,
  };
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 2000,
    overlay: true,
  });
  try {
    const { data } = await httpRequest.post('/80096/getExposureSku', params);
    if (data.records.length === 0) {
      showToast({
        message: '没有更多数据了',
        duration: 2000,
      });
      return;
    }
    skuList.value.push(...data.records);
    closeToast();
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
};
!isPreview && getSkuList();
</script>

<style scoped lang="scss">
.title {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
}
.sku-list-box {
  position: relative;
  padding-bottom: 1rem;
  .load-more {
    width: 2rem;
    height: 0.4rem;
    line-height: 0.4rem;
    text-align: center;
    background: linear-gradient(to right, #ff1f53, #ffd102);
    border-radius: 0.2rem;
    color: white;
    position: absolute;
    bottom: 0.24rem;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 600;
  }
}
</style>
