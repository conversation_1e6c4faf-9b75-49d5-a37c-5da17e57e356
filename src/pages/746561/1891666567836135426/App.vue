<template>
  <div id="container">
    <div class="kv">
      <img class="kv-img" :src="UIconfig?.kv"/>
      <div class="right-btn" @click="scrollToRule"></div>
      <div class="right-btn" style="top: 0.73rem;" @click="myPrize = true"></div>
      <div class="bottom-btn" @click="drawFn"></div>
    </div>
    <div class="link-box">
      <img class="link-img" :src="UIconfig?.linkImg" @click="openLink" :style="{height: `${ UIconfig?.linkImgHeight / 100 }rem`}"/>
    </div>
    <div class="rule-box" id="ruleView">
      <div class="rule-content" v-html="ruleFormat(rules)"></div>
    </div>
    <VanPopup teleport="body" v-model:show="memberPopup" position="center" :close-on-click-overlay="isCloseOverlay">
      <div class="popup-view">
        <div class="popup-title">亲爱的用户</div>
        <div class="popup-content">您还不是本店的会员<br>加入会员后再来参与吧</div>
        <img class="popup-img" :src="UIconfig?.productImg"/>
        <div class="popup-bottom-btn" @click="openCard">立即入会</div>
      </div>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="failPopup" position="center" :close-on-click-overlay="isCloseOverlay">
      <div class="popup-view">
        <div class="popup-title">亲爱的用户，很抱歉</div>
        <div class="popup-content">您活动前已购买过旺玥奶粉<br>不符合参与活动条件</div>
        <img class="popup-img" :src="UIconfig?.productImg"/>
        <div class="popup-bottom-btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
      </div>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="successPopup" position="center" :close-on-click-overlay="isCloseOverlay">
      <div class="popup-view">
        <div class="popup-title">亲爱的用户</div>
        <div class="popup-content" style="font-size: 0.48rem;">恭喜您领券成功，快去购买吧</div>
        <img class="popup-img" :src="UIconfig?.productImg"/>
        <div v-if="drawStatus === 1" class="popup-bottom-btn" style="filter: grayscale(1);">已领取</div>
        <div v-else class="popup-bottom-btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
      </div>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="myPrize" position="center" :close-on-click-overlay="isCloseOverlay">
      <div class="myprize-popup-view">
        <div class="my-prize-content" v-if="giftInfos?.length">
          <div class="my-prize-item" v-for="(item, index) in giftInfos" :key="index">
            <div style="flex: 1;">{{ dayjs(item?.createTime).format('YYYY年MM月DD日') }}</div>
            <div style="flex: 1;">{{ item.rightsName }}</div>
            <div style="flex: 1;">{{ UIconfig?.prizeTips }}</div>
          </div>
        </div>
        <div style="margin-top: 2rem;" v-else>暂无奖品信息</div>
      </div>
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
/* eslint-disable */
/* 快捷打开项目命令  npm run serve src/pages/1000014803/1863500219271974914 */
/* 测试链接 https://lzkjdz-isv.isvjcloud.com/test/cc/custom/1000014803/1863500219271974914/ */

import { inject, shallowRef, provide, computed, ref, onMounted } from 'vue';
import { showLoadingToast, showToast, closeToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { ruleFormat, formatDate, shareUuid, shareWork } from './common';
import { gotoShopPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const isCloseOverlay = ref(true);
const drawStatus = ref(0);
const memberPopup = ref(false);
const failPopup = ref(false);
const successPopup = ref(false);
const myPrize = ref(false);
const giftInfos = ref([]);
const getFirstbuy = async () => {
  const { data, message } = await httpRequest.post('/friso/firstbuy/activityInfo', {});
  drawStatus.value = data.status;
}
const getMyPrize = async () => {
  const { data, message } = await httpRequest.post('/friso/firstbuy/myPrize', {});
  giftInfos.value = data;
}
const scrollToRule = () => {
  document.getElementById('ruleView')?.scrollIntoView(true)
};
const rules = ref('');
const getRule = async () => {
  try {
    const res = await httpRequest.get('/common/getRule');
    rules.value = res.data;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
const openCard = () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(window.location.href)}`;
};
const UIconfig = ref();
const getActUIConfig = async () => {
  try {
    const { data } = await httpRequest.post('/common/getActivityConfig');
    UIconfig.value = JSON.parse(data);
    console.log(UIconfig.value);
  } catch (error: any) {
    console.error(error);
  }
  return '';
};
const openLink = () => {
  window.location.href = UIconfig.value.linkUrl;
};
const drawFn = async () => {
  if (baseUserInfo.level == 0) {
    memberPopup.value = true;
    return;
  }
  if (drawStatus.value == 1) {
    successPopup.value = true;
    return;
  }
  if (drawStatus.value == 2) {
    failPopup.value = true;
    return;
  }
  if (drawStatus.value == 3) {
    try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data, message } = await httpRequest.post('/friso/firstbuy/receiveGift', {});
    closeToast();
    if (data) {
      successPopup.value = true;
      getFirstbuy();
      getMyPrize();
    } else {
      showToast(message)
    }
    } catch (error:any) {
      closeToast();
      if (error.message) {
        showToast(error.message);
      }
    }
  }
}
onMounted(() => {
  if (baseUserInfo.level == 0) {
    memberPopup.value = true;
  }
  getActUIConfig();
  getFirstbuy();
  getRule();
  getMyPrize();
});
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'vivoSans';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZRUIZHJW/FZRUIZHJW_0.TTF') format('TTF'),
  url('https://lzcdn.dianpusoft.cn/fonts/FZRUIZHJW/FZRUIZHJW_CU.TTF') format('TTF');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
#container {
  width: 100vw;
  min-height: 100vh;
  max-width: 100vw;
  line-height: 1;
  font-family: vivoSans;
  overflow-x: hidden;
  background-color: #f7ebbf;
}
.kv {
  width: 7.5rem;
  height: auto;
  margin-bottom: 0.3rem;
  position: relative;
}
.kv-img {
  width: 7.5rem;
}
.right-btn {
  width: 0.78rem;
  height: 0.36rem;
  position: absolute;
  left: 0;
  top: 0.3rem;
}
.bottom-btn {
  width: 7.5rem;
  height: 1.35rem;
  position: absolute;
  left: 0;
  bottom: 0;
}
.link-box {
  width: 7.5rem;
  height: auto;
  margin-bottom: 0.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.link-img {
  height: 100%;
}
.rule-box {
  width: 7.5rem;
  height: 14.22rem;
  margin-bottom: 0.3rem;
  background-image: url(https://img20.360buyimg.com/imgzone/jfs/t1/267530/38/21266/316491/67b404ddFc656c594/08f37cfea30e5b2c.png);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  box-sizing: border-box;
  padding-top: 1.1rem;
  padding-left: 0.55rem;
  padding-right: 0.55rem;
  color: #014597;
  font-size: 0.24rem;
  line-height: 0.45rem;
  .rule-content {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
  }
}
.popup-view {
  width: 7.5rem;
  height: 8.5rem;
  background-image: url(https://img20.360buyimg.com/imgzone/jfs/t1/255925/1/21858/48863/67b404dfFb0c58e00/a76ef524f0397100.png);
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  position: relative;
  box-sizing: border-box;
  padding-top: 0.65rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.popup-title {
  width: auto;
  min-width: 3.48rem;
  margin-bottom: 0.35rem;
  height: 0.74rem;
  border-radius: 0.37rem;
  background-color: #ff2c1d;
  line-height: 0.74rem;
  text-align: center;
  color: #fff;
  font-weight: bold;
  font-size: 0.47rem;
  box-sizing: border-box;
  padding-left: 0.4rem;
  padding-right: 0.4rem;
}
.popup-content {
  width: 100%;
  height: 1.6rem;
  font-size: 0.55rem;
  font-weight: bold;
  line-height: 0.64rem;
  text-align: center;
  color: #a77736;
}
.popup-img {
  width: 4rem;
}
.popup-bottom-btn {
  width: 3.48rem;
  height: 0.74rem;
  border-radius: 0.37rem;
  background-color: #ff2c1d;
  line-height: 0.74rem;
  text-align: center;
  color: #fff;
  font-weight: bold;
  font-size: 0.47rem;
  box-sizing: border-box;
  padding-left: 0.4rem;
  padding-right: 0.4rem;
}
.myprize-popup-view {
  width: 7.5rem;
  height: 8.5rem;
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/254297/30/22367/14900/67b44032F4e0744ce/8442f4d5a66fb6e3.png);
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  position: relative;
  box-sizing: border-box;
  padding-top: 2.05rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.my-prize-content {
  width: 6.66rem;
  height: 5.66rem;
  background-size: cover;
  background-image: url(https://img20.360buyimg.com/imgzone/jfs/t1/253308/16/22618/3036/67b44039F09dbe326/ad8b29b70f0c588b.png);
  background-repeat: no-repeat;
  background-position: center;
}
.my-prize-item {
  width: 6.66rem;
  height: 0.55rem;
  line-height: 0.55rem;
  display: flex;
  font-size: 0.25rem;
  text-align: center;
}
</style>

<style lang="scss">
/* 隐藏Webkit内核浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}

// 禁止页面回弹行为
html,
body {
  overscroll-behavior-y: none;
  overscroll-behavior-x: none;
}

.van-popup {
  width: 7.5rem;
  background: none;
  overflow-y: unset;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.van-popup--center {
  max-width: 100%;
}
.add-btn {
  width: 1.38rem;
  height: 1.36rem;
  position: fixed;
  right: 0;
  bottom: 1.5rem;
}
.bottom-tab-view {
  width: 7.5rem;
  height: 1.32rem;
  background: #fff;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
  position: fixed;
  left: 0;
  bottom: 0;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/253761/40/8156/6692/67775ce8Ff34cdcaf/977461fdf93fd06f.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  display: flex;
}
</style>
