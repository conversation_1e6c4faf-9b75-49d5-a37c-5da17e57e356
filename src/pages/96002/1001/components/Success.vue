<template>
  <div class="join-bg">
    <div class="star">
      <img :src="getStar(prize.level)" alt="">
    </div>
    <div class="prizeImg">
      <img :src="prize.prizeImg" alt="">
    </div>
    <div class="toFinishInfo" v-click-track="'ljxd'" @click="gotoSkuPage(prize.skuId)"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject } from 'vue';
import {BaseInfo} from '@/types/BaseInfo';
import { gotoSkuPage } from '@/utils/platforms/jump';
const baseInfo = inject('baseInfo') as BaseInfo;

const props = defineProps(['prize']);
const starList = ref([
  {
    level: 2,
    pic: '//img10.360buyimg.com/imgzone/jfs/t1/284138/25/27514/8357/680f2330F7ea5ca93/d64c2124b953f5c8.png',
  },
  {
    level: 3,
    pic: '//img10.360buyimg.com/imgzone/jfs/t1/295014/4/813/7928/680f2331F66f9d93d/caf95ed32e0c860f.png',
  },
  {
    level: 4,
    pic: '//img10.360buyimg.com/imgzone/jfs/t1/234002/23/31756/7582/680f2331F16f853c3/5763631d3dd0b8e9.png',
  },
  {
    level: 5,
    pic: '//img10.360buyimg.com/imgzone/jfs/t1/301751/31/305/6773/680f2332Fe87fb7d1/539acd953f4e98f9.png',
  },
]);

const emits = defineEmits(['close']);

const getStar = (level: number) => {
  return starList.value.find((item) => item.level === level)?.pic;
};

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.join-bg {
  width: 6.5rem;
  height: 8rem;
  margin: 0 auto;
  text-align: center;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/300306/5/309/38245/680f14e5Fc28dadd7/88fddbf4509bf707.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 0.15rem 0.5rem 0;

  .star{
    width: 0.79rem;
    margin: 0 auto 1rem;
  }

  .prizeImg {
    width: 5.42rem;
    margin: 0 auto;
    img{
      width: 100%;
    }
  }

  .toFinishInfo{
    width: 1.44rem;
    height: 0.5rem;
    margin: 0.2rem auto 0;
    background-image: url(//img20.360buyimg.com/imgzone/jfs/t1/279171/25/27757/1650/680f26a3F1cc2d8d7/c21b98ba2ce5127d.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}
</style>
