<template>
  <div class="join-bg">
    <div class="toFinishInfo" v-click-track="'jdgg'" @click="goToShopPage(baseInfo.shopId)"></div>
  </div>
</template>

<script lang="ts" setup>
import {inject} from 'vue';
import {BaseInfo} from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';
const baseInfo = inject('baseInfo') as BaseInfo;

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.join-bg {
  width: 6.5rem;
  height: 8rem;
  margin: 0 auto;
  text-align: center;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/295028/18/675/26029/680ef640F3fecdd9b/29e0d854dca08f7c.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding: 6.8rem 0.5rem 0;

  .toFinishInfo{
    width: 2.8rem;
    height: 0.7rem;
    border-radius: 0.1rem;
    margin: 0.2rem auto;
    //background: #fff;
  }
}
</style>
