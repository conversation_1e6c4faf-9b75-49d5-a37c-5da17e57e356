<template>
  <div v-if="!canGetPrize" class="bg" :style="furnishStyles.pageBg.value">
    <div class="getStartBtn" :style="furnishStyles.getBtnBg.value" @click="getAllData"></div>
  </div>
  <div v-else class="getPrizeBg" :style="furnishStyles.getPrizePageBg.value">
    <div class="rule" v-click-track="'hdgz'" @click="showRulePopup"></div>
    <div class="centerArea">
      <div class="left" @click="left"></div>
      <div class="prize prizeList swiper-container" ref="swiperRef">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item, index) in levelArr" :key="index">
            <div class="prizeImg">
              <img :src="item.mainPrizeImg" alt="" />
            </div>
            <div class="getPrizeBtn" :style="furnishStyles.getBtnBg.value" v-click-track="'ljlq'" @click="exchangePrize(item)"></div>
          </div>
        </div>
      </div>
      <div class="right" @click="right"></div>
    </div>
  </div>
  <!-- 入会弹窗 -->
  <VanPopup teleport="body" v-model:show="showJoin">
    <JoinMember @close="showJoin = false"></JoinMember>
  </VanPopup>
  <!-- 规则 -->
  <VanPopup teleport="body" v-model:show="showRule" >
    <RulePopup :rule="ruleTest" @close="showRule = false"/>
  </VanPopup>
  <!-- 填写信息提示弹窗 -->
  <VanPopup teleport="body" v-model:show="showSorry">
    <Sorry @close="showSorry = false"></Sorry>
  </VanPopup>
  <!-- 已领完 -->
  <VanPopup teleport="body" v-model:show="showNoPrize">
    <NoPrizes @close="showNoPrize = false"></NoPrizes>
  </VanPopup>
  <!-- 领取成功 -->
  <VanPopup teleport="body" v-model:show="showSucceed">
    <Success @close="showSucceed = false" :prize="awardPrize"></Success>
  </VanPopup>
</template>

<script setup lang="ts">
import {ref, inject, onMounted, nextTick } from 'vue';
import furnishStyles, { furnish, Prize } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import JoinMember from '../components/JoinMember.vue';
import Sorry from '../components/Sorry.vue';
import RulePopup from '../components/RulePopup.vue';
import NoPrizes from '../components/NoPrizes.vue';
import Success from '../components/Success.vue';
import Swiper from "swiper";

const levelArr = ref([
  {
    name: '银卡会员',
    value: 2,
    gradeValue: 2,
    prizeId: '',
    skuId: '',
    prizeType: 11,
    prizeImg: '',
    mainPrizeImg: furnish.silverCardPrizesImg,
    taskId: '',
  },
  {
    name: '金卡会员',
    value: 3,
    gradeValue: 3,
    prizeId: '',
    skuId: '',
    prizeType: 11,
    prizeImg: '',
    mainPrizeImg: furnish.goldCardPrizesImg,
    taskId: '',
  },
  {
    name: '铂金卡会员',
    value: 4,
    gradeValue: 4,
    prizeId: '',
    skuId: '',
    prizeType: 11,
    prizeImg: '',
    mainPrizeImg: furnish.platinumCardPrizesImg,
    taskId: '',
  },
  {
    name: '白钻卡会员',
    value: 5,
    gradeValue: 5,
    prizeId: '',
    skuId: '',
    prizeType: 11,
    prizeImg: '',
    mainPrizeImg: furnish.diamondCardPrizesImg,
    taskId: '',
  },
]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error: any) {
    console.log(error.message);
  }
};

const taskRequestInfo = ref(
  {
    infoCheck: false, // 信息留存校验是否通过
    level: '', //等级
    memberCheck: false, // 会员信息校验是否通过
  },
);
const prizeInfos = ref<Prize[]>([]); // 奖品信息
// 等级是否可以领取奖品
const canGetPrize = ref(false);

// 存储当前选择的奖品数据
const selectItemData = ref<any>(null);

const isEnd = ref(false);
const endTime = ref(0);

const getTime = async () => {
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > endTime.value) {
    isEnd.value = true;
  }
};
// 入会弹窗
const showJoin = ref(false);
// 规则弹窗
const showRule = ref(false);
const ruleTest = ref('');
// 未完善信息弹窗
const showSorry = ref(false);
// 已领完
const showNoPrize = ref(false);
// 领取成功
const showSucceed = ref(false);
const awardPrize = ref(
  {
    prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/272371/20/28839/56458/680f2344Fb141a38f/8a7ded4f08aa16cc.png',
    skuId: '',
    level: 2,
  }
);
// 获取页面数据
const getAllData = async () => {
  try {
    const { data } = await httpRequest.post('/96002/activityInfo');
    taskRequestInfo.value.level = data.level;
    taskRequestInfo.value.infoCheck = data.infoCheck;
    prizeInfos.value = data.prizeInfos;
    levelArr.value.forEach((item) => {
      if (item.gradeValue === 2) {
        item.mainPrizeImg = furnish.silverCardPrizesImg;
      } else if (item.gradeValue === 3) {
        item.mainPrizeImg = furnish.goldCardPrizesImg;
      } else if (item.gradeValue === 4) {
        item.mainPrizeImg = furnish.platinumCardPrizesImg;
      } else if (item.gradeValue === 5) {
        item.mainPrizeImg = furnish.diamondCardPrizesImg;
      }
      prizeInfos.value.forEach((prizeItem) => {
        if (item.gradeValue === Number(prizeItem.prizeLevel)) {
          item.prizeId = prizeItem.prizeId;
          item.prizeImg = prizeItem.prizeImg;
          item.taskId = prizeItem.taskId;
        }
      });
    });
    // 校验通过，留存过信息
    if (taskRequestInfo.value.infoCheck){
      // 展示各个等级领奖页面
      canGetPrize.value = true;
      // 确保在切换到奖品页面后重新初始化Swiper
      nextTick(() => {
        setTimeout(() => {
          initSwiper();
        }, 300);
      });
    } else {
      showSorry.value = true;
    }
    closeToast();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

const swiperRef = ref();
let mySwiper: Swiper | null = null;
let swiperInitialized = ref(false);

const initSwiper = () => {
  try {
    // 使用类选择器查找 Swiper 容器
    const swiperContainer = document.querySelector('.swiper-container');
    if (swiperContainer) {
      // 如果已经有 Swiper 实例，先销毁它
      if (mySwiper) {
        mySwiper.destroy(true, true);
        mySwiper = null;
      }
      // 创建新的 Swiper 实例
      mySwiper = new Swiper('.swiper-container', {
        allowTouchMove: true,
        direction: 'horizontal',
        slidesPerView: 1,
        spaceBetween: 10,
        centeredSlides: true,
      });
      swiperInitialized.value = true;
    } else {
      console.warn('Swiper container not found');
    }
  } catch (error) {
    console.log('Failed to initialize Swiper:', error);
  }
};

onMounted(() => {
  nextTick(() => {
    initSwiper();
  });
});

const left = () => {
  if (mySwiper) {
    mySwiper.slidePrev();
  }
};

const right = () => {
  if (mySwiper) {
    mySwiper.slideNext();
  }
};

// 兑换奖品
const exchangePrize = async (itemData: any) => {
  console.log(itemData, '领取奖品');
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/96002/sendPrize', {
      activityPrizeId: itemData.value.prizeId,
      taskId: itemData.taskId,
    });
    closeToast();
    console.log(data, '调用领取奖品接口');
    if (data.status === 0) {
      selectItemData.value = itemData;
      showSucceed.value = true;
    } else {
      showToast('领取失败');
    }
  } catch (error) {
    if (error.message === '手慢啦！奖品已领光~') {
      showNoPrize.value = true;
    } else {
      showToast(error.message);
    }
  }
};

const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  if (baseInfo.thresholdResponseList.length && baseInfo.thresholdResponseList[0].thresholdCode === 4) {
    showJoin.value = true;
    console.log('您不是会员');
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await getTime();
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};
init();
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  padding: 13rem 0 1.5rem 0;
  .getStartBtn {
    background-size: 100%;
    background-repeat: no-repeat;
    width: 3.7rem;
    height: 0.94rem;
    margin: 0 auto;
  }
}
.getPrizeBg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  padding: 4rem 0 2rem 0;
  .rule {
    width:1.37rem;
    height: 0.41rem;
    background-size: 100%;
    background-repeat: no-repeat;
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/285232/39/26637/2573/680f13d9F056c0b7e/626e9876e79a4143.png");
    position: absolute;
    top: 1rem;
    right: 0rem;
  }
  .centerArea {
    display: flex;
    height: 8rem;
    .left{
      width: 0.46rem;
      height: 0.46rem;
      background-size: 100%;
      background-repeat: no-repeat;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/271297/22/27713/1496/680e0188F285d46d6/39b8be173fe5d8e0.png");
      display: flex;
      align-items: center;
      position: relative;
      top: 3.46rem;
      transform:translateY(50%);
      margin: 0 0.05rem 0 0.1rem;
      z-index: 10;
      cursor: pointer;
    }
    .swiper-slide {
      flex-shrink: 0;
      position: relative;
      transition-property: transform;
      width: 6.9rem;
      //display: flex;
      flex-wrap: wrap;
      //justify-content: flex-start;
      //gap: 0.2rem;
      align-content: flex-start;
    }

    .right {
      width: 0.46rem;
      height: 0.46rem;
      background-size: 100%;
      background-repeat: no-repeat;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/294153/14/666/1430/680e0188F150d6683/f4172d7ac2c93c42.png");
      display: flex;
      align-items: center;
      position: relative;
      top: 3.46rem;
      transform:translateY(50%);
      margin: 0 0.1rem 0 0.05rem;
      z-index: 10;
      cursor: pointer;
    }
    .prize {
      width: 5.4rem;
      min-height: 10rem;
      margin: 0 auto;
      overflow: hidden;

      .swiper-container {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .swiper-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        transition-property: transform;
      }

      .swiper-slide {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        //justify-content: center;
        flex-shrink: 0;
      }

      .prizeImg{
        width: 5.4rem;
        height: 8rem;
        img{
          width: 100%;
        }
      }
      .getPrizeBtn {
        width: 3.7rem;
        height: 0.94rem;
        background-size: 100%;
        background-repeat: no-repeat;
        margin: 0.6rem auto 0;
      }
    }
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
