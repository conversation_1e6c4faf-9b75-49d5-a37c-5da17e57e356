<template>
  <div class="rule-bk">
    <div class="tipDiv2" :style="{'backgroundImage': 'url(' + furnish.submitSuccPopBg + ')' }">
      <div class="btnDiv2">
        <div class="knowBtn" @click="close">知道了</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>

import { furnish } from '../ts/furnishStyles';

const emits = defineEmits(['close']);

// 我知道了和关闭
const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  .tipDiv2{
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/292279/15/2735/34501/6819e3c0Fdc0f9260/a8d5eb59a69d07e3.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 5.86rem;
    height: 5.90rem;
    position: relative;
    .btnDiv2{
      display: flex;
      justify-content: center;
      position: absolute;
      bottom: 0.9rem;
      font-size: 0;
      width: 100%;
      .knowBtn{
        //background-color: black;
        width: 2.73rem;
        height: 0.74rem;
      }
    }
  }
}
</style>
