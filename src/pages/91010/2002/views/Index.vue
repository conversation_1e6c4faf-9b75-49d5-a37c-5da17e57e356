<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg ?? '//img10.360buyimg.com/imgzone/jfs/t1/300105/4/3168/197302/6819eec7F12897f35/82f39826ccf67c39.png'" alt="" class="kv-img"/>
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="{'backgroundImage': 'url(' + furnish.ruleBtnBg + ')' }" @click="showReviewResultPop = true"><div></div></div>
          <div class="header-btn" :style="{'backgroundImage': 'url(' + furnish.myPrizeBg  + ')'}" @click="showMyPrize = true"><div></div></div>
        </div>
      </div>
    </div>
    <div class="applyTitleDiv" :style="{'backgroundImage': 'url(' + furnish.applyTitleBg + ')' }"></div>
    <div class="centerDivAll">
      <div class="uploadDivAll" :style="{'backgroundImage': 'url(' + furnish.infoUploadBg + ')' }">
        <div class="limitDiv">本活动每日限量{{examineLimit}}位登记，先到先得</div>
        <div class="submitDiv" v-if="status === 1" @click="goToFileClick(status)">去填写</div>
         <div class="submitDiv1" v-else @click="goToFileClick(status)">去查看</div>
      </div>
      <div class="skuListTitle" :style="{'backgroundImage': 'url(' + furnish.stepTwpTitleBg + ')' }"></div>
      <div class="skuListDivAll">
        <div class="topBg" :style="{'backgroundImage': 'url(' + furnish.stepTwpTopBG + ')' }"></div>
        <div class="skuListDiv" :style="{'backgroundImage': 'url(' + furnish.stepTwpCenterBG + ')' }">
          <div class="skuItemDiv" v-for="(item,index) in furnish.skuListDeco" :key="index">
            <img v-if="item.skuImg" :src="item.skuImg" alt="" />
          </div>
        </div>
        <div class="bottomBg" :style="{'backgroundImage': 'url(' + furnish.stepTwpBottomBG + ')' }"></div>
      </div>
      <div class="promptDiv">
        <img :src="furnish.infoTipBg" alt="" />
      </div>
    </div>
    <div class="ruleDivAll">
      <img :src="furnish.ruleBg" alt="" />
    </div>
  </div>

  <div>
    <!-- 聚合弹窗活动门槛 -->
    <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />

    <!-- 非会员 -->
    <VanPopup teleport="body" v-model:show="showToJoinVip" position="center" :close-on-click-overlay="closeOnClickOverlay">
      <ToJoinDialog @close="showToJoinVip=false"></ToJoinDialog>
    </VanPopup>
    <!--  住址选择-->
    <van-popup v-model:show="showArea" position="bottom">
      <van-area :area-list="areaList" @confirm="changeAreaConfirm" @cancel="showArea = false" />
    </van-popup>
    <!-- 填写档案-->
    <FilePop v-if="showFilePop" :show="showFilePop" :uploadImg="uploadImg" :isCanSubImg="isCanSubImg" :isCanSubParentsImg="isCanSubParentsImg" :isCanSubVideo="isCanSubVideo" :fileType="fileType" @submitInfo="submitInfo" @close="showFilePop=false" />
    <!--审核进度-->
    <van-popup v-model:show="showReviewTip" position="center">
      <ReviewTipPop :errMessage="errMessage" :popType="popType" @close="showReviewTip=false" />
    </van-popup>
    <!-- 提交成功提示-->
    <van-popup v-model:show="showInfoSuccessTipPop" position="center" @click-overlay="infoSuccessTipClickOverlay">
      <InfoSuccessTipPop @close="submitSuccessclose"/>
    </van-popup>
    <!-- 审核结果弹窗 showReviewResultPop-->
    <van-popup v-model:show="showReviewResultPop" position="center" >
      <ReviewResultPop v-if="showReviewResultPop" @close="showReviewResultPop = false" />
    </van-popup>
    <!-- 我的奖品-->
    <van-popup v-model:show="showMyPrize" position="center" >
      <MyPrizePop v-if="showMyPrize" @close="showMyPrize = false" />
    </van-popup>
  </div>

</template>

<script setup lang="ts">
import { ref, reactive, inject, computed } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast, Toast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { areaList } from '@vant/area-data';
import dayjs from 'dayjs';
import ToJoinDialog from '../components/ToJoinDialog.vue';
import useThreshold from '@/hooks/useThreshold';
import '../Threshold2/CPBStyle.scss';
import Threshold2 from '../Threshold2/ThresholdCPB.vue';
import FilePop from '../components/filePop.vue';
import ReviewTipPop from '../components/reviewTip.vue';
import InfoSuccessTipPop from '../components/InfoSuccessTipPop.vue';
import ReviewResultPop from '../components/ReviewResult.vue';
import MyPrizePop from '../components/MyPrize.vue';
Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const ruleTest = ref('');
const showToJoinVip = ref(false);
// 展示门槛显示弹框
const showLimit = ref(false);
const openCardArr = baseInfo.thresholdResponseList.filter((item) => item.type === 1);
if (openCardArr.length > 0) {
  showToJoinVip.value = true;
} else {
  showLimit.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList.filter((item) => item.type !== 1),
    className: 'common-message-cpb',
  });
}
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
const showDatePicker = ref(false); // 生日日期选择弹窗
const showReviewTip = ref(false); // 审核进度弹窗
const showMyPrize = ref(false); // 我的奖品
const showArea = ref(false); // 地址
const fileType = ref(1); // 1 填写档案 2 查看档案
const showFilePop = ref(false); // 是否显示档案弹窗
const showInfoSuccessTipPop = ref(false); // 是否显示档案信息提交成功弹窗
const popType = ref(1); // 1 提交注意  2 审核进度(审核中/未通过)  3审核通过
const errMessage = ref(''); // 审核未通过的原因
const showReviewResultPop = ref(false); // 是否显示审核结果
const changeDate = () => {
  showDatePicker.value = true;
};
// 住址修改
const area = ref('');
const changeAreaConfirm = ({ selectedOptions }: any) => {
  showArea.value = false;
  area.value = selectedOptions.map((item: any) => item.text).join('/');
};
const orderStatus = ref(0); // 订单审核状态 1待审核 2已审核 3审核未通过
const status = ref(0); // 1待提交 2待审核 3审核通过 4图片未通过
const uploadImg = ref(''); // 是否上传出生证明0否1是
const examineLimit = ref(0); // 每日客服审批限制
const isCanSubImg = ref(false); // 是否需要提交出生证明照片
const isCanSubParentsImg = ref(false); // 是否需要提交手持出生证明照片
const isCanSubVideo = ref(false); // 是否需要提交手持出生证明视频

const getActivity = async () => {
  try {
    const { data } = await httpRequest.post('/91010/activityInfo');
    examineLimit.value = data.examineLimit;
    uploadImg.value = data.uploadImg;
    status.value = data.status;
    orderStatus.value = data.orderStatus;
    if (data.uploadImg) {
      isCanSubImg.value = data.image;
      isCanSubParentsImg.value = data.parentsImg;
      isCanSubVideo.value = data.video;
    }
    // 1 提交注意  2 审核进度(审核中/未通过/订单审核通过)  3图片审核通过
    if (status.value === 2) {
      // 图片待审核
      errMessage.value = '您的信息我们已收到，正在飞速审核中，请耐心等待 '
      popType.value = 2;
      showReviewTip.value = true;
    } else if (orderStatus.value === 3) {
      // 订单审核未通过
      popType.value = 4;
      showReviewTip.value = true;
    } else if (status.value === 3 && orderStatus.value === 1) {
      // 图片审核通过，订单待审核
      popType.value = 3;
      showReviewTip.value = true;
    } else if (orderStatus.value === 2) {
      // 订单审核通过
      errMessage.value = ' 恭喜您获得新客礼赠   我们会在7-30个工作日内   将礼品发出 请在首页右上角—我的奖品中查看 ';
      popType.value = 2;
      showReviewTip.value = true;
    }
  } catch (error) {
    console.error(error);
  }
};
const submitInfo = async (submitDatas) => {
  console.log(submitDatas, '提交档案成功');
  // 提交档案信息成功关闭档案信息填写 打开提交成功提示弹窗
  await getActivity();
  showFilePop.value = false;
  // showInfoSuccessTipPop.value = true;
};
// 提交档案信息成功的弹窗关闭
const submitSuccessclose = async () => {
  showInfoSuccessTipPop.value = false;
  await getActivity();
};
// 点击提交档案信息成功的弹窗外部事件
const infoSuccessTipClickOverlay = () => {
  submitSuccessclose();
};

// 填写信息档案 status 1去填写 2去查看
const goToFileClick = (status) => {
  console.log(status, '去填写');
  const openCardArr = baseInfo.thresholdResponseList.filter((item) => item.type === 1);
  if (openCardArr.length > 0) {
    showToJoinVip.value = true;
    return;
  }
  const openCardArr1 = baseInfo.thresholdResponseList.filter((item) => item.type !== 1);
  if (baseInfo.thresholdResponseList.length > 0 && openCardArr1.length > 0) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList.filter((item) => item.type !== 1),
      className: 'common-message-cpb',
    });
    return;
  }
  if (status === 1) {
    fileType.value = 1;
  } else {
    fileType.value = 2;
  }
  showFilePop.value = true;
};
const init = async () => {
  console.log('init=========');
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    // getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivity()]);
    closeToast();
    // if (baseInfo.status === 1) {
    //   setTimeout(() => {
    //     // showToast('活动未开始');
    //   }, 1000);
    //   closeToast();
    //   return;
    // }
    // if (baseInfo.status === 3) {
    //   setTimeout(() => {
    //     // showToast('活动已结束');
    //   }, 1000);
    //   closeToast();
    //   return;
    // }
  } catch (error) {
    closeToast();
  }
};

init();
</script>

<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.5rem;
  //padding-top:2.90rem;
  background-color: #ecd7ba;
}
.header-kv {
  position: relative;
  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 3.2rem 0rem 0;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0;
  }

  .header-btn {
    width: 0.46rem;
    height: 1.21rem;
    cursor: pointer;
    background-size: 100%;
    background-repeat: no-repeat;
  }
}
.applyTitleDiv{
  position: relative;
  z-index: 1;
  width: 7.48rem;
  height: 0.45rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/299413/23/1709/5858/6819dbcdF14e4f205/47837ffe41eb7429.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-left: calc(50% - 7.48rem / 2);
}
.centerDivAll{
  //background-color: #fff;
  //border: 0.02rem solid #dbaf71;
  //width: 7.24rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  //margin-left: calc(50% - 7.24rem / 2);
  padding-bottom: 0.18rem;
  .uploadDivAll{
    width: 7.50rem;
    height: 3.26rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/287771/21/3527/50192/6819dbcdF5856c3ef/08c5c9bf9c71d875.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-top: 0.18rem;
    display: flex;
    justify-content: center;
    position: relative;
    .limitDiv{
      position: absolute;
      bottom: 0.9rem;
      text-align: center;
      font-size: 0.16rem;
      color: #5d3000;
    }
    .submitDiv{
      width: 2.54rem;
      height: 0.55rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/300494/11/3116/3210/6819dbceFf3b0fa9c/8b353b7ea51ddd66.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      font-size: 0;
      position: absolute;
      bottom: 0.28rem;
    }
    .submitDiv1{
      width: 2.54rem;
      height: 0.55rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/280788/39/29137/3242/6819dbcfF88ca5514/d660e8091400aaea.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      font-size: 0;
      position: absolute;
      bottom: 0.28rem;
    }
  }
  .skuListTitle{
    width:6.49rem;
    height: 2.02rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/286660/15/3418/22538/6819dbccF92492bda/e7df5edfaeb0454a.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-bottom: -1.2rem;
    position: relative;
    z-index: 10;
    margin-top: 0.36rem;
  }
  .skuListDivAll{
    padding-bottom: 0.18rem;
    margin-top:0.12rem;
    width: 7.23rem;
    position: relative;
    min-height: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .topBg{
      width: 7.23rem;
      height:1.3rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/297944/7/3771/4437/6819fa23Fe750bf85/edd7cd10447ccf84.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .skuListDiv{
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 7.23rem;
      background-size: 100%;
      background-repeat: repeat-y;
      .skuItemDiv{
        width: 6.50rem;
        height: 1.90rem;
        //background-color: #fff;
        margin-bottom: 0.1rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .bottomBg{
      width: 7.23rem;
      height:0.85rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/299918/7/3240/4616/6819fa24F6905b79e/5f9feccaa8c6c6ef.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .promptDiv{
    width: 7.50rem;
    //height: 19.45rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/286120/2/2596/270182/6819dbceF19d27002/d19df397f34e9860.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-top: 0.22rem;
    img {
      width: 100%;
    }
  }
}
.ruleDivAll{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-repeat: no-repeat;
  background-size: 100%;
  img {
    width:100%;
  }
  //width: 7.32rem;
  //.ruleTitle{
  //  width: 7.32rem;
  //  height: 0.85rem;
  //  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/278453/22/27665/11174/680f6aa1Fb07a609b/7f7027da7c7b29d9.png');
  //  background-size: 100% 100%;
  //  background-repeat: no-repeat;
  //}
  //.ruleContentDiv{
  //  background-color: #ffffff;
  //  border: 0.02rem solid #dbaf71;
  //  width: 7.24rem;
  //  margin-left: calc(50% - 7.24rem / 2);
  //  min-height: 2.64rem;
  //  color: #5d3000;
  //  font-size: 0.24rem;
  //  .ruleTitle1{
  //    width: 4.7rem;
  //    color: #fff;
  //    font-size: 0.24rem;
  //    background-color: #e30606;
  //    padding: 0.22rem 0.38rem;
  //    border-radius: 0 0.24rem 0.42rem 0;
  //  }
  //  .ruleText{
  //    padding: 0.26rem 0.5rem;
  //  }
  //}
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
