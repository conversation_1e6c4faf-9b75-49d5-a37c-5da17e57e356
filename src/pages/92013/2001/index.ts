import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin, { EventTrackParams } from '@/plugins/EventTracking';
import '@/style';
import './style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  urlPattern: '/custom/:activityType/:templateCode',
};

const _decoData = {
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/20475/24/22270/123676/66b96a70F6c206228/273703358748afe8.png',
  pageBg: '',
  successPageBg: '',
  actBgColor: '#f7f2e9',
  introductionImg: '//img20.360buyimg.com/imgzone/jfs/t1/234931/28/11391/6425/659670d3Ff6f96b05/7c3c876bbe4a86b0.png',
  giftIntroductionImg: '//img10.360buyimg.com/imgzone/jfs/t1/225211/29/10887/71900/6597da34Fe76cde9f/b376b6089dea77b7.png',
  ruleImg: '//img10.360buyimg.com/imgzone/jfs/t1/246850/23/1778/6201/65950195F4cfee85f/1f1c75a72eb3f1b8.png',
  recordImg: '//img10.360buyimg.com/imgzone/jfs/t1/243976/27/1652/5851/65950195F57db856c/e759f3e906aa5a8f.png',
  strategyImg: '//img10.360buyimg.com/imgzone/jfs/t1/231169/14/11136/6583/65950195F316a3998/9f10b7377c7c1f4e.png',
  detailsKvImg: '//img10.360buyimg.com/imgzone/jfs/t1/247560/21/16699/121866/66b96a71Fd8aa12a5/a30d198ef2720951.png',
  detailsBgColor: '#f7f2e9',
  strategyPopupImg: '//img10.360buyimg.com/imgzone/jfs/t1/239206/16/2172/26057/659510a8F3349287b/db92ae5bec9d1f42.png',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
};

init(config).then(({ baseInfo, pathParams, decoData }) => {
  // 设置页面title
  document.title = '爱马仕会员首购有礼';
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', baseInfo.activityId === '1817807860742279169' ? _decoData : decoData);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.mount('#app');
});
