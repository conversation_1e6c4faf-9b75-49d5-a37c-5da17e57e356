import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
// const a = {
//   actBg: '//img10.360buyimg.com/imgzone/jfs/t1/25508/27/20479/26035/669a23a0F62bae8d7/3d845b069c2885c9.png',
//   pageBg: '',
//   successPageBg: '',
//   actBgColor: '#f7f2e9',
//   introductionImg: '//img20.360buyimg.com/imgzone/jfs/t1/234931/28/11391/6425/659670d3Ff6f96b05/7c3c876bbe4a86b0.png',
//   giftIntroductionImg: '//img10.360buyimg.com/imgzone/jfs/t1/225211/29/10887/71900/6597da34Fe76cde9f/b376b6089dea77b7.png',
//   ruleImg: '//img10.360buyimg.com/imgzone/jfs/t1/246850/23/1778/6201/65950195F4cfee85f/1f1c75a72eb3f1b8.png',
//   recordImg: '//img10.360buyimg.com/imgzone/jfs/t1/243976/27/1652/5851/65950195F57db856c/e759f3e906aa5a8f.png',
//   strategyImg: '//img10.360buyimg.com/imgzone/jfs/t1/231169/14/11136/6583/65950195F316a3998/9f10b7377c7c1f4e.png',
//   detailsKvImg: '//img10.360buyimg.com/imgzone/jfs/t1/247139/8/15952/21555/669a23a0F85fbbec2/6701784d98360571.png',
//   detailsBgColor: '#f7f2e9',
//   strategyPopupImg: '//img10.360buyimg.com/imgzone/jfs/t1/239206/16/2172/26057/659510a8F3349287b/db92ae5bec9d1f42.png',
//   cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
//   h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
//   mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
// };
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员令牌首购';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
