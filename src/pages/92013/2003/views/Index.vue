<template>
  <!--  申领前-->
  <div class="bg" v-if="!success" :style="furnishStyles.pageBg.value">
    <div class="header-kv" :style="{ backgroundImage: `url(${furnish.actBg})` }">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="shop-name-text" :style="furnishStyles.shopNameColor.value">
        <span v-if="furnish.disableShopName === 1">{{ shopName }}</span>
      </div>
    </div>
    <div class="main" :class="{ 'long-act': longBgAct.some((val) => val === baseInfo.activityId) }">
      <div class="get-btn" @click="getPrizes" v-click-track="'ljlq'">即刻领取</div>
    </div>
  </div>
  <!--申领成功-->
  <div class="bg" v-if="success" :style="furnishStyles.successPageBg.value">
    <div class="header-kv" :style="{ backgroundImage: `url(${furnish.detailsKvImg})` }">
      <img :src="furnish.detailsKvImg" alt="" class="kv-img" />
      <div class="shop-name-text" :style="furnishStyles.shopNameColor.value">
        <span v-if="furnish.disableShopName === 1">{{ shopName }}</span>
      </div>
    </div>
    <div class="main" :class="{ 'long-act': longBgAct.some((val) => val === baseInfo.activityId) }">
      <div class="get-btn" @click="toIndex" v-click-track="'ljlq'">返回首页</div>
    </div>
  </div>
  <!-- 活动门槛 -->
  <ThresholdCPB v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
  <!--  新增门槛-->
  <VanPopup teleport="body" v-model:show="showAddLimit">
    <ThresholdNew @close="showAddLimit = false" :actStartTime="actStartTime" :actEndTime="actEndTime" :fullGiftThreshold="fullGiftThreshold" :maxParticipateNum="maxParticipateNum" :orderStartTime="orderStartTime" />
  </VanPopup>
  <!-- 规则 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!--  领取攻略-->
  <VanPopup teleport="body" v-model:show="showStrategyPopup">
    <StrategyPopup @close="showStrategyPopup = false"></StrategyPopup>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="showOrderRecord">
    <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
  </VanPopup>
</template>
<script setup lang="ts">
import { inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import RulePopup from '../components/RulePopup.vue';
import StrategyPopup from '../components/StrategyPopup.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import ThresholdCPB from '../components/Threshold.vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import useThreshold from '@/hooks/useThreshold';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import '../style/ThresholdStyle.scss';
import ThresholdNew from '../components/ThresholdNew.vue';
import openCard from '@/utils/openCard';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const longBgAct = ref(['1915706101923123202']);

// 是否领取成功
const success = ref(false);
// 是否参与过活动 0 未参与 1 参与成功
const status = ref(0);

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

// 店铺名称
const shopName = ref(baseInfo.shopName);

// // 门槛弹窗
// const showLimit = ref(true);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 中奖弹窗
const showAward = ref(false);
// 我的订单
const showOrderRecord = ref(false);
// 领取攻略
const showStrategyPopup = ref(false);

// 奖品列表
const prizeList = ref({
  prizeId: 0,
  prizeImg: '',
  prizeName: '',
  prizeType: 0,
  remainCount: 0,
  sendTotalCount: 0,
  // 奖品状态 -1 不满足条件 0 未领取 1 领取成功 2 取消报名 3 发放奖奖品 4剩余份数不足
  status: 0,
  receivePrizeId: 0,
});
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);
// 奖品信息
const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  prizeId: '',
  userReceiveRecordId: '',
});

// 展示门槛显示弹框
const showLimit = ref(false);
// 新增的门槛弹窗
const showAddLimit = ref(false);
const actStartTime = ref();
const actEndTime = ref();
const orderStartTime = ref();
const fullGiftThreshold = ref('');
const maxParticipateNum = ref(0);

const checkLimitDialog = async () => {
  if (baseInfo.thresholdResponseList[0].type === 0 && baseInfo.thresholdResponseList[0].thresholdTitle === '有前置订单') {
    showAddLimit.value = true;
  } else if (baseInfo.thresholdResponseList.find((item: any) => item.type === 1)) {
    // 去开卡
    openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
    // window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;`;
  } else {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
      className: 'common-message-cpb',
    });
  }
};

// 活动规则相关
const showRulePopup = async () => {
  try {
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const getRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    ruleTest.value = data;
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

// 领取奖品
const getPrizes = async () => {
  if (baseInfo.thresholdResponseList.length) {
    await checkLimitDialog();
    return;
  }
  try {
    const { data, code } = await httpRequest.post('/92013/sendUser');
    console.log(data, 'ackMessage');
    if (code === 200) {
      status.value = 1;
      success.value = true;
    }
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

// 跳转成功页
const toSuccessPage = async () => {
  if (baseInfo.thresholdResponseList.length) {
    await checkLimitDialog();
    return;
  }
  success.value = true;
};

// 返回首页
const toIndex = () => {
  gotoShopPage(baseInfo.shopId);
};

// 获取曝光商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/92013/skuList');
    skuList.value = data as any[];
  } catch (error) {
    console.error(error);
  }
};

// 获取参与状态
const getStatus = async () => {
  try {
    const { data } = await httpRequest.post('/92013/prize');
    status.value = data;
    if (status.value === 1) {
      success.value = true;
    }
  } catch (error) {
    console.error(error);
  }
};

// 初始化
const init = async () => {
  if (baseInfo.thresholdResponseList.find((item: any) => item.type === 1)) {
    // 去开卡
    openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
    // window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;`;
  }
  if (baseInfo.status !== 2) {
    checkLimitDialog();
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getRule(), getSkuList(), getStatus()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();
</script>
<style></style>
<style lang="scss" scoped>
.bg {
  position: relative;
  background-size: 100%;
  background-repeat: no-repeat;
}

.header-kv {
  // height: 18.8rem;
  position: relative;
  background-size: 100%;
  background-repeat: no-repeat;
  .kv-img {
    width: 100%;
  }
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .btn-list {
    position: absolute;
    top: 0.7rem;
    right: 0;
    img {
      width: 0.47rem;
      margin-bottom: 0.18rem;
    }
  }
  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.main {
  position: absolute;
  left: 0;
  right: 0;
  top: 11.8rem;
  .get-btn {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/297047/23/21385/6800/688b4863F4ca6c661/3b468646de55386d.png');
    //background-color: #444444;
    margin: 0 auto;
    width: 2.1rem;
    height: 0.66rem;
    border-radius: 0.1rem;
    font-size: 0.27rem;
    color: #555;
    line-height: 0.66rem;
    text-align: center;
  }
}
.long-act {
  top: 12.6rem;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
<style>
#footer,
.notice-marquee-context {
  display: none !important;
}
</style>
