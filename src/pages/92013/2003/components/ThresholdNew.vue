<template>
  <div class="bg">
    <div class="content">
      非常抱歉，您未满足入会后首单活动要
      <br />
      求，感谢您对于本活动的支持与关注。
    </div>
    <div class="btn" @click="close">返回首页</div>
    <div class="close-btn" @click="close"></div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from 'vue';
import dayjs from 'dayjs';

const props = defineProps(['orderStartTime', 'actStartTime', 'actEndTime', 'fullGiftThreshold', 'maxParticipateNum', 'show']);
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.bg {
  position: relative;
  //background-color: #f7f2e9;
  //border-radius: 0.3rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/296792/40/12375/33942/688b4ce7F4d965dcb/4bbe073505c73225.png');
  background-size: 100%;
  background-repeat: no-repeat;
  width: 5.5rem;
  margin: 0 auto;
  padding: 2.2rem 0 1.2rem;
  .content {
    text-align: center;
    white-space: pre-line;
    font-size: 0.26rem;
    line-height: 0.44rem;
  }
  .close-btn {
    width: 0.45rem;
    height: 0.45rem;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/226770/28/11313/1145/65969c51Fb9dda6aa/31b3866cf538306e.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}
.btn {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/297047/23/21385/6800/688b4863F4ca6c661/3b468646de55386d.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 0.6rem auto 0 auto;
  width: 2.1rem;
  height: 0.66rem;
  border-radius: 0.1rem;
  font-size: 0.27rem;
  color: #444;
  line-height: 0.66rem;
  text-align: center;
}
</style>
