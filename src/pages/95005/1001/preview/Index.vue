<template>
  <div class="bg" :style="furnishStyles.pageBg.value"  v-if="isLoadingFinish">
    <div class="header-kv select-hover"  >
      <img
        :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'"
        alt=""
        class="kv-img" />
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event">
            {{ btn.name }}
          </div>
        </div>
      </div>
<!--      <div class="draw-btn select-hover"  >-->
<!--        <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart" />-->
<!--      </div>-->
      <div v-if="startTime" class="time-range">领奖时间:{{ startTime }}至{{endTime}}</div>
    </div>
    <div class="orderPrice">活动订单金额:¥0</div>
    <div class="rank">活动排名:未上榜</div>
    <div class="prizeBox">
      <div class="swiper-container" ref="swiperRef" >
        <div class="swiper-wrapper" >
          <div class="swiper-slide" v-for="item in prizeList" :key="item">
           <div class="prizeItem" v-if="item.prizeName">
<!--             <div>消费满{{item.orderPrice || 'XX'}}元解锁</div>-->
             <img :src="item.prizeImg || '//img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png' " alt=""/>
             <div>{{item.prizeName || 'xx积分'}}</div>
             <div class="sign-up-button" :style="furnishStyles.singnStyle.value" @click="toast">领取奖品</div>
           </div>
          </div>
       </div>
        <div v-if="prizeList.length>1" class="swiper-button-prev" @click="prevSwiper"></div>
        <div v-if="prizeList.length>1" class="swiper-button-next" @click="nextSwiper"></div>
      </div>
    </div>
    <div class="reciveBox">暂无记录</div>

    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
     <MyPrize v-if="showMyPrize"  @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" @close="saveAddressClose"></SaveAddress>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, nextTick } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';
import CountDown from '../components/CountDown.vue';
import SaveAddress from '../components/SaveAddress.vue';
import Swiper from 'swiper';
import 'swiper/swiper.min.css';

let mySwiper: Swiper;
const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(true);
const startTime = ref(0);

const isLoadingFinish = ref(false);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
};
const prizeList = ref<any>(defaultStateList);

type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);
const nextStateAmount = ref(0);

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const showGoods = ref(false);
const showAward = ref(false);

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const activityPrizeId = ref('');

const saveAddressClose = (type: any) => {
  if (type) {
    console.log('保存成功');
  }
  showSaveAddress.value = false;
};
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};
const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      showRule.value = true;
    },
  },
  {
    name: '中奖记录',
    event: () => {
      showMyPrize.value = true;
    },
  },
];

const isExposure = ref(1);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showGoods.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};
// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
const prevSwiper = () => {
  mySwiper.slidePrev();
};
const nextSwiper = () => {
  console.log(mySwiper);
  mySwiper.slideNext();
};
// 活动数据监听
registerHandler('activity', (data) => {
  console.log('activity', data);
  document.title = data.activityName;
  endTime.value = dayjs(data.endTime).valueOf();
  if (data.prizeList.length) {
    prizeList.value = data.prizeList;
    nextTick(() => {
      mySwiper.update();
    });
  } else {
    prizeList.value = [];
  }
  if (data.skuList) {
    skuList.value = data.skuList;
  }
  if (data.orderSkuList) {
    orderSkuList.value = data.orderSkuList;
  }
  startTime.value = data.awardStartTime;
  endTime.value = data.awardEndTime;
  ruleTest.value = data.rules;
  isExposure.value = data.isExposure;
  nextTick(() => {
    mySwiper = new Swiper('.swiper-container', {
      loop: true,
      observeSlideChildren: true,
      observer: true,
    });
  });
});

// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
  showSelect.value = false;
});
// 点击边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  if (activityData) {
    const data = activityData;
    document.title = data.activityName;
    endTime.value = dayjs(data.endTime).valueOf();
    if (data.prizeList.length) {
      prizeList.value.splice(0);
      prizeList.value.push(...data.prizeList.filter((item: any) => item.prizeType !== 0));
    } else {
      prizeList.value = defaultStateList;
    }
    if (data.skuList) {
      skuList.value = data.skuList;
    }
    if (data.orderSkuList) {
      orderSkuList.value = data.orderSkuList;
    }
    ruleTest.value = data.rules;
    isExposure.value = data.isExposure;

  }
  if (decoData) {
    console.log('decoData', decoData);

    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
  nextTick(() => {
    mySwiper = new Swiper('.swiper-container', {
      loop: prizeList.value.length > 1,
      observeSlideChildren: true,
      observer: true,
    });
  });
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style lang="scss" scoped>
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }
  .time-range {
    color: #ffc688;
    font-size: .24rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/232070/30/23866/2344/66971bfbF901c9779/182c71df0c18cfdc.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: 50%;
    margin: .2rem;
    line-height: .8rem;
    text-align: center;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .draw-btn {
    width: 5rem;
    margin: 0 auto 0 auto;
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translate(-50%);
    img {
      width: 100%;
    }
  }
}
.orderPrice {
  text-align: center;
  padding-top: 0.2rem;
  text-decoration: underline;
  font-size: .2rem;
  color: #fff;
  opacity: .68;
}
.rank {
  font-size: .36rem;
  height: 0.8rem;
  color: #ffb274;
  text-align: center;
}
.prizeBox {
  font-size: 0.3rem;
  color: #fff;
  padding-top: 0.2rem;
  width:100%;
  height:4rem;
  overflow: hidden;
  position: relative;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/245843/11/12489/12149/6673fd42F7ea9c660/c3ad4bd8028865ee.png") no-repeat;
  background-size: 100%;
  .prizeItem {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 1.5rem;
    }
}}
.sign-up-button {
  margin-top: 0.3rem;
  width: 3rem;
  font-size: 0.3rem;
  text-align: center;
  background-repeat: no-repeat;
  background-size: 100%;
}
.reciveBox {
  margin:1rem auto 0;
  font-size: 0.3rem;
  color: #fff;
  text-align: center;
  width:7rem;
  height:7.4rem;
  line-height: 7.4rem;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/228899/31/21064/36416/6673f987F98bbebf7/c9c34169f3af01d9.png") no-repeat;
  background-size: 100%;
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #fff;
  text-align: center;
}
.swiper-button-prev {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0.5rem;
  height: 1rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/228776/2/21117/615/6673f567F6e3a79dd/a4af827fb55a1d2e.png') no-repeat;
  background-size: 100%;
  z-index: 1;

}
.swiper-button-next {
  z-index: 1;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0.5rem;
  height: 1rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/184285/1/47723/636/6673f566F3a33cfe6/f27151ff6e491986.png') no-repeat;
  background-size: 100%;

}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
