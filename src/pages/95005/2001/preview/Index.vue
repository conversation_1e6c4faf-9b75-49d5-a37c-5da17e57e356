<template>
  <div class="bg" :style="furnishStyles.pageBg.value"  v-if="isLoadingFinish">
    <div class="header-kv select-hover"  >
      <img
        :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'"
        alt=""
        class="kv-img" />

    </div>
    <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>
    <div class="rule-btn" @click="showRulePopup()">活动规则 ></div>

    <div class="prizeBox" v-if="prizeList.length>0">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/248549/8/14065/9188/668660c6Fbad0333f/fd53a7fb5202bda1.png" alt=""/>
      <div class="box">
        <div class="swiper-container" ref="swiperRef" >
          <div class="swiper-wrapper " >
            <div class="swiper-slide prizeItem" v-for="item in prizeList" :key="item">
              <div class="prizeItem" v-if="item.prizeName">
                <img :src="item.prizeImg || '//img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png' " alt=""/>
                <div>{{item.prizeName || 'xx积分'}}</div>
                <div class="sign-up-button"  @click="toast">领取奖品</div>
              </div>
            </div>
          </div>
          <div v-if="prizeList.length>2" class="swiper-button-prev" @click="prevSwiper"></div>
          <div v-if="prizeList.length>2" class="swiper-button-next" @click="nextSwiper"></div>
        </div>
      </div>

    </div>
    <div class="reciveBox">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/200328/34/39751/9887/668660c5Fe47e1f37/c2a649f1a7b5056c.png" alt=""/>
      <div class="header-box">
        <div class="header-btn" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event">
          {{ btn.name }}
        </div>
      </div>
      <div class="content">
        暂无记录
      </div>

    </div>
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
     <MyPrize v-if="showMyPrize"  @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!--我的订单弹窗-->
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup v-if="showOrderRecord" @close="showOrderRecord = false" ></OrderRecordPopup>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, nextTick } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';
import Swiper from 'swiper';
import 'swiper/swiper.min.css';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';

let mySwiper: Swiper;
const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(true);
const startTime = ref(0);

const isLoadingFinish = ref(false);

const prizeList = ref<any>(defaultStateList);

type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const showOrderRecord = ref(false);
const ruleTest = ref('');
const showGoods = ref(false);

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '您的订单 >',
    event: () => {
      showOrderRecord.value = true;
    },
  },
  {
    name: '我的奖品 >',
    event: () => {
      showMyPrize.value = true;
    },
  },
];

const isExposure = ref(1);

// 页面截图
const isCreateImg = ref(false);

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showGoods.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修时选择框
const showSelect = ref(false);

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  document.title = data.activityName;
  endTime.value = dayjs(data.endTime).valueOf();
  if (data.prizeList.length) {
    prizeList.value = data.prizeList;
    nextTick(() => {
      mySwiper.update();
    });
  } else {
    prizeList.value = [];
  }
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuList) {
    skuList.value = data.skuList;
  }
  if (data.orderSkuList) {
    orderSkuList.value = data.orderSkuList;
  }
  ruleTest.value = data.rules;
  isExposure.value = data.isExposure;
  nextTick(() => {
    mySwiper = new Swiper('.swiper-container', {
      loop: prizeList.value.length > 2,
      slidesPerView: 2 || 'auto',
      spaceBetween: prizeList.value.length > 2 ? 30 : 0,
      centeredSlides: [1, 3].includes(prizeList.value.length),
      navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' },
    });
  });
});

// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
  showSelect.value = false;
});
// 点击边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});
const prevSwiper = () => {
  mySwiper.slidePrev();
};
const nextSwiper = () => {
  mySwiper.slideNext();
};
onMounted(() => {
  if (activityData) {
    const data = activityData;
    document.title = data.activityName;
    endTime.value = dayjs(data.endTime).valueOf();
    if (data.prizeList.length) {
      prizeList.value.splice(0);
      prizeList.value.push(...data.prizeList.filter((item: any) => item.prizeType !== 0));
    }
    startTime.value = new Date(data.startTime).getTime();
    if (startTime.value > new Date().getTime()) {
      isStart.value = false;
    }
    if (startTime.value < new Date().getTime()) {
      isStart.value = true;
    }
    endTime.value = new Date(data.endTime).getTime();
    if (data.skuList) {
      skuList.value = data.skuList;
    }
    if (data.orderSkuList) {
      orderSkuList.value = data.orderSkuList;
    }
    ruleTest.value = data.rules;
    isExposure.value = data.isExposure;

  }
  if (decoData) {
    console.log('decoData', decoData);

    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
  nextTick(() => {
    mySwiper = new Swiper('.swiper-container', {
      loop: prizeList.value.length > 2,
      slidesPerView: 2 || 'auto',
      spaceBetween: prizeList.value.length > 2 ? 30 : 0,
      centeredSlides: [1, 3].includes(prizeList.value.length),
      navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' },
    });
  });
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style lang="scss" scoped>
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .draw-btn {
    width: 5rem;
    margin: 0 auto 0 auto;
    position: absolute;
    bottom: 1.6rem;
    left: 50%;
    transform: translate(-50%);
    img {
      width: 100%;
    }
  }
}
.header-box {
  height: 1rem;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .header-btn {
    width: 3.19rem;
    height: 0.68rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-size: 100%;
}
}
.rule-btn {
  font-size: 0.24rem;
  color: #fff;
  text-align: right;
  padding-right: 0.2rem;
  text-decoration: underline;
  text-underline-offset: .1rem;//下划线和文字间距
}
.prizeBox {
  font-size: 0.3rem;
  color: #fff;
  overflow: hidden;
  position: relative;
  .box {
    width: 80%;
    margin:0 auto;
    overflow: hidden;
  }
  img {
    width: 4rem;
    margin: 0.2rem auto;
  }
  .prizeItem {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    img {
      width: 1.5rem;
      background: #fff;
    }
}}
.sign-up-button {
  margin-top: 0.3rem;
  color: #6f6f6f;
  width: 2.8rem;
  height:0.68rem;
  font-size: 0.26rem;
  background: #e5c986;
  border-radius: .1rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.gray {
  background: #ccc;
}
.reciveBox {
  margin:1rem auto 0;
  font-size: 0.3rem;
  color: #000;
  padding-bottom: 1rem;
  img {
    width: 4rem;
    margin: 0.2rem auto;
  }
  .content {
    width: 7rem;
    height: 5rem;
    background: #fff;
    border-radius: 0.3rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #fff;
  text-align: center;
}
.swiper-button-prev {
  position: absolute;
  left: 0;
  top: 70%;
  transform: translateY(-50%);
  width: 0.28rem;
  height: 0.52rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/180230/35/37580/745/66755088F83e4bb06/fe2cefbfd6ece6c5.png') no-repeat;
  background-size: 100%;
  z-index: 1;
}
.swiper-button-next {
  z-index: 1;
  position: absolute;
  right: 0;
  top: 70%;
  transform: translateY(-50%);
  width: 0.28rem;
  height: 0.52rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/233542/28/20707/755/66755089Fd2d6dab8/628521ff436b49fa.png') no-repeat;
  background-size: 100%;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
