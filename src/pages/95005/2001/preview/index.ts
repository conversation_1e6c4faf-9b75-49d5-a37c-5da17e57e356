import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/11340/16/21693/57630/669883b3F0690a675/4f5f0ea68de3f17e.png',
  actBgColor: '#0a862f',
  btnColor: '#0e822f',
  btnBg: '#ffffff',
  btnBorderColor: '#ffffff',
  cutDownBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/137012/1/42465/6275/65e80562F66c606e9/6ab10639ccdebab2.png',
  cutDownColor: '#e2231a',
  signButtonBg: '//img10.360buyimg.com/imgzone/jfs/t1/226731/40/20550/605/668654b0Fd467ee7b/e729a44288f2d8ba.png',
  signButtonColor: '#6a4828',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/229461/34/20237/177965/6687bd62F3de20eb2/e5f831aaff0198b7.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/229324/38/22011/87006/6687bd61F03a806f3/6df164b27deef8c3.png',
  ruleBtnBg: 'https: //img10.360buyimg.com/imgzone/jfs/t1/235481/36/20858/1427/66752ca9F28e9b68d/9bbe47e8d232e488.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/249668/15/14163/9666/6687bd62F479e58ee/96b1b0bcf55aae93.png',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '消费金额排名有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
