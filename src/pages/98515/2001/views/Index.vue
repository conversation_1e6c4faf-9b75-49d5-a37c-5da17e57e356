<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <img :src="furnish.actBg" alt="" class="bg-img" />
    <div class="prize-result">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/184684/1/13313/8724/60eb9ee4E8959355e/d305158b9d24fb30.png" v-if="showPrize == 1" style="width: 5.82rem">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/77963/40/16349/3902/60eb9d09E9586d0df/937e5e2c785bb630.png" v-if="showPrize == 0" style="width: 4.72rem">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/177498/25/13709/7829/60eb9ee4E01961093/2ead0df7c65b099b.png" v-if="showPrize == 3" style="width: 4.52rem">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/190910/14/12839/2789/60ebaacbE0d09734f/746e664984fc6db6.png" v-if="showPrize == 2" style="width: 2.89rem">
      <span style="top: 0.9rem;left: 3.95rem;" v-show="showPrize == 0">{{ prizeMoney }}</span>
      <span style="top: 1.65rem;left: 3.5rem;" v-show="showPrize == 0">{{ prizeNum }}</span>
    </div>
    <div class="prize-buton">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/172092/39/19456/6481/60eb9d09E6861e3fd/b26b704874c6236a.png" v-show="showPrize == 0" @click="getPrizes" style="width: 2.44rem">
      <div v-show="showPrize == 1">不可领取</div>
      <div v-show="showButton == 3">已领取</div>
      <div v-show="showPrize == 2">已领完</div>
    </div>
  </div>
  <!-- 活动门槛 -->
  <ThresholdCPB v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
  <!--  新增门槛-->
  <VanPopup teleport="body" v-model:show="showAddLimit">
    <ThresholdNew @close="showAddLimit = false" :actStartTime="actStartTime" :actEndTime="actEndTime" :fullGiftThreshold="fullGiftThreshold" :maxParticipateNum="maxParticipateNum" :orderStartTime="orderStartTime" />
  </VanPopup>
  <!-- 规则 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!--  领取攻略-->
  <VanPopup teleport="body" v-model:show="showStrategyPopup">
    <StrategyPopup @close="showStrategyPopup = false"></StrategyPopup>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="showOrderRecord">
    <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
  </VanPopup>
</template>
<script setup lang="ts">
import { inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import RulePopup from '../components/RulePopup.vue';
import StrategyPopup from '../components/StrategyPopup.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import ThresholdCPB from '../components/Threshold.vue';
import ThresholdNew from '../components/ThresholdNew.vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import useThreshold from '@/hooks/useThreshold';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import '../style/A2Style.scss';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

// 是否领取成功
const success = ref(false);
// 是否参与过活动 0 未参与 1 参与成功
const showPrize = ref(0);
const prizeMoney = ref('');
const prizeNum = ref('');

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};
// 获取参与状态
const getStatus = async () => {
  try {
    const { data } = await httpRequest.post('/98516/main');
    showPrize.value = data.state;
    prizeMoney.value = data.unitPrice;
    prizeNum.value = data.unitCount;
    if (data.unitCount === 0 && data.state === 0) {
      showPrize.value = 3;
    }
  } catch (error) {
    console.error(error);
  }
};
// 领取奖品
const getPrizes = async () => {
  try {
    const { data, code } = await httpRequest.post('/98516/draw');
    console.log(data, 'ackMessage');
    if (code === 200) {
      getStatus();
    }
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getStatus()]);
    closeToast();
    checkLimitDialog();
  } catch (error) {
    closeToast();
  }
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();
</script>
<style lang="scss" scoped>
.prize-result {
  position: absolute;
  top: 3.3rem;
  height: 3.1rem;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.prize-result span{
  position: absolute;
  font-size: 0.42rem;
  color: #ae0000;
}
.prize-buton {
  position: absolute;
  top: 7.41rem;
  display: flex;
  justify-content: center;
  width: 100%;
}
.prize-buton div{
  width: 2.44rem;
  height: 0.68rem;
  border-radius: 0.15rem;
  background: linear-gradient(90deg, #bbbbbb, #8b8b8b);
  font-family: FZLTHK--GBK1-0;
  font-size: 0.38rem;
  letter-spacing: 0.02rem;
  color: #ffffff;
  text-align: center;
  align-items: center;
  line-height: 0.68rem;
}
.bg {
  min-height: 100vh;
  position: relative;
  background-repeat: no-repeat;
  background-size: 100%;
  .bg-img {
    width: 100%;
  }
  .rule-btn {
    position: absolute;
    top: 1.04rem;
    right: 0;
    width: 1.05rem;
  }
  .get-prize {
    position: absolute;
    top: 5.2rem;
    left: 50%;
    transform: translateX(-50%);
    width: 1.87rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
