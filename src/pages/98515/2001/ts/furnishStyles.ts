import { computed, reactive } from 'vue';

export const furnish = reactive({
  actBg: '',
  pageBg: '',
  actBgColor: '',
  mainColor: '',
  joinBtn: '',
  successBtn: '',
  ruleImg: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  color: furnish.mainColor ?? '',
  // backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

export default {
  pageBg,
};
