<template>
  <van-popup
    class="popup"
    v-model:show="isShowPopup"
  >
    <div
      class="rule-popup"
      id="rule"
    >
      <div class="content">
        <span v-html="ruleFormat(ruleInfo)"></span>
      </div>
    </div>
    <img
      class="close-btn"
      @click="isShowPopup = false"
      src="https://img10.360buyimg.com/imgzone/jfs/t1/245015/19/8452/4883/66611b2dFceaae9b1/61adbf93b250ee90.png"
      alt=""
    />
  </van-popup>
</template>

<script setup lang="ts">
/* eslint-disable */

import { toRefs, defineProps, computed, WritableComputedRef, withDefaults, defineEmits, onMounted } from 'vue';
import { Popup as VanPopup } from 'vant';
import { ruleFormat } from '../../common';

interface Props {
  ruleInfo: string;
  isShow: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  ruleInfo: '',
  isShow: false,
});

const { ruleInfo } = toRefs(props);
type DefineEmits = {
  (e: 'close-popup', type: string): void;
};
const emits = defineEmits<DefineEmits>();
const isShowPopup: WritableComputedRef<boolean> = computed({
  // getter
  get() {
    return props.isShow;
  },
  // setter
  set() {
    emits('close-popup', 'rule');
  },
});
onMounted(() => {});
</script>

<style lang="scss" scoped>
$widthRatio: var(--widthRatio);
$heightRatio: var(--heightRatio);
@function resizeWidth($pixels) {
  @return calc($widthRatio * $pixels);
}

@function resizeHeight($pixels) {
  @return calc($heightRatio * $pixels);
}
.rule-popup {
  background-repeat: no-repeat;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/224728/14/20368/27839/66611b2dF2171e0f2/249d58e184fc54d5.png');
  background-size: contain;
  width: resizeWidth(656px);
  height: resizeHeight(762px);
  padding-top: resizeHeight(260px);
  box-sizing: border-box;
  .content {
    width: resizeWidth(466px);
    height: resizeHeight(400px);
    margin: 0 auto;
    overflow-y: scroll;
    span {
      font-size: resizeWidth(24px);
      color: #fff;
    }
  }
}
.close-btn {
  width: resizeWidth(68px);
  height: resizeHeight(68px);
  display: block;
  margin: resizeHeight(20px) auto 0;
}
</style>
