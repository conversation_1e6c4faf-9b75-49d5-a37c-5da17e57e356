<template>
  <div
    id="answer-question"
    v-if="isShowPage"
  >
    <img
      class="boe-img-container"
      src="https://img10.360buyimg.com/imgzone/jfs/t1/196341/3/46416/1888/665ebbd0F4b81995e/46d298717bbfa12b.png"
      alt=""
    />
    <img
      v-if="isInitAni"
      class="question-container animate__animated animate__fadeIn"
      :src="question.title"
      alt=""
    />
    <div
      v-if="isInitAni"
      class="scene-container animate__animated animate__fadeIn"
      :style="{ backgroundImage: `url(${question.scene})` }"
    >
      <template v-if="question.id === 1">
        <div
          v-if="isShowCursor"
          class="cursor-posi-wrapper animate__animated animate__fadeIn"
          :style="{ backgroundImage: `url(${question.cursor})` }"
        >
          <div
            class="cursor-wrap"
            @click="_clickCursor(question.id, 1)"
          ></div>
          <div
            class="cursor-wrap"
            @click="_clickCursor(question.id, 2)"
          ></div>
          <div
            class="cursor-wrap"
            @click="_clickCursor(question.id, 3)"
          ></div>
          <div
            class="cursor-wrap"
            @click="_clickCursor(question.id, 4)"
          ></div>
        </div>
        <img
          v-if="isShowBubble"
          class="bubbles-wrapper animate__animated animate__fadeIn"
          :class="{
            'bubbles-wrapper-1': id1BubblePosi === 1,
            'bubbles-wrapper-2': id1BubblePosi === 2,
            'bubbles-wrapper-3': id1BubblePosi === 3,
            'bubbles-wrapper-4': id1BubblePosi === 4,
          }"
          :src="question.bubbles"
          alt=""
        />
      </template>
      <template v-else>
        <div
          class="other-cursor-wrapper animate__animated animate__fadeIn"
          v-if="isShowCursor"
          :class="{
            'other-cursor-wrapper-2': question.id === 2,
            'other-cursor-wrapper-3': question.id === 3,
            'other-cursor-wrapper-4': question.id === 4,
            'other-cursor-wrapper-5': question.id === 5,
          }"
          @click="_clickCursor(question.id)"
          :style="{ backgroundImage: `url(${question.cursor})` }"
        ></div>
        <img
          v-if="isShowBubble"
          class="other-bubbles-wrapper animate__animated animate__fadeIn"
          :class="{
            'other-bubbles-wrapper-2': question.id === 2,
            'other-bubbles-wrapper-3': question.id === 3,
            'other-bubbles-wrapper-4': question.id === 4,
            'other-bubbles-wrapper-5': question.id === 5,
          }"
          :src="question.bubbles"
          alt=""
        />
      </template>
    </div>
    <img
      v-if="isShowAnswer"
      class="answer-container animate__animated animate__fadeIn"
      :src="question.answer"
      alt=""
    />
    <img
      v-if="isShowMainBtn"
      @click="_mainBtn"
      class="main-btn-container animate__animated animate__fadeIn"
      :src="mainBtnUrl"
      alt=""
    />
    <img
      class="preload"
      v-for="it in questionList"
      :key="it.id"
      :src="it.scene"
      alt=""
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable */
import { ref, onMounted, nextTick, toRefs } from 'vue';
import { httpRequest } from '@/utils/service';
import { showSuccessToast, showToast } from 'vant';

const questionList = [
  {
    id: 1,
    title: 'https://img10.360buyimg.com/imgzone/jfs/t1/236202/16/17482/9685/665ec982F4b6ea5bb/149a316bde0e539c.png',
    cursor: 'https://img10.360buyimg.com/imgzone/jfs/t1/227486/7/19816/21349/665ffbc0F57f47240/6c09f9f3792945f0.png',
    answer: 'https://img10.360buyimg.com/imgzone/jfs/t1/183459/6/47741/38696/665ecbe7F72b6a2e4/dc6c2edb5de7cb68.png',
    scene: 'https://img10.360buyimg.com/imgzone/jfs/t1/197860/5/44373/169899/665eca89Ff9aa7f20/40ae7e89d40470f4.png',
    bubbles: 'https://img10.360buyimg.com/imgzone/jfs/t1/163785/4/26041/39522/665ffcbbFd1f39aed/5d88381cd0bfaf29.png',
  },
  {
    id: 2,
    title: 'https://img10.360buyimg.com/imgzone/jfs/t1/179076/18/46688/13037/665ec982F4d6b2a75/2d2c2f81106df001.png',
    cursor: 'https://img10.360buyimg.com/imgzone/jfs/t1/237149/30/18896/4614/66600e24F864f496f/1d3ccf1a72fc19a9.png',
    answer: 'https://img10.360buyimg.com/imgzone/jfs/t1/241235/7/10816/39918/66600eb4Ff247073a/a09989cf630e1008.png',
    scene: 'https://img10.360buyimg.com/imgzone/jfs/t1/189324/2/40675/112994/66600eb4Fe3612e6d/44d4231d80a4a195.png',
    bubbles: 'https://img10.360buyimg.com/imgzone/jfs/t1/230548/22/20091/52695/66600eb2F98656683/489dd086c955181c.png',
  },
  {
    id: 3,
    title: 'https://img10.360buyimg.com/imgzone/jfs/t1/222387/17/42171/13440/665ec982F27f5cc51/1e93b59708d8dc34.png',
    cursor: 'https://img10.360buyimg.com/imgzone/jfs/t1/237149/30/18896/4614/66600e24F864f496f/1d3ccf1a72fc19a9.png',
    answer: 'https://img10.360buyimg.com/imgzone/jfs/t1/192804/11/46548/42503/66601270F5be8a94c/06690a17fbd04ab1.png',
    scene: 'https://img10.360buyimg.com/imgzone/jfs/t1/249643/20/10711/118350/66601271Fd1eeb983/b58a7c469bbdcf28.png',
    bubbles: 'https://img10.360buyimg.com/imgzone/jfs/t1/220120/14/42923/43827/66601272Fbcf4f94e/50d011fda2c6abe9.png',
  },
  {
    id: 4,
    title: 'https://img10.360buyimg.com/imgzone/jfs/t1/190196/12/45823/10686/665ec982F3e1df5a5/f5c32dca34de48cd.png',
    cursor: 'https://img10.360buyimg.com/imgzone/jfs/t1/237149/30/18896/4614/66600e24F864f496f/1d3ccf1a72fc19a9.png',
    answer: 'https://img10.360buyimg.com/imgzone/jfs/t1/155460/25/39572/40402/666013b8F74010adb/43d0673386f667e1.png',
    scene: 'https://img10.360buyimg.com/imgzone/jfs/t1/186419/25/45439/157529/666013bbF79a351a8/76dacfcfce50102f.png',
    bubbles: 'https://img10.360buyimg.com/imgzone/jfs/t1/185783/21/47070/50613/666013bbF2f67e729/bbe498cb204478ed.png',
  },
  {
    id: 5,
    title: 'https://img10.360buyimg.com/imgzone/jfs/t1/227487/27/19615/13452/665ec983Ff39390fc/6302cf54f6f53c79.png',
    cursor: 'https://img10.360buyimg.com/imgzone/jfs/t1/237149/30/18896/4614/66600e24F864f496f/1d3ccf1a72fc19a9.png',
    answer: 'https://img10.360buyimg.com/imgzone/jfs/t1/242527/18/10880/39845/666013beF556776a9/44f31e2e39091f04.png',
    scene: 'https://img10.360buyimg.com/imgzone/jfs/t1/194759/2/46544/121015/666013bdF8cf89fae/261f882466be7b63.png',
    bubbles: 'https://img10.360buyimg.com/imgzone/jfs/t1/231934/36/19574/43629/666013beF17c5ddd4/1025d18b543fdb57.png',
  },
];
interface Props {
  isAnswered: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  isAnswered: false,
});

const { isAnswered } = toRefs(props);

type DefineEmits = {
  (e: 'can-toggle-component', componentName: string): void;
};
const emits = defineEmits<DefineEmits>();
// 当前题目信息
const question = ref<any>({});
// 答题计数，每点击答题抓手算一次答题
const questionCounts = ref(0);
// 是否初始化动画
const isInitAni = ref(false);
// 是否展示气泡，默认不展示
const isShowBubble = ref<boolean>(false);
// 是否展示答案
const isShowAnswer = ref<boolean>(false);
// 是否展示主要按钮
const isShowMainBtn = ref<boolean>(false);
const isShowCursor = ref(false);
const id1BubblePosi = ref<number>(-1);
const mainBtnUrl = ref(
  'https://img10.360buyimg.com/imgzone/jfs/t1/165816/3/26655/17681/665ecd34F56d82e9b/f35fd29dffd93ad5.png'
);
const isShowPage = ref(false);

// 定义一个函数，用于在指定的时间后执行某个操作
const doSomethingAfterTime = (seconds: number, callback: Function) => {
  setTimeout(() => {
    callback();
  }, seconds * 1000);
};
// 点击抓手
const _clickCursor = (id: number, num?: number) => {
  if (id === 1) {
    id1BubblePosi.value = num as number;
  }
  isShowBubble.value = true;
  isShowCursor.value = false;
  questionCounts.value += 1;
  doSomethingAfterTime(0.7, () => {
    isShowAnswer.value = true;
  });
  doSomethingAfterTime(1.4, () => {
    isShowMainBtn.value = true;
  });
};

// 切换组件
const _canToggleComponent = () => {
  emits('can-toggle-component', 'Draw');
};

// 存储答题状态
const _saveAnswerStatus = async () => {
  try {
    await httpRequest.post('/boe/question/1797471746559229954/answerFinish');
    showToast('恭喜您完成体验，获得1次抽奖机会~');
    setTimeout(() => {
      _canToggleComponent();
    }, 2000);
  } catch (error: any) {
    console.error(error);
  }
};
// 主要按钮的处理
const _mainBtn = () => {
  if (questionCounts.value >= 5) {
    //答完题了
    if (isAnswered.value) {
      // 答过了...直接跳转
      _canToggleComponent();
      return;
    }
    _saveAnswerStatus();
  } else {
    // 没答完
    if (questionCounts.value === 4) {
      mainBtnUrl.value =
        'https://img10.360buyimg.com/imgzone/jfs/t1/207200/32/43188/15443/66601494F1cc473bc/61d71f594303ea0a.png';
    }
    question.value = questionList[questionCounts.value];
    isShowBubble.value = false;
    isShowAnswer.value = false;
    isShowMainBtn.value = false;
    isInitAni.value = false;
    doSomethingAfterTime(0.2, () => {
      isInitAni.value = true;
    });
    doSomethingAfterTime(0.8, () => {
      isShowCursor.value = true;
    });
  }
};
onMounted(() => {
  question.value = questionList[0];
  doSomethingAfterTime(0.3, () => {
    isInitAni.value = true;
  });
  doSomethingAfterTime(0.8, () => {
    isShowCursor.value = true;
  });
  nextTick(() => {
    isShowPage.value = true;
  });
});
</script>

<style lang="scss" scoped>
$widthRatio: var(--widthRatio);
$heightRatio: var(--heightRatio);
@function transformVW($pixels) {
  @return calc($widthRatio * $pixels);
}

@function transformVH($pixels) {
  @return calc($heightRatio * $pixels);
}
.preload {
  position: absolute;
  left: -9999px;
  top: -9999px;
}
#answer-question {
  width: 100vw;
  max-width: 100vw;
  height: 100vh;
  max-height: 100vh;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/246602/8/10756/98777/665ec1dcFf17f7598/20e540206aeff804.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
  padding-top: transformVH(117px);
  box-sizing: border-box;
  .boe-img-container {
    width: transformVW(128px);
    height: transformVH(58px);
    position: absolute;
    left: transformVW(48px);
    top: transformVH(30px);
  }
  .question-container {
    width: transformVW(646px);
    height: transformVH(90px);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: transformVH(119px);
  }
  .scene-container {
    width: transformVW(659px);
    height: transformVH(962px);
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/197860/5/44373/169899/665eca89Ff9aa7f20/40ae7e89d40470f4.png');
    background-size: contain;
    background-repeat: no-repeat;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: transformVH(245px);
    .cursor-posi-wrapper {
      width: transformVW(403px);
      height: transformVH(626px);
      background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/227486/7/19816/21349/665ffbc0F57f47240/6c09f9f3792945f0.png');
      background-size: contain;
      background-repeat: no-repeat;
      position: absolute;
      left: transformVW(201px);
      top: transformVH(293px);
      .cursor-wrap {
        width: transformVW(93px);
        height: transformVH(75px);
        &:nth-child(1) {
          position: absolute;
          left: 0;
          top: 0;
        }
        &:nth-child(2) {
          position: absolute;
          right: 0;
          top: transformVH(65px);
        }
        &:nth-child(3) {
          position: absolute;
          left: 0;
          top: transformVH(487px);
        }
        &:nth-child(4) {
          position: absolute;
          right: transformVW(38px);
          bottom: 0;
        }
      }
    }
    .other-cursor-wrapper {
      background-size: contain;
      background-repeat: no-repeat;
      width: transformVW(80px);
      height: transformVH(74px);
      position: absolute;
      &-2 {
        left: transformVW(530px);
        top: transformVH(329px);
      }
      &-3 {
        left: transformVW(485px);
        top: transformVH(380px);
      }
      &-4 {
        left: transformVW(300px);
        top: transformVH(463px);
      }
      &-5 {
        left: transformVW(425px);
        top: transformVH(310px);
      }
    }
    .bubbles-wrapper {
      width: transformVW(323px);
      height: transformVH(195px);
      position: absolute;
      &-1 {
        top: transformVH(85px);
        left: -transformVW(11px);
      }
      &-2 {
        top: transformVH(170px);
        left: transformVW(301px);
      }
      &-3 {
        top: transformVH(569px);
        left: -transformVW(27px);
      }
      &-4 {
        top: transformVH(662px);
        left: transformVW(269px);
      }
    }
    .other-bubbles-wrapper {
      position: absolute;
      &-2 {
        width: transformVW(381px);
        height: transformVH(195px);
        left: transformVW(265px);
        top: transformVH(116px);
      }
      &-3 {
        width: transformVW(322px);
        height: transformVH(195px);
        left: transformVW(277px);
        top: transformVH(163px);
      }
      &-4 {
        width: transformVW(370px);
        height: transformVH(239px);
        left: transformVW(92px);
        top: transformVH(246px);
      }
      &-5 {
        width: transformVW(359px);
        height: transformVH(193px);
        left: transformVW(196px);
        top: transformVH(367px);
      }
    }
  }
  .answer-container {
    width: transformVW(656px);
    height: transformVH(215px);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: transformVH(1242px);
  }
  .main-btn-container {
    width: transformVW(392px);
    height: transformVH(103px);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: transformVH(1492px);
  }
}
</style>
