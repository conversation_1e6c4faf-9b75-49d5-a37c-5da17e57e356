<template>
  <KV> </KV>
  <div class="place-self-center punch-number">
    <div>
      连续打卡月份: <span class="special-text">{{ actInfo?.signMonthResponse.contiSignMonth }}</span
      >个月
    </div>
    <div>
      当月最大连续打卡: <span class="special-text">{{ actInfo?.contiTimes }}</span> 天
    </div>
    <div>
      可用补卡次数: <span class="special-text">{{ actInfo?.resignTimes }}</span> 次
    </div>
  </div>
  <!-- 日历部分 -->
  <div class="place-self-center punch-calendar">
    <img class="tips" src="./assets/tip.png" alt="tips" />
    <div class="cus-calendar">
      <div class="swiper-wrapper calendar-swiper">
        <div class="swiper-wrapper">
          <div class="swiper-slide calendar-item" v-for="item in calendar" :key="item.month">
            <div class="calendar-month">{{ item.month }}<span class="special">月</span></div>
            <DateList :dateList="item.days" @clickDate="handleClickDate" />
          </div>
        </div>
      </div>
      <!-- <div v-for="(item, index) in calendar" :key="index" class="calendar-item">
        <div class="calendar-month">{{ item.month }}<span class="special">月</span></div>
        <DateList :dateList="item.days" @clickDate="handleClickDate" />
      </div> -->
    </div>
    <div class="pubch-btns">
      <div class="pubch-btn" @click="handlePunch" :class="{ 'btn-disabled': todayIsPunch }">{{ todayIsPunch ? '已打卡' : '点击打卡' }}</div>
      <div class="pubch-btn pubch-btn-supplement" @click="handleSupplement" :class="{ 'btn-disabled': actInfo?.resignTimes <= 0 }">补卡</div>
    </div>
  </div>
  <!-- 按月统计的打卡天数 -->
  <img class="w100 task-title" src="https://img10.360buyimg.com/imgzone/jfs/t1/262737/4/24888/15569/67bed79bF692f354b/196ec4c18ca11a21.png" alt="" />
  <ClockDays :userContiTimes="actInfo?.contiTimes" class="w698 place-self-center"></ClockDays>
  <img class="place-self-center w100 task-title" src="https://img10.360buyimg.com/imgzone/jfs/t1/263085/20/24822/7747/67bec824F79838090/adebb264f87a9433.png" alt="" />
  <!-- 扭蛋机 -->
  <div class="gacha-machine-box w100">
    <div class="swiper-wrapper gift-swiper">
      <div class="swiper-wrapper">
        <div class="swiper-slide" v-for="(item, index) in 5" :key="index">
          <img class="gacha-machine-gift" src="./assets/gift.png" alt="" />
        </div>
      </div>
    </div>
    <img v-if="isShowBall" class="gacha-machine-ball" src="./assets/ball.png" alt="" />
    <lucky-gacha ref="myLucky" @end="endCallback" :width="generateConfig.width" :height="generateConfig.height" :left="generateConfig.left" :top="generateConfig.top" :background="generateConfig.background" :border-radius="generateConfig.borderRadius" :ballList="generateConfig.ballList" :defaultConfig="generateConfig.defaultConfig" />
    <div class="gacha-machine-nums">剩余:{{ actInfo?.canDrawChance }}次扭蛋机会</div>
    <img @click="handleDraw" :class="{ 'btn-disabled': actInfo.canDrawChance <= 0 }" class="gacha-machine-btn" src="https://img10.360buyimg.com/imgzone/jfs/t1/255239/38/25494/49491/67beb3ebF03072fc1/40dd6588cd4b2eff.png" alt="" />
  </div>
  <!-- 打卡月份 -->
  <img class="w100 title-img month-title" src="https://img10.360buyimg.com/imgzone/jfs/t1/263085/20/24822/7747/67bec824F79838090/adebb264f87a9433.png" alt="" />
  <ClockMonths :userNumInfo="actInfo?.signMonthResponse" class="w698 place-self-center"></ClockMonths>
  <!-- 奖品 -->
  <div class="place-self-center w698 gift-container">
    <img class="w100 task-title" :src="UIConfig.GiftImg" alt="" />
    <div class="btn-group">
      <img class="btn register-btn" :class="{ 'btn-disabled': actInfo.signMonthResponse.receivePrizeStatus !== 1 }" @click="handleClickPrize" src="https://img10.360buyimg.com/imgzone/jfs/t1/262136/1/24380/10293/67bd5e25F725c4b3b/7e5a1792eccdef23.png" alt="" />
      <img class="btn draw-btn" :class="{ 'btn-disabled': actInfo.signMonthResponse.drawPrizeStatus !== 1 }" @click="handleClickIndicator" src="https://img10.360buyimg.com/imgzone/jfs/t1/255445/32/25378/9317/67bd5e25Ffc23760e/3b3d52f2e7120916.png" alt="" />
    </div>
  </div>
  <!-- 任务列表 -->
  <img class="w100 task-title" src="https://img10.360buyimg.com/imgzone/jfs/t1/225703/1/35035/13468/67bd564cF89dbea06/5fa36b3727dd64db.png" alt="" />
  <TaskList :taskList="actInfo?.taskResponses" class="w698 place-self-center"></TaskList>
  <SaveAddress :addressId="addressId" :showPopup="showPopup.isShowAddressPopup" @closeDialog="showPopup.isShowAddressPopup = false"></SaveAddress>
  <FollowShop @followSuccess="init(), (showPopup.isShowFollowShopPopup = false)" :showPopup="showPopup.isShowFollowShopPopup" @closeDialog="showPopup.isShowFollowShopPopup = false"></FollowShop>
  <SupplementCof @handleCom="handleClickCom" :showPopup="showPopup.isShowSupplementCofPopup" @closeDialog="showPopup.isShowSupplementCofPopup = false"></SupplementCof>
  <DrawFail :showPopup="showPopup.isShowDrawFailPopup" @closeDialog="showPopup.isShowDrawFailPopup = false"></DrawFail>
  <DrawSuccess :prizeInfo="prizeInfo" @handleSaveAddress="handleSaveAddress" :showPopup="showPopup.isShowDrawSuccessPopup" @closeDialog="showPopup.isShowDrawSuccessPopup = false"></DrawSuccess>
  <DrawAnimation :showPopup="showPopup.isShowDrawAnimationPopup" @closeDialog="animationEnd"></DrawAnimation>
</template>
<script lang="ts" setup>
import { ref, reactive, provide, onMounted, inject, onUnmounted, nextTick } from 'vue';
import KV from './components/KV.vue';
import { getActUIConfig, getActInfo, setTodaySign, setDayResign, getDraw, getPrizeInfo } from './scripts/ajax';
import ClockDays from './components/ClockDays.vue';
import ClockMonths from './components/ClockMonths.vue';
import TaskList from './components/TaskList.vue';
import { generateConfig, formatCalendarData, updateSignStatus, hasSign, isObjectEmpty } from './scripts/tools';
import DateList from './components/DateList.vue';
import SaveAddress from './components/SaveAddress.vue';
import FollowShop from './components/FollowShop.vue';
import SupplementCof from './components/SupplementCof.vue';
import type { BaseInfo } from '@/types/BaseInfo';
import type { ActInfo, Prize } from './scripts/type';
import type { MonthCalendar } from './scripts/tools';
import dayjs from 'dayjs';
import { showToast } from 'vant';
import DrawFail from './components/DrawFail.vue';
import DrawSuccess from './components/DrawSuccess.vue';
import DrawAnimation from './components/DrawAnimation.vue';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import { values } from 'lodash';

const isLoading = ref(false);
const UIConfig = reactive<any>({});
provide('UIConfig', UIConfig);
const pathParams = inject('pathParams') as any;
const baseInfo = inject('baseInfo') as BaseInfo;
const getActCon = async () => {
  const data = await getActUIConfig();
  Object.assign(UIConfig, JSON.parse(data));
};
const showPopup = reactive({
  isShowSupplementCofPopup: false,
  isShowAddressPopup: false,
  isShowFollowShopPopup: false,
  isShowDrawFailPopup: false,
  isShowDrawSuccessPopup: false,
  isShowDrawAnimationPopup: false,
});
const actInfo = reactive<ActInfo>({
  canDrawChance: 0,
  contiTimes: 0,
  detailResponses: [],
  resignTimes: 0,
  taskResponses: [],
  signMonthResponse: {
    contiSignMonth: 0,
    drawPrizeStatus: 0,
    receivePrizeStatus: 0,
    signTotalMonth: [],
  },
});
Swiper.use([Autoplay]);
const initSwiper = () => {
  console.log('initSwiper');
  const swiper = new Swiper('.gift-swiper', {
    slidesPerView: 3,
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
  });
  const swiper2 = new Swiper('.calendar-swiper', {
    slidesPerView: 1,
    spaceBetween: 20,
  });
  swiper2.slideTo(new Date().getMonth() + 1 - 3, 0, false);
};
const calendar = ref<MonthCalendar[]>([]);
// 当天是否打卡;
const todayIsPunch = ref(false);
const init = async () => {
  Object.assign(actInfo, await getActInfo());
  calendar.value = formatCalendarData(actInfo.detailResponses);
  console.log('🚀 ~ init ~ calendar:', calendar.value);
  todayIsPunch.value = actInfo.detailResponses.some((item) => dayjs(item.signDate).isSame(new Date(), 'day') && item.signStatus);
  nextTick(() => {
    initSwiper();
  });
};

onMounted(async () => {
  getActCon();
  // 需要关注店铺 未关注
  if (baseInfo.thresholdResponseList.find((item) => item.thresholdCode === 5)) {
    showPopup.isShowFollowShopPopup = true;
    return;
  }
  init();

  if (pathParams?.isJoin && localStorage.getItem('returnType')) {
    showToast('开卡成功');
    localStorage.removeItem('returnType');
  }
  if (!pathParams?.isJoin && localStorage.getItem('returnType')) {
    // 没开卡 但是点击按钮了  不提示 删除提示信息
    localStorage.removeItem('returnType');
  }
  document.addEventListener('visibilitychange', (e: any) => {
    if (e.target.visibilityState === 'visible') {
      // 检查 localStorage 是否有提示信息
      const returnType = localStorage.getItem('returnType');

      if (returnType) {
        1000004065 / 1879425443088613378;
        showToast('浏览成功');
        actInfo.resignTimes += 1;
        // 显示完提示后，可以清除提示信息
        localStorage.removeItem('returnType');
      }
    }
  });
});
onUnmounted(() => {
  document.removeEventListener('visibilitychange', () => {
    console.log('destroyed');
  });
});

// 打卡
const handlePunch = async () => {
  if (isLoading.value) return;
  if (todayIsPunch.value) return;
  isLoading.value = true;
  const data = await setTodaySign();
  if (!data.contiTimes) return;
  isLoading.value = false;
  showToast(`打卡成功${data.getDrawChance > 0 ? `,获得${data.getDrawChance}次扭蛋机会` : ''}`); // 获取抽奖次数getDrawChance次
  todayIsPunch.value = true;
  actInfo.canDrawChance = data.canDrawChance;
  actInfo.contiTimes = data.contiTimes;
  actInfo.signMonthResponse = data.signMonthResponse;
  //   重新渲染日历数据 当天改为已打卡
  calendar.value = updateSignStatus(calendar.value, data.signDate);
};
// 用户选择的日期
const userClickDate = ref('');
const handleClickDate = (date: string) => {
  userClickDate.value = date;
};
// 补卡
const handleSupplement = async () => {
  if (actInfo.resignTimes <= 0) {
    showToast('抱歉，补卡次数不足');
    return;
  }
  if (userClickDate.value === '') {
    showToast('请选择补卡日期');
    return;
  }
  if (dayjs(userClickDate.value).isAfter(dayjs(new Date()), 'day')) {
    showToast('不可选择超过当前日期的补卡');
    return;
  }
  if (hasSign(calendar.value, userClickDate.value)) {
    showToast('该日已打过卡啦，无法补卡');
    return;
  }
  if (dayjs(userClickDate.value).isSame(new Date(), 'day')) {
    showToast('不可补签今日，请点击左侧按钮进行打卡');
    return;
  }
  showPopup.isShowSupplementCofPopup = true;
};
const handleClickCom = async () => {
  showPopup.isShowSupplementCofPopup = false;
  const data = await setDayResign(userClickDate.value);
  if (!data.contiTimes) return;
  showToast(`补卡成功${data.getDrawChance > 0 ? `,获得${data.getDrawChance}次扭蛋机会` : ''}`); // 获取抽奖次数getDrawChance次

  isLoading.value = false;
  actInfo.canDrawChance = data.canDrawChance;
  actInfo.contiTimes = data.contiTimes;
  actInfo.signMonthResponse = data.signMonthResponse;
  actInfo.resignTimes = data.resignTimes;
  //   重新渲染日历数据 当天改为已打卡
  calendar.value = updateSignStatus(calendar.value, data.signDate);
};

// 扭蛋机
const myLucky = ref();
const prizeInfo = ref<Prize>({
  activityPrizeId: '',
  prizeImg: '',
  prizeName: '',
  prizeType: 0,
  result: {},
  sortId: 0,
  status: 0,
  userPrizeId: '',
});
const isShowBall = ref(true); // 防止网络慢导致小球加载失败 先展示一个小球的截图
const handleDraw = async () => {
  if (isLoading.value) return;
  if (actInfo.canDrawChance <= 0) {
    showToast('抱歉，扭蛋机机会不足');
    return;
  }
  isLoading.value = true;
  const data = await getDraw('1');
  prizeInfo.value = data;
  actInfo.canDrawChance -= 1;
  console.log('🚀 ~ handleDraw ~ data:', data);
  isShowBall.value = false;
  myLucky.value.play();
};
const endCallback = () => {
  isLoading.value = false;
  if (prizeInfo.value.status === 0) {
    showPopup.isShowDrawFailPopup = true;
  } else {
    showPopup.isShowDrawSuccessPopup = true;
  }
};
const addressId = ref('');
const handleSaveAddress = () => {
  addressId.value = prizeInfo.value.userPrizeId;
  showPopup.isShowDrawSuccessPopup = false;
  showPopup.isShowAddressPopup = true;
};
// 大礼包
const handleClickPrize = async () => {
  if (isLoading.value) return;
  // 礼包奖品状态 0-无法领取 1-可以领取 2-已领取
  if (actInfo.signMonthResponse.receivePrizeStatus === 2) {
    showToast('您已领取奖励，请点击【我的奖品】查看');
    return;
  }
  if (actInfo.signMonthResponse.receivePrizeStatus !== 1) return;
  isLoading.value = true;
  const data = await getPrizeInfo();
  isLoading.value = false;
  addressId.value = data.userPrizeId;
  showPopup.isShowAddressPopup = true;
  actInfo.signMonthResponse.receivePrizeStatus = 2;
};
const handleClickIndicator = async () => {
  // 礼包奖品状态 0-无法领取 1-可以领取 2-已领取
  if (actInfo.signMonthResponse.drawPrizeStatus === 0) {
    showToast('抱歉，暂无抽奖机会，连续打卡10个月，可获得抽奖次数哦！');
    return;
  }
  if (actInfo.signMonthResponse.drawPrizeStatus === 2) {
    showToast('您已参与抽奖');
    return;
  }
  if (actInfo.signMonthResponse.drawPrizeStatus !== 1) return;
  showPopup.isShowDrawAnimationPopup = true;
};
const animationEnd = (info: Prize) => {
  if (isObjectEmpty(info)) {
    showPopup.isShowDrawAnimationPopup = false;
    return;
  }
  actInfo.signMonthResponse.drawPrizeStatus = 2;
  showPopup.isShowDrawAnimationPopup = false;
  if (info.status === 1) {
    addressId.value = info.userPrizeId;
    prizeInfo.value = info;
    showPopup.isShowDrawSuccessPopup = true;
  } else {
    showPopup.isShowDrawFailPopup = true;
  }
};
</script>
<style lang="scss" scoped>
.punch-number {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/265860/35/23872/18304/67bc28f3Fc4616428/9d98a8bd902b21e1.png) no-repeat;
  background-size: 100% 100%;
  width: 6.98rem;
  height: 1.84rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0.1rem 0;
  box-sizing: border-box;
  margin-top: 0.2rem;
  div {
    background-color: #000;
    font-family: FZLTHJW;
    font-size: 0.2rem;
    line-height: 1;
    padding: 0.15rem 0;
    box-sizing: border-box;
    color: #fff;
    flex: 1;
    text-align: center;
    margin-right: 0.07rem;
    .special-text {
      font-family: GothamMedium;
      font-size: 0.26rem;
    }
    &:nth-child(2) {
      flex: 1.2;
    }
    &:last-child {
      margin-right: 0;
      flex: 0.9;
    }
  }
}
.punch-calendar {
  background: url(./assets/calendar-bg.png) no-repeat;
  background-size: 100% 100%;
  width: 6.98rem;
  height: 9.8rem;
  margin-top: 0.29rem;
  padding: 0.5rem 0 0;
  box-sizing: border-box;
  margin-bottom: 0.52rem;
  .tips {
    display: block;
    width: 2.56rem;
    margin: 0 auto;
  }
  .cus-calendar {
    width: 100%;
    padding: 0 0.1rem;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    align-items: start;
    justify-content: start;
    flex-wrap: nowrap;
    .calendar-item {
      padding: 0 0.1rem;
      box-sizing: border-box;
      min-width: 100%;
    }
    .calendar-month {
      font-size: 0.8rem;
      margin-top: 0.1rem;
      color: #ffffff;
      margin-left: 0.2rem;
      font-weight: bold;
      font-family: GothamMedium;
      .special {
        font-size: 0.31rem;
      }
    }
  }
  .pubch-btns {
    margin-top: 0.3rem;
    display: flex;
    justify-content: space-between;
    padding: 0 0.25rem;
    box-sizing: border-box;
    .pubch-btn {
      background: url(https://img10.360buyimg.com/imgzone/jfs/t1/264626/31/24883/8075/67bf177eFd05b852b/8d55c6b4aa6b54ef.png) no-repeat;
      background-size: 100% 100%;
      width: 3.27rem;
      height: 0.87rem;
      text-align: center;
      line-height: 0.87rem;
      font-size: 0.38rem;
      color: #fff;
      font-weight: bold;
    }
    .pubch-btn-supplement {
      width: 2.64rem;
    }
  }
}
.title-img {
  margin-top: 0.2rem;
}
.gift-container {
  position: relative;
  margin-top: 0.2rem;
  .btn-group {
    position: absolute;
    top: 4.5rem;
    left: 0.7rem;
    right: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn {
      width: 2.35rem;
    }
  }
}
.gacha-machine-box {
  background: url(./assets/gacha-bg.png) no-repeat;
  background-size: 100% 100%;
  height: 12.45rem;
  position: relative;
  .gift-swiper {
    position: absolute;
    top: 2.2rem;
    left: 2.3rem;
    height: 1rem;
    width: 3rem;
    overflow: hidden;
    .swiper-slide {
      width: 1.1rem;
      .gacha-machine-gift {
        width: 0.78rem;
      }
    }
  }
  .gacha-machine-ball {
    width: 4.79rem;
    position: absolute;
    top: 5.6rem;
    left: 1.2rem;
    z-index: 1;
  }
  .gacha-machine-nums {
    font-size: 0.23rem;
    color: #ffffff;
    position: absolute;
    top: 7.75rem;
    left: 2.82rem;
  }
  .gacha-machine-btn {
    position: absolute;
    top: 8.3rem;
    left: 2rem;
    width: 3.58rem;
  }
}
.month-title {
  margin-top: -1.6rem;
  margin-bottom: 0.2rem;
  z-index: 1;
  position: relative;
}
.task-title {
  margin-top: 0.4rem;
  margin-bottom: 0.3rem;
}
</style>

<style>
.place-self-center {
  margin-left: 50%;
  transform: translateX(-50%);
}

@font-face {
  font-family: 'FZLTXHJW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTXIHJW/FZLTXHK.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTXIHJW/FZLTXHK.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'FZLTH4K';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTHK/FZLTHK--GBK1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTHK/FZLTHK--GBK1-0.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'FZLTCHJW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTCHJW/FZLTCHJW--GB1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTCHJW/FZLTCHJW--GB1-0.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'GothamMedium';
  src: url('https://lzcdn.dianpusoft.cn/fonts/Gotham/GothamMedium.otf') format('otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.w100 {
  width: 100%;
}
.btn-disabled {
  filter: grayscale(1);
}

html {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/264451/31/24989/144830/67bf04dfF2298d9d9/0b1d079c4489793e.jpg) no-repeat;
  background-size: 100% 100%;
  background-position: center 7.5rem;
  background-color: #14103b;
}
html body {
  font-family: 'FZLTXHJW', sans-serif;
  color: #fff;
}
.w698 {
  width: 6.98rem;
}
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
