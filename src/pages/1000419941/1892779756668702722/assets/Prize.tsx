import { PropType, defineComponent } from 'vue';
import styles from './Prize.module.scss';

// 奖品信息结构体
interface Prize {
  name: string; // 奖品名称
  // prizeDesc: string, // 奖品描述
  showImage: string; // 奖品图片
  type: number; // 奖品类型
  id: number | string; // 奖品ID
}

interface PrizeProp {
  prize: Prize;
}

/**
 * 实物商品
 *
 * @param {PrizeProp} { prize }商品信息
 */
const Product = ({ prize }: PrizeProp) => (
  <>
    <div class={styles['prize-name-sw']}>{prize.name}</div>
    <div
      class={styles['prize-product']}
      style={{
        backgroundImage: `url(${prize.showImage})`,
      }}></div>
  </>
);

/**
 * 京豆
 *
 * @param {PrizeProp} { prize }商品信息
 */
const OrBean = ({ prize }: PrizeProp) => (
  <>
    <div class={styles['prize-name-num']}>{prize.name}京豆</div>
    <img class={styles['prize-icon']} src="//img10.360buyimg.com/imgzone/jfs/t1/167576/3/36028/23763/64d35aacF4a2651a7/58a93a62d83c8e13.png" alt="" />
  </>
);

/**
 * 优惠券
 *
 * @param {PrizeProp} { prize }商品信息
 */
const Coupon = ({ prize }: PrizeProp) => (
  <>
    <div class={styles['prize-name-num']}>{prize.name}</div>
    <img class={styles['prize-icon']} src="//img10.360buyimg.com/imgzone/jfs/t1/189603/19/36407/25561/64d35aabFd01e2659/fb4cebbbc68908fb.png" alt="" />
  </>
);

/**
 * 店铺积分
 *
 * @param {PrizeProp} { prize }商品信息
 */
const Integral = ({ prize }: PrizeProp) => (
  <>
    <div class={styles['prize-name-num']}>{prize.name}积分</div>
    <img class={styles['prize-icon']} src="//img10.360buyimg.com/imgzone/jfs/t1/107479/34/36879/7858/64d35aabF9eacd614/cb10f30da67df50c.png" alt="" />
  </>
);
/**
 * 红包
 *
 * @param {PrizeProp} { prize }商品信息
 */
const RedEnvelope = ({ prize }: PrizeProp) => (
  <>
    <div class={styles['prize-name-num']}>{prize.name}</div>
    <img class={styles['prize-icon']} style={{ width: '.6rem' }} src="//img10.360buyimg.com/imgzone/jfs/t1/193291/38/36171/20318/64d35aabFc636a6cd/f86915999bec1125.png" alt="" />
  </>
);
/**
 * 其他（再来一次/谢谢参与）
 *
 * @param {PrizeProp} { prize }商品信息
 */
const Other = ({ prize }: PrizeProp) => <div class={styles['prize-other']}>{prize.name}</div>;
/**
 * 谢谢参与
 *
 * @param {PrizeProp} { prize }商品信息
 */
const OtherJoin = ({ prize }: PrizeProp) => (
  <>
    <div class={styles['prize-other-join']}>{prize.name}</div>
  </>
);

const getPrizeComp = (prize: Prize) => {
  if (prize.type === 1 || prize.type === 12 || prize.type === 10 || prize.type === 6 || prize.type === 9) {
    prize.name = prize.name.replace(/[^\d.]/g, '');
  } else if (prize.type === 0) {
    prize.name = prize.name.replace(/[!！]/g, '');
  }
  switch (prize.type) {
    case 0:
      return <OtherJoin prize={prize} />;
    case 1:
    case 20:
    case 10:
    case 11:
    case 12:
    case 17:
    case 19:
      return <Coupon prize={prize} />;
    case 6:
      return <OrBean prize={prize} />;
    case 3:
    case 21:
      return <Product prize={prize} />;
    case 9:
      return <Integral prize={prize} />;
    case 16:
      return <RedEnvelope prize={prize} />;
    default:
      return <Other prize={prize} />;
  }
};

export default defineComponent({
  props: {
    data: {
      type: Object as PropType<Prize>,
      required: true,
    },
    scale: {
      type: Number,
      default: 1,
    },
  },
  setup(props) {
    return () => (
      <div class={styles['prize-wrapper']} style={{ transform: `scale(${props.scale})` }}>
        {getPrizeComp(props.data)}
      </div>
    );
  },
});
