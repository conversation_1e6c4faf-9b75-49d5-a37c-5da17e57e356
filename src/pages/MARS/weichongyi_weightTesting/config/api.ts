import { httpRequest, httpNoAuthRequest } from '@/utils/service';
import { showToast, showFailToast, showLoadingToast, closeToast } from 'vant';
import { ref } from 'vue';

export const getInitData = async (param:any) => {
  try {
    // 发送POST请求到指定的URL，获取宠物列表数据
    const res = await httpNoAuthRequest.post('/user/marsWxLogin', param);
    // 检查响应数据是否存在
    if (res.data) {
    // 如果存在，返回整个响应对象
      return res;
    }
    // 如果不存在，显示错误信息并返回false
    showFailToast(res.message);
    return false;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    showFailToast(error.message);
  }
  // 无论上述情况如何，最终返回false
  return false;
};
/**
 * 获取宠物列表
 *
 * 此函数通过发送HTTP POST请求来获取用户的宠物列表如果提供了otherPin参数，则使用该参数进行查询
 * 如果请求成功且返回的数据存在，则返回整个响应对象；否则，显示错误信息并返回false
 *
 * @param otherPin 可选参数，指定其他用户的PIN，以获取该用户的宠物列表
 * @returns 返回宠物列表的响应对象，或者在失败时返回false
 */
export const getPetList = async (otherPin?: string| '') => {
  try {
    // 发送POST请求到指定的URL，获取宠物列表数据
    const res = await httpRequest.post('/mars/1899006146263613442/getPetList', otherPin);
    // 检查响应数据是否存在
    if (res.data) {
    // 如果存在，返回整个响应对象
      return res;
    }
    // 如果不存在，显示错误信息并返回false
    showFailToast(res.message);
    return false;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    showFailToast(error.message);
  } finally {
    closeToast();
  }
  // 无论上述情况如何，最终返回false
  return false;
};
/**
 * 异步保存宠物信息
 *
 * 此函数通过HTTP请求向服务器提交宠物信息，并在成功或失败后显示相应的通知
 * 它首先显示一个加载中的通知，然后根据请求结果关闭加载通知并显示结果通知
 *
 * @param petInfo 宠物信息对象，包含需要保存的宠物详细信息
 * @returns 返回一个Promise，根据保存操作的成功与否解析为true或false
 */
export const savePetInfo = async (petInfo: any) => {
  try {
    // 显示加载中的通知，禁止用户点击，无限期持续
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    // 发起HTTP POST请求，保存宠物信息
    const res = await httpRequest.post('/mars/1899006146263613442/savePetInfo', petInfo);
    // 关闭加载通知
    closeToast();
    // 显示保存成功的通知
    showToast('保存成功');
    // 返回成功标志
    return true;
  } catch (error: any) {
    // 捕获到错误时，显示失败的通知
    showFailToast(error.message);
  } finally {
    closeToast();
  }
  // 返回失败标志
  return false;
};
/**
 * 根据宠物类型获取宠物品种信息
 *
 * 此函数通过发送HTTP POST请求来获取特定宠物类型的品种信息
 * 它处理网络请求、成功和失败的逻辑，并将结果返回给调用者
 *
 * @param petType 宠物类型，用于查询品种信息的唯一标识
 * @returns 返回宠物品种信息的响应对象，如果请求失败则返回false
 */
export const getPetBreed = async (petType: any) => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    // 发送HTTP POST请求以获取宠物品种信息
    const res = await httpRequest.post('/mars/1899006146263613442/getPetBreed', petType);
    closeToast();
    // 检查响应数据是否存在
    if (res.data) {
    // 如果存在，则返回整个响应对象
      return res;
    }
    // 如果响应数据不存在，则显示失败提示，并返回false
    showFailToast(res.message);
    return false;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    showFailToast(error.message);
  }
  // 无论上述情况如何，最终返回false
  return false;
};
/**
 * 上传牙齿图片函数
 * 该函数用于通过HTTP POST请求上传牙齿图片并获取相关信息
 * @param {any} uploadInfo 包含上传图片信息的对象
 * @returns {Promise<any>} 返回包含响应数据的Promise对象，或在失败时返回false
 */
export const uploadToothImg = async (formdata: any) => {
  try {
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    const res = await httpRequest.post('/mars/1899006146263613442/uploadImg', formdata, config);
    if (res.data) {
      return res;
    }
    showFailToast(res.message);
    return false;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    showFailToast(error.message);
  }
  // 无论上述情况如何，最终返回false
  return false;
};
// 记录问卷结果
export const saveQaInfo = async (formdata: any) => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    const res = await httpRequest.post('/mars/1899006146263613442/saveQaInfo', formdata);
    closeToast();
    return true;
  } catch (error: any) {
    closeToast();
    // 捕获到异常时，显示错误信息
    showFailToast(error.message);
  }
  // 无论上述情况如何，最终返回false
  return false;
};
// 获取检测结果
export const getCheckUpResult = async (formdata: any) => {
  try {
    const res = await httpRequest.post('/mars/1899006146263613442/getCheckUpResult', formdata);
    return {
      status: true,
      res,
    };
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    // showFailToast(error.message);
    return {
      status: false,
      res: error,
    };
  }
  return false;
};
// 获取问卷结果
export const getQaInfo = async (formdata: any) => {
  try {
    const res = await httpRequest.post('/mars/1899006146263613442/getQaInfo', formdata);
    return res;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    showFailToast(error.message);
  }
  return false;
};
export const saveCheckResultImg = async (formdata: any) => {
  try {
    const res = await httpRequest.post('/mars/1899006146263613442/saveCheckWeightResultImg', formdata);
    return res;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    showFailToast(error.message);
  }
  return false;
};
export const getSkuList = async (formdata: any) => {
  try {
    const res = await httpRequest.post('/mars/1899006146263613442/getSkuList', formdata);
    return res;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    showFailToast(error.message);
  }
  return false;
};
export const getDetectionResult = async (formdata: any) => {
  try {
    const res = await httpRequest.post('/mars/1899006146263613442/getDetectionResult', formdata);
    return res;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    // showFailToast(error.message);
  }
  return false;
};
export const marsWxUploadToOss = async (formdata: any) => {
  try {
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    const res = await httpRequest.post('/mars/1899006146263613442/marsWxUploadToOss', formdata, config);
    return res;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    // showFailToast(error.message);
  }
  return false;
};
export const getRoyalCanin = async (formdata: any) => {
  try {
    const res = await httpRequest.post('/mars/1899006146263613442/getRoyalCanin', formdata);
    return res;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    // showFailToast(error.message);
  }
  return false;
};
export const updateWcyBreed = async (formdata: any) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/mars/1899006146263613442/updateWcyBreed', formdata);
    closeToast();
    return res;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    showFailToast(error.message);
  } finally {
    closeToast();
  }
  return false;
};
export const getPetBodySize = async (formdata: any) => {
  try {
    const res = await httpRequest.post('/mars/1899006146263613442/getPetBodySize', formdata);
    return res;
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    // showFailToast(error.message);
  }
  return false;
};
// 获取体重检测结果
export const getWeightCheckResult = async (formdata: any) => {
  try {
    const res = await httpRequest.post('/mars/1899006146263613442/getWeightCheckResult', formdata);
    return {
      status: true,
      res,
    };
  } catch (error: any) {
    // 捕获到异常时，显示错误信息
    // showFailToast(error.message);
    return {
      status: false,
      res: error,
    };
  }
  return false;
};
