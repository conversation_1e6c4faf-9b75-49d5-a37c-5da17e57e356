<template>
  <div class="main-view">
    <div>
      <img class="common-dog" :src="require(`../../asset/qaDog3.png`)"/>
      <div class="photo-bottom">
        <img class="item-btn" style="width: 2.16rem;" :src="require('../../asset/before.png')" @click="backStep"/>
        <img class="item-btn" style="width: 2.16rem;" v-if="questionData.status == -1" :src="require('../../asset/next-no.png')"/>
        <img class="item-btn" style="width: 4.16rem;" v-else :src="require('../../asset/to-result.png')" @click="submitData"/>
      </div>
      <div class="common-content">
          <div class="qustion-slise-box" :style="`background-color: ${PAGE_CONFIG.questionSlideBg};`">
          <div class="qustion-slise-item" :style="`background-color: ${PAGE_CONFIG.quuetionSlideColor};width: ${ Math.floor((3 / (PAGE_CONFIG.problemList.length || 1)) * 100) }%;`"></div>
        </div>
        <div class="qustion-step-text">{{ `(3/${PAGE_CONFIG.problemList.length})` }}</div>
        <img class="qustion-text" :src="require(`../../asset/step3-title.png`)"/>
        <div @click="questionData.status = 1">
            <img class="qustion-yes-btn" v-if="questionData.status == 1"  :src="require(`../../asset/step-yes-act.png`)"/>
            <img class="qustion-yes-btn" v-else :src="require(`../../asset/step-yes.png`)"/>
          </div>
          <div @click="questionData.status = 0">
            <img class="qustion-yes-btn" v-if="questionData.status == 0" :src="require(`../../asset/step-no-act.png`)"/>
            <img class="qustion-yes-btn" v-else :src="require(`../../asset/step-no.png`)"/>
          </div>
          <img class="qustion-step-tips" :src="require(`../../asset/qadesc3.png`)" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { inject, ref, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { saveQaInfo } from '../../config/api';
import { lzReportClick } from '@/pages/MARS/lzReport';

const route = useRoute();
const router = useRouter();
const store = useStore();

const { checkId } = route.query;

const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const questionData = reactive({
  title: PAGE_CONFIG.problemList[2].title,
  status: -1,
  desc: PAGE_CONFIG.problemList[2].desc,
  type: PAGE_CONFIG.problemList[2].type,
});

const submitData = () => {
  if (questionData.status === -1) {
    return;
  }
  lzReportClick('qa3');
  // 将当前步骤的数据保存到Vuex中
  store.commit('setStepData', {
    step: 3,
    data: {
      [questionData.type]: questionData.status === 1,
    },
  });

  // 整合所有步骤的数据
  const formatData: {
    checkId: any;
    ifBleeding: boolean;
    ifDiscomfort: boolean;
    ifHalitosis: boolean;
    petNick: string;
    [key: string]: any; // 允许动态属性
  } = {
    checkId,
    ifBleeding: false,
    ifDiscomfort: false,
    ifHalitosis: false,
    petNick: '',
  };

  // 从Vuex获取第一步数据
  const step1Data = store.state.stepData?.[1];
  if (step1Data) {
    if (step1Data.petNick) {
      formatData.petNick = step1Data.petNick || '';
    }

    // 检查第一步是否有问题答案
    if (step1Data.ifHalitosis !== undefined) {
      formatData.ifHalitosis = step1Data.ifHalitosis;
    } else if (step1Data.ifBleeding !== undefined) {
      formatData.ifBleeding = step1Data.ifBleeding;
    } else if (step1Data.ifDiscomfort !== undefined) {
      formatData.ifDiscomfort = step1Data.ifDiscomfort;
    }
  }

  // 从Vuex获取第二步数据
  const step2Data = store.state.stepData?.[2];
  if (step2Data) {
    if (step2Data.ifHalitosis !== undefined) {
      formatData.ifHalitosis = step2Data.ifHalitosis;
    } else if (step2Data.ifBleeding !== undefined) {
      formatData.ifBleeding = step2Data.ifBleeding;
    } else if (step2Data.ifDiscomfort !== undefined) {
      formatData.ifDiscomfort = step2Data.ifDiscomfort;
    }
  }

  // 直接使用当前组件中的状态，确保最新状态
  formatData[questionData.type] = questionData.status === 1;

  console.log('提交的数据:', formatData);

  // 提交数据到API
  saveQaInfo(formatData).then((res) => {
    if (res) {
      router.push({
        path: '/endResult',
        query: {
          checkId,
        },
      });
    } else {
      router.push('/stepError');
    }
  });
};

const backStep = () => {
  router.back();
};

const init = () => {
  // 如果Vuex中已有第三步数据，则恢复
  const stepData = store.state.stepData?.[3];
  if (stepData && stepData[questionData.type] !== undefined) {
    questionData.status = stepData[questionData.type] ? 1 : 0;
  }
};

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss">
/* 隐藏Webkit内核浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}
</style>
