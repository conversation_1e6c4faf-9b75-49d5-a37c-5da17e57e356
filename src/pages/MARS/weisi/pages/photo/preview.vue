<template>
  <div class="main-view" :style="{paddingTop: uploadStatus === 'loading' ? 'unset' : '0.43rem'}">
    <div>
      <div v-if="uploadStatus === 'upload'">
        <img class="common-dog" :src="require(`../../asset/photoDog.png`)"/>
        <img class="common-btn" :src="require(`../../asset/uploadbtn.png`)" @click="uploadImmg"/>
        <div class="common-content">
          <div class="photo-review" :style="`background-image: url(${needHandleImageSrc})`"></div>
          <img class="retake-photo" :src="require(`../../asset/retakePhoto.png`)" @click="retakePhoto"/>
        </div>
      </div>
      <div v-else-if="uploadStatus === 'loadding'">
        <Loading :pageNo="1"></Loading>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { uploadToothImg, getDetectionResult } from '../../config/api';
import Loading from '../../components/Loading.vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { PAGE_CONFIG } from '../../config/config';
import { RootState } from '../../store/state';
import { lzReportClick } from '@/pages/MARS/lzReport';

const router = useRouter();
const store = useStore<RootState>();
const needHandleImageSrc = ref('');
const uploadStep = ref(1);
const uploadStatus = ref('upload');
const fileBlob = ref<Blob>();
const retakePhoto = () => {
  uploadStep.value = 1;
  needHandleImageSrc.value = '';
  fileBlob.value = undefined;
  router.push('/photo');
};

const checkId = ref('');
const getResNo = ref(0);

const getDetectionResultTest = () => {
  getDetectionResult({ checkId: checkId.value }).then((ressult: any) => {
    if (ressult.data) {
      if (ressult.data.status === 1) {
        router.push({
          path: '/result',
          query: {
            checkId: checkId.value,
          },
        });
        sessionStorage.setItem('toothImg', ressult.data.imgUrl);
        store.commit('setToothImg', ressult.data.imgUrl);
      } else if (ressult.data.status === 0 && getResNo.value <= 20) {
        setTimeout(() => {
          getDetectionResultTest();
          console.log(getResNo.value);
          getResNo.value += 1;
        }, 3000);
      } else if (ressult.data.status === 0 && getResNo.value > 20) {
        router.push('/stepError');
      } else {
        router.push(`/error${ressult.data.errorType}`);
      }
    } else {
      router.push('/stepError');
    }
  });
};
const PetInfo = computed(() => store.state.petInfo);
const uploadImmg = async () => {
  lzReportClick('uploadPhoto');
  uploadStatus.value = 'loadding';
  const formData = new FormData();
  fileBlob.value && formData.append('file', fileBlob.value, 'postimg.png');
  // TODO: petId可以从url上获取 问问子平看要不要传
  formData.append('petId', PetInfo.value.petId);
  uploadToothImg(formData).then((res: any) => {
    console.log(res);
    if (res.data) {
      store.commit('setCheckId', res.data.checkId);
      checkId.value = res.data.checkId;
      getDetectionResultTest();
    } else {
      router.push('/photoError');
    }
  });
};

const getBlob = () => {
  const dataUrl = sessionStorage.getItem('fileBlobUrl') as string;
  needHandleImageSrc.value = dataUrl;
  fetch(dataUrl).then((res) => res.blob())
    .then((blob) => {
      fileBlob.value = blob;
    });
};
getBlob();

</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss">
/* 隐藏Webkit内核浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}
</style>
