<template>
  <div class="main-view" style="display: flex;align-items: center;">
    <img style="width: 7.5rem;" alt="" :src="require(`../../asset/photoErrorDfault.png`)" @click="router.push('/photo')"/>
  </div>
</template>
<script lang="ts" setup>
import { useRouter } from 'vue-router';

const router = useRouter();
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss">
/* 隐藏Webkit内核浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}
</style>
