<template>
  <div class="main-view" :style="{paddingTop:  '0.43rem'}">
    <div>
      <img class="common-dog" :src="require(`../../asset/photoErrorDog.png`)"/>
      <img class="common-btn" :src="require(`../../asset/retakebtn.png`)" @click="()=>{router.push('/photo');lzReportClick('reUpload')}"/>
      <div class="common-content">
        <img :src="require('../../asset/home-top-logo.png')" class="upload-error-title"/>
        <img class="upload-error-img" :src="require(`../../asset/notdog.png`)" style="margin-top: 0.6rem;"/>
        <img class="upload-error-desc" :src="require(`../../asset/errorDesc.png`)"/>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>

import { useRouter } from 'vue-router';
import { lzReportClick } from '@/pages/MARS/lzReport';

import { PAGE_CONFIG } from '../../config/config';

const router = useRouter();
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss">
/* 隐藏Webkit内核浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}
</style>
