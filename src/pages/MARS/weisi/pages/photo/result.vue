<template>
  <div class="main-view" :style="{paddingTop: '0.43rem'}">
    <div>
      <img class="common-dog" :src="require(`../../asset/photoDog.png`)"/>
      <img class="common-btn" :src="require(`../../asset/toqa.png`)" @click="handleOpenQuestion"/>
      <div class="common-content">
        <img class="upload-error-img" :src="require(`../../asset/uploadSucces.png`)"/>
        <div class="upload-info" v-for="(it, index) in PAGE_CONFIG.uploadSuceesTips" :key="index">
          {{ it }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router';
import { lzReportClick } from '@/pages/MARS/lzReport';
import { PAGE_CONFIG } from '../../config/config';

const router = useRouter();
const route = useRoute();

const { checkId } = route.query;

const handleOpenQuestion = () => {
  lzReportClick('goAnswer');
  router.push({
    path: '/step1',
    query: {
      checkId,
    },
  });
};
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss">
/* 隐藏Webkit内核浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}
</style>
