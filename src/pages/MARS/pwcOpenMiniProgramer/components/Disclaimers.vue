<template>
  <VanPopup teleport="body" v-model:show="show" position="center" :close-on-click-overlay="true">
      <div>
          <img class="pet-close-icon close-icon" :src="require('../asset/closeIcon.png')" @click="closePopup()"/>
          <div class="common-text-popup">
            <!-- <img class="bg-img" :src="require('../asset/免责声明popup.png')"/> -->
            <img class="bg-img" src="//img10.360buyimg.com/imgzone/jfs/t1/280634/25/25636/68419/680f3823F2448e5fc/191ede1efc3f528f.png"/>
            <div class="click-div" style="left: 3.56rem;top: 7.45rem;" @click="openPrivacyLinkLink"></div>
            <div class="click-div" style="left: 4.98rem;top: 7.45rem;" @click="openTermsOfUse"></div>
          </div>
      </div>
  </VanPopup>
</template>
<script lang="ts" setup>
import { FLAGS } from 'html2canvas/dist/types/dom/element-container';
import { emit } from 'process';
import { defineProps, computed, ref, inject } from 'vue';

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
  },
});

const PAGE_CONFIG:any = inject('PAGE_CONFIG');

const emits = defineEmits(['closePopup', 'showprivacy', 'showtermsOfUse']);

const show = computed(() => props.isShow);
const openPrivacyLinkLink = () => {
  emits('showprivacy');
};
const openTermsOfUse = () => {
  emits('showtermsOfUse');
};
const closePopup = () => {
  emits('closePopup');
};

</script>
<style lang="scss" scoped>
@import '../page.scss';
.click-div {
position: absolute;
width: 1.3rem;
height: 0.3rem;
}
</style>
