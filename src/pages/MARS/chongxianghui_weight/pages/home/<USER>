<template>
  <div class="main-view" style="background-color: #e7ad3c;">
      <!-- 隐藏的 SVG 滤镜定义 -->
      <svg style="display: none;">
          <defs>
              <filter id="blackwhite-mask">
                  <feColorMatrix type="matrix" values="0.33 0.33 0.33 0 0.2
                                                       0.33 0.33 0.33 0 0.2
                                                       0.33 0.33 0.33 0 0.2
                                                       0 0 0 1 0"/>
                  <feComponentTransfer>
                      <feFuncR type="table" tableValues="0 1"/>
                      <feFuncG type="table" tableValues="0 1"/>
                      <feFuncB type="table" tableValues="0 1"/>
                  </feComponentTransfer>
              </filter>
          </defs>
      </svg>
      <img class="common-dog" :src="require(`../../asset/home/<USER>"/>
      <!-- <img class="common-btn" :src="require(`../../asset/home/<USER>" v-if="failbtn" @click="showFailToast('抱歉，您不符合参与资格，无法参与')"/> -->
      <img class="common-btn" :src="require(`../../asset/home/<USER>" @click="joinNow"/>
      <div class="common-content">
          <img :src="require('../../asset/home/<USER>')" style="width: 7.5rem;"/>
          <div class="home-content-view BL-home-content">
              <img class="kv-img" :src="require('../../asset/home/<USER>')"/>
              <div class="click-div" style="left: 1rem;top: 2.5rem;" @click="isShowOralHealthIssues = true"></div>
              <div class="click-div" style="top: 6.5rem;" @click="isShowDisclaimers = true"></div>
              <div class="bottom-click-div" @click="showhomeLink"></div>
          </div>
          <div class="home-check-box" style="margin-top: 0.25rem;" @click="isRead = !isRead">
              <img v-if="isRead" class="check-icon" :src="require(`../../asset/home/<USER>"/>
              <img v-else class="check-icon" :src="require(`../../asset/home/<USER>"/>
              <img class="check-text" :src="require(`../../asset/home/<USER>"/>
          </div>
      </div>
      <!-- 这家店没有接会员通，暂时用不上宠物列表，但是以防万一，保留 -->
      <VanPopup teleport="body" v-model:show="petList" position="center" :close-on-click-overlay="true">
          <div class="pet-popup">
              <img class="pet-close-icon close-icon" :src="require('../../asset/home/<USER>')" @click="petList = false"/>
              <div class="pet-content">
                  <div class="pet-title">选择宠物</div>
                  <div style="width: 100%;height: 6.5rem;overflow-y: scroll;">
                    <div v-for="(it, index) in petListArr" :key="index">
                        <div class="pet-item" v-if="it === selectPet && it.petType == 1 && it.petBodySize != 6 && it.status == 0" :style="`background-color: ${PAGE_CONFIG.mainBgColor};color: ${PAGE_CONFIG.mainTextColor}`">
                            <div class="pet-img">
                                <img v-if="it.petAvatar" :src="it.petAvatar"/>
                                <img v-else :src="require(`../../asset/home/<USER>"/>
                            </div>
                            <div class="pet-info-wrapper">
                                <div class="row-1-wrap">
                                    <text>{{it.petNick}}</text>
                                    <img v-if="it.petGender === 1" :src="require(`../../asset/home/<USER>" />
                                    <img v-else-if="it.petGender === 2" :src="require(`../../asset/home/<USER>"/>
                                </div>
                                <div class="row-2-wrap">
                                    <text>{{it.petAge}}</text>
                                    <!-- <div>{{it.ageStr}}</div> -->
                                </div>
                                <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                                </div>
                            <img class="pet-icon" :src="require(`../../asset/home/<USER>"/>
                        </div>
                        <div class="pet-item" v-else-if="it !== selectPet && it.petType == 1 && it.petBodySize != 6 && it.status == 0" :style="`border-color: ${PAGE_CONFIG.mainBgColor};`" @click="selectPet = it">
                            <div class="pet-img">
                                <img v-if="it.petAvatar" :src="it.petAvatar"/>
                                <img v-else :src="require(`../../asset/home/<USER>"/>
                            </div>
                            <div class="pet-info-wrapper">
                                <div class="row-1-wrap">
                                    <text>{{it.petNick}}</text>
                                    <img v-if="it.petGender === 1" :src="require(`../../asset/home/<USER>" />
                                    <img v-else-if="it.petGender === 2" :src="require(`../../asset/home/<USER>"/>
                                </div>
                                <div class="row-2-wrap">
                                    <text>{{it.petAge}}</text>
                                    <!-- <div>{{it.ageStr}}</div> -->
                                </div>
                                <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                                </div>
                            <img class="pet-icon" :src="require(`../../asset/home/<USER>"/>
                        </div>
                        <div class="pet-item" v-else-if="it !== selectPet && it.petType == 1 && it.petBodySize == 6 && it.status == 0" :style="`border-color: ${PAGE_CONFIG.mainBgColor};`" @click="showFailToast('抱歉，工具暂不支持成年后体重超过40kg的超大型犬种')">
                            <div class="pet-img">
                                <img v-if="it.petAvatar" :src="it.petAvatar"/>
                                <img v-else :src="require(`../../asset/home/<USER>"/>
                            </div>
                            <div class="pet-info-wrapper">
                                <div class="row-1-wrap">
                                    <text>{{it.petNick}}</text>
                                    <img v-if="it.petGender === 1" :src="require(`../../asset/home/<USER>" />
                                    <img v-else-if="it.petGender === 2" :src="require(`../../asset/home/<USER>"/>
                                </div>
                                <div class="row-2-wrap">
                                    <text>{{it.petAge}}</text>
                                    <!-- <div>{{it.ageStr}}</div> -->
                                </div>
                                <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                                </div>
                            <img class="pet-icon" :src="require(`../../asset/home/<USER>"/>
                        </div>
                        <div v-else-if="it !== selectPet && it.petType == 1 && it.status == 1" style="position: relative;">
                        <div class="pet-item"  style="filter: url(#blackwhite-mask);" :style="`border-color: ${PAGE_CONFIG.mainBgColor};`" @click="showFailToast('狗狗未满12周，不适用此工具')">
                            <div class="pet-img">
                                <img v-if="it.petAvatar" :src="it.petAvatar"/>
                                <img v-else :src="require(`../../asset/home/<USER>"/>
                            </div>
                            <div class="pet-info-wrapper">
                                <div class="row-1-wrap">
                                    <text>{{it.petNick}}</text>
                                    <img v-if="it.petGender === 1" :src="require(`../../asset/home/<USER>" />
                                    <img v-else-if="it.petGender === 2" :src="require(`../../asset/home/<USER>"/>
                                </div>
                                <div class="row-2-wrap">
                                    <text>{{it.petAge}}</text>
                                    <!-- <div>{{it.ageStr}}</div> -->
                                </div>
                                <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                                </div>
                            <img class="pet-icon" :src="require(`../../asset/home/<USER>"/>
                        </div>
                        <div style="color: #eb031d;position: absolute;top: 1.15rem;left: 1.8rem;font-size: 0.2rem;">
                                <text>狗狗未满12周，不适用此工具</text>
                                <!-- <div>{{it.ageStr}}</div> -->
                            </div>
                      </div>
                        <div v-else-if="it !== selectPet && it.petType == 1 && it.status == 2" style="position: relative;">
                            <div class="pet-item" style="filter: url(#blackwhite-mask);" :style="`border-color: ${PAGE_CONFIG.mainBgColor};`" @click="showFailToast('狗狗已成年，不适用此工具')">
                                <div class="pet-img">
                                    <img v-if="it.petAvatar" :src="it.petAvatar"/>
                                    <img v-else :src="require(`../../asset/home/<USER>"/>
                                </div>
                                <div class="pet-info-wrapper">
                                    <div class="row-1-wrap">
                                        <text>{{it.petNick}}</text>
                                        <img v-if="it.petGender === 1" :src="require(`../../asset/home/<USER>" />
                                        <img v-else-if="it.petGender === 2" :src="require(`../../asset/home/<USER>"/>
                                    </div>
                                    <div class="row-2-wrap">
                                        <text>{{it.petAge}}</text>
                                        <!-- <div>{{it.ageStr}}</div> -->
                                    </div>
                                    <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                                    </div>
                                <img class="pet-icon" :src="require(`../../asset/home/<USER>"/>
                            </div>
                            <div style="color: #eb031d;position: absolute;top: 1.15rem;left: 1.8rem;font-size: 0.2rem;">
                                <text>狗狗已经成年啦，不适用此工具</text>
                                <!-- <div>{{it.ageStr}}</div> -->
                            </div>
                        </div>
                        <div class="pet-item" v-else style="filter: url(#blackwhite-mask);" :style="`border-color: ${PAGE_CONFIG.mainBgColor};`">
                            <div class="pet-img">
                                <img v-if="it.petAvatar" :src="it.petAvatar"/>
                                <img v-else :src="require(`../../asset/home/<USER>"/>
                            </div>
                            <div class="pet-info-wrapper">
                                <div class="row-1-wrap">
                                    <text>{{it.petNick}}</text>
                                    <img v-if="it.petGender === 1" :src="require(`../../asset/home/<USER>" />
                                    <img v-else-if="it.petGender === 2" :src="require(`../../asset/home/<USER>"/>
                                </div>
                                <div class="row-2-wrap">
                                    <text>{{it.petAge}}</text>
                                    <!-- <div>{{it.ageStr}}</div> -->
                                </div>
                                <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                                </div>
                            <img class="pet-icon" :src="require(`../../asset/home/<USER>"/>
                        </div>
                    </div>
                  </div>
                  <img class="pet-add-btn" v-if="petListArr.length < 5" :src="require(`../../asset/home/<USER>" @click="router.push('/create')"/>
                  <img class="pet-confirm-btn" :src="require(`../../asset/home/<USER>" @click="confirmPet"/>
              </div>
          </div>
      </VanPopup>
  <Disclaimers :isShow="isShowDisclaimers" @closePopup="closeDisclaimers" @showprivacy="showprivacy" @showtermsOfUse="showtermsOfUse"/>
  <OralHealthIssues :isShow="isShowOralHealthIssues" @closePopup="isShowOralHealthIssues = false" @showprivacy="showprivacy" @showtermsOfUse="showtermsOfUse"/>
  <SuitableForPets :isShow="isShowSuitableForPets" @closePopup="isShowSuitableForPets = false" @showprivacy="showprivacy" @showtermsOfUse="showtermsOfUse"/>
  <LinkPopup :isShow="isShowLinkPopup" :type="linkpoupType" @closePopup="isShowLinkPopup = false"/>
  </div>
</template>
<script lang="ts" setup>
import { inject, ref, onMounted } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import Disclaimers from '../../components/Disclaimers.vue';
import OralHealthIssues from '../../components/OralHealthIssues.vue';
import SuitableForPets from '../../components/SuitableForPets.vue';
import LinkPopup from '../../components/LinkPopup.vue';
import { getPetList, getToken } from '../../config/api';
import { showFailToast } from 'vant';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import { useRouter } from 'vue-router';
import { lzReportClick } from '@/pages/MARS/lzReport';

const router = useRouter();
const store = useStore<RootState>();
interface PetItem {
  id: number;
  petAvatar: string;
  petNick: string;
  petType: number; // 1'dog' | 2'cat'
  petBreed: string;
  petGender: number; // 0:未知，1：公，2：母
  petBodySize: number;
  status: number;
  petAge: string;
}

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG: any = inject('PAGE_CONFIG');

const petList = ref(false);
const isRead = ref(false);

const isShowDisclaimers = ref(false);
const isShowOralHealthIssues = ref(false);
const isShowSuitableForPets = ref(false);
const isShowLinkPopup = ref(false);
const linkpoupType = ref('');
const emits = defineEmits(['toggleComponent']);
const showprivacy = () => {
  linkpoupType.value = 'privacyLink';
  isShowLinkPopup.value = true;
  isShowOralHealthIssues.value = false;
  isShowDisclaimers.value = false;
  isShowSuitableForPets.value = false;
};
const showtermsOfUse = () => {
  linkpoupType.value = 'termsOfUse';
  isShowLinkPopup.value = true;
  isShowOralHealthIssues.value = false;
  isShowDisclaimers.value = false;
  isShowSuitableForPets.value = false;
};
const showhomeLink = () => {
  linkpoupType.value = 'homeLink';
  isShowLinkPopup.value = true;
  isShowOralHealthIssues.value = false;
  isShowDisclaimers.value = false;
  isShowSuitableForPets.value = false;
};
const closeDisclaimers = () => {
  isShowDisclaimers.value = false;
};
const url = ref('');
url.value = `/pages/loginAuthorization/index?${encodeURIComponent(window.location.href)}`;

const toMp = () => {
  window.wx.miniProgram.navigateTo({
    url: url.value,
  });
};
const joinNow = () => {
  lzReportClick('startCheck');
  if (!isRead.value) {
    showFailToast('请阅读、知晓并同意本页面免责声明');
    return;
  }
  getToken().then((res: any) => {
    if (res.data) {
      petList.value = true;
    } else {
      toMp();
    }
  });
};

const petListArr = ref<PetItem[]>([]);
const selectPet = ref();
const confirmPet = () => {
  if (!selectPet.value) {
    showFailToast('请选择宠物');
    return;
  }
  console.log('=>(App.vue:59) selectPet', selectPet.value);
  petList.value = false;
  store.commit('setPetInfo', selectPet.value);
  router.push('/step1');
};
const openHomeLink = () => {
  window.open(PAGE_CONFIG.homeLink);
};
const failbtn = ref(false);
// 定义请求签名的函数
const requestSignature = async () => {
  try {
    const response = await fetch(`${process.env.VUE_APP_API_HOST}/common/wx/signature`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url,
      }),
    });
    return await response.json();
  } catch (error) {
    console.error('请求签名失败:', error);
    throw error;
  }
};

// 重构后的 wxSdkConfig 函数
const wxSdkConfig = () => new Promise((resolve) => {
  try {
    console.log('获取微信签名-开始', window.location.href);

    // 使用 async/await 包装的请求函数
    requestSignature()
      .then((data) => {
        console.log('获取微信签名-完成', data);
        const wxConfig = {
          debug: false,
          appId: data.data.appid,
          timestamp: data.data.timestamp,
          nonceStr: data.data.nonceStr,
          signature: data.data.signature,
          openTagList: ['wx-open-launch-weapp'],
          jsApiList: ['updateAppMessageShareData'],
        };

        console.log('微信config-开始', window.location.href);
        window.wx.config(wxConfig);
        console.log('微信config-完成', wxConfig);

        window.wx.ready(() => {
          console.log('wx sdk ready');
        });

        window.wx.error((res: any) => {
          console.error('wx sdk error', res);
        });

        resolve(true);
      })
      .catch((e) => {
        console.log('获取微信签名-失败', e);
        resolve(true);
      });
  } catch (e) {
    console.log('获取微信签名-失败', e);
    resolve(true);
  }
});
const init = () => {
  wxSdkConfig();
  if (navigator.userAgent.indexOf('iPhone') !== -1) {
    window.entryUrl = window.location.href;
  }
  getToken().then((res: any) => {
    if (res.data) {
      getPetList().then((res) => {
        // console.log(res);
        if (res.data && res.data.length > 0) {
          petListArr.value = res.data.filter((it: any) => it.petType && it.petNick);
        } else {
        // 如果数据为空，初始化为空值或默认值
          selectPet.value = undefined;
          petListArr.value = [];
        }
        if (store.state.homeShowList) {
          petList.value = true;
        }
      });
    } else {
      failbtn.value = true;
    }
  });
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss" scoped>
.BL-home-content {
  height: 8rem;
  .text-item {
      text-align: left;
  }
  .open-popup {
      color: #fff;
  }
  .link-text {
      color: #fff;
  }
}
.BL-home-bottom-btn {
  width: 6.74rem;
  height: 1.03rem;
  border-radius: 0.52rem;
  margin: unset;
  position: absolute;
  bottom: 0.55rem;
  left: 0.38rem;
}
</style>
