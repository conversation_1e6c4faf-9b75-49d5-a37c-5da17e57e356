<script setup lang="ts">
import { ref, onMounted, inject } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const { checkId, ossPicUrl, petId, userKey } = route.query;

const iframeUrl = ref('');
const loading = ref(true);

onMounted(() => {
  // 首次加载不带picUrl参数的URL
  const initialUrl = `${process.env.VUE_APP_WEICHONGYI_CONSULT_URL}?userKey=${encodeURIComponent(userKey as string)}&companyID=676344787275836663414260&petid=${petId}`;
  iframeUrl.value = initialUrl;
  // 监听iframe加载完成事件
  const iframe = document.getElementById('consultIframe') as HTMLIFrameElement;
  iframe.onload = () => {
    loading.value = false;

    // 首次加载完成后，切换到带picUrl参数的URL
    if (iframeUrl.value === initialUrl) {
      setTimeout(() => {
        iframeUrl.value = `${process.env.VUE_APP_WEICHONGYI_CONSULT_URL}?userKey=${encodeURIComponent(userKey as string)}&companyID=676344787275836663414260&petid=${petId}&picUrl=${encodeURIComponent(ossPicUrl as string)}`;
      }, 5000);
    }
  };
});
</script>

<template>
  <div class="iframe-container">
    <div v-if="loading" class="loading">加载中...</div>
    <iframe
      id="consultIframe"
      :src="iframeUrl"
      frameborder="0"
      scrolling="auto"
      class="consult-iframe"
    ></iframe>
  </div>
</template>

<style scoped lang="scss">
.iframe-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
  color: #666;
}

.consult-iframe {
  width: 100%;
  height: 100%;
  border: none;
  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    display: none;
  }

  /* 隐藏IE、Edge和Firefox的滚动条 */
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
</style>
