<template>
  <div class="main-view">
    <!-- 隐藏的 SVG 滤镜定义 -->
    <svg style="display: none;">
      <defs>
        <filter id="blackwhite-mask">
          <feColorMatrix type="matrix" values="0.33 0.33 0.33 0 0.2
                                                         0.33 0.33 0.33 0 0.2
                                                         0.33 0.33 0.33 0 0.2
                                                         0 0 0 1 0"/>
          <feComponentTransfer>
            <feFuncR type="table" tableValues="0 1"/>
            <feFuncG type="table" tableValues="0 1"/>
            <feFuncB type="table" tableValues="0 1"/>
          </feComponentTransfer>
        </filter>
      </defs>
    </svg>
    <img class="home-dog" :src="require(`../../asset/homeDog.png`)"/>
    <img class="home-right-icon" :src="require(`../../asset/免责声明.png`)" style="top: 0.5rem;" @click="isShowDisclaimers = true"/>
    <img class="home-right-icon" :src="require(`../../asset/适用宠物.png`)" style="top: 1.8rem;" @click="isShowSuitableForPets = true"/>
    <img class="home-bottom-btn" :src="require(`../../asset/json-now.png`)" @click="joinNow"/>
    <div class="home-check-box" @click="isRead = !isRead">
      <img v-if="isRead" class="check-icon" :src="require(`../../asset/radioIcon-red.png`)"/>
      <img v-else class="check-icon" :src="require(`../../asset/radio-icon.png`)"/>
      <img class="check-text" :src="require(`../../asset/read-text.png`)"/>
    </div>
    <div class="home-content-view">
      <img class="bg-img" :src="require(`../../asset/home-content.png`)"/>
      <div class="click-div" @click="isShowOralHealthIssues = true"></div>
      <div class="bottom-click-div" @click="showhomeLink()"></div>
    </div>
    <VanPopup teleport="body" v-model:show="petList" position="center" :close-on-click-overlay="true">
      <div class="pet-popup">
        <img class="pet-close-icon close-icon" :src="require('../../asset/closeIcon.png')" @click="petList = false"/>
        <div class="pet-content">
          <div class="pet-title">选择宠物</div>
          <div style="width: 100%;height: 6.5rem;overflow-y: scroll;">
            <template v-for="(it, index) in petListArr" :key="index">
              <div class="pet-item" v-if="it === selectPet && it.petType == 1" :style="`background-color: ${PAGE_CONFIG.mainBgColor};color: ${PAGE_CONFIG.mainTextColor}`">
                <div class="pet-img">
                  <img v-if="it.petAvatar" :src="it.petAvatar"/>
                  <img v-else :src="require(`../../asset/defaultDogImg.jpg`)"/>
                </div>
                <div class="pet-info-wrapper">
                  <div class="row-1-wrap">
                    <text>{{it.petNick}}</text>
                    <img v-if="it.petGender === 1" :src="require(`../../asset/sex-m.png`)" />
                    <img v-else-if="it.petGender === 2" :src="require(`../../asset/sex-f.png`)"/>
                  </div>
                  <div class="row-2-wrap">
                    <text>{{it.petBreed}}</text>
                    <!-- <div>{{it.ageStr}}</div> -->
                  </div>
                  <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                </div>
                <img class="pet-icon" :src="require(`../../asset/zhuazi.png`)"/>
              </div>
              <div class="pet-item" v-else-if="it !== selectPet && it.petType == 1" :style="`border-color: ${PAGE_CONFIG.mainBgColor};`" @click="selectPet = it">
                <div class="pet-img">
                  <img v-if="it.petAvatar" :src="it.petAvatar"/>
                  <img v-else :src="require(`../../asset/defaultDogImg.jpg`)"/>
                </div>
                <div class="pet-info-wrapper">
                  <div class="row-1-wrap">
                    <text>{{it.petNick}}</text>
                    <img v-if="it.petGender === 1" :src="require(`../../asset/sex-m-red.png`)" />
                    <img v-else-if="it.petGender === 2" :src="require(`../../asset/sex-f-red.png`)"/>
                  </div>
                  <div class="row-2-wrap">
                    <text>{{it.petBreed}}</text>
                    <!-- <div>{{it.ageStr}}</div> -->
                  </div>
                  <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                </div>
                <img class="pet-icon" :src="require(`../../asset/zhuazi-red.png`)"/>
              </div>
              <div class="pet-item" v-else style="filter: url(#blackwhite-mask);" :style="`border-color: ${PAGE_CONFIG.mainBgColor};`" @click="showFailToast('猫咪暂不支持 尽情期待')">
                <div class="pet-img">
                  <img v-if="it.petAvatar" :src="it.petAvatar"/>
                  <img v-else :src="require(`../../asset/defaultDogImg.jpg`)"/>
                </div>
                <div class="pet-info-wrapper">
                  <div class="row-1-wrap">
                    <text>{{it.petNick}}</text>
                    <img v-if="it.petGender === 1" :src="require(`../../asset/sex-m.png`)" />
                    <img v-else-if="it.petGender === 2" :src="require(`../../asset/sex-f.png`)"/>
                  </div>
                  <div class="row-2-wrap">
                    <text>{{it.petBreed}}</text>
                    <!-- <div>{{it.ageStr}}</div> -->
                  </div>
                  <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                </div>
                <img class="pet-icon" :src="require(`../../asset/zhuazi-red.png`)"/>
              </div>
            </template>
          </div>
          <img class="pet-add-btn" v-if="petListArr.length < 5" :src="require(`../../asset/addPet.png`)" @click="router.push('/createPet')"/>
          <img class="pet-confirm-btn" :src="require(`../../asset/pet-confirm-btn.png`)" @click="confirmPet"/>
        </div>
      </div>
    </VanPopup>
    <Disclaimers :isShow="isShowDisclaimers" @closePopup="closeDisclaimers" @showprivacy="showprivacy" @showtermsOfUse="showtermsOfUse"/>
    <OralHealthIssues :isShow="isShowOralHealthIssues" @closePopup="isShowOralHealthIssues = false" @showprivacy="showprivacy" @showtermsOfUse="showtermsOfUse"/>
    <SuitableForPets :isShow="isShowSuitableForPets" @closePopup="isShowSuitableForPets = false" @showprivacy="showprivacy" @showtermsOfUse="showtermsOfUse"/>
    <LinkPopup :isShow="isShowLinkPopup" :type="linkpoupType" @closePopup="isShowLinkPopup = false"/>
  </div>
</template>
<script lang="ts" setup>
import { inject, ref, onMounted } from 'vue';
import Disclaimers from '../../components/Disclaimers.vue';
import LinkPopup from '../../components/LinkPopup.vue';
import OralHealthIssues from '../../components/OralHealthIssues.vue';
import SuitableForPets from '../../components/SuitableForPets.vue';
import { getPetList } from '../../config/api';
import { showFailToast } from 'vant';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import { useRouter } from 'vue-router';
import { lzReportClick } from '@/pages/MARS/lzReport';

const router = useRouter();
const store = useStore<RootState>();
interface PetItem {
  id: number;
  petAvatar: string;
  petNick: string;
  petType: number; // 1'dog' | 2'cat'
  petBreed: string;
  petGender: number; // 0:未知，1：公，2：母
}

const PAGE_CONFIG: any = inject('PAGE_CONFIG');

const petList = ref(true);
const isRead = ref(true);

const isShowDisclaimers = ref(false);
const isShowOralHealthIssues = ref(false);
const isShowSuitableForPets = ref(false);
const isShowLinkPopup = ref(false);
const linkpoupType = ref('');
const emits = defineEmits(['toggleComponent']);
const showprivacy = () => {
  linkpoupType.value = 'privacyLink';
  isShowLinkPopup.value = true;
  isShowOralHealthIssues.value = false;
  isShowDisclaimers.value = false;
  isShowSuitableForPets.value = false;
};
const showtermsOfUse = () => {
  linkpoupType.value = 'termsOfUse';
  isShowLinkPopup.value = true;
  isShowOralHealthIssues.value = false;
  isShowDisclaimers.value = false;
  isShowSuitableForPets.value = false;
};
const showhomeLink = () => {
  linkpoupType.value = 'homeLink';
  isShowLinkPopup.value = true;
  isShowOralHealthIssues.value = false;
  isShowDisclaimers.value = false;
  isShowSuitableForPets.value = false;
};
const closeDisclaimers = () => {
  isShowDisclaimers.value = false;
};
const joinNow = () => {
  lzReportClick('startCheck');
  if (!isRead.value) {
    showFailToast('请阅读、知晓并同意本页面右上角免责声明');
    return;
  }
  petList.value = true;
};

const petListArr = ref<PetItem[]>([]);
const selectPet = ref();
const confirmPet = () => {
  if (!selectPet.value) {
    showFailToast('请选择宠物');
    return;
  }
  store.commit('setPetInfo', selectPet.value);
  router.push('/photo');
};
const openHomeLink = () => {
  window.open(PAGE_CONFIG.homeLink);
};
const init = () => {
  console.log(store);
  getPetList().then((res) => {
    // console.log(res);
    if (res.data && res.data.length > 0) {
      petListArr.value = res.data;
    } else {
      petListArr.value = [];
    }
  });
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss" scoped>
</style>
