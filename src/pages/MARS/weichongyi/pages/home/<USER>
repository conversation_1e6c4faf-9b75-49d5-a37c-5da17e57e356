<template>
  <div class="main-view">
    <div class="search_box">
      <van-search
        v-model="name"
        placeholder="搜索宠物品种"
        @search="onSearch"
        @cancel="onClear"
      />
    </div>
    <div class="pet-list-box">
      <div class="select_title">
        热门品种
      </div>
      <div class="variety_box">
        <div
          class="variety_item"
          v-for="(item, index) in hotPetList"
          :key="index"
          @click="createPetType = item"
        >
          <img
            class="avater"
            :class="`${createPetType?.petBreed === item.petBreed ? 'red' : ''}`"
            :src="item.imgUrl"
          />
          <text class="name one_line_show">{{ item.petBreed }}</text>
        </div>
      </div>
      <div class="select_title">
        品种列表
      </div>
      <van-index-bar ref="indexBarRef">
        <div v-for="(item, index) in petList" :key="index">
          <van-index-anchor :index="item?.label">{{ item?.label }}</van-index-anchor>
          <van-cell
            v-for="(pet, num) in item?.petConfigs"
            :key="num"
            @click="createPetType = pet"
          >
            <div class="petLister">
              <img
                class="petImage"
                :class="`${createPetType?.petBreed === pet.petBreed ? 'red' : ''}`"
                :src="pet.imgUrl"
              />
              <div class="petText">{{ pet.petBreed }}</div>
            </div>
          </van-cell>
        </div>
      </van-index-bar>
    </div>
    <div class="btn_box">
      <div class="btn graybtn" @click="goBack">
        返回
      </div>
      <div class="btn redbtn" @click="goSubmit">
        提交
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { inject, ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import { getPetBreed } from '../../config/api';
import { useRouter } from 'vue-router';

const router = useRouter();
const store = useStore<RootState>();

interface VarietyInfo {
  alphabet: string;
  imgUrl: string;
  petBreed: string;
  petBreedId: number;
}
interface VarietyInfoList {
  label: string;
  petConfigs: VarietyInfo[];
}

const PAGE_CONFIG: any = inject('PAGE_CONFIG');

const petList = ref<VarietyInfoList[]>([]);
const hotPetList = ref<VarietyInfo[]>([]);
const createPetType = ref();
const name = ref();

const filteredPets = () => {
  if (!name.value) {
    return createPetType.value;
  }
  return petList.value.map((item: any) => ({
    ...item,
    petConfigs: item.petConfigs.filter((pet: any) => pet.petBreed.toLowerCase().includes(name.value.toLowerCase())),
  })).filter((item: any) => item.petConfigs.length > 0);
};

const indexBarRef = ref();
const onSearch = (val: string) => {
  name.value = val;
  const res = filteredPets();
  console.log(res);
  if (res.length > 0) {
    indexBarRef.value?.scrollTo(res[0].label);
  }
};

const onClear = () => {
  name.value = '';
};

const goBack = () => {

  router.back();
};

const goSubmit = () => {
  store.commit('setSelectVariety', createPetType.value);
  router.back();
};

const init = () => {
  console.log(PAGE_CONFIG);
  getPetBreed({ petType: 1 }).then((res: any) => {
    const { pets, hotPets } = res.data;
    console.log(pets, 'pets');
    petList.value = pets;
    hotPetList.value = hotPets;
  });
};

onMounted(() => {
  init();
});
</script>

<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss" scoped>
::-webkit-scrollbar {
  display: none;
}
</style>
<style lang="scss" scoped>
.pet-list-box {
  width: 100%;
  height: calc(100vh - 2.5rem);
  overflow-y: scroll;
  position: fixed;
  top: 1.2rem;
}

.select_title {
  font-size: 0.3rem;
  color: #333333;
  width: 6.64rem;
  margin: 0.25rem auto;
}

.petLister {
  width: 6.2rem;
  height: 0.8rem;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.petImage {
  width: 0.8rem;
  height: 0.8rem;
  border-radius: 50%;
}

.petText {
  margin: 0.05rem 0 0 0.3rem;
  font-size: 0.24rem;
  color: #333333;
}

.variety_box {
  width: 6.64rem;
  height: 3.5rem;
  overflow: hidden;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
}

.variety_item {
  width: 1.4rem;
  height: 1.6rem;
  display: flex;
  margin: 0 0.2rem 0.2rem 0;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.variety_item .avater {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
}

.variety_item .name {
  font-size: 0.2rem;
  color: #333333;
  margin-top: 0.2rem;
  width: 1.4rem;
  text-align: center;
}

.red {
  border: 0.01rem solid #ed0b1e;
}

.btn_box {
  width: 6.6rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  bottom: 0.3rem;
  left: 0.45rem;
}

.btn_box .btn {
  width: 3.05rem;
  height: 0.94rem;
  border-radius: 0.1rem;
  font-size: 0.24rem;
  text-align: center;
  line-height: 0.94rem;
}

.btn_box .redbtn {
  background-color: #ec051c;
  color: #fff;
}

.btn_box .graybtn {
  background-color: #fff;
  color: #333333;
  border: 0.01rem solid #333333;
}
</style>
