<template>
  <div class="main-view" :style="{paddingTop:  '0.43rem'}">
    <div>
      <div class="upload-error-title">{{ baseInfo.activityName }}</div>
      <img class="upload-error-img" :src="require(`../../asset/notdog.png`)"/>
      <img class="upload-error-desc" :src="require(`../../asset/errorDesc.png`)"/>
      <img class="common-dog" :src="require(`../../asset/photoErrorDog.png`)"/>
      <img class="common-btn" :src="require(`../../asset/retakeBtn.png`)" @click="() => {router.push('/photo');lzReportClick('reUpload')}"/>
    </div>
  </div>
</template>

<script lang="ts" setup>

import { useRouter } from 'vue-router';

import { PAGE_CONFIG } from '../../config/config';
import { BaseInfo } from '@/types/BaseInfo';
import { inject } from 'vue';
import { lzReportClick } from '@/pages/MARS/lzReport';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const router = useRouter();
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss" scoped>
::-webkit-scrollbar {
  display: none;
}
</style>
