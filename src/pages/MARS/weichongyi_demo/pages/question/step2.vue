<template>
  <div class="main-view" style="padding-top: 0.86rem">
    <div>
      <div class="qustion-slise-box" :style="`background-color: ${PAGE_CONFIG.questionSlideBg};`">
        <div class="qustion-slise-item" :style="`background-color: ${PAGE_CONFIG.quuetionSlideColor};width: ${ Math.floor((2 / (PAGE_CONFIG.problemList.length || 1)) * 100) }%;`"></div>
      </div>
      <div class="qustion-step-text">{{ `(2/${PAGE_CONFIG.problemList.length})` }}</div>
      <img class="qustion-text" :src="require(`../../asset/step2-title.png`)"/>
      <div @click="questionData.status = 1">
        <img class="qustion-yes-btn" v-if="questionData.status == 1"  :src="require(`../../asset/step-yes-act.png`)"/>
        <img class="qustion-yes-btn" v-else :src="require(`../../asset/step-yes.png`)"/>
      </div>
      <div @click="questionData.status = 0">
        <img class="qustion-yes-btn" v-if="questionData.status == 0" :src="require(`../../asset/step-no-act.png`)"/>
        <img class="qustion-yes-btn" v-else :src="require(`../../asset/step-no.png`)"/>
      </div>
      <img class="qustion-step-tips" :src="require(`../../asset/qadesc2.png`)" />
      <div class="photo-bottom">
        <img class="item-btn" style="width: 2.8rem;height: 1.13rem;" :src="require('../../asset/before.png')" @click="backStep"/>
        <img class="item-btn" style="width: 2.8rem;height: 1.13rem;" v-if="questionData.status == -1" :src="require('../../asset/next-no.png')" @click="nextStep"/>
        <img class="item-btn" style="width: 2.8rem;height: 1.13rem;" v-else :src="require('../../asset/next-act.png')" @click="nextStep"/>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { inject, ref, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { lzReportClick } from '@/pages/MARS/lzReport';

const route = useRoute();
const router = useRouter();
const store = useStore();

const { checkId } = route.query;

const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const questionData = reactive({
  title: PAGE_CONFIG.problemList[1].title,
  status: -1,
  desc: PAGE_CONFIG.problemList[1].desc,
  type: PAGE_CONFIG.problemList[1].type,
});

const nextStep = () => {
  if (questionData.status === -1) {
    return;
  }
  lzReportClick('qa2');

  // 将当前步骤的数据保存到Vuex中
  store.commit('setStepData', {
    step: 2,
    data: {
      [questionData.type]: questionData.status === 1,
    },
  });

  // 跳转到第三步
  router.push({
    path: '/step3',
    query: {
      checkId,
    },
  });
};

const backStep = () => {
  router.back();
};

const init = () => {
  // 如果Vuex中已有第二步数据，则恢复
  const stepData = store.state.stepData?.[2];
  if (stepData && stepData[questionData.type] !== undefined) {
    questionData.status = stepData[questionData.type] ? 1 : 0;
  }
};

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss" scoped>
::-webkit-scrollbar {
  display: none;
}
</style>
