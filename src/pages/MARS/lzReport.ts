import { httpEventRequest } from '@/utils/service';
import constant from '@/utils/constant';

/**
 * 获取adSource
 */
function getAdSource(): string {
  // 从连接中获取参数
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('adSource') || urlParams.get('adsource') || '';
}

// 这里可以获取通用的埋点参数
export const reportDefaultValue = () => ({
  prd: 'crmdz',
  te: 'c',
  sid: sessionStorage.getItem(constant.LZ_SHOP_ID) || '0',
  uid: sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN) || '0',
  t: '',
  opid: sessionStorage.getItem(constant.LZ_ACTIVITY_ID) || '0',
  ver: '1',
  c: '', // 具体内容标识
  e: '', // 访问时间定义
  l: '1',
  vid: '1',
  url: window.location.href,
  ch: getAdSource(),
  at: sessionStorage.getItem(constant.LZ_ACTIVITY_TYPE) || '0',
});

/**
 * 点击埋点
 * 前端埋点支持服务接口地址
 * 京东测试环境域名地址:  https://subapps-test.dianpusoft.cn/burypoint/getBuryPointBehaviors
 * 京东生产环境域名地址：https://jdsupport-api.dianpusoft.cn/burypoint/getBuryPointBehaviors
 * @param data
 */
export function getBuryPointBehaviors(data: any) {
  httpEventRequest.post('/getBuryPointBehaviors', {
    ...data,
    uid: sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN),
    t: new Date().getTime(),
  });
}

export function lzReportEnter() {
  const data = {
    ...reportDefaultValue(),
    e: 'enter',
  };
  getBuryPointBehaviors(data);
}

/**
 * 点击埋点,
 * @param val c字段：
 * 如果是字符串，{ c: { code:val } }
 * 如果是对象，{ c: JSON.stringify(val) }
 */
export function lzReportClick(val: any) {
  const data = {
    ...reportDefaultValue(),
    e: 'click',
  };
  if (typeof val === 'string') {
    const c = JSON.stringify({ code: val });
    Object.assign(data, { c });
  } else if (typeof val === 'object') {
    Object.assign(data, { c: val });
  }
  getBuryPointBehaviors(data);
}
