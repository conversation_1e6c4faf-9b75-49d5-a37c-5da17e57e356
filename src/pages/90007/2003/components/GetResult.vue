<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false">
    <div class="dialog" :class="{ fail: !isShowSuccess, physicalSuccess: isShowSuccess && giftInfo.prizeType === 3 }">
      <icon class="close_icon" name="close" color="#d6b377" size="25" @click="closeDialog" />
      <div v-if="isShowSuccess && giftInfo.prizeType === 3" class="btn" @click="handleSaveAddress">填写地址&gt;</div>
      <div v-else class="btn" @click="closeDialog"></div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, handleError } from 'vue';

const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  isShowSuccess: {
    type: Boolean,
    required: true,
    default: false,
  },
  giftInfo: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog', 'saveAddress']);
const closeDialog = () => {
  emits('closeDialog');
};
const handleSaveAddress = () => {
  emits('saveAddress');
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.5rem;
  height: 8rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/240585/13/6096/53156/6606aa8eF8fcd0a2c/3edd212ae24277b8.png') no-repeat;
  background-size: contain;
  padding: 0.28rem;
  box-sizing: border-box;
  text-align: center;
  .logo {
    width: 1.1rem;
  }

  .title {
    font-size: 0.3rem;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;

    &:after {
      content: '';
      width: 0.5rem;
      height: 0.02rem;
      margin-left: 0.2rem;
      background: #333;
    }

    &:before {
      content: '';
      width: 0.5rem;
      height: 0.02rem;
      margin-right: 0.2rem;
      background: #333;
    }
  }

  .btn {
    position: absolute;
    bottom: 0.6rem;
    left: 0.9rem;
    right: 1.2rem;
    text-align: center;
    line-height: 1;
    padding: 0.15rem 0;
    box-sizing: border-box;
  }

  .tips {
    margin: 1.5rem 0;
    font-size: 0.32rem;
    text-align: center;
  }
}
.fail {
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/216915/28/39134/41882/6606aa8fF2d48dc64/5c77d03f1734432c.png') no-repeat;
  background-size: contain;
}
.physicalSuccess {
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/159510/20/44459/41059/66f37a48F06636acb/4ac88541447a2449.png);
  .btn {
    font-size: 0.38rem;
    font-weight: bold;
    letter-spacing: 0.2rem;
    color: #fff;
  }
}

.close_icon {
  position: absolute;
  right: 0.3rem;
}
</style>
