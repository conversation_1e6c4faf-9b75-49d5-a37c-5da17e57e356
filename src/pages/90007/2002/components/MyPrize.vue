<template>
  <div class="rule-bk">
    <!-- <div class="close" @click="close" /> -->
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="time">{{ item.createTime }}</div>
        <div class="name">{{ item.prizeName }}</div>
        <div class="status" v-if="item.winStatus === 0">
          <div class="red">发放失败</div>
        </div>
        <div class="status" v-else-if="item.prizeType === 3">
          <div class="orange" v-if="!item.deliveryStatus">待发货</div>
          <div class="green" v-else>已发货</div>
        </div>
        <div class="status" v-else>
          <div class="green">已发放</div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div>
  <!-- <div class="rule-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>我的奖品</div>
      <div class="rightLineDiv"></div>
      <img alt=""
        data-v-705393a4=""
        src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png"
        class="close"
        @click="close" />
    </div>
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="type">{{ prizeType[item.prizeType] }}</div>
        <div class="info">
          <img :src="item.prizeImg" alt="" class="show-img" />
          <div class="detail">
            <div class="name">{{ item.prizeName }}</div>
            <div class="time">时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div class="status" v-if="item.winStatus === 0">
            <div class="red">发放失败</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 3">
            <div class="orange" v-if="!item.deliveryStatus">待发货</div>
            <div class="green" v-else>已发货</div>
          </div>
          <div class="status" v-else>
            <div class="green">已发放</div>
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div> -->
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  winStatus: number;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90007/getUserPrizes');
    res.data.forEach((item: Prize) => {
      item.createTime = dayjs(item.createTime).format('YYYY-MM-DD');
    });
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const showSaveAddress = ref(false);
const addressId = ref('');
const activityPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('../assets/myPrize.png');
  background-size: 100% 100%;
  width: 5.94rem;
  height: 7.32rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.8rem;

  .close {
    position: absolute;
    top: 0.2rem;
    right: 0.1rem;
    width: 0.5rem;
    height: 0.5rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/168506/5/36479/4323/65026cdaF89166ba9/051b60854bc4f0f9.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    cursor: pointer;
  }

  .content {
    height: 5.47rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    padding: 0 0.2rem;

    .prize {
      padding: 0.2rem 0;
      color: #814534;
      display: flex;
      align-items: center;
      .time,
      .name,
      .status {
        width: 33.33%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .no-data {
      text-align: center;
      line-height: 5rem;
      font-size: 0.24rem;
      color: #814534;
    }
  }
}
</style>
