<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="kv">
      <div class="ac-time" :style="furnishStyles.shopNameColor.value">活动时间:{{ dayjs().format('YYYY/MM/DD') }} -{{ dayjs().subtract(15, 'day').format('YYYY/MM/DD') }}</div>
      <div class="kv-btn" :style="furnishStyles.headerBtn.value">活动规则</div>
      <div class="kv-btn prize" :style="furnishStyles.headerBtn.value">我的奖品</div>
      <div class="kv-btn history" :style="furnishStyles.headerBtn.value">上期回顾</div>
    </div>
    <div v-for="(series, index) in seriesList" :key="series.seriesName">
      <div class="content" v-if="index === seriesIndex" :style="{ backgroundImage: `url(${series.seriesPic})` }">
        <div class="tab">
          <div @click="seriesIndex = 0"></div>
          <div @click="seriesIndex = 1"></div>
        </div>
        <div class="progress" :style="{ backgroundImage: `url(${furnish.seriesBoxBkHead})` }">
          <div class="progress-bar" :style="{ backgroundImage: `url(${furnish.seriesBoxBkBody})` }"></div>
        </div>
        <div class="gift" :style="{ '--drawTextColor': furnish.drawTextColor, '--drawBtnColor': furnish.drawBtnColor, '--drawBtnBg': furnish.drawBtnBg }">
          <div class="gift-item" v-for="item in series?.seriesPrizeList" :key="item.prizeKey">
            <div class="gift-step">累计满{{ item.potNum }}罐</div>
            <img class="gift-icon" :src="furnish.seriesBoxBkFooter ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/196280/1/44570/3998/662f9d67F38a9f591/a809b7b3cdbb4a9a.png'" alt="" />
            <div class="gift-img">
              <img :src="item.prizeImg" alt="奖品图" />
            </div>
            <div class="gift-name" :style="{ color: furnish.actTipsColor }">{{ item?.prizeName }}</div>
            <div class="gift-remainder">(奖品剩余{{ item.sendTotalCount }}份)</div>
            <div class="gift-btn" @click="toSign">立即领取</div>
          </div>
        </div>
        <div class="user-info" :style="{ color: furnish.drawTextColor }">您已购买22罐，已使用10罐，剩余12罐</div>
      </div>
    </div>

    <!-- <div class="act-info" :style="{ color: furnish.actTipsColor }">{{ furnish.actTips }}</div> -->
    <div class="content sku" :style="{ backgroundImage: `url(${furnish.acIntroductionBg})` }" v-if="exposureSkuList.length">
      <div class="sku-til">
        <div @click="skuCurIndex = 0"></div>
        <div @click="skuCurIndex = 1"></div>
      </div>
      <div class="sku-list">
        <div class="sku-item" v-for="item in exposureSkuList" :key="item.skuId">
          <img class="sku-img" :src="item.skuMainPicture" alt="" />
          <img class="sku-btn" :src="furnish.buyBtnBg ?? '//img10.360buyimg.com/imgzone/jfs/t1/240414/32/8098/6624/662fa5caFb406adba/582c4c9a431dd1ad.png'" alt="" />
        </div>
      </div>
    </div>
    <img class="go-shop" :src="furnish.exchangeBtn || '//img10.360buyimg.com/imgzone/jfs/t1/167918/30/43384/10450/662fa766F407f9fd8/8fc1732f65cd2830.png'" alt="" />
  </div>
</template>

<script lang="ts" setup>
import '../assets/style.scss';
import { computed, inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useSendMessage from '@/hooks/useSendMessage';
import { showToast, Icon } from 'vant';
import { deepCopy } from '@/utils/platforms/client';
import dayjs from 'dayjs';

const { registerHandler } = usePostMessage();
const seriesIndex = ref(0);
const skuCurIndex = ref(0);
const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
console.log('decorateData', decoData);
const seriesBoxBk = computed(
  () => `url('${furnish?.seriesBoxBkHead || '//img10.360buyimg.com/imgzone/jfs/t1/138612/33/42854/19737/6615f6faF0ecdb368/e647914f6b922868.png'}'),
    url('${furnish?.seriesBoxBkFooter || '//img10.360buyimg.com/imgzone/jfs/t1/225791/24/16092/2271/6615f6f8F1edd7b8a/ab8c21c4adce2dc7.png'}'),
    url('${furnish?.seriesBoxBkBody || '//img10.360buyimg.com/imgzone/jfs/t1/204141/28/41500/76465/6615f6f9Fb2cc96f1/35939b2ce3a6e6d9.png'}')`,
);
// 曝光商品的类型
export interface SkuType {
  skuId: number; // skuId
  skuName: string; // 商品名称
  skuMainPicture: string; // 图片路径
  jdPrice: string; // 京东价格
}

const shopName = ref('xxx自营旗舰店');
// 订单弹窗是否显示
const showOrderRecord = ref(false);
const isExposure = ref(1);
const isLoadingFinish = ref(false);
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};

const defaultSeriesList = [
  {
    seriesName: '系列名',
    seriesPic: 'https://img10.360buyimg.com/imgzone/jfs/t1/3028/29/24425/26603/66c7004fF05a74126/613aff4453dd6566.png',
    seriesPrizeList: [
      {
        skuId: 12345678,
        prizeName: '奖品名称',
        sendTotalCount: 8,
      },
    ],
    seriesSkuList: [
      {
        potNum: 6,
        skuId: 12345678,
      },
    ],
    seriesUrl: '',
  },
];
interface SeriesList {
  seriesName: string;
  seriesPic: string;
  seriesPrizeList: any[];
  seriesSkuList: any[];
}
// 系列列表
const seriesList = ref<SeriesList[]>(deepCopy(defaultSeriesList));

const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const prizeTipPopup = ref(false);
const exposureSkuList = ref<SkuType[]>([
  {
    skuId: 100020993208,
    skuName: '惠氏（Wyeth）启赋未来 6HMO新生儿婴幼儿配方奶粉1段(0-6月)850g Luxa',
    skuMainPicture: 'http://img13.360buyimg.com/n0/jfs/t1/163153/25/28078/95745/66977133Faf80234b/c39ed82ead833362.jpg',
    jdPrice: '302.00',
  },
]);
const toSign = () => {
  showToast('活动预览，仅供查看');
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  useSendMessage('deco', 'changeSelect', id);
  selectedId.value = id;
};

const createImg = async () => {
  rulePopup.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  console.log('activityData', data);

  ruleText.value = data.rules;
  shopName.value = data.shopName;
  isExposure.value = data.isExposure;
  seriesList.value = data.seriesList.length ? data.seriesList : deepCopy(defaultSeriesList);
  exposureSkuList.value = data.exposureSkuList;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    isExposure.value = activityData.isExposure;
    seriesList.value = activityData.seriesList.length ? activityData.seriesList : deepCopy(defaultSeriesList);
    showToast('活动预览，仅供查看');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.19rem;
    line-height: 0.27rem;
    top: 2.54rem;
    left: 0.3rem;
    right: 0.3rem;
    text-align: center;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem 0 0 0.22rem;
    border: 0.01rem;
    border-right: 0;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: absolute;
    right: 0;
    font-weight: 700;
  }

  .header-btn1 {
    top: 0.2rem;
  }

  .header-btn2 {
    top: 0.7rem;
  }

  .header-btn3 {
    top: 1.2rem;
  }
}

.top-ac-rule {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/238466/18/7839/70803/661e6ccfF775daf11/a91df377bf615abe.png');
  background-repeat: no-repeat;
  background-size: 100%;
  width: 7.5rem;
  height: 5.93rem;
  margin: 0 auto;
  padding-top: 1.5rem;

  .ac-Introduction {
    // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/209819/10/40835/79774/66151148Fa00bf5a1/e9158b30a8fffdb6.png');
    background-repeat: no-repeat;
    background-size: 100%;
    width: 7.37rem;
    height: 4.33rem;
    margin: 0 auto;
  }
}

.series-box {
  width: 7.5rem;
  min-height: 6rem;
  // height: 15.57rem;
  margin: 0 auto;
  // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/218570/24/40014/193665/661e6cceF2e8ef50f/10c09b437d7eeb31.png');
  // background-repeat: no-repeat;
  // background-size: 100% 100%;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/124843/31/40755/27743/661f2d0cF7302ad97/ef2bce497617eb1e.png'), url('//img10.360buyimg.com/imgzone/jfs/t1/151275/18/42498/119749/661f2d0bFa12868a3/fa0bb8e303cae02e.png'), url('//img10.360buyimg.com/imgzone/jfs/t1/248163/15/7525/29411/661f2d09F993d8d9d/29599d216d1baf08.png');
  background-repeat: no-repeat, no-repeat, no-repeat;
  background-size: 100%, 100%, 100% calc(100% - 4.24rem);
  background-position: top, bottom, calc(2rem);

  .series-name {
    font-size: 0.4rem;
    background: linear-gradient(180deg, #8b4406, #c47a2a);
    -webkit-background-clip: text;
    color: transparent;
    padding: 0.3rem 0;
    text-align: center;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .series-goods {
    width: 6.75rem;
    height: 4.66rem;
    margin: 0 auto;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/172172/35/45086/61120/661e6cccF9a510bdc/de20f61834e6b699.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin-bottom: 0.5rem;

    .had-buy {
      position: absolute;
      top: 3.2rem;
      left: 50%;
      transform: translateX(-50%);
      font-size: 0.3rem;
      font-weight: 700;
      color: #7c5400;
      text-align: center;
    }

    .buy-btn {
      position: absolute;
      bottom: 0.3rem;
      left: 50%;
      transform: translateX(-50%);
      width: 3.38rem;
      height: 0.69rem;
      line-height: 0.67rem;
      text-align: center;
      cursor: pointer;
      // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/183774/23/42582/7645/6615114aF3215b691/ead252530ccc7427.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  .prize-list {
    display: flex;
    justify-content: space-between;
    padding: 0.1rem;
    flex-wrap: wrap;

    .prize-item {
      width: 3.57rem;
      height: 4.11rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/178376/20/44779/16556/661e6cd0F5f71fe89/0083c7708ec99028.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: relative;
      margin-bottom: 0.1rem;

      .prize-threshold {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/245002/19/7709/2374/661e6ccfF3689110a/4f503432a5c311b8.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 1.9rem;
        height: 0.51rem;
        color: white;
        size: 0.22rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        top: -0.15rem;
      }

      .prize-img {
        width: 2rem;
        margin: 0 auto;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        top: 0.5rem;
      }

      .inventory {
        min-width: 1.05rem;
        height: 0.3rem;
        position: absolute;
        font-size: 0.24rem;
        color: #dc050d;
        left: 50%;
        transform: translate(-50%);
        top: 3rem;
        font-weight: 600;
      }

      .exchange-btn {
        width: 2.35rem;
        height: 0.49rem;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        bottom: 0.2rem;
      }

      .prize-desc {
        font-size: 0.25rem;
        text-align: center;
        position: absolute;
        bottom: 1.1rem;
        width: 100%;
        color: #dc050d;
        font-weight: 600;
      }
    }
  }
}

.footer-ac-rule {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/245678/25/7614/623770/661e6ccbFff6bf893/228cd2b351c2b628.png');
  background-repeat: no-repeat;
  background-size: 100%;
  width: 7.5rem;
  height: 10.52rem;
  margin: 0 auto;
  position: relative;

  .rule-content {
    background: #f7f6f6;
    // opacity: 0.5;
    width: 7.1rem;
    height: 8.8rem;
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    top: 1.4rem;
    border-radius: 3%;
    padding: 0.3rem;

    .rule-text {
      height: 8rem;
      white-space: pre-wrap;
      overflow-y: scroll;
      color: #7c5400;
      font-size: 0.22rem;
      font-weight: 500;
    }
  }
}
</style>
