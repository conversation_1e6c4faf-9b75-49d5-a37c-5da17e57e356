<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/139876/9/21637/89337/619f33f3E28ef638d/7aa9c2fad2d8275b.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">{{ shopName }}</div>
        <div>
          <!-- <div class="header-btn header-btn1" :style="furnishStyles.headerBtn.value" @click="rulePopup = true"><div>活动规则</div></div> -->
          <div class="header-btn header-btn2" :style="furnishStyles.headerBtn.value" @click="myPrizePopup = true"><div>我的奖品</div></div>
          <div class="header-btn header-btn3" :style="furnishStyles.headerBtn.value" @click="showOrderRecord = true"><div>我的订单</div></div>
        </div>
      </div>
    </div>
    <!-- 上方活动规则 -->
    <div class="top-ac-rule">
      <div class="ac-Introduction" :style="furnishStyles.acIntroductionBg.value"></div>
    </div>
    <div class="series-box" :style="{ backgroundImage: seriesBoxBk }" v-for="(item, index) in seriesList" :key="index">
      <div class="series-name">{{ item.seriesName }}</div>
      <div class="series-goods" :style="{ backgroundImage: `url(${item.seriesPic})` }" v-show="item.seriesPic">
        <div class="had-buy">您已经购买<span style="color: #dc2c1c">XX</span>罐</div>
        <div class="buy-btn" :style="furnishStyles.buyBtnBg.value" @click="toSign"></div>
      </div>
      <div class="prize-list">
        <div class="prize-item" v-for="(child, childIndex) in item.seriesPrizeList" :key="childIndex">
          <div class="prize-threshold">满{{ child.potNum }}罐</div>
          <img :src="child.prizeImg" alt="" class="prize-img" />
          <div class="prize-desc">{{ child.prizeName }}</div>
          <div class="inventory">
            剩余: <span style="color: #814534">{{ child.sendTotalCount }}</span>
          </div>
          <img :src="furnish.exchangeBtn ?? '//img10.360buyimg.com/imgzone/jfs/t1/157504/16/41149/5738/661e6cd0Fa3687e43/80c1d6d0a0052668.png'" alt="" class="exchange-btn" @click="toSign" />
        </div>
      </div>
    </div>
    <div class="footer-ac-rule">
      <div class="rule-content">
        <div class="rule-text" v-html="ruleText"></div>
      </div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="prizeTipPopup"><PrizeTip @close="prizeTipPopup = false"></PrizeTip></VanPopup>
  <VanPopup teleport="body" v-model:show="rulePopup">
    <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="myPrizePopup">
    <MyPrize @close="myPrizePopup = false"></MyPrize>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="showOrderRecord">
    <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useSendMessage from '@/hooks/useSendMessage';
import { showToast } from 'vant';
import PrizeTip from '../components/PrizeTip.vue';
import MyPrize from '../components/MyPrize.vue';
import Rule from '../components/Rule.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import { deepCopy } from '@/utils/platforms/client';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const seriesBoxBk = computed(
  () => `url('${furnish?.seriesBoxBkHead || '//img10.360buyimg.com/imgzone/jfs/t1/138612/33/42854/19737/6615f6faF0ecdb368/e647914f6b922868.png'}'),
    url('${furnish?.seriesBoxBkFooter || '//img10.360buyimg.com/imgzone/jfs/t1/225791/24/16092/2271/6615f6f8F1edd7b8a/ab8c21c4adce2dc7.png'}'),
    url('${furnish?.seriesBoxBkBody || '//img10.360buyimg.com/imgzone/jfs/t1/204141/28/41500/76465/6615f6f9Fb2cc96f1/35939b2ce3a6e6d9.png'}')`,
);

const shopName = ref('xxx自营旗舰店');
// 订单弹窗是否显示
const showOrderRecord = ref(false);
const isExposure = ref(1);
const isLoadingFinish = ref(false);
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};

const defaultSeriesList = [
  {
    seriesName: '系列名',
    seriesPic: '',
    seriesPrizeList: [],
    seriesSkuList: [
      {
        potNum: 6,
        skuId: 12345678,
      },
    ],
    seriesUrl: '',
  },
];
// 系列列表
const seriesList = ref<any[]>(deepCopy(defaultSeriesList));

const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const prizeTipPopup = ref(false);

const toSign = () => {
  showToast('活动预览，仅供查看');
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  useSendMessage('deco', 'changeSelect', id);
  selectedId.value = id;
};

const createImg = async () => {
  rulePopup.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleText.value = data.rules;
  shopName.value = data.shopName;
  isExposure.value = data.isExposure;
  seriesList.value = data.seriesList.length ? data.seriesList : deepCopy(defaultSeriesList);
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    isExposure.value = activityData.isExposure;
    seriesList.value = activityData.seriesList.length ? activityData.seriesList : deepCopy(defaultSeriesList);
    showToast('活动预览，仅供查看');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem 0 0 0.22rem;
    border: 0.01rem;
    border-right: 0;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: absolute;
    right: 0;
    font-weight: 700;
  }
  .header-btn1 {
    top: 0.2rem;
  }
  .header-btn2 {
    top: 0.7rem;
  }
  .header-btn3 {
    top: 1.2rem;
  }
}
.top-ac-rule {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/238466/18/7839/70803/661e6ccfF775daf11/a91df377bf615abe.png');
  background-repeat: no-repeat;
  background-size: 100%;
  width: 7.5rem;
  height: 5.93rem;
  margin: 0 auto;
  padding-top: 1.5rem;
  .ac-Introduction {
    // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/209819/10/40835/79774/66151148Fa00bf5a1/e9158b30a8fffdb6.png');
    background-repeat: no-repeat;
    background-size: 100%;
    width: 7.37rem;
    height: 4.33rem;
    margin: 0 auto;
  }
}

.series-box {
  width: 7.5rem;
  min-height: 6rem;
  // height: 15.57rem;
  margin: 0 auto;
  // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/218570/24/40014/193665/661e6cceF2e8ef50f/10c09b437d7eeb31.png');
  // background-repeat: no-repeat;
  // background-size: 100% 100%;
  // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/124843/31/40755/27743/661f2d0cF7302ad97/ef2bce497617eb1e.png'), url('//img10.360buyimg.com/imgzone/jfs/t1/151275/18/42498/119749/661f2d0bFa12868a3/fa0bb8e303cae02e.png'), url('//img10.360buyimg.com/imgzone/jfs/t1/248163/15/7525/29411/661f2d09F993d8d9d/29599d216d1baf08.png');
  background-repeat: no-repeat, no-repeat, no-repeat;
  background-size: 100%, 100%, 100% calc(100% - 4.24rem);
  background-position-y: top, bottom, 2rem;
  .series-name {
    font-size: 0.4rem;
    background: linear-gradient(180deg, #8b4406, #c47a2a);
    -webkit-background-clip: text;
    color: transparent;
    padding: 0.3rem 0;
    text-align: center;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  .series-goods {
    width: 6.75rem;
    height: 4.66rem;
    margin: 0 auto;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/172172/35/45086/61120/661e6cccF9a510bdc/de20f61834e6b699.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin-bottom: 0.5rem;
    .had-buy {
      position: absolute;
      top: 3.2rem;
      left: 50%;
      transform: translateX(-50%);
      font-size: 0.3rem;
      font-weight: 700;
      color: #7c5400;
      text-align: center;
    }
    .buy-btn {
      position: absolute;
      bottom: 0.3rem;
      left: 50%;
      transform: translateX(-50%);
      width: 3.38rem;
      height: 0.69rem;
      line-height: 0.67rem;
      text-align: center;
      cursor: pointer;
      // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/183774/23/42582/7645/6615114aF3215b691/ead252530ccc7427.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
  .prize-list {
    display: flex;
    justify-content: space-between;
    padding: 0.1rem;
    flex-wrap: wrap;
    .prize-item {
      width: 3.57rem;
      height: 4.11rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/178376/20/44779/16556/661e6cd0F5f71fe89/0083c7708ec99028.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: relative;
      margin-bottom: 0.1rem;
      .prize-threshold {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/245002/19/7709/2374/661e6ccfF3689110a/4f503432a5c311b8.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 1.9rem;
        height: 0.51rem;
        color: white;
        size: 0.22rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        top: -0.15rem;
      }
      .prize-img {
        width: 2rem;
        margin: 0 auto;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        top: 0.5rem;
      }
      .inventory {
        min-width: 1.05rem;
        height: 0.3rem;
        position: absolute;
        font-size: 0.24rem;
        color: #dc050d;
        left: 50%;
        transform: translate(-50%);
        top: 3rem;
        font-weight: 600;
      }
      .exchange-btn {
        width: 2.35rem;
        height: 0.49rem;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        bottom: 0.2rem;
      }
      .prize-desc {
        font-size: 0.25rem;
        text-align: center;
        position: absolute;
        bottom: 1.1rem;
        width: 100%;
        color: #dc050d;
        font-weight: 600;
      }
    }
  }
}
.footer-ac-rule {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/245678/25/7614/623770/661e6ccbFff6bf893/228cd2b351c2b628.png');
  background-repeat: no-repeat;
  background-size: 100%;
  width: 7.5rem;
  height: 10.52rem;
  margin: 0 auto;
  position: relative;
  .rule-content {
    background: #f7f6f6;
    // opacity: 0.5;
    width: 7.1rem;
    height: 8.8rem;
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    top: 1.4rem;
    border-radius: 3%;
    padding: 0.3rem;
    .rule-text {
      height: 8rem;
      white-space: pre-wrap;
      overflow-y: scroll;
      color: #7c5400;
      font-size: 0.22rem;
      font-weight: 500;
    }
  }
}
</style>
