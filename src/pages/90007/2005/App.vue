<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/139876/9/21637/89337/619f33f3E28ef638d/7aa9c2fad2d8275b.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">{{ shopName }}</div>
        <div>
          <div class="header-btn header-btn1" v-click-track="'hdgz'" :style="furnishStyles.headerBtn.value" @click="showRulePopup"><div>活动规则</div></div>
          <div class="header-btn header-btn2" v-click-track="'wdjp'" :style="furnishStyles.headerBtn.value" @click="myPrizePopup = true"><div>我的奖品</div></div>
          <div class="header-btn header-btn3" v-click-track="'wddd'" :style="furnishStyles.headerBtn.value" @click="showOrderRecord = true"><div>我的订单</div></div>
        </div>
      </div>
    </div>
    <div class="series-box" :style="{ backgroundImage: seriesBoxBk }" v-for="(item, index) in seriesList" :key="index">
      <div class="series-name">{{ item.seriesName }}</div>
      <div class="prize-list" v-for="(child, childIndex) in item.prizes" :key="childIndex">
        <div class="series-goods" :style="{ backgroundImage: `url(${item.seriesImg})` }">
          <!-- 门槛 -->
          <div class="potNum" :class="`potNum${child.standardNum.toString().length}`">{{ child.standardNum }}</div>
          <!-- 奖品图 -->
          <img :src="child.prizeImg" alt="" class="prize-img" />
          <!-- 奖品名 -->
          <div class="prize-desc">{{ child.prizeName }}</div>
          <!-- 奖品剩余份数 -->
          <div class="inventory">剩余:{{ child.stockNum }}</div>
          <div class="buy-btn" :style="furnishStyles.buyBtnBg.value" @click="goToShop(item.seriesUrl)"></div>
          <div class="exchange-btn" :style="furnishStyles.exchangeBtn.value" @click="handleExchange(child.canClick, child.prizeKey, item.seriesId, child.prizeType, child)"></div>
        </div>
        <!--进度条-->
        <div class="progress-bar-box">
          <div class="bar-item-box">
            <div class="bar-item" :style="{ right: getProgress(item.userPotNum, child.standardNum) }"></div>
          </div>
        </div>
        <div class="had-buy">
          您已经购买<span style="font-size: 0.4rem">{{ item?.userPotNum ?? 0 }}</span
          >罐
        </div>
        <div class="difference" v-if="child.standardNum - (item?.userPotNum ?? 0) > 0">
          <span style="font-size: 0.4rem">再买{{ child.standardNum - (item?.userPotNum ?? 0) }}罐</span>就可以拿到奖品了
        </div>
      </div>
    </div>
    <div class="sku" :style="furnishStyles.skuBg.value" v-if="exposureSkuList.length > 0">
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in exposureSkuList" :key="index">
          <img :src="item.skuMainPicture" alt="" />
          <div class="sku-text">{{ item.skuName }}</div>
          <div class="sku-btns" @click="gotoSkuPage(item.skuId)"></div>
        </div>
        <!-- <div class="more-btn-all">
          <div class="more-btn" v-if="exposureSkuList.length && exposureSkuList.length !== total" @click="toSign()">点我加载更多</div>
        </div> -->
      </div>
    </div>
  </div>

  <!-- 奖品提示弹窗 -->
  <!-- <VanPopup teleport="body" v-model:show="prizeTipPopup" position="bottom" :closeOnClickOverlay="false"
      ><PrizeTip @close="prizeTipPopup = false" :prizeList="prizeList"></PrizeTip
    ></VanPopup> -->
  <VanPopup teleport="body" v-model:show="rulePopup">
    <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="myPrizePopup">
    <MyPrize v-if="myPrizePopup" @close="myPrizePopup = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="showOrderRecord">
    <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
  </VanPopup>
  <!-- 活动商品弹窗-->
  <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
    <GoodsPopup :data="orderSkuList" @close="showGoods = false"></GoodsPopup>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="awardPopup">
    <Award :prize="award" @close="awardPopup = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></Award>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress">
    <SaveAddress v-if="showSaveAddress" @close="showSaveAddress = false" @handleSaveAddress="handleSaveAddress"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup" :closeOnClickOverlay="false">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 确认兑换弹窗 -->
  <VanPopup teleport="body" v-model:show="confirmExange">
    <div class="confirm-exange">
      <p class="prize-name">{{ newPrizeObj.prizeName }}</p>
      <img :src="newPrizeObj.prizeImg" alt="" class="prize-img" />
      <div class="btn" @click="handleConfirmExchange"></div>
    </div>
  </VanPopup>
  <!-- 活动门槛 -->
  <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
</template>

<script lang="ts" setup>
import { computed, inject, reactive, ref } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import Threshold2 from './components/Threshold.vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import MyPrize from './components/MyPrize.vue';
import Rule from './components/Rule.vue';
import Award from './components/AwardPopup.vue';
import SaveAddress from './components/SaveAddress.vue';
import OrderRecordPopup from './components/OrderRecordPopup.vue';
import CopyCard from './components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { CardType } from './ts/type';
import useThreshold from '@/hooks/useThreshold';
import GoodsPopup from './components/GoodsPopup.vue';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const confirmExange = ref(false);
const shopName = ref(baseInfo.shopName);

const seriesBoxBk = computed(
  () => `url('${furnish?.seriesBoxBkHead || '//img10.360buyimg.com/imgzone/jfs/t1/138612/33/42854/19737/6615f6faF0ecdb368/e647914f6b922868.png'}'),
    url('${furnish?.seriesBoxBkFooter || '//img10.360buyimg.com/imgzone/jfs/t1/225791/24/16092/2271/6615f6f8F1edd7b8a/ab8c21c4adce2dc7.png'}'),
    url('${furnish?.seriesBoxBkBody || '//img10.360buyimg.com/imgzone/jfs/t1/204141/28/41500/76465/6615f6f9Fb2cc96f1/35939b2ce3a6e6d9.png'}')`,
);

const isLoadingFinish = ref(false);
// 订单弹窗是否显示
const showOrderRecord = ref(false);
// 系列列表
const seriesList = ref<any[]>([]);
// 曝光商品
const showGoods = ref(false);
// 曝光商品是否展示
const isExposure = ref(1);
// 曝光商品列表
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
  skuId: string;
};

const pageNum = ref(1);
const total = ref(0);
const exposureSkuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);
// 获取曝光商品
const getSkuList = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/90007/getSkuListPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    exposureSkuList.value.push(...data.records);
    total.value = data.total;
  } catch (error: any) {
    console.error(error);
  }
};

const getProgress = (userPotNum: number, standardNum: number) => {
  const percent = (userPotNum / standardNum) * 100;
  if (percent >= 100) {
    return '0%';
  }
  if (percent <= 0) {
    return '100%';
  }
  return `${100 - percent}%`;
};

// const orderPageNum = ref(1);
// const orderTotal = ref(0);
// // 获取系列以及系列商品
// const getOrderSkuList = async () => {
//   showLoadingToast({
//     message: '加载中...',
//     forbidClick: true,
//     duration: 0,
//   });
//   try {
//     const { data } = await httpRequest.post('/90007/getSeriesTypeSkuListPage', {
//       pageNum: orderPageNum.value,
//       pageSize: 10,
//     });
//     orderSkuList.value.push(...data.records);
//     orderTotal.value = data.total;
//   } catch (error: any) {
//     console.error(error);
//   }
// };

// 展示门槛显示弹框
const showLimit = ref(false);
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
});

const exchangeObj = {
  prizeKey: '',
  seriesId: 0,
};
let newPrizeObj = {
  prizeName: '111',
  prizeImg: '',
  prizeType: 0,
};
const prizeList = ref([]); // 奖品列表
const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
// const prizeTipPopup = ref(true);

// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleText.value = data;
    }
    rulePopup.value = true;
  } catch (error: any) {
    console.error();
  }
};

const awardPopup = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const addressForm = ref({});
// 计算用户罐数
const calcUserPotNum = () => {
  httpRequest
    .post('/90007/computePinSeriesPotNum')
    .then((res) => {
      console.log(res);
      if (res.code === 200) {
        httpRequest.post('/90007/getSeriesPrizes').then((res) => {
          console.log(res);
          if (res.code === 200) {
            seriesList.value = res.data;
          }
        });
      }
    })
    .catch((err) => {
      console.log(err);
      httpRequest.post('/90007/getSeriesPrizes').then((res) => {
        console.log(res);
        if (res.code === 200) {
          seriesList.value = res.data;
        }
      });
    });
};
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  awardPopup.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  awardPopup.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  awardPopup.value = false;
  myPrizePopup.value = false;
  savePhonePopup.value = true;
};
// 确认兑换
const handleConfirmExchange = () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  httpRequest
    .post('/90007/cashPrize', {
      prizeKey: exchangeObj.prizeKey,
      seriesId: exchangeObj.seriesId,
    })
    .then((data) => {
      if (data.code === 200) {
        closeToast();
        confirmExange.value = false;
        calcUserPotNum();
        showToast('兑换成功，请等待发放');
      }
    })
    .catch((err) => {
      closeToast();
      console.log(err);
      showToast(err.message);
    });
};
// 暂存地址
const handleSaveAddress = (data: any) => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  addressForm.value = data;
  httpRequest
    .post('/90007/cashPrize', {
      prizeKey: exchangeObj.prizeKey,
      seriesId: exchangeObj.seriesId,
    })
    .then((res) => {
      console.log(res);
      if (res.code === 200) {
        httpRequest
          .post('/90007/saveAddress', {
            ...data,
            addressId: res.data,
            seriesId: exchangeObj.seriesId,
            prizeKey: exchangeObj.prizeKey,
          })
          .then((data) => {
            if (data.code === 200) {
              showSaveAddress.value = false;
              calcUserPotNum();
              closeToast();
              showToast('兑换成功，请等待发放');
            }
          })
          .catch((err) => {
            console.log(err);
            closeToast();
            showToast(err.message);
          });
      }
    })
    .catch((err) => {
      console.log(err);
      closeToast();
      showToast(err.message);
    });
};
// 继续集罐
const goToShop = (url: string) => {
  if (baseInfo.status === 1) {
    showToast({
      message: '活动暂未开始',
      duration: 2000,
    });
    return;
  }
  if (baseInfo.status === 3) {
    showToast({
      message: '抱歉，活动已结束',
      duration: 2000,
    });
    return;
  }
  window.location.href = url;
};

// 立即兑换
const handleExchange = (canClick: boolean, prizeKey: string, seriesId: number, prizeType: number, prizeObj: any) => {
  console.log(prizeObj);
  if (baseInfo.status === 1) {
    showToast({
      message: '活动未开始',
      duration: 2000,
    });
    return;
  }
  if (baseInfo.status === 3) {
    showToast({
      message: '抱歉，活动已结束',
      duration: 2000,
    });
    return;
  }
  if (prizeObj.stockNum <= 0) {
    showToast({
      message: '奖品已兑完',
      duration: 2000,
    });
    return;
  }
  if (canClick) {
    exchangeObj.prizeKey = prizeKey;
    exchangeObj.seriesId = seriesId;
    newPrizeObj = prizeObj;
    if (prizeType === 3) {
      showSaveAddress.value = true;
    } else {
      confirmExange.value = true;
    }
  } else {
    showToast({
      message: `${prizeObj.reason}`,
      duration: 2000,
    });
  }
};
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([calcUserPotNum(), getSkuList()]);
    isLoadingFinish.value = true;

    closeToast();
  } catch (error: any) {
    console.error(error);

    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
@font-face {
  font-family: 'FZLTZHJW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTZHJW/FZLTZHJW--GB1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTZHJW/FZLTZHJW--GB1-0.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 2.2rem 0 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
    opacity: 0;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem 0 0 0.22rem;
    border: 0.01rem;
    border-right: 0;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: 700;
    margin-bottom: 0.1rem;
  }
}
//.ac-Introduction {
//  // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/209819/10/40835/79774/66151148Fa00bf5a1/e9158b30a8fffdb6.png');
//  background-repeat: no-repeat;
//  background-size: 100%;
//  width: 6.9rem;
//  height: 6.72rem;
//  margin: 0 auto;
//  font-size: 0.2rem;
//  line-height: 0.4rem;
//  color: #7c5400;
//  padding: 0 0.5rem;
//  padding-top: 1.2rem;
//  position: relative;
//  margin-bottom: 0.3rem;
//  .ac-Introduction-footer {
//    width: 100%;
//    position: absolute;
//    text-align: center;
//    color: #dc2c1c;
//    font-weight: 600;
//    bottom: 0.2rem;
//    left: 50%;
//    transform: translate(-50%);
//  }
//}
.series-box {
  width: 7.5rem;
  margin: 0 auto;
  // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/138612/33/42854/19737/6615f6faF0ecdb368/e647914f6b922868.png'), url('//img10.360buyimg.com/imgzone/jfs/t1/225791/24/16092/2271/6615f6f8F1edd7b8a/ab8c21c4adce2dc7.png'), url('//img10.360buyimg.com/imgzone/jfs/t1/204141/28/41500/76465/6615f6f9Fb2cc96f1/35939b2ce3a6e6d9.png');
  background-repeat: no-repeat, no-repeat, no-repeat;
  background-size: 100%, 100%, 100% calc(100% - 2.8rem);
  background-position-y: top, bottom, 1.4rem;
  margin-bottom: 0.3rem;
  margin-top: 0.3rem;
  padding-bottom: 0.5rem;
  .series-name {
    font-family: 'FZLTZHJW';
    font-size: 0.4rem;
    padding: 0.3rem 0;
    color: #e7c87e;
    text-align: center;
  }
  .series-goods {
    width: 5.59rem;
    height: 2.4rem;
    margin: 0.3rem auto;
    // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/182387/30/43641/66908/66151149F053b1850/119e49d97df5e086.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    .potNum {
      width: 0.8rem;
      height: 0.8rem;
      border-radius: 50%;
      line-height: 0.8rem;
      text-align: left;
      padding-left: 0.15rem;
      font-size: 0.3rem;
      font-weight: 600;
      color: #0c2149;
      position: absolute;
      left: 0rem;
      top: 0.12rem;
    }
    .potNum1 {
      top: 0.1rem;
      font-size: 0.47rem;
    }
    .potNum2 {
      top: 0.12rem;
      font-size: 0.3rem;
      padding-left: 0.1rem;
    }
    .potNum3 {
      top: 0.12rem;
      font-size: 0.24rem;
      padding-left: 0.05rem;
    }
    .prize-img {
      width: 1.2rem;
      position: absolute;
      right: 0.34rem;
      top: 0.15rem;
    }
    .prize-desc {
      width: 1.9rem;
      text-align: center;
      position: absolute;
      right: 0rem;
      bottom: 0.7rem;
      color: #e7c87e;
      font-size: 0.2rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .inventory {
      width: 1.9rem;
      text-align: center;
      position: absolute;
      right: 0rem;
      bottom: 0.3rem;
      color: #e7c87e;
      font-size: 0.24rem;
    }
    .buy-btn {
      position: absolute;
      bottom: -0.2rem;
      left: 0.65rem;
      width: 1.42rem;
      height: 0.45rem;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    .exchange-btn {
      position: absolute;
      bottom: -0.2rem;
      right: 0.2rem;
      width: 1.42rem;
      height: 0.45rem;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
  .progress-bar-box {
    width: 5.42rem;
    height: 0.22rem;
    margin: 0 auto;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/249345/2/26230/3626/67496402F0698459b/8d2b06509dbf9cd7.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .bar-item-box {
      width: 98%;
      height: 0.22rem;
      margin: 0 auto;
      position: relative;
      overflow: hidden;
      .bar-item {
        width: 5.42rem;
        height: 0.22rem;
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/158721/23/38110/5344/674963ffF665438cf/ae55b7bb3876a0ab.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        position: absolute;
        right: 40%;
        bottom: 0;
      }
    }
  }
  .had-buy {
    font-size: 0.3rem;
    line-height: 0.8rem;
    // font-weight: 700;
    color: #e7c87e;
    text-align: center;
  }
  .difference {
    font-size: 0.3rem;
    line-height: 0.8rem;
    // font-weight: 700;
    color: #e7c87e;
    text-align: center;
  }
  // .prize-list {
  //   display: flex;
  //   justify-content: space-between;
  //   padding: 0.1rem;
  //   flex-wrap: wrap;
  //   .prize-item {
  //     width: 3.23rem;
  //     height: 3.55rem;
  //     background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/242108/26/3502/9353/6615114aF5c1bd6fd/b318d30364c2a5f8.png');
  //     background-repeat: no-repeat;
  //     background-size: 100% 100%;
  //     position: relative;
  //     margin-bottom: 0.1rem;
  //     .prize-threshold {
  //       color: #72421f;
  //       size: 0.22rem;
  //       height: 0.6rem;
  //       line-height: 0.6rem;
  //       text-align: center;
  //     }
  //     .prize-img {
  //       width: 2rem;
  //       margin: 0 auto;
  //     }
  //     .inventory {
  //       min-width: 1.05rem;
  //       height: 0.3rem;
  //       background-color: #6e6c65;
  //       opacity: 0.9;
  //       position: absolute;
  //       font-size: 0.24rem;
  //       color: white;
  //       left: 0;
  //       top: 1rem;
  //       border-radius: 0 1rem 1rem 0;
  //     }

  //     .prize-desc {
  //       font-size: 0.25rem;
  //       color: #734320;
  //       font-weight: 500;
  //       text-align: center;
  //       position: absolute;
  //       bottom: 0.1rem;
  //       width: 100%;
  //     }
  //   }
  // }
}
.sku {
  width: 7.5rem;
  height: 11.53rem;
  margin: 0 auto;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding-top: 1rem;
  // padding: 0.2rem;
  // .title-img {
  //   width: 7.5rem;
  //   margin: 0 auto;
  //   background-repeat:no-repeat;
  //   background-size: 100% 100%;
  // }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    place-content: flex-start space-between;
    padding: 0.2rem;
    height: 10rem;
    position: relative;
    overflow-y: scroll;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.07rem;
      height: 4.46rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/217682/24/50707/13329/674963ffF4d549ebb/ffad6b0082172cfe.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      // background-color: #f6ebd0;
      overflow: hidden;
      position: relative;
      img {
        display: block;
        width: 3.07rem;
        height: 3.07rem;
      }
      .sku-text {
        width: 3.07rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        font-size: 0.24rem;
        color: #0b193e;
        height: 0.4rem;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.43rem;
        box-sizing: border-box;
      }
      .sku-btns {
        width: 1.6rem;
        height: 0.5rem;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        bottom: 0rem;
      }
    }
  }
}
.confirm-exange {
  background: url('./assets/confirm.png') no-repeat;
  background-size: 100%;
  width: 6.8rem;
  height: 8rem;
  padding-top: 1.4rem;
  .prize-img {
    height: 2.8rem;
    width: 2.8rem;
    margin: 0.4rem auto 0;
  }
  .prize-name {
    font-size: 0.46rem;
    line-height: 0.75rem;
    color: #fff;
    text-align: center;
  }
  .btn {
    position: absolute;
    left: 2.2rem;
    bottom: 0.9rem;
    width: 2.4rem;
    height: 0.6rem;
  }
}
</style>
