<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/139876/9/21637/89337/619f33f3E28ef638d/7aa9c2fad2d8275b.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">{{ shopName }}</div>
        <div>
          <div class="header-btn header-btn1" v-click-track="'hdgz'" :style="furnishStyles.headerBtn.value" @click="showRulePopup"><div>活动规则</div></div>
          <div class="header-btn header-btn2" v-click-track="'wdjp'" :style="furnishStyles.headerBtn.value" @click="myPrizePopup = true"><div>我的奖品</div></div>
          <div class="header-btn header-btn3" v-click-track="'wddd'" :style="furnishStyles.headerBtn.value" @click="showOrderRecord = true"><div>我的订单</div></div>
         <div class="header-btn header-btn4" v-click-track="'hdsp'" :style="furnishStyles.headerBtn.value" @click="showGoods = true"><div>活动商品</div></div>
        </div>
      </div>
      <div class="ac-Introduction" :style="furnishStyles.acIntroductionBg.value">
        <!-- <p>
        活动时间：2024年2月26日一2024年4月30日（仅识别入会后的订单） 奖品领取时间：2024年2月26日-2024年5月10日 活动对象：伊利母婴京东自营旗舰店会员
        参与活动商品：QQ星榛高铂金装正装奶粉；塞纳牧2、3、4段正装奶粉； 睿护2、3、4段正装奶粉；其它系列2、3、4段及QQ星系列其他正装奶粉
        *购买指定产品并达到指定罐数赠送指定实物奖品，兑换奖品后消耗对应的罐数， 需要重新累计获取下一级奖品，奖品兑换后不可更换
        活动规则：活动时间内，会员购买指定产品，累计罐数达标后，可申请兑换相应 赠品；兑换赠品后，需扣除相应罐数；活动结束后，罐数清零 参与方式:
        会员在活动期间报名参加活动后产生购买行为，且购买罐数累计满足以下条件 （储值卡也计入金额） 购买金领冠指定系列奶粉指定商品且确认收货后可申请领取
      </p>
      <div class="ac-Introduction-footer">*集罐有礼活动截止日期: 2024年4月30日</div> -->
      </div>
      <div class="series-box" :style="{ backgroundImage: seriesBoxBk }" v-for="(item, index) in seriesList" :key="index">
        <div class="series-name">{{ item.seriesName }}</div>
        <div class="series-goods" :style="{ backgroundImage: `url(${item.seriesImg})` }">
          <div class="had-buy">您已经购买{{ item.userPotNum }}罐</div>
          <!-- 继续集罐按钮 -->
          <div class="buy-btn" :style="furnishStyles.buyBtnBg.value" @click="goToShop(item.seriesUrl)"></div>
        </div>
        <div class="prize-list">
          <div class="prize-item" v-for="(child, childIndex) in item.prizes" :key="childIndex" v-show="child.prizeKey !== 's240528174459532286' && child.prizeKey !== 's240528174601547116' && child.prizeKey !== 's240528174643472561'">
            <div class="prize-threshold">满{{ child.standardNum }}罐</div>
            <img :src="child.prizeImg" alt="" class="prize-img" />
            <div v-if="child.stockNum > 0" class="inventory">剩余:{{ child.stockNum }}</div>
            <div v-else class="inventory">剩余:0</div>
            <!-- 已兑完 -->
            <div class="has-empty" v-show="child.stockNum <= 0"></div>
            <img :src="furnish.exchangeBtn ?? '//img10.360buyimg.com/imgzone/jfs/t1/191145/33/40745/4516/6615114aFe6ad325a/72e3159873772eef.png'" alt="" class="exchange-btn" @click="handleExchange(child.canClick, child.prizeKey, item.seriesId, child.prizeType, child)" />
            <div class="prize-desc">{{ child.prizeName }}</div>
          </div>
        </div>
      </div>
      <!-- 曝光商品 -->
      <div class="sku" v-if="exposureSkuList.length > 0">
        <img class="title-img" :src="furnish.winnersBg" alt="" />
        <div class="sku-list" v-if="exposureSkuList.length">
          <div class="sku-item" v-for="(item, index) in exposureSkuList" :key="index">
            <img :src="item.skuMainPicture" alt="" />
            <div class="sku-text">{{ item.skuName }}</div>
            <div class="sku-btns">
              <div class="price">￥{{ Number(item.jdPrice).toFixed(2) }}</div>
              <div class="to-bug" @click="gotoSkuPage(item.skuId)">抢购</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 奖品提示弹窗 -->
    <!-- <VanPopup teleport="body" v-model:show="prizeTipPopup" position="bottom" :closeOnClickOverlay="false"
      ><PrizeTip @close="prizeTipPopup = false" :prizeList="prizeList"></PrizeTip
    ></VanPopup> -->
    <VanPopup teleport="body" v-model:show="rulePopup" position="bottom" :closeOnClickOverlay="false">
      <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="myPrizePopup" position="bottom" :closeOnClickOverlay="false">
      <MyPrize v-if="myPrizePopup" @close="myPrizePopup = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
    </VanPopup>
    <!--我的订单弹窗-->
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
    </VanPopup>
    <!-- 活动商品弹窗-->
    <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
      <GoodsPopup :data="orderSkuList" @close="showGoods = false"></GoodsPopup>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="awardPopup">
      <Award :prize="award" @close="awardPopup = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></Award>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom" :closeOnClickOverlay="false">
      <SaveAddress v-if="showSaveAddress" @close="showSaveAddress = false" @handleSaveAddress="handleSaveAddress"></SaveAddress>
    </VanPopup>
    <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup" :closeOnClickOverlay="false">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
    </VanPopup>
    <!-- 确认兑换弹窗 -->
    <VanPopup teleport="body" v-model:show="confirmExange" :closeOnClickOverlay="false">
      <div class="confirm-exange">
        <div class="title">确认兑换</div>
        <div class="content">
          <p class="p1">恭喜您即将获得:</p>
          <img :src="newPrizeObj.prizeImg" alt="" class="prize-img" />
          <p class="prize-name">{{ newPrizeObj.prizeName }}</p>
          <div>
            <p class="p3" v-if="newPrizeObj.prizeType === 2">请等待京豆放到您的账户中 京东-我的-京豆 中查看</p>
            <p class="p3" v-if="newPrizeObj.prizeType === 6">请等待红包发放到您的账户中 京东-我的-我的钱包-红包 中查看</p>
            <p class="p3" v-if="newPrizeObj.prizeType === 8">请等待京东E卡发放到您的账户中 京东-我的-我的钱包-礼品卡 中查看</p>
          </div>
          <div class="btn-list">
            <div class="btn btn-left" @click="handleConfirmExchange">确认兑换</div>
            <div class="btn btn-right" @click="confirmExange = false">取消兑换</div>
          </div>
        </div>
      </div>
    </VanPopup>
    <!-- 活动门槛 -->
    <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, reactive, ref } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import Threshold2 from '@/components/Threshold2/index.vue';
import { gotoSkuPage } from '@/utils/platforms/jump';
import MyPrize from './components/MyPrize.vue';
import Rule from './components/Rule.vue';
import Award from './components/AwardPopup.vue';
import SaveAddress from './components/SaveAddress.vue';
import OrderRecordPopup from './components/OrderRecordPopup.vue';
import CopyCard from './components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { CardType } from './ts/type';
import useThreshold from '@/hooks/useThreshold';
import GoodsPopup from './components/GoodsPopup.vue';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const confirmExange = ref(false);
const shopName = ref(baseInfo.shopName);

const seriesBoxBk = computed(
  () => `url('${furnish?.seriesBoxBkHead || '//img10.360buyimg.com/imgzone/jfs/t1/138612/33/42854/19737/6615f6faF0ecdb368/e647914f6b922868.png'}'),
    url('${furnish?.seriesBoxBkFooter || '//img10.360buyimg.com/imgzone/jfs/t1/225791/24/16092/2271/6615f6f8F1edd7b8a/ab8c21c4adce2dc7.png'}'),
    url('${furnish?.seriesBoxBkBody || '//img10.360buyimg.com/imgzone/jfs/t1/204141/28/41500/76465/6615f6f9Fb2cc96f1/35939b2ce3a6e6d9.png'}')`,
);

const isLoadingFinish = ref(false);
// 订单弹窗是否显示
const showOrderRecord = ref(false);
// 系列列表
const seriesList = ref<any[]>([]);
// 曝光商品
const showGoods = ref(false);
// 曝光商品是否展示
const isExposure = ref(1);
// 曝光商品列表
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
  skuId: string;
};

const pageNum = ref(1);
const total = ref(0);
const exposureSkuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);
// 获取曝光商品
const getSkuList = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/90007/getSkuListPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    exposureSkuList.value.push(...data.records);
    total.value = data.total;
  } catch (error: any) {
    console.error(error);
  }
};

// const orderPageNum = ref(1);
// const orderTotal = ref(0);
// // 获取系列以及系列商品
// const getOrderSkuList = async () => {
//   showLoadingToast({
//     message: '加载中...',
//     forbidClick: true,
//     duration: 0,
//   });
//   try {
//     const { data } = await httpRequest.post('/90007/getSeriesTypeSkuListPage', {
//       pageNum: orderPageNum.value,
//       pageSize: 10,
//     });
//     orderSkuList.value.push(...data.records);
//     orderTotal.value = data.total;
//   } catch (error: any) {
//     console.error(error);
//   }
// };

// 展示门槛显示弹框
const showLimit = ref(false);
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
});

const exchangeObj = {
  prizeKey: '',
  seriesId: 0,
};
let newPrizeObj = {
  prizeName: '',
  prizeImg: '',
  prizeType: 0,
};
const prizeList = ref([]); // 奖品列表
const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
// const prizeTipPopup = ref(true);

// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleText.value = data;
    }
    rulePopup.value = true;
  } catch (error: any) {
    console.error();
  }
};

const awardPopup = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const addressForm = ref({});
// 计算用户罐数
const calcUserPotNum = () => {
  httpRequest
    .post('/90007/computePinSeriesPotNum')
    .then((res) => {
      console.log(res);
      if (res.code === 200) {
        httpRequest.post('/90007/getSeriesPrizes').then((res) => {
          console.log(res);
          if (res.code === 200) {
            seriesList.value = res.data;
          }
        });
      }
    })
    .catch((err) => {
      console.log(err);
      httpRequest.post('/90007/getSeriesPrizes').then((res) => {
        console.log(res);
        if (res.code === 200) {
          seriesList.value = res.data;
        }
      });
    });
};
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  awardPopup.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  awardPopup.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  awardPopup.value = false;
  myPrizePopup.value = false;
  savePhonePopup.value = true;
};
// 确认兑换
const handleConfirmExchange = () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  httpRequest
    .post('/90007/cashPrize', {
      prizeKey: exchangeObj.prizeKey,
      seriesId: exchangeObj.seriesId,
    })
    .then((data) => {
      if (data.code === 200) {
        closeToast();
        confirmExange.value = false;
        calcUserPotNum();
        showToast('兑换成功，请等待发放');
      }
    })
    .catch((err) => {
      closeToast();
      console.log(err);
      showToast(err.message);
    });
};
// 暂存地址
const handleSaveAddress = (data: any) => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  addressForm.value = data;
  httpRequest
    .post('/90007/cashPrize', {
      prizeKey: exchangeObj.prizeKey,
      seriesId: exchangeObj.seriesId,
    })
    .then((res) => {
      console.log(res);
      if (res.code === 200) {
        httpRequest
          .post('/90007/saveAddress', {
            ...data,
            addressId: res.data,
            seriesId: exchangeObj.seriesId,
            prizeKey: exchangeObj.prizeKey,
          })
          .then((data) => {
            if (data.code === 200) {
              showSaveAddress.value = false;
              calcUserPotNum();
              closeToast();
              showToast('兑换成功，请等待发放');
            }
          })
          .catch((err) => {
            console.log(err);
            closeToast();
            showToast(err.message);
          });
      }
    })
    .catch((err) => {
      console.log(err);
      closeToast();
      showToast(err.message);
    });
};
// 继续集罐
const goToShop = (url: string) => {
  if (baseInfo.status === 1) {
    showToast({
      message: '活动暂未开始',
      duration: 2000,
    });
    return;
  }
  if (baseInfo.status === 3) {
    showToast({
      message: '抱歉，活动已结束',
      duration: 2000,
    });
    return;
  }
  window.location.href = url;
};

// 立即兑换
const handleExchange = (canClick: boolean, prizeKey: string, seriesId: number, prizeType: number, prizeObj: any) => {
  console.log(prizeObj);
  if (baseInfo.status === 1) {
    showToast({
      message: '活动未开始',
      duration: 2000,
    });
    return;
  }
  if (baseInfo.status === 3) {
    showToast({
      message: '抱歉，活动已结束',
      duration: 2000,
    });
    return;
  }
  if (prizeObj.stockNum <= 0) {
    showToast({
      message: '奖品已兑完',
      duration: 2000,
    });
    return;
  }
  if (canClick) {
    exchangeObj.prizeKey = prizeKey;
    exchangeObj.seriesId = seriesId;
    newPrizeObj = prizeObj;
    if (prizeType === 3) {
      showSaveAddress.value = true;
    } else {
      confirmExange.value = true;
    }
  } else {
    showToast({
      message: `${prizeObj.reason}`,
      duration: 2000,
    });
  }
};
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([calcUserPotNum(), getSkuList()]);
    isLoadingFinish.value = true;

    closeToast();
  } catch (error: any) {
    console.error(error);

    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem 0 0 0.22rem;
    border: 0.01rem;
    border-right: 0;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: absolute;
    right: 0;
    font-weight: 700;
  }
  .header-btn1 {
    top: 0.2rem;
  }
  .header-btn2 {
    top: 0.7rem;
  }
  .header-btn3 {
    top: 1.2rem;
  }
  .header-btn4 {
    top: 1.7rem;
  }
}
.ac-Introduction {
  // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/209819/10/40835/79774/66151148Fa00bf5a1/e9158b30a8fffdb6.png');
  background-repeat: no-repeat;
  background-size: 100%;
  width: 6.9rem;
  height: 6.72rem;
  margin: 0 auto;
  font-size: 0.2rem;
  line-height: 0.4rem;
  color: #7c5400;
  padding: 0 0.5rem;
  padding-top: 1.2rem;
  position: relative;
  margin-bottom: 0.3rem;
  .ac-Introduction-footer {
    width: 100%;
    position: absolute;
    text-align: center;
    color: #dc2c1c;
    font-weight: 600;
    bottom: 0.2rem;
    left: 50%;
    transform: translate(-50%);
  }
}
.series-box {
  width: 6.91rem;
  margin: 0 auto;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/138612/33/42854/19737/6615f6faF0ecdb368/e647914f6b922868.png'), url('//img10.360buyimg.com/imgzone/jfs/t1/225791/24/16092/2271/6615f6f8F1edd7b8a/ab8c21c4adce2dc7.png'), url('//img10.360buyimg.com/imgzone/jfs/t1/204141/28/41500/76465/6615f6f9Fb2cc96f1/35939b2ce3a6e6d9.png');
  background-repeat: no-repeat, no-repeat, no-repeat;
  background-size: 100%, 100%, 100% calc(100% - 2.53rem);
  background-position-y: top, bottom, 2rem;
  margin-bottom: 0.3rem;
  .series-name {
    font-size: 0.4rem;
    background: linear-gradient(180deg, #8b4406, #c47a2a);
    -webkit-background-clip: text;
    color: transparent;
    padding: 0.3rem 0;
    text-align: center;
    font-weight: 600;
  }
  .series-goods {
    width: 6.65rem;
    height: 4.97rem;
    margin: 0 auto;
    // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/182387/30/43641/66908/66151149F053b1850/119e49d97df5e086.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    .had-buy {
      position: absolute;
      top: 3.4rem;
      left: 50%;
      transform: translateX(-50%);
      font-size: 0.3rem;
      font-weight: 700;
      color: #7c5400;
      text-align: center;
    }
    .buy-btn {
      position: absolute;
      bottom: 0.3rem;
      left: 50%;
      transform: translateX(-50%);
      width: 2.95rem;
      height: 0.67rem;
      line-height: 0.67rem;
      text-align: center;
      cursor: pointer;
      // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/183774/23/42582/7645/6615114aF3215b691/ead252530ccc7427.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
  .prize-list {
    display: flex;
    justify-content: space-between;
    padding: 0.1rem;
    flex-wrap: wrap;
    .prize-item {
      width: 3.23rem;
      height: 3.55rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/242108/26/3502/9353/6615114aF5c1bd6fd/b318d30364c2a5f8.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: relative;
      margin-bottom: 0.1rem;
      .prize-threshold {
        color: #72421f;
        size: 0.22rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
      }
      .prize-img {
        width: 2rem;
        margin: 0 auto;
      }
      .inventory {
        min-width: 1.05rem;
        height: 0.3rem;
        background-color: #6e6c65;
        opacity: 0.9;
        position: absolute;
        font-size: 0.24rem;
        color: white;
        left: 0;
        top: 1rem;
        border-radius: 0 1rem 1rem 0;
      }
      .has-empty {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/227652/31/16303/12591/66151147F91446914/456a9fb24ad26de8.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 1.72rem;
        height: 1.72rem;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: 0.8rem;
      }
      .exchange-btn {
        width: 2.05rem;
        // height: 0.47rem;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        top: 2.5rem;
      }
      .prize-desc {
        font-size: 0.25rem;
        color: #734320;
        font-weight: 500;
        text-align: center;
        position: absolute;
        bottom: 0.1rem;
        width: 100%;
      }
    }
  }
}
.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  .title-img {
    width: 2.82rem;
    height: 0.4rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.4rem;
      background-color: #f6ebd0;
      overflow: hidden;
      img {
        display: block;
        width: 3.4rem;
        height: 3.4rem;
      }
      .sku-text {
        width: 3.4rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        font-size: 0.3rem;
        color: #262626;
        height: 0.8rem;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.43rem;
        box-sizing: border-box;
      }
      .sku-btns {
        width: 3rem;
        height: 0.6rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/244615/4/7225/1431/661644d6F575dfb61/6ee1cc99f718c0ca.png');
        background-size: 100%;
        margin: 0 auto 0.2rem;
        .price {
          width: 2.05rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #fff;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .to-bug {
          width: 0.95rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #df006e;
          text-align: center;
        }
      }
    }
  }
}
.confirm-exange {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/150581/34/43118/14685/66179a49F816eb435/d2a2adfaf6a20c02.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 6rem;
  height: 6.9rem;
  .title {
    font-size: 0.4rem;
    color: #f0f7f5;
    text-align: center;
    padding-top: 0.2rem;
    padding-bottom: 0.2rem;
  }
  .content {
    width: 5.6rem;
    height: 5.5rem;
    background-color: white;
    border-radius: 0.2rem;
    margin: 0rem auto 0;
    padding: 0.3rem;
    .prize-img {
      height: 2rem;
      width: 2rem;
      margin: 0 auto;
    }
    .p1 {
      display: block;
      color: #262626;
      font-size: 0.24rem;
      font-weight: 500;
    }
    .prize-name {
      font-size: 0.36rem;
      font-weight: bold;
      margin: 0.27rem 0 0;
      text-align: center;
      color: #ff3333;
    }
    .p3 {
      font-size: 0.2rem;
      color: #b8b8b8;
      display: block;
      text-align: center;
      height: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        color: #f2270c;
      }
    }
    .btn-list {
      display: flex;
      justify-content: space-between;
      .btn {
        width: 2.4rem;
        height: 0.8rem;
        line-height: 0.8rem;
        text-align: center;
        color: white;
        font-size: 0.3rem;
        border-radius: 0.1rem;
      }
      .btn-left {
        background: linear-gradient(to right, #f2270c, #ff6320);
      }
      .btn-right {
        background: #ff9900;
      }
    }
  }
}
</style>
