<template>
  <div class="sku">
    <div class="sku-til">{{ isNewStep ? '欧加新品热卖区' : '精选推荐' }}</div>
    <img class="banner" v-if="UIConfig.skuBanner" :src="UIConfig.skuBanner" alt="banner" @click="bannerLink(UIConfig.skuBannerLink)" />
    <img v-if="UIConfig.lineSkuPic" :src="UIConfig.lineSkuPic" alt="banner" @click="gotoSkuPage(UIConfig.lineSkuId)" />
    <div class="sku-list">
      <div class="sku-item" v-for="item in skuList" :key="item.skuId">
        <img class="sku-item-img" :src="item.skuMainPicture" alt="" />
        <div class="sku-item-name van-ellipsis">{{ item.desc1 }}</div>
        <div class="sku-item-desc van-ellipsis">{{ item.desc2 }}</div>
        <img class="sku-item-btn" src="//img10.360buyimg.com/imgzone/jfs/t1/261058/23/14548/11264/67909678Faf45babf/f833fe86e5abcae2.png" alt="" @click="gotoSkuPage(item.skuId)" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { inject, onMounted, ref } from 'vue';
import { getSkuList } from '../script/ajax';
import type { SKUItem } from '../script/type';
import { gotoSkuPage } from '@/utils/platforms/jump';

const UIConfig = inject('UIConfig') as any;
const isNewStep = inject('isNewStep');
const skuList = ref<SKUItem[]>([]);
const bannerLink = (link: string) => {
  if (!link) return;
  window.location.href = link;
};
onMounted(async () => {
  console.log('SkuList mounted');
  skuList.value = await getSkuList();
});
</script>

<style scoped lang="scss">
.sku {
  padding: 0 0.3rem 0.3rem;
  box-sizing: border-box;
}
.banner {
  margin: 0.1rem auto;
  width: 100%;
}
.sku-til {
  margin-top: 0.44rem;
  font-family: OPPOSans-M;
  font-size: 0.48rem;
  color: #fffdfc;
  text-align: center;
}
.sku-list {
  margin: 0.1rem 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.1rem;
  width: 100%;
  .sku-item {
    background-color: #fff;
    padding: 0.2rem;
    box-sizing: border-box;
    width: 100%;
    border-radius: 0.1rem;
    text-align: center;
    .sku-item-img {
      border-radius: 0.1rem;
      width: 100%;
      height: 3rem;
      object-fit: contain;
      border: 0.1rem #7f0a0a solid;
    }
    .sku-item-name {
      width: 100%;
      font-size: 0.24rem;
      margin-top: 0.1rem;
    }
    .sku-item-desc {
      width: 100%;
      font-size: 0.2rem;
      margin-top: 0.1rem;
    }
    .sku-item-btn {
      width: 2.41rem;
      margin: 0.1rem auto 0;
    }
  }
}
</style>
