<template>
  <Popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false" @closed="handleClose" @opened="handleOpen">
    <div class="box">
      <div class="title">我的竞猜</div>
      <div class="gusse">
        <div class="til-list">
          <div class="til-item">参与竞猜</div>
          <div class="til-item">竞猜数据</div>
          <div class="til-item">竞猜时间</div>
        </div>
        <div class="guess-list" v-if="guessList.length">
          <div class="guess-item" v-for="item in guessList" :key="item.percent">
            <div class="til-item">{{ item.guessActName }}</div>
            <div class="til-item">{{ item.guessString }}</div>
            <div class="til-item">{{ dayjs(item.guessTime).format('YYYY/MM/DD') }}</div>
          </div>
        </div>
        <div class="no-data" v-else>暂无数据</div>
      </div>
      <div class="close-icon" @click="handleClose"></div>
    </div>
  </Popup>
</template>
<script setup lang="ts">
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import { Popup } from 'vant';
import type { BaseInfo } from '@/types/BaseInfo';
import { getMyGuess } from '../script/ajax';
import dayjs from 'dayjs';
import type { GuessItem } from '../script/type';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog']);
const handleClose = () => {
  emits('closeDialog');
};
const guessList = ref<GuessItem[]>([]);
const handleOpen = async () => {
  guessList.value = await getMyGuess();
};
</script>
<style scoped lang="scss">
.box {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/262914/1/13947/13767/678de2c7F56987419/38b9586f3267ba10.png) no-repeat;
  background-size: contain;
  width: 5.51rem;
  height: 8.23rem;
  padding: 0.23rem 0.2rem;
  box-sizing: border-box;
  position: relative;
  .title {
    font-size: 0.52rem;
    font-family: 'YouSheBiaoTiHei';
    text-align: center;
    color: #fff;
  }
  .gusse {
    .til-list {
      font-size: 0.28rem;
      margin: 0.1rem 0.3rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .guess-list {
      max-height: 5rem;
      overflow-y: auto;
      .guess-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 0.1rem 0;
        padding: 0.2rem;
        box-sizing: border-box;
        border-radius: 0.14rem;
        line-height: 1;
        background: linear-gradient(90deg, #fa2f3c 0%, #a40603 99%, #a40603 100%);
        color: #fff;
        font-size: 0.24rem;
        div {
          flex: 1;
          text-align: center;
        }
      }
    }
  }
  .no-data {
    margin-top: 0.5rem;
    font-size: 0.3rem;
    text-align: center;
  }
  .close-icon {
    position: absolute;
    width: 1rem;
    height: 1rem;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
  }
}
</style>
