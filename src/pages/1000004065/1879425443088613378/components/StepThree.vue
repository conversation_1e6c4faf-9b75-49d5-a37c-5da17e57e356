<template>
  <img class="w-full mart-5" src="https://img10.360buyimg.com/imgzone/jfs/t1/230171/35/29092/30361/67a5ccbbFaa23321e/d9f5cff13b52d4b0.png" alt="" />
  <div class="content-box place-self-center">
    <div class="place-self-center priview-box">
      <div class="priview-video">
        <img :src="UIConfig.videoPoster" alt="" @click="playVideo" />
      </div>
      <img class="priview-sku" :src="UIConfig.priviewSkuPic" @click="goPriviewSku" alt="" />
    </div>
    <div class="place-self-center draw-content">
      <img class="place-self-center til" src="https://img10.360buyimg.com/imgzone/jfs/t1/237707/23/34966/5343/67a9cc33F6e2f7175/159fbcd5a5a4700b.png" alt="" />
      <img class="gift-pic" :src="UIConfig.step3Gift" />
      <div class="draw-btn place-self-center" :class="{ 'btn-disabled': Math.floor(info?.drawChance?.leftChance / info?.drawNeed) <= 0 }">
        <div class="cus-badge" v-if="Math.floor(info?.drawChance?.leftChance / info?.drawNeed) > 0">{{ Math.floor(info?.drawChance?.leftChance / info?.drawNeed) }}</div>
        <img @click="drawGift" src="https://img10.360buyimg.com/imgzone/jfs/t1/266957/40/14254/10859/678f3680Fb21230e0/d34e8fddbec219df.png" alt="" srcset="" />
      </div>
    </div>

    <div class="content place-self-center">
      <div class="upload-record" @click="showPopUp.isShowUploadPopup = true">我的上传记录</div>
      <div class="btn-group">
        <div v-for="(item, index) in tabList" :key="item.title" :class="{ 'btn-active': index === tabCurIndex }" class="btn-item" @click="handleTabClick(index)">{{ item.title }}</div>
      </div>
      <div class="upload-box">
        <div class="upload">
          <input accept="image/jpeg, image/png" class="upload-input" type="file" @change="handleFileChange" />
          <img class="upload-img" src="https://img10.360buyimg.com/imgzone/jfs/t1/263257/21/18707/11772/67ab022aFba78f0ae/ad09bbad841d228d.png" alt="" />
          <div class="upload-mask" v-if="isUploaded">
            <img src="//img10.360buyimg.com/imgzone/jfs/t1/262604/7/17404/7633/67a6c3c4F18ff8c06/86c0342c92c7c952.png" alt="" />
            <div class="upload-tip" v-if="isUploaded === 3">{{ dayjs(UIConfig.videoStartTime).format('M月D号H点') }}解锁<br />敬请期待</div>
            <div class="upload-tip" v-else>已上传</div>
          </div>
        </div>
        <div class="upload-info">
          <img v-clipboard-text="UIConfig.copyText || '#OPPO Find N5# #裸眼3D先看OPPO大折叠有多薄#'" :src="tabList[tabCurIndex]?.infoPic" alt="" />
        </div>
      </div>
      <div class="draw-btn" @click="handleDoTask" :class="{ 'btn-disabled': picStatus?.taskButtonStatus }">
        <div class="btn-tips" v-if="picStatus?.taskButtonStatus">任意平台审核通过后解锁</div>
        <div>做任务攒新年欧气</div>
        <div class="text">
          我的欧气：<span>{{ info?.guessChance?.leftChance }}</span>
        </div>
      </div>
    </div>
  </div>
  <TaskList :task="info?.tasks" :showPopup="showPopUp.isShowTaskPopup" @closeDialog="(showPopUp.isShowTaskPopup = false), actRestart()"></TaskList>
  <DrawSuccess :giftInfo="giftInfo" :showPopup="showPopUp.isShowDrawSuccessPopup" @closeDialog="showPopUp.isShowDrawSuccessPopup = false"></DrawSuccess>
  <UploadRecode :show-popup="showPopUp.isShowUploadPopup" @close-dialog="showPopUp.isShowUploadPopup = false" @change="handleFileChange"></UploadRecode>
  <DrawAnimation :status="drawStatus" :show-popup="showPopUp.isShowDrawAnimationPopup" @close-dialog="closedAnimation"></DrawAnimation>
  <PlayVideo :showPopup="showPopUp.isShowPlayVideoPopup" @closeDialog="showPopUp.isShowPlayVideoPopup = false"></PlayVideo>
</template>
<script lang="ts" setup>
import { reactive, ref, PropType, computed, inject } from 'vue';
import TaskList from './TaskList.vue';
import { Icon, Popup, Picker, showToast, showLoadingToast, closeToast, Toast } from 'vant';
import { drawGifts, uploadPicture, savePic } from '../script/ajax';
import DrawSuccess from './DrawSuccess.vue';
import PlayVideo from './PlayVideo.vue';
import type { GiftItem, LunBoItem } from '../script/type';
import UploadRecode from './UploadRecode.vue';
import { compressImage } from '../script/compressImage';
import { gotoSkuPage } from '@/utils/platforms/jump';
import DrawAnimation from './DrawAnimation.vue';
import dayjs from 'dayjs';

const UIConfig = inject('UIConfig') as any;
const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  picStatus: {
    type: Object,
    reqiured: true,
    default: () => ({}),
  },
});
const tabList = [
  {
    title: '小红书',
    value: 'xhsStatus',
    infoPic: 'https://img10.360buyimg.com/imgzone/jfs/t1/267598/29/20215/40942/67b2a651Fd5bf4a9b/f63a804863c4e423.png',
  },
  {
    title: '抖音',
    value: 'dyStatus',
    infoPic: 'https://img10.360buyimg.com/imgzone/jfs/t1/265360/15/20797/40550/67b2a652F5e18087f/93f3bc3e51e94ccc.png',
  },
  {
    title: '微博',
    value: 'wbStatus',
    infoPic: 'https://img10.360buyimg.com/imgzone/jfs/t1/250286/32/28524/39738/67b2a651F6c6495f7/c6c669743cf0dab9.png',
  },
];
const tabCurIndex = ref(0);
// 是否已上传
const isUploaded = computed(() => props.picStatus[tabList[tabCurIndex.value]?.value]);
const handleTabClick = (index: number) => {
  tabCurIndex.value = index;
};
const emits = defineEmits(['restart']);
const showPopUp = reactive({
  isShowTaskPopup: false,
  isShowDrawSuccessPopup: false,
  isShowUploadPopup: false,
  isShowDrawAnimationPopup: false,
  isShowPlayVideoPopup: false,
});
const actRestart = () => {
  emits('restart');
};
const giftInfo = ref<GiftItem>({});
const drawStatus = ref(false);
const drawGift = async () => {
  if (!Math.floor(props.info?.drawChance?.leftChance / props.info?.drawNeed)) {
    showToast('抱歉，当前抽奖机会不足');
    return;
  }
  showPopUp.isShowDrawAnimationPopup = true;
  const data = await drawGifts();
  if (data.result.length === 0) {
    return;
  }
  drawStatus.value = true;
  const timer = setTimeout(() => {
    clearTimeout(timer);
    emits('restart');
  }, 2000);
  const [firstGift] = data.result;
  giftInfo.value = firstGift;
  giftInfo.value.drawRecordId = data.drawRecordId || '';
};
const closedAnimation = () => {
  drawStatus.value = false;
  showPopUp.isShowDrawAnimationPopup = false;
  showPopUp.isShowDrawSuccessPopup = true;
};
const handleFileChange = async (e: any) => {
  // 0 为可上传
  if (isUploaded.value) return;
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const file = e.target.files[0];
    if (!file) return;
    // 判断文件类型为图片
    if (!/image/.test(file.type)) {
      showToast('请上传图片');
      return;
    }
    const fileNew = await compressImage(file);
    const formData = new FormData();
    formData.append('file', fileNew);
    formData.append('pictureCateId', '173981837');
    const data = await uploadPicture(formData);
    if (!data) return;
    const result = await savePic(tabList[tabCurIndex.value].title, data);
    if (result) {
      showToast('上传成功');
    }
    const timer = setTimeout(() => {
      clearTimeout(timer);
      emits('restart');
      closeToast();
    }, 2000);
  } catch (error: any) {
    closeToast();
    console.log('🚀 ~ file:', error);
    showToast('上传失败，请稍后再试');
  }
};
// 做任务
const handleDoTask = () => {
  // props.picStatus.taskButtonStatus =0 已完成任务
  if (props.picStatus.taskButtonStatus === 0) {
    showPopUp.isShowTaskPopup = true;
  }
};
const playVideo = () => {
  if (new Date() < new Date(UIConfig.videoStartTime)) {
    showToast(`${dayjs(UIConfig.videoStartTime).format('M月D号H点')}解锁，敬请期待`);
    return;
  }
  showPopUp.isShowPlayVideoPopup = true;
};
const goPriviewSku = () => {
  if (new Date() < new Date(UIConfig.videoStartTime)) {
    showToast(`${dayjs(UIConfig.videoStartTime).format('M月D号H点')}解锁，敬请期待`);
    return;
  }
  gotoSkuPage(UIConfig.priviewSkuId);
};
</script>
<style scoped lang="scss">
.content-box {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/260812/34/18118/19066/67a9948fF65052857/2da502dd055c7fd1.png) no-repeat;
  background-size: 100% 100%;
  width: 6.93rem;
  height: 20.13rem;
  padding: 0.15rem 0.1rem 0;
  box-sizing: border-box;

  .content {
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/265148/11/18184/7032/67a99c16F298d351d/2abb8882af9e54cb.png) no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 7.23rem;
    box-sizing: border-box;
    padding-top: 0.52rem;
    margin-top: 0.1rem;
    position: relative;
    .upload-record {
      position: absolute;
      background-color: #d10c0a;
      color: white;
      padding: 0.1rem 0.1rem 0.1rem 0.3rem;
      box-sizing: border-box;
      font-size: 0.2rem;
      clip-path: polygon(0 100%, 0.15rem 0, 100% 0, 100% 100%, 0 100%);
      right: 0rem;
    }
    .btn-group {
      padding: 10px 20px;
      border-radius: 5px;
      font-size: 16px;
      color: white;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 0.5rem;
      .btn-item {
        width: 1.76rem;
        height: 0.54rem;
        line-height: 0.54rem;
        background: url(https://img10.360buyimg.com/imgzone/jfs/t1/262706/2/18373/10704/67a97a11F61a6773d/7ecf568fe9affb56.png) no-repeat;
        background-size: 100% 100%;
        font-size: 0.28rem;
        color: #624b15;
        text-align: center;
      }
      .btn-active {
        background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/255032/24/19243/9893/67a97a11F540610aa/1869d049301f4fbb.png);
        color: #fff;
      }
    }
    .upload-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0.2rem 0.3rem;
      .upload {
        position: relative;
        margin-right: 0.2rem;
        // overflow: hidden;
        .upload-input {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
          opacity: 0;
          //   background-color: #fac;
        }
        .upload-img {
          width: 2.6rem;
        }
        .upload-mask {
          position: absolute;
          inset: 0;
          z-index: 2;
          .upload-tip {
            position: absolute;
            top: 1.8rem;
            left: 0.2rem;
            right: 0.2rem;
            color: white;
            font-size: 0.28rem;
            white-space: nowrap;
            text-align: center;
          }
        }
      }
      .upload-info {
        width: 3.5rem;
        margin-left: 0.1rem;
      }
    }
    .draw-btn {
      width: 3.8rem;
      height: 1rem;
      background: url(https://img10.360buyimg.com/imgzone/jfs/t1/267008/24/14414/8555/67906db0F62dfe7e3/dbe485ad0ee9387c.png) no-repeat;
      background-size: 100% 100%;
      text-align: center;
      font-family: OPPOSans-B;
      font-weight: 600;
      font-size: 0.36rem;
      color: #ffffff;
      margin: 0.6rem auto 0;
      position: relative;
      .btn-tips {
        position: absolute;
        top: -0.45rem;
        left: 50%;
        font-size: 0.2rem;
        color: #fff;
        background: #d10c0a;
        border-radius: 0.3rem;
        padding: 0.05rem 0.2rem;
        box-sizing: border-box;
        white-space: nowrap;
        &::after {
          content: '';
          position: absolute;
          bottom: -0.28rem;
          left: 0.2rem;
          width: 0;
          height: 0;
          border-top: 0.15rem solid #d10c0a;
          border-bottom: 0.15rem solid transparent;
          border-left: 0.15rem solid transparent;
          border-right: 0.15rem solid transparent;
        }
      }
      .text {
        font-size: 0.22rem;
        font-weight: 100;
      }
    }
  }
  .draw-content {
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/258298/26/17792/5139/67a6d61eFab88c0a3/3df67b469f4e38d4.png) no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 6.16rem;
    margin-top: 0.1rem;
    padding-top: 0.63rem;
    box-sizing: border-box;
    .gift-pic {
      width: 100%;
      margin-top: 0.1rem;
    }
    .til {
      width: 3.53rem;
    }
    .draw-btn {
      position: relative;
      margin-top: 0.2rem;
      .cus-badge {
        width: 0.3rem;
        height: 0.3rem;
        text-align: center;
        line-height: 0.3rem;
        background-color: #fcc080;
        font-family: OPPOSansB;
        font-size: 0.17rem;
        border-radius: 50%;
        position: absolute;
        right: -0.1rem;
        top: -0.1rem;
      }
      img {
        width: 3.7rem;
      }
    }
  }
  .priview-box {
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/258298/26/17792/5139/67a6d61eFab88c0a3/3df67b469f4e38d4.png) no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 6.16rem;
    padding: 0.63rem 0.4rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .priview-video {
      min-width: 2.6rem;
      height: 4.61rem;
      clip-path: polygon(0 0, 2.3rem 0, 100% 0.26rem, 100% 100%, 100% 100%, 0 100%);
      position: relative;
      img {
        position: absolute;
        inset: 0;
        z-index: 1;
      }
      video {
        width: 100%;
        height: 100%;
      }
    }
    .priview-sku {
      width: 3.5rem;
      margin-left: 0.2rem;
    }
  }
  .step-content {
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/264207/38/12752/24476/6789f8cdFa9d11183/5696f431a2a330b1.png) no-repeat;
    background-size: 100% 100%;
    width: 6.75rem;
    height: 4.06rem;
    margin-top: 0.25rem;
    padding-top: 2.88rem;
    .step-btn {
      width: 4.62rem;
    }
  }
}
</style>
