import { init } from '@/utils';
import EventTrackPlugin, { EventTrackParams } from '@/plugins/EventTracking';
import { InitRequest } from '@/types/InitRequest';
import { reportDefaultValue } from '@/utils/trackEvent/lzReport';
import constant from '@/utils/constant';
import { gotoSkuPage } from '@/utils/platforms/jump';
import './styles.css';
import { httpEventRequest } from '@/utils/service';

// 初始化页面
const config: InitRequest = {
  urlPattern: '/custom/:activityType/:templateCode',
};

init(config).then(({ baseInfo, pathParams, decoData }) => {
  httpEventRequest.post('/getBuryPointBehaviors', {
    ...reportDefaultValue(),
    sid: sessionStorage.getItem(constant.LZ_SHOP_ID),
    opid: '100020240315',
    at: '99',
    uid: window.sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN) ?? '',
    e: 'enter',
    c: JSON.stringify({ code: pathParams.customerServiceId, value: pathParams.skuId }),
    t: new Date().getTime(),
  }).then(() => {
    console.log('埋点上报成功');
    console.log(pathParams.skuId);

    gotoSkuPage(pathParams.skuId);
  });
});
