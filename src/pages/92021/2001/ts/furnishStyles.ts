import { computed, reactive } from 'vue';

export const furnish = reactive({
// 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  pageSuccBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 按钮
  btnColor: '',
  btnBg: '',
  btnBorderColor: '',
  // 活动规则
  ruleBg: '',
  rulePageBg: '',
  drawBtnDiv: '',
  drawSuccBtnDiv: '',
  skuBtnsDiv: '',
  // 是否能关闭入会弹窗
  canNotCloseJoinPopup: 1,
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  disableShopName: 0,
  giftImg: '',
  isShowJump: false,
  jumpUrl: '',
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));
const pageSuccBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageSuccBg ? `url("${furnish.pageSuccBg}")` : '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundColor: furnish.btnBg ?? '',
  borderColor: furnish.btnBorderColor ?? '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

export default {
  pageBg,
  pageSuccBg,
  shopNameColor,
  headerBtn,
};
