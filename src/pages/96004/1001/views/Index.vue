<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/228720/6/16409/162726/66135cfcFd2729ff7/1416393b07f23e15.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1">{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event" v-click-track="btn.clickCode">
            <div>{{ btn.name }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="select-hover wheel-box">
      <div class="wheel">
        <lz-lucky-wheel ref="myLucky" width="90vw" height="90vw" :blocks="furnishStyles.params.value.blocks" :prizes="furnishStyles.params.value.prizes" :buttons="furnishStyles.params.value.buttons" @start="startCallback" @end="endCallback" :defaultConfig="furnishStyles.params.value.defaultConfig" />
      </div>
<!--      <div class="girl"></div>-->
<!--      <div class="boy"></div>-->
      <div class="draws-num" :style="furnishStyles.drawsNum.value">今日抽奖机会：{{ chanceNum }} 次</div>
      <div class="draws-num-member" v-if="+furnish.showPersonNum === 1 || +furnish.personNum < joinNum">已有 {{ joinNum }} 人参与</div>
    </div>
    <div class="sku sku-box" v-if="skuList.length > 0">
      <div class="sku-list-box" :style="furnishStyles.showSkuBg.value">
        <div class="sku-list">
          <div class="sku-item" v-for="(item, index) in skuList" :key="index" @click="gotoSkuPage(item.skuId)">
            <img :src="item.skuMainPicture" alt="" />
            <div class="sku-text">{{ item.skuName }}</div>
            <div class="sku-price">￥{{ item.jdPrice }}</div>
          </div>
          <div class="more-btn-all">
            <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="furnish.showWinnersBg === 1" class="winners" :style="furnishStyles.realWinnersBg.value">
      <div class="winners-content">
        <div class="winner-list swiper-container" ref="swiperRef">
          <div class="swiper-wrapper" v-if="activityGiftRecords.length != 0">
            <div class="winnerRecord swiper-slide" v-for="(item, index) in activityGiftRecords" :key="index">
              <div>
                <img src="https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png" alt="" v-if="!item.avatar" />
                <img v-else :src="item.avatar" alt="" />
                <span>{{ item.nickName }}</span>
              </div>
              <span>{{ item.prizeName }}</span>
            </div>
          </div>
          <div v-else>
            <p class="winner-null">暂无相关获奖信息哦~</p>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule" position="bottom">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
    <OrderRecordPopup @close="showOrderRecord = false" :orderRestrainStatus="orderRestrainStatus"></OrderRecordPopup>
  </VanPopup>
  <!--抽奖记录弹窗-->
  <VanPopup teleport="body" v-model:show="showDrawRecord" position="bottom">
    <DrawRecordPopup @close="showDrawRecord = false"></DrawRecordPopup>
  </VanPopup>
  <!-- 活动商品弹窗-->
  <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
    <GoodsPopup :data="orderSkuList" @close="showGoods = false" :orderSkuisExposure="orderSkuisExposure"></GoodsPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import GoodsPopup from '../components/GoodsPopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import DrawRecordPopup from '../components/DrawRecordPopup.vue';
import vueDanmaku from 'vue3-danmaku';
import { gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import { Handler } from '@/utils/handle';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import LzLuckyGrid from '@/components/LzLuckyDraw/LzLuckyGrid.vue';
import LzLuckyGacha from '@/components/LzLuckyDraw/LzLuckyGacha.vue';
import LzLuckyCurettage from '@/components/LzLuckyDraw/LzLuckyCurettage.vue';

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
// 0-全部商品 1-指定商品  2-排除
const orderSkuisExposure = ref(0);
const shopName = ref(baseInfo.shopName);

const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

// 抽奖次数
const chanceNum = ref(0);
// 参与人数
const joinNum = ref(0);
// 订单状态
const orderRestrainStatus = ref(0);
const showDrawRecord = ref(false);
const showMyPrize = ref(false);

const showGoods = ref(false);
const showOrderRecord = ref(false);
type Sku = {
  skuName: string;
  skuMainPicture: string;
  skuId: number;
  jdPrice: string;
};

const skuList = ref<Sku[]>([]);

const orderSkuList = ref<Sku[]>([]);
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

const btnList: {
  name: string;
  clickCode: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    clickCode: 'hdgz',
    event: () => {
      showRulePopup();
    },
  },
  {
    name: '我的奖品',
    clickCode: 'wdjp',
    event: () => {
      showMyPrize.value = true;
    },
  },
  {
    name: '活动商品',
    clickCode: 'hdsp',
    event: () => {
      showGoods.value = true;
    },
  },
  {
    name: '我的订单',
    clickCode: 'wddd',
    event: () => {
      showOrderRecord.value = true;
    },
  },
  {
    name: '抽奖记录',
    clickCode: 'cjjl',
    event: () => {
      showDrawRecord.value = true;
    },
  },
];

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  showGoods.value = false;
  showOrderRecord.value = false;
  savePhonePopup.value = true;
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

// 获取客抽奖次数
const getChanceNum = async () => {
  try {
    const { data } = await httpRequest.post('/96004/chanceNum');
    chanceNum.value = data.chanceNum;
    joinNum.value = data.joinNum;
    orderRestrainStatus.value = data.orderRestrainStatus;
    orderSkuisExposure.value = data.orderSkuisExposure;
  } catch (error) {
    console.error(error);
  }
};

// 获取中奖名单
const getWinners = async () => {
  try {
    const res = await httpRequest.post('/96004/winners');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container', {
        autoplay: activityGiftRecords.length > 5 ? { delay: 1000, stopOnLastSlide: false, disableOnInteraction: false } : false,
        direction: 'vertical',
        loop: activityGiftRecords.length > 5,
        slidesPerView: 5,
        loopedSlides: 7,
        spaceBetween: 10,
      });
    });
  } catch (error) {
    console.error(error);
  }
};

const myLucky = ref();
// 抽奖接口
const lotteryDraw = async () => {
  try {
    const res = await httpRequest.post('/96004/lotteryDraw');
    if (res.data.prizeType) {
      award.value = {
        prizeType: res.data.prizeType,
        prizeName: res.data.prizeName,
        showImg: res.data.prizeImg,
        result: res.data.result ?? '',
        activityPrizeId: res.data.activityPrizeId ?? '',
        userPrizeId: res.data.userPrizeId,
      };
      const index = prizeInfo.findIndex((item) => item.index === res.data.sortId);
      myLucky.value.stop(index);
    } else {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };

      const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
      myLucky.value.stop(index);
    }
  } catch (error) {
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
    myLucky.value.stop(index);
    console.error(error);
  }
  getChanceNum();
  getWinners();
};
const startCallback = async () => {
  lzReportClick('kscj');
  if (chanceNum.value <= 0) {
    showToast('您的抽奖次数已用完');
    return;
  }
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  lotteryDraw();
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};

// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/96004/getPrizes');
    prizeInfo.splice(0);
    prizeInfo.push(...data);
  } catch (error) {
    console.error(error);
  }
};

const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

// 获取曝光商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/96004/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    skuList.value.push(...data.records);
    total.value = data.total;
    pagesAll.value = data.pages;
  } catch (error) {
    console.error(error);
  }
};

const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getChanceNum(), getPrizes(), getWinners(), getSkuList()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
  if (baseInfo.status === 1) {
    const time = baseInfo.startTime - dayjs().valueOf();
    setTimeout(() => {
      window.location.reload();
    }, time);
  }
};
init();
</script>

<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    line-height: 0.52rem;
    position: relative;
    right: -0.3rem;
    top: 0;
    width: 1.25rem;
    height: 0.58rem;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.danmaku {
  width: 6.2rem;
  height: 1rem;
  margin: 0 auto;
}
.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 5.96rem;
  margin: 0.49rem auto 0;
  padding-top: 1.1rem;

  .winners-content {
    width: 6.6rem;
    height: 4.7rem;
    background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
  }
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0 0.3rem;
  .winnerRecord {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.3rem;
    background-color: #fff;
    margin-bottom: 0.05rem;
    border-radius: 0.04rem;
    font-size: 0.26rem;
    img {
      width: 0.6rem;
      height: 0.6rem;
      object-fit: cover;
      border-radius: 1.2rem;
      display: inline;
      vertical-align: middle;
      margin-right: 0.1rem;
    }

    span {
      vertical-align: middle;
      font-size: 0.28rem;
      color: #333333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .winner-null {
    text-align: center;
    line-height: 3.9rem;
    font-size: 0.24rem;
    color: #8c8c8c;
  }
}

.winner {
  display: flex;
  align-items: center;
  height: 0.65rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/168276/30/41149/4329/65683557F493dbec4/b7608a4a44724145.png') no-repeat;
  background-size: 100%;
  width: 5.2rem;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 0.15rem;
  padding-right: 0.4rem;
  color: #333;
  font-size: 0.24rem;
  margin: 0.1rem 0;
  span {
    margin-left: 0.8rem;
    width: 5.2rem;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}

.wheel-box {
  position: absolute;
  top: 10.5rem;
}
.sku-box {
  position: relative;
  //top:12rem;
  //bottom:-3rem;
}

.wheel {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: -6.5rem;
  left: 0.4rem;

  .wheel-img {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    object-fit: contain;
  }
}
.girl {
  width: 1.28rem;
  height: 2.71rem;
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/192595/33/38139/56396/64ffc178F2e54f154/fa3e91368650c35e.png);
  background-repeat: no-repeat;
  background-size: 100%;
  position: absolute;
  left: 0.8rem;
  top: -1rem;
  z-index: 10;
}

.boy {
  width: 2.94rem;
  height: 4.29rem;
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/158185/7/39751/156610/64ffc14cFfc248cc2/6c2a81a8ec9dfd12.png);
  background-repeat: no-repeat;
  background-size: 100%;
  position: absolute;
  left: 4.55rem;
  top: -1.4rem;
  z-index: 10;
}

.sku {
  width: 7.5rem;
  margin: -0.7rem auto 0 auto;
  padding: 0.2rem;
  .sku-list-box {
    background-repeat: no-repeat;
    background-size: 100%;
    width: 7.16rem;
    height: 11.4rem;
    margin: 0 auto;
    padding-top: 1.1rem;
    .sku-list {
      width: 6.6rem;
      height: 9.8rem;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      display: flex;
      margin: 0.2rem auto 0.1rem auto;
      overflow: hidden;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch;
      .more-btn-all {
        width: 6.9rem;
        .more-btn {
          width: 1.8rem;
          height: 0.5rem;
          font-size: 0.2rem;
          color: #fff;
          background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
          background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
          border-radius: 0.25rem;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 auto 0.3rem;
        }
      }
      .sku-item {
        width: 3.22rem;
        margin-bottom: 0.1rem;
        background: rgb(255, 255, 255);
        border-radius: 0.2rem;
        overflow: hidden;
        img {
          display: block;
          width: 3.22rem;
          height: 3.22rem;
        }
        .sku-text {
          width: 3.4rem;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          font-size: 0.3rem;
          color: #262626;
          line-height: 0.4rem;
          height: 0.8rem;
          padding: 0 0.2rem;
          margin: 0.1rem 0;
          box-sizing: border-box;
        }
        .sku-price {
          font-size: 0.3rem;
          color: #ff5f00;
          padding: 0 0.2rem;
          margin-bottom: 0.15rem;
        }
      }
    }
  }
}

.draws-num {
  text-align: center;
  font-size: 0.3rem;
  margin: 0.45rem 0.3rem 0 2.2rem;
}
.draws-num-member {
  position: absolute;
  top: -9.9rem;
  left: 0.25rem;
  color: rgb(0, 0, 0);
  font-size: 0.24rem;
  margin-top: 0.1rem;
  background: rgb(244, 253, 254);
  border-radius: 0.36rem;
  padding: 0.1rem 0.15rem;
}

.draw-btn {
  width: 4rem;
  margin: 0 auto;

  img {
    width: 100%;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
