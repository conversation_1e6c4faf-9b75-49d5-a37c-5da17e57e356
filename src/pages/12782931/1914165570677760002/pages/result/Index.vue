<template>
    <div class="main-view">
        <div class="result-content-box">
            <img class="logo-img" :src="require('../../asset/result/logo.png')" alt="">
            <VanSwipe ref="swiper" :loop="false">
                <van-swipe-item>
                    <div class="result-content">
                      <img class="bg" :src="require('../../asset/result/aiResBg.png')"/>
                      <img class="icon" :src="require('../../asset/result/icon.png')"/>
                      <canvas id="res1Title" style="width: 7.33rem;height: 0.77rem;position: absolute;left: 0;top: 4.44rem;z-index: 5 !important;"></canvas>
                      <div class="weight-type-box">
                        <div v-for="(item, index) in [1,2,3]" :key="index">
                          <img class="res-img" v-if="item == weightType" :src="require(`../../asset/result/petweight/${item}-yes.png`)"/>
                          <img class="res-img" v-else :src="require(`../../asset/result/petweight/${item}-no.png`)"/>
                        </div>
                      </div>
                    </div>
                </van-swipe-item>
                <van-swipe-item>
                  <div class="result-content">
                    <img class="bg" :src="require('../../asset/result/aiResBg2.png')"/>
                    <canvas id="res2content" style="width: 6.2rem;height: 5rem;position: absolute;left: 0.565rem;top: 1.65rem;z-index: 5 !important;"></canvas>
                    <div class="weit-bottom-box">
                      <img class="common-btn" style="position: unset;" :src="require('../../asset/result/toEnd.png')" @click="isShowDisclaimers = true;lzReportClick('veterinarianClick');"/>
                      <!-- <img class="common-btn" style="position: unset;" :src="require('../../asset/result/toShare.png')" @click="toShare"/> -->
                    </div>
                  </div>
                </van-swipe-item>
                <template #indicator="{ active, total }">
                    <div class="page-no-box" :style="{color: `${PAGE_CONFIG.mainBgColor}`}">
                        <div>结果{{ `${active + 1}/${total}`}}</div>
                        <div class="bottom-div">
                            <img class="direction-icon" :src="require(`../../asset/result/direction-left.png`)" @click="swiper.prev()"/>
                            <div style="display: flex;flex-direction: row;">
                                <div class="page-point" :style="{background: `${PAGE_CONFIG.mainBgColor}`, opacity: active == 0 ? 1 : 0.2}"></div>
                                <div class="page-point" :style="{background: `${PAGE_CONFIG.mainBgColor}`, opacity: active == 1 ? 1 : 0.2}"></div>
                            </div>
                            <img class="direction-icon" :src="require(`../../asset/result/direction-right.png`)" @click="swiper.next()"/>
                        </div>
                    </div>
                </template>
            </VanSwipe>
        </div>
        <CopyPopup :isShow="isShowDisclaimers" :copyText="copyText" :copyShowText="copyShowText" @closePopup="isShowDisclaimers = false" @toEnd="toEnd"></CopyPopup>
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted, reactive } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import Loading from '../../components/Loading.vue';
import CopyPopup from '../../components/CopyPopup.vue';
import { getWeightCheckResult } from '../../config/api';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { useRouter, useRoute } from 'vue-router';
import { drawRoundedRect, fillTextWithWrap } from '../../config/common';
import { toCustomerService } from '@/utils/platforms/jump';

const route = useRoute();
const router = useRouter();
const store = useStore<RootState>();
const swiper = ref();

const weightType = ref(store.state.weightType);

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const PetInfo = reactive({
  petNick: window.localStorage.getItem('12782931-petNick') || '',
});
const toothImg = computed(() => store.state.toothImg);
const checkId = computed(() => store.state.checkId);
const isShowDisclaimers = ref(false);

const copyText = ref('');
const copyShowText = ref('');
const gengerList = ['公', '母'];
const bodySizeList = ['玩具犬', '小型犬', '中小型犬', '中大型犬', '大型犬'];
const resList = ['体重偏轻', '体重相似', '体重偏重'];

const setCopyText = (weightType: number, petNick: string, petGender: number, petBreed: string, step1Select: number, petBirth: string, petWeight: string) => {
  if (store.state.createPetForm.petNick) {
    copyText.value = `宠物名:${petNick} 性别:${gengerList[petGender - 1]} 品种:${petBreed} 预计成年后体型: ${bodySizeList[step1Select - 1]} 出生日期:${petBirth} 目前体重:${petWeight}kg 与同龄同体型狗狗相比${resList[weightType - 1]}`;
    copyShowText.value = `宠物名:${petNick}<br>性别:${gengerList[petGender - 1]}<br>品种:${petBreed}<br>预计成年后体型:${bodySizeList[step1Select - 1]}<br>出生日期:${petBirth}<br>目前体重:${petWeight}kg<br>与同龄同体型狗狗相比${resList[weightType - 1]}`;
    window.localStorage.setItem('12782931-copy', JSON.stringify({
      textVale: copyText.value,
      showText: copyShowText.value,
    }));
  } else {
    const copyData = JSON.parse(window.localStorage.getItem('12782931-copy') || '{}');
    copyText.value = copyData.textVale;
    copyShowText.value = copyData.showText;
  }
};

const text2List = [
  '低于90%的同龄同体型幼犬',
  '在同龄同体型幼犬的平均范围内',
  '高于90%的同龄同体型幼犬',
];
const addTitle1 = () => {
  const canvas = document.getElementById('res1Title') as HTMLCanvasElement;
  const ctx = canvas.getContext('2d')!;
  const posterWidth = 733; // 7.5rem 转换为像素
  const posterHeight = 77; // 示例高度
  canvas.width = posterWidth;
  canvas.height = posterHeight;
  // ctx.fillStyle = '#fff';
  // ctx.fillRect(0, 0, posterWidth, posterHeight);
  ctx.font = '28px Arial';
  ctx.fillStyle = '#333333';
  ctx.textAlign = 'center';
  ctx.fillText(`与同龄狗狗相比，${PetInfo.petNick}的体重`, posterWidth / 2, 28);
};
const addContent2 = () => {
  const canvas = document.getElementById('res2content') as HTMLCanvasElement;
  const ctx = canvas.getContext('2d')!;
  const posterWidth = 620; // 7.5rem 转换为像素
  const posterHeight = 500; // 示例高度
  canvas.width = posterWidth;
  canvas.height = posterHeight;
  ctx.fillStyle = '#ffefed';
  // 绘制圆角矩形背景
  drawRoundedRect(ctx, 0, 0, posterWidth, posterHeight, 24); // 20 是圆角半径
  ctx.font = '28px Arial';
  ctx.fillStyle = '#ec001a';
  ctx.textAlign = 'left';
  const text = `要确定健康的体重，我们需要不止一个时间点的数据，建议您在多个时间点跟踪${PetInfo.petNick}的体重，并与兽医讨论。`;
  const x = 30;
  const y = 64;
  const text2 = `根据您今天分享的信息，${PetInfo.petNick}的体重${text2List[weightType.value - 1]}。这可能由多种因素导致，比如它的基因、活动量以及饮食。`;
  const y2 = 286;
  const maxWidth = 540; // 文本的最大宽度
  const lineHeight = 56; // 行高
  fillTextWithWrap(ctx, text, x, y, maxWidth, lineHeight);
  fillTextWithWrap(ctx, text2, x, y2, maxWidth, lineHeight);
};

const openPopup: any = inject('openPopup');
const qustionRes = ref();
const aiRes = ref();

const isLoadding = ref(true);
const pageStep = ref(0);
const resNo = ref(0);
const toEnd = () => {
  isShowDisclaimers.value = false;
  lzReportClick('copy');
  toCustomerService('13309724');
  // window.jmfe.toAny('https://jdcs.m.jd.com/index.action?venderId=13309724');
  // window.location.href = 'https://jdcs.m.jd.com/index.action?venderId=13309724';
};
const toShare = () => {
  lzReportClick('clickShare');
  router.push({
    path: '/post',
  });
};
const timeNow = ref(Date.now());
const getAiRes = (params: any) => {
  console.log(store.state, '*********');
  getWeightCheckResult(params).then((ressult: any) => {
    console.log(ressult);
    if (ressult.status) {
      store.commit('setWeightType', ressult.res.data.checkResult);
      weightType.value = ressult.res.data.checkResult;
      aiRes.value = ressult.res.data;
      setTimeout(() => {
        setCopyText(weightType.value, PetInfo.petNick, store.state.createPetForm.petGender, store.state.createPetForm.petBreed, store.state.step1Select, store.state.createPetForm.petBirth, store.state.petWeight);
        addTitle1();
        addContent2();
        openPopup();
      }, 500);
    } else {
      router.push('/pageerror');
    }
  });
};
const init = () => {
  let params;
  if (checkId.value) {
    params = {
      bodySize: store.state.step1Select,
      checkId: checkId.value,
      petBirth: store.state.createPetForm.petBirth || '',
      weight: store.state.petWeight,
    };
    window.localStorage.setItem('12782931-params', JSON.stringify(params));
  } else {
    params = JSON.parse(window.localStorage.getItem('12782931-params') || '{}');
  }
  getAiRes(params);
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  lzReportClick('resultPage');
  init();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
