<template>
    <div class="main-view">
        <div class="qustion-slise-box" :style="`background-color: ${PAGE_CONFIG.questionSlideBg};`">
            <div class="qustion-slise-item" :style="`background-color: ${PAGE_CONFIG.quuetionSlideColor};width: ${ Math.floor((4 / 6) * 100) }%;`"></div>
        </div>
        <div class="qustion-step-text">{{ `(4/6)` }}</div>
        <canvas id="step1Title" style="width: 7.5rem;height: 1.4rem;"></canvas>
        <div class="question-step1-content">
            <div class="step1-item" v-for="(item, index) in [1,2,3,4,5]" :key="index">
                <img v-if="item === selectVal" :src="require(`../../asset/question/${item - 1}-yes.png`)" alt="">
                <img v-else :src="require(`../../asset/question/${item - 1}-no.png`)" alt="" @click="selectVal = item">
            </div>
        </div>
        <img class="qustion-step-tips" :src="require(`../../asset/question/tips1.png`)"/>
        <div class="photo-bottom">
          <img class="item-btn" style="width: 2.2rem;height: 1.13rem;" :src="require('../../asset/question/before.png')" @click="router.back();"/>
          <img class="item-btn" style="width: 2.2rem;height: 1.13rem;" v-if="!selectVal" :src="require('../../asset/question/next-no.png')"/>
          <img class="item-btn" style="width: 2.2rem;height: 1.13rem;" v-else :src="require('../../asset/question/next-yes.png')" @click="nextStep"/>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted, reactive } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import { getPetBodySize } from '../../config/api';
import { emit } from 'process';
import { lzReportClick } from '@/pages/MARS/lzReport';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant/es';

const route = useRoute();
const router = useRouter();
const store = useStore<RootState>();
const PetInfo = computed(() => store.state.createPetForm);

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');

const addTitle = () => {
  const canvas = document.getElementById('step1Title') as HTMLCanvasElement;
  const ctx = canvas.getContext('2d')!;
  const posterWidth = 750; // 7.5rem 转换为像素
  const posterHeight = 140; // 示例高度
  canvas.width = posterWidth;
  canvas.height = posterHeight;
  ctx.fillStyle = '#fff';
  ctx.fillRect(0, 0, posterWidth, posterHeight);
  ctx.font = 'bold 40px Arial';
  ctx.fillStyle = '#000';
  ctx.textAlign = 'center';
  ctx.fillText(`成年后，${PetInfo.value.petNick}的体型是？`, posterWidth / 2, 45);
};

const selectVal = ref(store.state.step1Select);
const nextStep = () => {
  if (!selectVal.value) return;
  store.commit('setStep1Select', selectVal.value);
  lzReportClick('qa-petBodySize');
  router.push('/step5');
};

onMounted(() => {
  console.log('step1', store.state.step1Select);
  addTitle();
  if (!selectVal.value) {
    getPetBodySize({
      petBreed: PetInfo.value.petBreed,
      petGender: PetInfo.value.petGender,
      petId: PetInfo.value.petId,
      petNick: PetInfo.value.petNick,
      petBreedId: PetInfo.value.petBreedId,
    }).then((res: any) => {
      // console.log(res);
      selectVal.value = res.data.petBodySize;
      store.commit('setStep1Select', res.data.petBodySize);
      store.commit('setCheckId', res.data.checkId);
    });
  }
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss">
.swiper-box {
  width: 7.5rem;
  height: auto;
  background: #fff;
  overflow: hidden;
  position: relative;
}
</style>
