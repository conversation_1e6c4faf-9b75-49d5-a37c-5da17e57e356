/* eslint-disable */

import { showToast, showLoadingToast } from 'vant';

// 重写log，除测试和开发环境不执行log
// console.log = (function resetLog(oriLogFunc) {
//   return function log() {
//     const arg = Array.from(arguments);
//     process.env.VUE_APP_MODE === 'dev' || (process.env.VUE_APP_MODE === 'test' && oriLogFunc(...arg));
//   };
// })(console.log);

// 活动规则格式化
const ruleFormat = (rule: string): string => {
  // 活动规则格式化
  if (rule === '') {
    return '暂无规则';
  }
  const rules: string[] = rule.split('\n');
  const ua = navigator.userAgent.toLowerCase();
  // rules.push('本次活动为商家自发举办，与苹果公司及App Store无关；');
  if (/iphone|ipad|ipod/.test(ua)) {
    rules.push('本次活动为商家自发举办，与苹果公司及App Store无关；');
  }
  return rules.join('<br/>');
};

const getMobileModel = () => {
  var userAgent = navigator.userAgent || navigator.vendor || window.opera;

  // iPhone模型检测
  if (/iPhone/.test(userAgent) && !window.MSStream) {
    var iPhoneModel = userAgent.match(/iPhone\s+([\w\s]+)/i);
    return iPhoneModel ? 'iPhone' : 'Unknown iPhone model';
  }

  // Android模型检测
  if (/Android/.test(userAgent)) {
    var androidModel = userAgent.match(/Android\s+([\w\.]+);.*\s([^\s;]+)\sBuild/i);
    return androidModel ? 'Android' : 'Unknown Android model';
  }

  // 其他设备的通用检测
  return 'Unknown device model';
};
// 表情match
const iosFace = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\u00A9|\u00AE|\u3030/gi;
const androidFace = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\u00A9|\u00AE|\u3030/gi;

const delayToast = async (text: any): Promise<void> => {
  setTimeout(() => {
    showToast(text);
  }, 1000);
};
const getUrlQueryValueByKey = (url: string, keyName: string) => {
  const reg = new RegExp(`(^|[&?])${keyName}=([^&]*)(&|$)`);
  const r = url.match(reg); // search,查询？后面的参数，并匹配正则
  if (r != null) {
    return unescape(r[2]);
  }
  return null;
};
export const shareUuid = getUrlQueryValueByKey(window.location.href, 'shareUuid') || '';
console.log('shareUuid===>', shareUuid);
export const shareWork = getUrlQueryValueByKey(window.location.href, 'shareWork') || '';
console.log('shareWork===>', shareWork);
const formatDate = (date: Date) => {
  let year = date.getFullYear();
  let month: string | number = date.getMonth() + 1; // 月份是从0开始的
  let day: string | number = date.getDate();

  // 补齐月份和日期的位数
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;

  return year + '-' + month + '-' + day;
};
export { ruleFormat, iosFace, androidFace, getMobileModel, delayToast, formatDate };
