<template>
    <VanPopup teleport="body" v-model:show="show" position="center" :close-on-click-overlay="true">
        <div>
            <img class="pet-close-icon close-icon" :src="require('../asset/closeIcon.png')" @click="closePopup()"/>
            <div class="common-text-popup">
                <div class="title">适用宠物</div>
                <div style="margin-bottom: 0.6rem;">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;许多宠物主认为他们狗狗的牙齿和牙龈是健康的，但据美国兽医协会(AVMA)称，80%的狗狗到三岁时都存在不同程度的口腔健康问题。
                </div>
                <div style="margin-bottom: 0.6rem;">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;因此，了解口腔健康问题的潜在迹象非常重要，如果发现这些问题，应咨询兽医以保持狗狗的口腔健康。
                </div>
                <div style="margin-bottom: 0.6rem;">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;使用汪汪爱牙宝来查看您的狗狗是否有牙结石堆积或许多牙龈发红和发炎等视觉迹象，这些都可能表明狗狗存在口腔健康问题。
                </div>
                <div>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;汪汪爱牙宝适用于所有6个月以上的狗狗，此时它们的恒牙应该都已长出。
                </div>
            </div>
        </div>
    </VanPopup>
</template>
<script lang="ts" setup>
import { FLAGS } from 'html2canvas/dist/types/dom/element-container';
import { emit } from 'process';
import { defineProps, computed, ref } from 'vue';

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
  },
});

const show = computed(() => props.isShow);
const emits = defineEmits(['closePopup']);
const closePopup = () => {
  emits('closePopup');
};

</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
