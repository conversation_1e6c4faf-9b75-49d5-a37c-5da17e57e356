<template>
  <div id="container">
    <!-- 用动画如果在组件页内没有根节点，组件会加载失败，页面清空，可以不用动画或者自己写一个动画 -->
    <Transition enter-active-class="animate__animated animate__fadeIn" mode="out-in">
      <component :is="curComponent" @toggle-component="handleToggleComponent">
      </component>
    </Transition>
  </div>
</template>

<script lang="ts" setup>
/* eslint-disable */
/* 快捷打开项目命令  npm run serve src/pages/1000014803/1863500219271974914 */
/* 测试链接 https://lzkjdz-isv.isvjcloud.com/test/cc/custom/1000014803/1863500219271974914/ */

import { inject, shallowRef, provide, computed, ref, onMounted } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { PAGE_CONFIG } from './config/config';
import Photo from './views/Photo.vue';
import PhotoError from './views/PhotoError.vue';
import PageError from './views/PageError.vue';
import { httpRequest } from '@/utils/service';

const componentList: any = {
  Photo,
  PhotoError,
  PageError,
};

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
provide('PAGE_CONFIG', PAGE_CONFIG);
// 当前显示的组件
const curComponent = shallowRef(Photo);
const componentname = ref('');
const toggleComponent = ref(0);
provide('toggleComponent', computed(() => toggleComponent.value));
// 处理切换组件
const handleToggleComponent = (componentName: string) => {
  console.log('切换到组件:', componentName);
  console.log('组件对象:', componentList[componentName]);
  if (componentList[componentName]) {
    curComponent.value = componentList[componentName];
    componentname.value = componentName;
    toggleComponent.value += 1;
  } else {
    console.error('组件不存在:', componentName);
  }
};
provide('componentName', computed(() => componentname.value));
onMounted(() => {
  // getCategory();
  // getRule();
});
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'SourceHanSansCN';
  src: url('https://lzcdn.dianpusoft.cn/fonts/SourceHanSansCN/SourceHanSansCN-Medium.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
#container {
  width: 100vw;
  min-height: 100vh;
  max-width: 100vw;
  line-height: 1;
  font-family: SourceHanSansCN;
  overflow-x: hidden;
}
</style>

<style lang="scss">
/* 隐藏Webkit内核浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}

// 禁止页面回弹行为
html,
body {
  overscroll-behavior-y: none;
  overscroll-behavior-x: none;
}

.van-popup {
  width: 7.5rem;
  background: none;
  overflow-y: unset;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.van-popup--center {
  max-width: 100%;
}
.van-picker {
  width: 100%;
}
.add-btn {
  width: 1.38rem;
  height: 1.36rem;
  position: fixed;
  right: 0;
  bottom: 1.5rem;
}
.bottom-tab-view {
  width: 7.5rem;
  height: 1.32rem;
  background: #fff;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
  position: fixed;
  left: 0;
  bottom: 0;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/253761/40/8156/6692/67775ce8Ff34cdcaf/977461fdf93fd06f.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  display: flex;
}
</style>
./config/12782931_config./config/1000001706_config./config/1000000844_config./config/config