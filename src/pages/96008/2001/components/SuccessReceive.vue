<template>
  <div class="rule-bk">
    <div class="receiveTitle">
      <!--领取成功-->
      <img v-if="props.prize.prizeType === 3 || props.prize.prizeType === 8" src="//img10.360buyimg.com/imgzone/jfs/t1/305412/19/11905/3196/6853d94aF00ce0a31/c3c5ef754a7d078e.png" alt="">
      <!--恭喜您获得-->
      <img v-else src="//img10.360buyimg.com/imgzone/jfs/t1/315089/1/10676/4158/6853d94fF7f759d7a/1d90c1fc31760c1b.png" alt="">
    </div>
    <div class="prizeImg">
      <img :src="props.prize.prizeImg" alt="">
    </div>
    <div class="prizeName">
      {{props.prize.prizeName}}
    </div>
    <div class="prizeTips">
      <div v-if="props.prize.prizeType === 1 || props.prize.prizeType === 6 || props.prize.prizeType === 8">奖品稍后将放入你的账户</div>
      <div v-else-if="props.prize.prizeType === 2">
        奖品稍后将放入您的账户，届时可在个人中心-京豆中查着
      </div>
    </div>
    <div class="btn" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>

const props = defineProps(['prize']);

const emits = defineEmits(['close','addAddress']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-size: 100% 100%;
  width: 6.03rem;
  height: 6.9rem;
  position: relative;
  padding-top: 1.1rem;
  padding-left: 0.3rem;
  padding-right: 0.3rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/287246/9/14131/17980/6853d885Fd1d29ff0/a0ba2222b82cd7c8.png') no-repeat;
  background-size: 100% 100%;

  .receiveTitle{
    width: 100%;
    img{
      width: auto;
      height: 0.3rem;
      margin: 0 auto;
    }
  }
  .prizeImg{
    width: auto;
    height: 2rem;
    img{
      width: auto;
      height: 2rem;
      margin: 0.3rem auto;
    }
  }
  .prizeName{
    margin: 0 auto;
    text-align: center;
    font-size: 0.3rem;
    color: #000;
    line-height: 0.3rem;
  }
  .prizeTips{
    font-size: 0.24rem;
    margin: 0.2rem auto;
    text-align: center;
    width: 4.5rem;
    color: #565656;
  }

  .btn {
    width: 4.48rem;
    height: 0.72rem;
    margin: 0 auto;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/288056/37/15368/5029/685a20aeFb366a0e8/2f172771b32b043c.png') no-repeat;
    background-size: 100% 100%;
  }
  .addressBtn{
    width: 4.48rem;
    height: 0.72rem;
    margin: 0 auto;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/313580/5/11990/6412/685a2135F70b95233/a7af2c3d3a0787aa.png') no-repeat;
    background-size: 100% 100%;
  }
}
</style>
