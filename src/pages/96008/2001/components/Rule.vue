<template>
  <div class="rule-bk">
    <div class="content" v-html="rule"/>
  </div>
</template>

<script lang="ts" setup>

const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 6.03rem;
  height: 7.65rem;
  position: relative;
  padding-top: 1rem;
  padding-left: 0.4rem;
  padding-right: 0.4rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/320040/14/9962/18065/6853b917Fd306d0d6/1d2efe33900afc42.png') no-repeat;
  background-size: 100% 100%;

  .content {
    height: 6rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #4e220b;
    padding: 0 0.15rem;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
