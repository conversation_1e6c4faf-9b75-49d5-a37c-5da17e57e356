import { computed, reactive } from 'vue';
export const furnish = reactive<{ [x: string]: string }>({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  ruleBtn: '', // 活动规则按钮
  myPrizeBtn: '', // 我的奖品按钮
  myOrderBtn: '', // 我的订单按钮
  stepBg: '', // 步骤图
  prizeBg: '', // 奖品背景图
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
}));

const ruleBtn = computed(() => ({
  backgroundImage: furnish.ruleBtn ? `url("${furnish.ruleBtn}")` : '',
}));

const myPrizeBtn = computed(() => ({
  backgroundImage: furnish.myPrizeBtn ? `url("${furnish.myPrizeBtn}")` : '',
}));

const myOrderBtn = computed(() => ({
  backgroundImage: furnish.myOrderBtn ? `url("${furnish.myOrderBtn}")` : '',
}));

const prizeBg = computed(() => ({
  backgroundImage: furnish.prizeBg ? `url("${furnish.prizeBg}")` : '',
}));

export default {
  pageBg,
  ruleBtn,
  myPrizeBtn,
  myOrderBtn,
  prizeBg,
};
