<template>
  <div class="bg" :style="isSuccess ? furnishStyles.successPageBg.value : furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div :class="isSuccess ? 'header-kv-success' : 'header-kv'">
      <img class="rule-btn" :src="furnish.ruleBtn" @click="showRulePopup" alt="" />
    </div>
    <div class="blank">
      <div v-if="!isSuccess">
        <div><span class="text1">成功申领</span> <span class="text2">{{furnish.mainPageText}}</span></div>
        <!-- 进度条 -->
        <div class="progress-bar">
          <div class="progress-bar-inner" :style="{ width: progressWidth }">
            <div class="bubble">{{progressWidth}}</div>
          </div>
        </div>
        <div class="content-text">
         {{ruleTestShort}}
        </div>
        <div class="join-btn" @click="ShowToast"></div>
      </div>
      <div v-if="isSuccess">
        <div class="success-btn"></div>
        <div class="success-center">成功申领</div>
        <div class="success-text">{{furnish.successPageText}}</div>
        <!-- 进度条 -->
        <div class="progress-bar">
          <div class="progress-bar-inner" :style="{ width: progressWidth }">
            <div class="bubble">{{progressWidth}}</div>
          </div>
        </div>
        <div class="step"></div>
      </div>
    </div>
    <!-- 曝光商品-->
    <div class="sku-bg" v-if="skuListPreview.length">
      <div class="sku-list" >
        <div class="sku-item" v-for="(item,index) in skuListPreview" :key="index">
          <div class="img-box">
            <img :src="item.skuMainPicture" alt="">
          </div>
          <div class="sku-text">{{item.skuName}}</div>
          <div class="sku-btns">
          </div>
        </div>
        <div class="more-btn-all">
          <div class="more-btn" v-if="skuListPreview.length && skuListPreview.length !== total" @click="ShowToast()">点我加载更多</div>
        </div>
      </div>
    </div>
  </div>
  <!--活动规则弹窗-->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, inject, onMounted } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const shopName = ref('');

const isLoadingFinish = ref(true);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
}
const prizeList = ref<Prize>(defaultStateList);

const total = ref(0);

type Sku = {
  skuId: number,
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuListPreview = ref<Sku[]>([]);
const orderSkuListPreview = ref<Sku[]>([]);
const nextStateAmount = ref(0);

const showRule = ref(false);
const ruleTest = ref('');
const ruleTestShort = ref('');
const showAward = ref(false);
const showOrderRecord = ref(false);
const showStrategyPopup = ref(false);

// 活动规则相关
const showRulePopup = async () => {
  showRule.value = true;
};

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 装修时选择框
const showSelect = ref(false);
// 装修展示页面  1主页 2成功页 3领取攻略
const activeKey = ref('1');
const isSuccess = ref(false);
const selectedId = ref(1); // 装修时选择框序号

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 页面截图
const isCreateImg = ref(false);
const tabNum = ref('0');
const createImg = async () => {
  showRule.value = false;
  showOrderRecord.value = false;
  isCreateImg.value = true;
  tabNum.value = activeKey.value;
  const interactC = document.getElementById('interact-c') as HTMLElement;
  useHtmlToCanvas(interactC).then(() => {
    activeKey.value = tabNum.value;
  });
};

// 进度条
const progressWidth = ref('80%');

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuListPreview) {
    skuListPreview.value = data.skuListPreview;
  }
  if (data.orderSkuListPreview) {
    orderSkuListPreview.value = data.orderSkuListPreview;
  }
  ruleTest.value = data.rules;
  ruleTestShort.value = data.shortRules;
  shopName.value = data.shopName;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
  showSelect.value = false;
});
// 点击边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

registerHandler('activeKey', (data: any) => {
  activeKey.value = data;
  // if (activeKey.value === '3') {
  //   showRulePopup();
  // }
  if (activeKey.value === '2') {
    isSuccess.value = true;
    showRule.value = false;
  }
  if (activeKey.value === '1') {
    isSuccess.value = false;
    showRule.value = false;
  }
});

onMounted(() => {
  if (activityData) {
    prizeList.value = activityData.prizeList;
    ruleTest.value = activityData.rules;
    ruleTestShort.value = activityData.shortRules;
    orderSkuListPreview.value = activityData.orderSkuListPreview;
    // endTime.value = dayjs(activityData.endTime).valueOf();
    nextStateAmount.value = prizeList.value[0].stepAmount || 0;
    shopName.value = activityData.shopName;
    skuListPreview.value = activityData.skuListPreview;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>

<style>
@font-face {
  font-family: 'FZLTHJW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTHJW/FZLTHJW--GB1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTHJW/FZLTHJW--GB1-0.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'FZLTHJW';
}
</style>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom:0.2rem;
}

.header-kv {
  position: relative;
  height: 9.72rem;
  .rule-btn {
    position: absolute;
    top: 2.93rem;
    right: 0;
    width: 0.58rem;
  }
}
.header-kv-success{
  position: relative;
  height: 8.68rem;
  .rule-btn {
    position: absolute;
    top: 2.93rem;
    right: 0;
    width: 0.58rem;
  }
}

.blank {
  width: 7rem;
  height: 4.5rem;
  margin: 0 auto;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/129687/2/43368/34134/661d1f29Fff33956e/de7250d6031ec069.png') no-repeat;
  background-size: 100%;
  padding: 0.3rem 0.4rem 0.4rem;
  .text1 {
    font-size: 0.36rem;
    color: #b80818;
    margin-right: 0.2rem;
  }
  .text2 {
    font-size: 0.22rem;
  }
  .progress-bar {
    width: 6.12rem;
    height: 0.25rem;
    margin: 0.62rem auto 0;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/249757/33/7464/1060/661d1f29F930257f9/6fa6f03a86ac956f.png') no-repeat;
    background-size: 100%;
    padding: 0.025rem 0.07rem 0;
    .progress-bar-inner {
      //width: 80%;
      height: 0.2rem;
      border-radius: 0.2rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/110292/35/26393/1306/661d1f29Fac0da7ef/2c185f9944cf29f7.png');
      background-repeat: no-repeat;
      background-size: 120% 100%;
      background-position: center;
      position:relative;
      .bubble{
        position: absolute;
        top: -0.45rem;
        right: -0.25rem;
        width: 0.56rem;
        height: 0.38rem;
        line-height: 0.32rem;
        text-align: center;
        font-size: 0.2rem;
        color: #fff;
        background: url('//img10.360buyimg.com/imgzone/jfs/t1/194286/21/44054/769/661dea5fFfb74b522/223c5ba4f740f71c.png') no-repeat;
        background-size: 100%;
      }
    }
  }
  .content-text{
    font-size: 0.18rem;
    line-height: 0.32rem;
    color: #494944;
    height: 1.6rem;
    overflow: hidden;
    overflow-y: scroll;
    margin: 0.2rem 0;
    white-space: pre-wrap;
  }
  .join-btn {
    margin:0.2rem auto 0;
    background: url("//img10.360buyimg.com/imgzone/jfs/t1/229105/10/16843/11096/661de91eF3ac289b1/a2e34261151fe256.png") no-repeat;
    background-size: 100%;
    width:2.13rem;
    height:0.49rem;
  }
  .success-btn{
    margin:0 auto;
    background: url("//img10.360buyimg.com/imgzone/jfs/t1/191483/27/44066/3836/661e12dcF22da95b9/8e6849b7bac9dff6.png") no-repeat;
    background-size: 100%;
    width:2.43rem;
    height:0.67rem;
  }
  .success-center {
    color: #b8952b;
    margin:0.15rem auto 0;
    text-align: center;
    font-size: 0.26rem;
  }
  .success-text {
    margin:0 auto;
    font-size: 0.22rem;
    text-align: center;
  }
  .step{
    margin:0.3rem auto 0;
    background: url("//img10.360buyimg.com/imgzone/jfs/t1/230190/16/14023/13090/661e12dcF04c67d89/2f2632f11243d586.png") no-repeat;
    background-size: 100%;
    width:6.05rem;
    height:0.76rem;
  }
}
.sku-bg{
  width: 6.8rem;
  max-height: 9.54rem;
  background-color: #ffffff;
  border-radius: 0.1rem;
  margin: 0.4rem auto;
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    //margin: 0.2rem auto 0.1rem auto;
    width: 6.8rem;
    max-height: 9.3rem;
    margin: -0.43rem auto 0px;
    place-content: flex-start space-between;
    padding: 0.2rem;
    border-radius: 0.4rem 0 0.05rem 0.05rem;
    //height: 5.9rem;
    position: relative;
    overflow-y: scroll;
  }
  .more-btn-all {
    width:6.9rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #494944;
      background: -webkit-gradient(linear, left top, right top, from(#e5d4a3), to(#d5c091));
      background: linear-gradient(90deg, #e5d4a3 0%, #d5c091 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
  .sku-item{
    width: 3.05rem;
    height:4.5rem;
    margin-bottom: 0.1rem;
    overflow: hidden;
    background:url("//img10.360buyimg.com/imgzone/jfs/t1/96213/34/43168/3404/661df26cF65f8a208/7eb7a71aa5945c91.png") no-repeat;
    background-size: 100%;
    .img-box{
      margin: 0.16rem auto 0;
      width: 2.8rem;
      height: 2.8rem;
      border-radius: 0.1rem;
      //background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/115779/26/43052/4243/65a5ef3eF130b15a6/a7f6c61bf8fc34c6.png");
      background-size: 100%;
      background-repeat: no-repeat;
      overflow: hidden;
      //padding: 0.1rem;
    }
    img{
      display: block;
      height: 2.8rem;
      margin:  auto;
      border-radius: 0.2rem;
    }
    .sku-text{
      width: 2.7rem;
      font-size: 0.23rem;
      margin: 0.1rem auto 0.1rem auto;
      box-sizing: border-box;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }
      .sku-btns{
        margin:0 auto;
        width: 1.53rem;
        height: 0.42rem;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/216057/12/38056/3059/661df26dF563f980a/c55c4abba6dcdb45.png);
      }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
