<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }" @click="onSelected(1)">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img" />
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <!-- <div> -->
          <div class="header-btn" :style="furnishStyles.ruleBtn.value" @click="showRule = true"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.prizeBtn.value" @click="showMyPrize = true"><div>我的奖品</div></div>
          <div class="header-btn" :style="furnishStyles.shareFriendBtn.value" @click="showShareFriendPop = true"><div>邀请好友</div></div>
          <div class="header-btn" :style="furnishStyles.drawRecordBtn.value" @click="showDrawRecordPop = true"><div>抽奖记录</div></div>
        <!-- </div> -->
      </div>
    </div>
    <div class="gameAllDiv" :class="{ 'on-select': selectedId === 2 }" @click="onSelected(2)"  :style="furnishStyles.gameBg.value">
      <div class="topRightStyle" :style="furnishStyles.moreGameColor.value" @click="showTask = true">获取更多游戏机会>></div>
      <div class="bottomBtnAll">
        <div class="startGameDiv" :style="furnishStyles.gameBtn.value" @click.stop="showToastText()">开始游戏
          <div class="gameCountDiv">0</div>
        </div>
        <div class="startDrawDiv" :style="furnishStyles.gameDrawBtn.value" @click.stop="showToastText()">立即抽奖
          <div class="drawCountDiv">0</div></div>
      </div>
    </div>
    <div class="prizeListDiv" :class="{ 'on-select': selectedId === 3 }" @click="onSelected(3)">
      <div class="prizeTitleDiv" :style="furnishStyles.prizeShowTitle.value"></div>
      <div class="prizeAll" v-if="prizeList.length > 0">
        <div class="itemDiv" v-for="(item, index) in prizeList" :key="index">
          <div class="prizeImage">
            <img :src="item.prizeImg" alt="" />
            <div class="backDiv">{{PRIZE_TYPE[item.prizeType]}}</div>
          </div>
          <div class="prizeMessage">
            <div class="prizeName">{{item.prizeName}}</div>
            <div class="prizeRestDiv">奖品剩余<span>{{item.sendTotalCount}}份</span></div>
          </div>
        </div>
      </div>
    </div>
    <div class="sku" :class="{ 'on-select': selectedId === 4 }" @click="onSelected(4)">
      <img class="title-img" v-if="showSelect || isExposure === 1" :src="furnish.winnersBg" alt="" />
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuListPreview" :key="index">
          <img :src="item.skuMainPicture" alt="" />
          <div class="sku-text">{{ item.skuName }}</div>
          <div class="sku-btns">
            <div class="price">￥{{ item.jdPrice }}</div>
            <div class="to-bug" @click.stop="showToastText()">抢购</div>
          </div>
        </div>
        <div class="more-btn-all">
          <div class="more-btn" v-if="skuListPreview.length && skuListPreview.length !== total" @click="ShowToast()">点我加载更多</div>
        </div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <div v-if="!isCreateImg">
    <VanPopup teleport="body" position="center" close-on-click-overlay v-model:show="furnishStyles.isShowWheel.value" @click-overlay="wheelClick">
        <lz-lucky-wheel ref="myLucky" width="90vw" height="90vw" :blocks="furnishStyles.params.value.blocks" :prizes="furnishStyles.params.value.prizes" :buttons="furnishStyles.params.value.buttons" @start="startCallback" @end="endCallback" :defaultConfig="furnishStyles.params.value.defaultConfig" />
    </VanPopup>
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!--  抽奖记录弹窗-->
    <VanPopup teleport="body" v-model:show="showDrawRecordPop" position="bottom">
      <DrawRecordPop @close="showDrawRecordPop = false">
      </DrawRecordPop>
    </VanPopup>
    <!--  邀请好友记录-->
    <VanPopup teleport="body" v-model:show="showShareFriendPop" position="bottom">
      <ShareFriends @close="showShareFriendPop = false">
      </ShareFriends>
    </VanPopup>
    <!-- 做任务弹窗 -->
    <VanPopup teleport="body" v-model:show="showTask" position="bottom">
      <DoTask :times="times" :tasks="tasks" @close="showTask = false"></DoTask>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :addressId="addressId" :activityPrizeId="''" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import Swiper, { Autoplay } from 'swiper';
import html2canvas from 'html2canvas';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import DoTask from '../components/DoTask.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import { Prize, PRIZE_TYPE, Task } from '../ts/type';
import DrawRecordPop from '../components/drawRecord.vue';
import ShareFriends from '../components/ShareFriends.vue';
import { showToast } from 'vant';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';

Swiper.use([Autoplay]);

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
console.log(activityData);
const isExposure = ref(1);
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuListPreview = ref<Sku[]>([]);
const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

const shopName = ref('xxx自营旗舰店');
const showDrawRecordPop = ref(false);
const showShareFriendPop = ref(false);

const isLoadingFinish = ref(false);
const total = ref(0);

const showRule = ref(false);
const ruleTest = ref('');

const showMyPrize = ref(false);

const tasks = reactive([] as Task[]);
const showTask = ref(false);
const times = ref(0);

// 奖品展示
const prizeList = ref([] as Prize[]);

// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});
const wheelClick = () => {
  furnish.isShowWheel = false;
};
// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 装修时选择框
const showSelect = ref(false);
const isShowWheel = ref(false);
// console.log(isShowWheel.value, 'sdddddddddddd');
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const isShowWheel1 = computed(() => {
  console.log('cccccccccccccc');
  return false;
});
const myLucky = ref();
const startCallback = async () => {
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  // 模拟调用接口异步抽奖
  setTimeout(() => {
    // 假设后端返回的中奖索引是0
    const index = Math.floor(Math.random() * 8);
    const _award = prizeInfo[index];
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    // 调用stop停止旋转并传递中奖索引
    myLucky.value.stop(index);
  }, 2000);
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};

const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showTask.value = false;
  showAward.value = false;
  showSaveAddress.value = false;

  showSelect.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    showSelect.value = true;
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改 B端通信
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  console.log('assssssssss', type, data);
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    prizeList.value = [];
    skuListPreview.value = [];
    if (data.prizeList.length) {
      data.prizeList.forEach((items:any) => {
        if (items.prizeType) {
          prizeList.value.push(items);
        }
      });
      prizeInfo.splice(0);
      prizeInfo.push(...data.prizeList);
    }
    tasks.splice(0);
    tasks.push(...data.taskList);
    if (data.skuListPreview.length > 0) {
      skuListPreview.value.push(...data.skuListPreview);
    }
    ruleTest.value = data.rules;
    isExposure.value = data.isExposure;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'task') {
    showMyPrize.value = false;
    showAward.value = false;
    showRule.value = false;
    showTask.value = data;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    prizeList.value = [];
    skuListPreview.value = [];
    activityData.prizeList.forEach((items:any) => {
      if (items.prizeType) {
        prizeList.value.push(items);
      }
    });
    prizeInfo.splice(0);
    prizeInfo.push(...activityData.prizeList);
    if (activityData.skuListPreview.length > 0) {
      skuListPreview.value.push(...activityData.skuListPreview);
    }
    tasks.splice(0);
    tasks.push(...activityData.taskList);
    ruleTest.value = activityData.rules;
    isExposure.value = activityData.isExposure;
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
const showToastText = () => {
  showToast('活动预览，仅供查看');
};
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: cover;
  background-position: top;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/181636/36/47850/1539513/66e16444Feea3536c/a01a514aac4d3bf8.png);
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 11.64rem;
    left: 0.75rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 6rem;
  }
  .create-img {
    .header-btn {
      background-repeat:no-repeat;
      background-size:100% 100%;
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    background-repeat:no-repeat;
    background-size:100% 100%;
    width: 2.2rem;
    height: 0.73rem;
    margin-bottom: 0.1rem;
    font-size: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.gameAllDiv{
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/145637/16/35945/51830/64494652F1dc1cc76/ca7503efaacf33a6.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 6.88rem;
  height:4.63rem;
  margin-left:calc(50% - 6.88rem / 2);
  margin-top: 0.32rem;
  position: relative;
  .topRightStyle{
    position: absolute;
    top:0.8rem;
    //right:0.54rem;
    font-size:0.24rem;
    color:rgb(0, 100, 16);
    width:100%;
    text-align:center;
  }
  .bottomBtnAll{
    position: absolute;
    bottom:0.2rem;
    font-size:0.24rem;
    color:rgb(0, 100, 16);
    display: flex;
    justify-content: space-between;
    width:100%;
    padding:0 0.54rem;
    .startGameDiv{
      position: relative;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/194463/10/24954/4588/62972077Ec82e1af3/e0762748518d3e12.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 2.8rem;
      height:0.88rem;
      font-size:0;
      .gameCountDiv{
        position:absolute;
        right:0.3rem;
        top:-0.2rem;
        background:rgb(224, 31, 53);
        border:2px solid rgb(255, 255, 255);
        border-radius:50%;
        color:rgb(255, 242, 185);
        width:0.42rem;
        height:0.42rem;
        font-size:0.24rem;
        display:flex;
        justify-content:center;
        align-items:center;
      }
    }
    .startDrawDiv{
      position: relative;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/4656/10/17854/20089/62972077E04eb7e42/bbd4e8c5088810b6.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 2.8rem;
      height:0.88rem;
      font-size:0;
      .drawCountDiv{
        position:absolute;
        right:0.3rem;
        top:-0.2rem;
        background:rgb(224, 31, 53);
        border:2px solid rgb(255, 255, 255);
        border-radius:50%;
        color:rgb(255, 242, 185);
        width:0.42rem;
        height:0.42rem;
        font-size:0.24rem;
        display:flex;
        justify-content:center;
        align-items:center;
      }
    }
  }
}
.prizeListDiv{
  width:100%;
  position: relative;
  display: flex;
  justify-content: center;
  margin-top:0.4rem;
  flex-direction:column;
  align-items:center;
  .prizeTitleDiv{
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/52552/24/20820/57607/63f31a25F37254a4c/bab3564976b07479.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 6.9rem;
    height:0.88rem;
  }
  .prizeAll{
    max-height:7.32rem;
    overflow-y: scroll;
    .itemDiv{
      display: flex;
      position:relative;
      height:2.4rem;
      width:6.9rem;
      background:rgb(255, 255, 255);
      margin-bottom:0.04rem;
      .prizeImage{
        width:2rem;
        height:2rem;
        margin:0.2rem 0.32rem 0.2rem 0.2rem;
        border:2px solid rgb(255, 54, 51);
        border-radius:0.1rem;
        position:relative;
        overflow:hidden;
        img{
          width:100%;
        }
        .backDiv{
          background-image:url("//img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png");
          width:2rem;
          height:0.7rem;
          background-size:100%;
          background-repeat:no-repeat;
          position:absolute;
          bottom:-2px;
          left:-2px;
          text-align:center;
          padding-top:0.32rem;
          font-size:0.24rem;
          color:#fff;
          box-sizing:border-box
        }
      }
      .prizeMessage{
        .prizeName{
          text-align:left;
          color:rgb(38, 38, 38);
          font-size:0.3rem;
          width:3.4rem;
          overflow:hidden;
          white-space:nowrap;
          text-overflow:ellipsis;
          margin-top:0.37rem;
        }
        .prizeRestDiv{
          text-align:left;
          color:rgb(140, 140, 140);
          font-size:0.2rem;
          margin-top:0.45rem;
        }
      }
    }
  }
}
.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  .title-img {
    width: 3.39rem;
    height: 0.72rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    .more-btn-all {
      width:6.9rem;
      .more-btn {
        width: 1.8rem;
        height: 0.5rem;
        font-size: 0.2rem;
        color: #fff;
        background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
        background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
        border-radius: 0.25rem;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto 0.3rem;
      }
    }
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.4rem;
      background: rgb(255, 255, 255);
      overflow: hidden;
      img {
        display: block;
        width: 3.4rem;
        height: 3.4rem;
      }
      .sku-text {
        width: 3.4rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        font-size: 0.27rem;
        color: #262626;
        //height: 0.8rem;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.2rem 0;
        box-sizing: border-box;
      }
      .sku-btns {
        width: 3rem;
        height: 0.6rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/159308/25/21187/7832/619b8d96Ee8b26a4f/a4dd481902c8e6e6.png);
        background-size: 100%;
        margin: 0 auto 0.2rem;
        .price {
          width: 2.05rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #fff;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .to-bug {
          width: 0.95rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #df006e;
          text-align: center;
        }
      }
    }
  }
}

.wheel {
  display: flex;
  align-items: center;
  justify-content: center;
  position:absolute;
  top:0;
  background:rgba(0,0 ,0, 0.7);
  width:100%;
  height:100%;

  .wheel-img {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    object-fit: contain;
  }
}

.draws-num {
  text-align: center;
  font-size: 0.3rem;
  margin-bottom: 0.2rem;
}

.draw-btn {
  width: 4rem;
  margin: 0 auto;

  img {
    width: 100%;
  }
}

.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 5.96rem;
  margin: 0.49rem auto 0;
  padding-top: 1.1rem;

  .winners-content {
    width: 6.6rem;
    height: 4.7rem;
    background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3rem 0;
  border-bottom: 1px dashed rgb(230, 230, 230);

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
