import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/', // 主页
    name: 'index',
    component: () => import('../views/index.vue'),
  },
  {
    path: '/record',
    name: 'record',
    component: () => import('../views/ExchangeRecord.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/',
    name: 'notFound',
    hidden: true,
  },
];

// 路由
function initializeRouter() {
  const router = createRouter({
    history: createWebHistory(`${process.env.VUE_APP_PATH_PREFIX_NO_CDN}92002/1002/`),
    routes,
  });
  return router;
}

export default () => initializeRouter();
