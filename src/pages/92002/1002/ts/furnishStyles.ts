import { computed, reactive } from 'vue';

export const furnish = reactive({
  // 奖品背景图
  actBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 兑换品名称文字是否显示
  prizeNameShow: true,
  // 兑换品名称文字颜色
  prizeNameTextColor: '',
  // 积分文字颜色
  integralTextColor: '',
  // 兑换条件背景颜色
  exchangeConditionsBgColor: '',
  // 兑换条件文字颜色
  exchangeConditionsTextColor: '',
  // 按钮
  btnTextColor: '',
  btnBg: '',
  btnBorderColor: '',
  recordBtnTextColor: '',
  recordBtnBg: '',
  recordBtnBorderColor: '',
  // 店铺名称颜色
  shopNameColor: '',
  disableShopName: 0,
});
// 页面背景颜色
const actBgColor = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
}));
// 奖品背景图
const actBg = computed(() => ({
  backgroundImage: furnish.actBg ? furnish.actBg : '',
}));
// 兑换品名称文字颜色
const prizeNameTextColor = computed(() => ({
  display: furnish.prizeNameShow ? '' : 'none',
  color: furnish.prizeNameShow ? furnish.prizeNameTextColor : '',
}));
// 兑换品区域
const exchangeBox = computed(() => ({
  justifyContent: furnish.prizeNameShow ? 'space-between' : 'flex-end',
}));
// 积分文字颜色
const integralTextColor = computed(() => ({
  color: furnish.integralTextColor ?? '',
}));
// 兑换条件颜色
const exchangeConditions = computed(() => ({
  backgroundColor: furnish.exchangeConditionsBgColor ?? '',
  color: furnish.exchangeConditionsTextColor ?? '',
}));
// 兑换按钮
const exchangeBtn = computed(() => ({
  color: furnish.btnTextColor ?? '',
  backgroundColor: furnish.btnBg ?? '',
  borderColor: furnish.btnBorderColor ?? '',
}));

// 兑换记录按钮
const recordBtn = computed(() => ({
  color: furnish.recordBtnTextColor ?? '',
  backgroundColor: furnish.recordBtnBg ?? '',
  borderColor: furnish.recordBtnBorderColor ?? '',
}));
const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));
export default {
  actBgColor,
  actBg,
  prizeNameTextColor,
  integralTextColor,
  exchangeBox,
  exchangeConditions,
  exchangeBtn,
  recordBtn,
  shopNameColor,
};
