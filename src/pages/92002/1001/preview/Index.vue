<template>
  <div class="bg" :style="furnishStyles.actBgColor.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <!-- 奖品主图区域 -->
    <div class="prize-box">
      <img alt="" :src="furnishStyles.actBg.value.backgroundImage" :style="{'width' : '7.5rem','height':furnishStyles.actBg.value.backgroundImage? '' : '7.5rem'}"/>
      <div class="shop-name-text" :style="furnishStyles.shopNameColor.value" >
        <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
      </div>
      <div class="user-points">
        <div class="item" v-for="item in userPoints" :key="item">{{ item }}</div>
      </div>
    </div>
    <!-- 兑换产品信息区域 -->
    <div class="content" :style="furnishStyles.prizeInfoBg.value">
      <div class="block">
        <div class="goods-info">
<!--          <img :src="exchangeGoodsImg" alt="" class="goods-img" />-->
          <div class="info" :style="furnishStyles.prizeNameTextColor.value">
            <div class="goods-price">京东价:￥{{  salePrice }}</div>
            <div class="name">{{ exchangeGoodsName }}</div>
            <div class="points" v-show="exchangeGoodsName">
              消耗<span :style="furnishStyles.integralTextColor.value">{{ exchangePoint }}</span
            >积分 <span
            >+<span class="price-box" :style="furnishStyles.integralTextColor.value">{{ exchangePrice }}</span
            >元
        </span>
            </div>
          </div>
        </div>
        <div class="exchange-limit">
          <span class="red-bk">{{ exchangeLimitTips }}</span><span v-if="sameTermOnce">同期内所有奖品仅限兑换1次</span>
        </div>
      </div>
    </div>
    <!-- 兑换条件区域 -->
    <div class="exchange-condition-big-box">
      <div class="title-box" :style="furnishStyles.ruleTitleBox.value">兑换条件</div>
      <div class="exchange-condition-box">
        <div v-for="(item, index) in exchangeConditions" :key="index">
          <div  :style="furnishStyles.ruleContentBox.value" class="exchange-condition">{{ item }}</div>
        </div>
      </div>
    </div>
    <div class="rules-box">
      <div class="title-box title-box-rule" :style="furnishStyles.ruleTitleBox.value">兑换规则:</div>
      <div class="rules-line" :style="furnishStyles.ruleContentBox.value">
        {{ exchangeRule }}
      </div>
    </div>
    <!-- 兑奖按钮区域 -->
    <div class="exchange-btn-box">
      <img class="exchange-record-btn" :src="furnish.recordImg" alt="" @click="toast" />
      <img class="exchange-btn" :src="furnish.exchangeImg" alt="" @click="toast" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import html2canvas from 'html2canvas';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';

const userPoints = ref('1024');
const { registerHandler } = usePostMessage();
const activityData = inject('activityData') as any;
const isLoadingFinish = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const shopName = ref('xxx自营店');
const showRule = ref(false);
const exchangeGoodsName = ref('积分加钱购等级阶梯'); // 兑换商品名称
const exchangeLimitTips = ref('活动期内限领取1次');
const sameTermOnce = ref(false); // 同期内所有奖品是否限兑换1个
const exchangeSkuInfo = ref({ jingDongPrice: '19.9' }); // 兑换商品信息
const exchangeConditions = ref(['一星会员', '二星会员', '三星会员', '四星会员', '五星会员', '关注店铺用户']); // 兑换条件
const exchangeQuantityRemain = ref('XX'); // 兑换剩余数量
const exchangeRule = ref(`1.活动时间：xxxx-xx-xx xx:xx:xx至xxxx-xx-xx xx:xx:xx。
2.活动对象：店铺会员（一星会员，二星会员，三星会员，四星会员，五星会员）、关注店铺用户。
3.参与规则：仅活动对象可参与活动。
4.兑换件数限制：每单每个SKU最少购买x件，最多购买x件。
5.兑换次数限制：活动期限内限兑换一次
6.兑换数量说明：活动兑换总人数最多XX人；每日兑换总量共XXX份；兑换时间：与活动时间同步。
7.【活动参与主体资格】
（1）每位自然人用户仅能使用一个京东账号参与活动，WX号、QQ、京东账号、手机号码等任一信息一致或指向同一用户的，视为同一个用户，则第一个参与本活动的账号参与结果有效，其余账号参与结果均视为无效。
（2）若发现同一位用户使用不同账号重复参与活动，承办方有权取消其参与资格。
8.【注意事项】
（1）活动过程中，凡以不正当手段（如作弊领取、恶意套取、刷信誉、虚假交易、扰乱系统、实施网络攻击等）参与本次活动的用户，商家有权终止其参与活动，并取消其参与资格（如优惠已发放，商家有权追回），如给商家造成损失的，商家将保留向违规用户继续追索的权利。
（2）如遇不可抗力(包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动中存在大面积作弊行为、活动遭受严重网络攻击或因系统故障导致活动中奖名单大批量出错，活动不能正常进行的)，商家有权取消、修改或暂停本活动。
（3）是否获得优惠以活动发布者后台统计结果为准。
（4）因平台订单接口限流，部分订单可能有所延迟，请您耐心等待，订单状态会持续更新。
（5）法律允许范围内，本活动最终解释权归商家所有。
（6）活动商品数量有限，先到先得。`); // 兑换规则
const exchangePoint = ref('XX'); // 兑换积分
const exchangePrice = ref(' xx'); // 兑换价格
const salePrice = ref('19.9'); // 划线价
const showExchangeConfirm = ref(false);

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage({
    from: 'C',
    type: 'deco',
    event: 'changeSelect',
    data: id,
  });
  selectedId.value = id;
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showExchangeConfirm.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
const setDataInfo = (data:any) => { // 数据赋值
  exchangeConditions.value = data.gradeLabel;
  exchangeRule.value = data.rules;
  salePrice.value = data?.salePrice;
  sameTermOnce.value = data.sameTermOnce;
  exchangePoint.value = data.exchangePointType === '1' ? data.unifyExchangePoint : data.exchangePoint[0].points;
  shopName.value = data.shopName;
  if (data?.rightsName) {
    exchangeGoodsName.value = data.rightsName;
  }
  if (data?.totalExchangeAmount) {
    exchangeQuantityRemain.value = data.totalExchangeAmount;
  }
  if (data?.conversionPrice) {
    exchangePrice.value = data?.conversionPrice;
  }
  if (data?.skuInfo[0]) {
    exchangeSkuInfo.value = data?.skuInfo[0];
  }
  if (data?.salePrice) {
    salePrice.value = data?.salePrice;
  }
};
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    console.log(data, '0000');
    setDataInfo(data);
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'task') {
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage({
    from: 'C',
    type: 'mounted',
    event: 'sendMounted',
    data: true,
  });

  if (activityData) {
    setDataInfo(activityData);
  }

  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});
const toast = () => {
  showToast('活动预览，仅供查看');
};
onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
.bg {
  padding-bottom: 1rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}
.unit-box {
  margin-left: 0.05rem;
}
.prize-box {
  margin-bottom: 0.2rem;
  width: 7.5rem;
  position: relative;
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .user-points {
    position: absolute;
    top: 4.85rem;
    left: 0;
    right: 0;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 0 1.9rem;
    .item {
      background: url('//img10.360buyimg.com/imgzone/jfs/t1/154332/24/29826/2044/66c40543Ff63f56ca/e484d216d4f568ec.png') no-repeat;
      background-size: 100%;
      background-position-y: center;
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: 0.71rem;
      height: 1rem;
      color: #f7e7d1;
      width: 0.7rem;
      font-size: 0.7rem;
    }
  }
  .exchange-record {
    display: flex;
    justify-content: flex-end;
  }
  .prize-img {
    background-size: 100%;
    background-repeat: no-repeat;
    height: 7.5rem;
    width: 7.5rem;
  }
}
.exchange-condition-big-box {
  margin: 0.2rem 0rem;
  padding: 0.3rem;
  width: 7.5rem;
  .exchange-condition-box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 0.17rem;
    .exchange-condition {
      margin: 0.1rem 0.1rem 0rem 0rem;
      padding: 0rem 0.1rem;
      border-radius: 0.02rem;
      height: 0.34rem;
      line-height: 0.34rem;
      text-align: center;
      color: #0083ff;
      font-size: 0.2rem;
    }
  }
}
.content {
  width: 6.9rem;
  padding: 1rem 0.22rem 0.35rem;
  margin: 0 auto 0;
  background-repeat: no-repeat;
  background-size: 100% 1rem, 100% 0.5rem, 100% calc(100% - 1.5rem);
  background-position-y: top, bottom, 1rem;
  .block {
    position: relative;
    width: 100%;
    background-color: #ffffff;
    border-radius: 0.2rem;
    padding: 0.7rem 0.33rem 0.2rem;
    overflow: hidden;
    .goods-info {
      display: flex;
      align-items: center;
      .goods-img {
        width: 1.98rem;
        height: 1.98rem;
        object-fit: cover;
        border-radius: 50%;
      }
      .info {
        padding-left: 0.25rem;
        .goods-price {
          margin-bottom: 0.35rem;
          font-family: PingFang-SC-Medium;
          font-size: 0.24rem;
          color: #999999;
          text-decoration: line-through;
        }
        .name {
          font-size: 0.36rem;
          word-break: break-all;
        }
        .points {
          font-size: 0.3rem;
        }
      }
    }
    .goods-num {
      text-align: right;
      font-size: 0.24rem;
      span {
        font-size: 0.3rem;
      }
    }
    .exchange-address-big-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.2rem;
      height: 0.6rem;
      border-top: solid 0.03rem #dcdcdc;
      .choose-address-title {
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #333333;
      }
      .choose-address-btn {
        flex: 1;
        overflow: hidden;
        text-align: right;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #a7a7a7;
      }
    }
    .exchange-limit {
      position: absolute;
      top: 0;
      left: 0;
      border-bottom-right-radius: 0.2rem;
      height: 0.45rem;
      line-height: 0.45rem;
      background-color: #48341f;
      color: #fff;
      font-size: 0.2rem;
      span {
        padding: 0 0.1rem;
      }
      .red-bk {
        display: inline-block;
        height: 0.45rem;
        line-height: 0.45rem;
        border-bottom-right-radius: 0.2rem;
        background-color: #d73b35;
      }
    }
  }
}
.rules-box {
  margin-bottom: 0.2rem;
  padding: 0.3rem;
  width: 7.5rem;
  min-height: 2.4rem;
  .rules-line {
    margin-top: 0.3rem;
    font-family: PingFang-SC-Medium;
    font-size: 0.24rem;
    color: #666666;
    white-space: pre-wrap;
  }
}
.title-box {
  font-family: PingFang-SC-Bold;
  font-size: 0.3rem;
  color: #333333;
}
.exchange-btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  width: 100vw;
  height: 1rem;
  line-height: 1rem;
  text-align: center;
  font-family: PingFang-SC-Medium;
  font-size: 0.3rem;
  cursor: pointer;
  .exchange-btn {
    flex: 1;
    color: #fff;
    background-color: #0083ff;
    border-style: solid;
    border-width: 0.01rem;
    border-color: #0083ff;
  }
  .exchange-record-btn {
    flex: 1;
    color: #0083ff;
    background-color: #fff;
    border-color: #fff;
    border-style: solid;
    border-width: 0.01rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
