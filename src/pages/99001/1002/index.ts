import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';

initRem();

const app = createApp(index);

// 初始化页面
const config: InitRequest = {
  urlPattern: '/custom/:activityType/:templateCode',
};

const _decoData = {
  pageBg: '',
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/204051/13/41214/44728/6629b842Fc2a8c01d/efebbb2f9211f503.png', // 主页背景图
  actBgColor: '#dfe9ff', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '#000004', // 按钮字体颜色
  btnBg: 'rgba(0, 0, 0, 0)', // 按钮背景颜色
  btnBorderColor: '#000004', // 按钮边框颜色
  giftImg: '//img10.360buyimg.com/imgzone/jfs/t1/56323/10/23831/166191/65b361e9F6af84f5c/e8d1798d2c6c0c9c.png',
  step: '//img10.360buyimg.com/imgzone/jfs/t1/93691/25/46623/340100/65b361e9Faa161d9d/d0c5d119c8a76857.png',
  popupImg: '//img10.360buyimg.com/imgzone/jfs/t1/241243/33/4225/101729/65b7a06eF6db56827/0a66df2a6ab12576.png',
  popupLink: '',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/89883/24/37948/29417/650c07c9F8a5f2c33/07ee4745235da699.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/89883/24/37948/29417/650c07c9F8a5f2c33/07ee4745235da699.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/89883/24/37948/29417/650c07c9F8a5f2c33/07ee4745235da699.png',
};

init(config).then(async ({ baseInfo, pathParams, decoData }) => {
  // 设置页面title
  console.log(decoData);

  document.title = baseInfo?.activityName;
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', decoData);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.mount('#app');
});
