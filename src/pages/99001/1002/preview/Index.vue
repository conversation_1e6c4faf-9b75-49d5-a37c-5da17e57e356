<template>
  <div class="bg" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">{{ shopName }}</div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRule = true">活动规则</div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="recordPopup = true">锁权记录</div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showMyPrize = true">我的奖品</div>
        </div>
      </div>
    </div>
  </div>
  <div class="lock-header-title">锁权赢好礼</div>
  <div class="Lock-box" :style="furnishStyles.prizeBoxBg.value">
    <div class="lock-content-bg" :style="furnishStyles.prizeContentBg.value">
      <div v-if="lockPrizeList.length === 2" class="lock-contents">
        <div class="lock-prize-bg" v-for="(item, index) in lockPrizeList" :key="index">
          <div class="bg-box">
            <img v-if="item.prizeImg" :src="item.prizeImg" alt="" />
          </div>
          <div class="lock-name">{{ item.prizeName }}</div>
        </div>
        <div class="lock-btn">
          <div class="lock-title">锁权赢好礼</div>
          <div class="has-lock" v-if="isLock" @click="toast()">已锁权></div>
          <div class="before-lock" v-else @click="toast()">立即锁权></div>
        </div>
      </div>
      <div v-else class="lock-content" v-for="(item, index) in lockPrizeList" :key="index">
        <div class="prize-bg">
          <img v-if="item.prizeImg" :src="item.prizeImg" alt="" />
          <div>{{ item.prizeName }}</div>
        </div>
        <div class="lock-btn">
          <div class="lock-title">锁权赢好礼</div>
          <div class="has-lock" v-if="isLock" @click="toast()">已锁权></div>
          <div class="before-lock" v-else @click="toast()">立即锁权></div>
        </div>
      </div>
    </div>
  </div>
  <div class="lock-header-title" v-if="orderPrizeList.length > 0 && orderPrizeList[0].prizeName !== '谢谢参与'">下单领好礼</div>
  <div class="Lock-box" v-if="orderPrizeList.length > 0 && orderPrizeList[0].prizeName !== '谢谢参与'" :style="furnishStyles.prizeBoxBg.value">
    <div class="lock-content-bg" :style="furnishStyles.prizeContentBg.value">
      <div v-if="orderPrizeList.length === 2" class="lock-contents">
        <div class="lock-prize-bg" v-for="(item, index) in orderPrizeList" :key="index">
          <div class="bg-box">
            <img v-if="item.prizeImg" :src="item.prizeImg" alt="" />
          </div>
          <div class="lock-name">{{ item.prizeName }}</div>
        </div>
        <div class="lock-btn">
          <div class="lock-title">下单赢好礼</div>
          <div class="has-lock" v-if="isLock" @click="toast()">已领取></div>
          <div class="before-lock" v-else @click="toast()">下单领取></div>
        </div>
      </div>
      <div v-else class="lock-content" v-for="(item, index) in orderPrizeList" :key="index">
        <div class="prize-bg">
          <img v-if="item.prizeImg" :src="item.prizeImg" alt="" />
          <div>{{ item.prizeName }}</div>
        </div>
        <div class="lock-btn">
          <div class="lock-title">下单赢好礼</div>
          <div class="has-lock" v-if="isLock" @click="toast()">已领取></div>
          <div class="before-lock" v-else @click="toast()">下单领取></div>
        </div>
      </div>
    </div>
  </div>
  <div class="sku">
    <div class="sku-list" v-if="exposureSkuList.length">
      <div class="sku-item" v-for="(item, index) in exposureSkuList" :key="index">
        <img :src="item.skuMainPicture" alt="" />
        <div class="sku-text">{{ item.skuName }}</div>
        <div class="sku-btns" @click="toast()">立即购买></div>
      </div>
      <div class="more-btn" v-if="exposureSkuList.length > 18" @click="toast">点我加载更多</div>

    </div>
  </div>
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleText" @close="showRule = false"></RulePopup>
  </VanPopup>
  <van-popup v-model:show="recordPopup" :closeOnClickOverlay="false">
    <Record v-if="recordPopup" @close="recordPopup = false"></Record>
  </van-popup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false"></MyPrize>
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useSendMessage from '@/hooks/useSendMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';
// import Rule from '../components/Rule.vue';
import Record from '../components/Record.vue';
import MyPrize from '../components/MyPrize.vue';
import RulePopup from '../components/RulePopup.vue';
import { Sku } from '../ts/logic';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const shopName = ref('xxx自营旗舰店');

const isLoadingFinish = ref(false);
// 我的奖品弹窗
const showMyPrize = ref(false);
// 规则弹窗
const showRule = ref(false);
const ruleText = ref('');
// 记录弹窗
const recordPopup = ref(false);
// 曝光商品
const exposureSkuList = ref<Sku[]>([]);

const orderDataList = ref<any[]>([]);

// 锁权奖品
const lockPrizeList = ref<any[]>([{}, {}]);
// 是否锁权
const isLock = ref(false);
// 下单奖品
const orderPrizeList = ref<any[]>([]);
// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号

const createImg = async () => {
  showRule.value = false;
  recordPopup.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleText.value = data.rules;
  shopName.value = data.shopName;
  orderDataList.value = data.orderDataList;
  lockPrizeList.value = data.lockPrizeList;
  orderPrizeList.value = data.orderPrizeList;
  exposureSkuList.value = data.exposureSkuList;

});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    orderDataList.value = activityData.orderDataList;
    lockPrizeList.value = activityData.lockPrizeList;
    orderPrizeList.value = activityData.orderPrizeList;
    exposureSkuList.value = activityData.exposureSkuList;

    showToast('活动预览，仅供查看');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style lang="scss">
@font-face {
  font-family: 'EnglishFont';
  src: url('../font/Cronos.ttf') format('truetype');
}

@font-face {
  font-family: 'ChineseFont';
  src: url('../font/fzxh.TTF') format('truetype');
}
</style>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  // min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.54rem 0rem 0.3rem 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.1rem;
    height: 0.36rem;
    line-height: 0.38rem;
    margin-bottom: 0.1rem;
    font-size: 0.19rem;
    text-align: center;
    border-radius: 0.23rem 0 0 0.23rem;
    background-size: 100%;
    background-repeat: no-repeat;
    font-family: 'EnglishFont', 'ChineseFont', sans-serif;
    border: 0.01rem;
    border-style: solid;
  }
}

.lock-header-title {
  width: 7.5rem;
  height: 1rem;
  line-height: 1rem;
  font-size: 0.4rem;
  font-weight: 600;
  color: #b08300;
  text-align: center;
  font-family: 'EnglishFont', 'ChineseFont', sans-serif;
}

.Lock-box {
  //background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/210638/30/40625/3755/6628f691Fd2a46556/05dc512972d7bb6d.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 7.5rem;
  height: 4.51rem;
  display: flex;
  justify-content: center;
  align-items: center;
  .lock-content-bg {
    // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/163545/33/23919/12465/6628f690Fd87532a0/acbae582e41cbe74.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 7.04rem;
    height: 3.19rem;
    .lock-contents{
      width: 7.04rem;
      height: 3.19rem;
      display: flex;
      justify-content: space-around;
      align-items: center;
      .lock-prize-bg {
        width: auto;
        height: auto;
        .bg-box{
          width: 1rem;
          height: 1rem;
          display: flex;
          //flex-direction: column;
          //justify-content: center;
          //align-items: center;
          color: #b08300;
          font-weight: bold;
          margin: 0 auto;
          img {
            width: 1rem;
          }
        }
        .lock-name {
          width: 2.2rem;
          height: 0.7rem;
          margin: 0.1rem auto;
          text-align: center;
          color: #b08300;
          font-weight: bold;
        }
      }
    }
    .lock-content {
      width: 7.04rem;
      height: 3.19rem;
      display: flex;
      justify-content: space-around;
      align-items: center;
      .prize-bg {
        width: 3.4rem;
        height: 3.2rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #b08300;
        font-weight: bold;
        img {
          width: 2rem;
        }
      }
    }
    .lock-btn {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      height: 3.2rem;
      .lock-title {
        font-size: 0.3rem;
        color: #b08300;
        font-weight: bold;
        text-align: center;
        padding-top: 0.6rem;
      }
      .has-lock {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/186287/7/45021/1052/66292c65F320b8717/a1e72ca7ff1eca32.png');
        background-size: 100%;
        background-repeat: no-repeat;
        width: 2.17rem;
        height: 0.53rem;
        line-height: 0.53rem;
        color: #fff;
        text-align: center;
      }
      .before-lock {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/91850/19/39326/1054/66292c65F87b3f630/deddfe80967d6a15.png');
        background-size: 100%;
        background-repeat: no-repeat;
        width: 2.17rem;
        height: 0.53rem;
        line-height: 0.53rem;
        color: #fff;
        text-align: center;
      }
    }
  }
}
.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0 0.1rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/156976/22/43541/10227/6628f692F3d6abbdb/4ccddede7759dd77.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  .title-img {
    width: 2.82rem;
    height: 0.4rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0 auto 0.1rem auto;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0.24rem auto 0.3rem;
    }
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.6rem;
      height: 5.04rem;
      // background-color: #f6ebd0;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/152160/36/37273/6762/6628f691Faf035eb1/7c4c8a567ef7e60a.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      overflow: hidden;
      padding-top: 0.4rem;
      img {
        display: block;
        width: 2rem;
        margin: 0 auto;
        // height: 3.4rem;
      }
      .sku-text {
        width: 3.4rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        color: #262626;
        height: 0.8rem;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.43rem;
      }
      .sku-btns {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/91850/19/39326/1054/66292c65F87b3f630/deddfe80967d6a15.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 2.17rem;
        height: 0.53rem;
        margin: 0 auto 0.2rem;
        text-align: center;
        line-height: 0.53rem;
        color: #fff;
        // width: 3rem;
        // height: 0.6rem;
        // display: -webkit-box;
        // display: -ms-flexbox;
        // display: flex;
        // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/244615/4/7225/1431/661644d6F575dfb61/6ee1cc99f718c0ca.png');
        // background-size: 100%;
        // margin: 0 auto 0.2rem;
        // .price {
        //   width: 2.05rem;
        //   height: 0.6rem;
        //   line-height: 0.6rem;
        //   font-size: 0.3rem;
        //   color: #fff;
        //   text-align: left;
        //   padding-left: 0.2rem;
        //   box-sizing: border-box;
        // }
        // .to-bug {
        //   width: 0.95rem;
        //   height: 0.6rem;
        //   line-height: 0.6rem;
        //   font-size: 0.3rem;
        //   color: #df006e;
        //   text-align: center;
        // }
      }
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
