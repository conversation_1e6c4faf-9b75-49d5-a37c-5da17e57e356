<!-- 开卡专享 -->
<template>
  <div class="card-exclusive">
    <div class="exclusive-item" v-for="(item, index) in jsonData.exclusiveList" :key="index"
      :style="[{ backgroundImage: `url(${item.exclusiveImg})` }, { width: itemWidth(jsonData.exclusiveList.length) }]"
      @click="goUrl(item)"></div>
  </div>
</template>

<script lang="ts" setup>
/* eslint-disable */
import { computed } from 'vue';
import { configData } from '../common';

const emits = defineEmits(['showMemberPopup']);

// 模块数据
const moduleName = 'CardExclusive';
const { jsonData } = configData.value[moduleName];

const goUrl = (item: any) => {
  if (!configData.value.activityContent.memberLevel) {
    emits('showMemberPopup');
    return;
  }
  if (item.exclusiveUrl) {
    window.location.href = item.exclusiveUrl;
  }
};

const itemWidth = computed(() => (len: any) => {
  let newwidth = '';
  if (len === 1) {
    newwidth = '7rem';
  } else if (len === 2) {
    newwidth = '3.5rem';
  }
  return newwidth;
});

</script>

<style lang="scss" scoped>
.card-exclusive {
  // width: 6.5rem *1.05;
  display: flex;
  justify-content: space-around;

  .exclusive-item {
    width: 3.5rem;
    height: 1.8rem;
    background-size: 100% 100%;
  }
}
</style>
