<template>
  <van-popup class="my-popup rule-popup" :show="show">
    <div class="popup-container animate__animated animate__faster" :style="{ backgroundImage: `url(${configData.KvSwiper.jsonData.rulePopupBgImg}})` }">
      <div class="popup-content">
        <div class="rule-text no-scrollbar" :style="{ color: `${configData.KvSwiper.jsonData.rulePopupTextColor} !important` }" v-html="ruleFormat(ruleInfo)"></div>
      </div>
      <button class="close-popup-btn" @click="show = false"></button>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, Ref, toRefs, defineProps, defineExpose, inject } from 'vue';
import { ruleFormat, configData } from '../../common';

const show = ref(false);

const rulePopupTextColor = ref(configData.value.KvSwiper.jsonData.rulePopupTextColor);

const props = defineProps({
  ruleInfo: {
    type: String,
    required: true,
    default: '暂无活动规则!',
  },
});
const { ruleInfo } = toRefs(props);

// 显示暴露的数据，才可以在父组件拿到
defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.popup-content {
  display: flex;
  justify-content: center;
  width: 6.21rem;
  height: 7.49rem;
  padding: 1.2rem 0.3rem 0.5rem;
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
}

.rule-text {
  width: 5.7rem;
  height: 100%;
  overflow: auto;
  font-size: 0.24rem;
  word-break: break-all;
}
</style>
<style lang="scss">
a:-webkit-any-link {
  color: v-bind(rulePopupTextColor);
}
.rule-popup.van-popup {
}
</style>
