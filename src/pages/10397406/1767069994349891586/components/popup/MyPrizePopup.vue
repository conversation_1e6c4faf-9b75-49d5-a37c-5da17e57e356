<template>
  <van-popup class="my-popup" :show="show">
    <div class="popup-container animate__animated animate__faster" :style="{ backgroundImage: `url(${myPrizePopupBgImg}})` }">
      <div class="popup-content" :style="{ color: `${myPrizePopupTextColor}` }">
        <div class="my-prize no-scrollbar">
          <div class="prize-list no-scrollbar" v-if="myPrizeList.length > 0">
            <div class="prize-item" v-for="(prize, index) in myPrizeList" :key="index">
              <span class="date">{{ dayjs(prize.date).format('MM-DD HH:mm:ss') }}</span>
              <div class="prize-name">
                <span class="text-hide-1">{{ prize.rightsName }}</span>
              </div>
              <!-- 填写地址 -->
              <img :src="fillInAddressButtonImg" v-if="prize.rightsType === 3 && !prize.receiverName" class="prize-button" @click="goToSave(prize.grantId)" />
              <button v-else-if="prize.rightsType === 3 && prize.receiverName" class="prize-button">已填写</button>
              <!-- 复制卡密 -->
              <img :src="copyCardPasswordButtonImg" v-else-if="prize.rightsType === 7" class="prize-button" @click="emit('showCopyCard', prize)" />
              <button v-else class="prize-button">已发放</button>
            </div>
          </div>
          <span v-else class="no-prize-text">暂无奖品记录哦</span>
        </div>
      </div>
      <button class="close-popup-btn" @click="show = false"></button>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, Ref, toRefs, defineProps, inject, PropType } from 'vue';
import dayjs from 'dayjs';
import { configData } from '../../common';

const commonDecoration = configData.value.KvSwiper.jsonData;

const { myPrizePopupBgImg, myPrizePopupTextColor, copyCardPasswordButtonImg, fillInAddressButtonImg } = commonDecoration;

const emit = defineEmits(['goToSavePage', 'showCopyCard']);

const show = ref(false);

const props = defineProps({
  myPrizeList: {
    type: Array as PropType<any[]>,
    required: true,
    default: () => [],
  },
});

const goToSave = (id: string): void => {
  emit('goToSavePage', id);
  show.value = false;
};

// 显示暴露的数据，才可以在父组件拿到
defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.popup-content {
  display: flex;
  justify-content: center;
  width: 6.21rem;
  height: 7.49rem;
  padding: 1.6rem 0.5rem 0.5rem 0.3rem;
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
}

.my-prize {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  overflow: auto;
  font-size: 0.24rem;
  font-family: 'FZPSXIHJW--GB1-0';
}

.no-prize-text {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  padding-left: 0.2rem;
}

.prize-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: auto;
  font-size: 0.25rem;
  letter-spacing: -0.01rem;
}

.prize-item {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  height: 0.5rem;
  padding-right: 0.15rem;
  border-bottom: 1px dashed #ffeacb;
}

.date {
  flex-shrink: 0;
}

.prize-name {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  height: 100%;

  span {
    max-width: 2.2rem;
  }
}

.prize-button {
  width: 1rem;
}
</style>
<style lang="scss"></style>
