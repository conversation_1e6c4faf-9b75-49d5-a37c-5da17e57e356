<template>
  <van-popup class="my-popup" :show="show">
    <div class="popup-container animate__animated animate__faster" :style="{ backgroundImage: `url(${configData.SignInLottery.jsonData.reSignPopupBgImg}})` }">
      <div class="popup-content" :style="{ color: `${configData.SignInLottery.jsonData.reSignPopupTextColor}` }">
        <div class="prize-info">
          <div class="prize-info-top">
            <span class="text-1" :style="{ borderColor: `${configData.SignInLottery.jsonData.reSignPopupTextColor}` }">是否消耗</span>
            <span class="text-2">{{ configData.SignInLottery.reSignPoints }}积分</span>
            <span class="text-2">补签1天</span>
          </div>
          <div class="handle-button-container">
            <img class="handle-button-img" :src="configData.SignInLottery.jsonData.confirmResignButtonImg" @click="handleSignIn" />
          </div>
        </div>
      </div>
      <button class="close-popup-btn" @click="show = false"></button>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
/* eslint-disable */
import { ref, Ref, toRefs, defineProps, defineExpose, inject } from 'vue';
import { configData, getConfigData } from '../../common';
import { showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

console.log(configData.value.SignInLottery.jsonData);

const show = ref(false);

const emits = defineEmits(['reSignSuccess']);

// 签到
const handleSignIn = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/dingzhi/chunzhen/initiation/sign', {
      signType: 2,
    });
    await getConfigData('SignInLottery');
    showToast('补签成功');
    emits('reSignSuccess');
    setTimeout(() => {
      show.value = false;
    }, 2000);
  } catch (error: any) {
    showToast(error.message);
  }
};

// 显示暴露的数据，才可以在父组件拿到
defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.popup-content {
  display: flex;
  justify-content: center;
  width: 6.21rem;
  height: 7.49rem;
  padding: 1.5rem 0.9rem 0.7rem;
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
}

.prize-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  font-family: 'FZPSHJW--GB1-0';
}

.prize-info-top {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  padding: 0.6rem 0;

  .text-1 {
    padding: 0.08rem 0.24rem;
    font-size: 0.3rem;
    border-style: dashed;
    border-width: 1px;
    border-radius: 0.2rem;
  }

  .text-2 {
    font-size: 0.62rem;
  }
}

.handle-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 1rem;
  margin-bottom: 0.3rem;
}

.handle-button-img {
  width: 1.92rem;
  height: 0.57rem;
}
</style>
<style lang="scss">
.rule-popup.van-popup {
}
</style>
