<!-- 下单赢礼 -->
<template>
  <div class="order-receive-gifts">
    <!-- 标题 -->
    <div class="order-receive-gifts-title" :style="{ backgroundImage: `url(${jsonData.titleImg}})` }"></div>
    <!-- 活动列表 -->
    <div class="order-activity-container" :style="{ backgroundImage: `url(${jsonData.orderActivityBgImg}})` }">
      <carousel :touchDrag="configData[moduleName].prizeInfoList.length > 1" :autoplay="configData[moduleName].prizeInfoList.length > 1 && !hasTouchSwiper ? 3000 : 0" :wrapAround="configData[moduleName].prizeInfoList.length > 1">
        <slide class="activity-slide" v-for="slide in configData[moduleName].prizeInfoList" :key="slide">
          <div class="activity-item" @click="hasTouchSwiper = true">
            <!-- 倒计时 -->
            <div
              class="activity-count-down"
              :style="{
                color: `${jsonData.countDownColor}`,
                borderColor: `${jsonData.countDownColor}`,
              }">
              <span>活动结束倒计时：</span>
              <van-count-down
                class="vant-count-down"
                :style="{
                  color: `${jsonData.countDownColor}`,
                  borderColor: `${jsonData.countDownColor}`,
                }"
                :time="new Date(slide.activityEndTime).getTime() - Date.now()"
                format="DD天HH时mm分" />
            </div>
            <!-- 活动规则按钮 -->
            <img :src="configData.KvSwiper.jsonData.ruleButtonImg" class="rule-button" @click="showRule(slide.rule)" />
            <!-- 领奖记录按钮 -->
            <img :src="configData.KvSwiper.jsonData.prizeRecordButtonImg" class="prize-record-button" @click="getOwnPrizeData" />
            <!-- 奖品展示 -->
            <div class="gift-img">
              <img :src="slide.showImg" />
              <!-- 兑换按钮 -->
              <div class="gift-img-btn" @click="handleDraw(slide.prizeId)"></div>
            </div>
            <!-- sku列表 -->
            <div class="sku-list no-scrollbar">
              <div class="sku-img-item" v-for="skuImgItem in slide.orderSkuList" :key="skuImgItem" @click="gotoSkuPage(skuImgItem.skuId)">
                <img :src="skuImgItem.skuImg" class="sku-img" />
              </div>
            </div>
          </div>
        </slide>
      </carousel>
    </div>

    <!-- 规则弹窗 -->
    <RulePopup ref="rulePopup" :ruleInfo="ruleText" />
    <!-- 我的奖品弹窗 -->
    <MyPrizePopup ref="myPrizePopup" :myPrizeList="myPrizeList.values" @goToSavePage="goToSavePage" @showCopyCard="showCopyCard" />
    <!-- 中奖结果弹窗 -->
    <DrawResPopup ref="winPrizePopup" :drawRes="drawResInfo.value" @goToSavePage="goToSavePage" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, toRefs, inject, reactive } from 'vue';
import { Carousel, Slide } from 'vue3-carousel';
import { configData, getConfigData, getPrizeRecord } from '../common';

import RulePopup from './popup/RulePopup.vue';
import MyPrizePopup from './popup/MyPrizePopup.vue';
import DrawResPopup from './popup/DrawResPopup.vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { gotoSkuPage } from '@/utils/platforms/jump';

const emits = defineEmits(['showMemberPopup']);

const goToSavePage = inject('goToSavePage') as () => void;

// 操作过swiper
const hasTouchSwiper = ref(false);

// 模块数据
const moduleName = 'OrderReceiveGifts';
const { jsonData } = configData.value[moduleName];

const ruleText = ref('');
// 显示活动规则弹窗
const rulePopup = ref();
const showRule = (rule: string) => {
  ruleText.value = rule;
  rulePopup.value.show = true;
};

// 获取我的奖品返回信息
const myPrizeList = reactive({
  values: [],
});
// 我的奖品弹窗
const myPrizePopup = ref();
const getOwnPrizeData = async () => {
  myPrizeList.values = await getPrizeRecord(2);
  myPrizePopup.value.show = true;
};

// 兑换结果
const winPrizePopup = ref();
const drawResInfo = reactive({
  value: {},
});

// 兑换逻辑
const handleDraw = async (prizeId: string) => {
  if (!configData.value.activityContent.memberLevel) {
    emits('showMemberPopup');
    return;
  }
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/dingzhi/chunzhen/initiation/exchangeOrderPrize', { prizeId });
    closeToast();
    if (res.data.sendStatus) {
      drawResInfo.value = res.data;
      winPrizePopup.value.show = true;
    } else {
      showToast('活动太火爆，请尝试兑换其他奖品');
    }

    await getConfigData('OrderReceiveGifts');
  } catch (error: any) {
    console.error(error);
    showToast(error.message);
  }
};

// 展示卡密
const showCopyCard = (info: any) => {
  drawResInfo.value = info;
  myPrizePopup.value.show = false;
  winPrizePopup.value.show = true;
};
</script>

<style lang="scss" scoped>
.order-receive-gifts {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 0.25rem;
  font-family: 'FZPSXIHJW--GB1-0';
}

.order-receive-gifts-title {
  width: 100%;
  height: 0.86rem;
  background-repeat: no-repeat;
  background-position: center;
  background-size: auto 100%;
}

.order-activity-container {
  width: 7.36rem;
  height: 7.47rem;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  padding: 0 0.2rem;
}

.activity-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 7.32rem;
  height: 7.47rem;
  padding-top: 0.75rem;
}

.activity-count-down {
  position: absolute;
  top: 0.25rem;
  left: 0.2rem;
  display: flex;
  align-items: center;
  padding: 0 0.12rem;
  font-size: 0.22rem;
  border-style: solid;
  border-width: 1px;
  border-radius: 1rem;

  .vant-count-down {
    font-size: 0.22rem;
  }
}

.rule-button {
  position: absolute;
  top: 0.2rem;
  right: 1.8rem;
  width: 1.3rem;
  height: 0.4rem;
}

.prize-record-button {
  position: absolute;
  top: 0.2rem;
  right: 0.35rem;
  width: 1.3rem;
  height: 0.4rem;
}

.gift-img {
  position: relative;
  width: 6.8rem;
  height: 2.18rem;
  img {
    width: 100%;
    height: 100%;
  }
  .gift-img-btn {
    position: absolute;
    top: 1.3rem;
    left: 0.35rem;
    width: 1.45rem;
    height: 0.5rem;
  }
}

.sku-list {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  width: 95%;
  margin-bottom: 0.4rem;
  overflow: auto;
}

.sku-img-item {
  max-width: 50%;
}

.sku-img {
  width: 3.47rem;
  max-width: 100%;
  height: 4.16rem;
}
</style>
