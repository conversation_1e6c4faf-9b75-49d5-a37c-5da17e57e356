<!-- 主kv轮播 -->
<template>
  <carousel
    :touchDrag="jsonData.kvImageList.length > 1"
    :autoplay="jsonData.kvImageList.length > 1 ? 3000 : 0"
    :wrapAround="jsonData.kvImageList.length > 1"
  >
    <slide class="kv-slide" v-for="(slide, index) in jsonData.kvImageList" :key="slide">
      <div class="kv-img">
        <img :src="slide.src" @click="goLink(slide.url, index)" v-click-track="`kv${index + 1}`" />
      </div>
    </slide>
  </carousel>
</template>

<script lang="ts" setup>
/* eslint-disable */
import { Carousel, Slide } from 'vue3-carousel';
import { configData } from '../common';
import { reactive } from 'vue';

// 模块数据
const moduleName = 'KvSwiper';
const { jsonData } = configData.value[moduleName];

const goLink = (link: string, index: number) => {
  if (link) {
    window.location.href = link;
  }
};
</script>

<style lang="scss" scoped>
.kv-slide {
  align-items: flex-start;
}

.kv-img {
  width: 100%;
  height: 100%;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
