import { ActivityBaseInfo } from '@/types/ActivityBaseInfo';
import { httpRequest } from './service';
import CrmUtil from './products/crm';
import { showLoadingToast, closeToast, showToast } from 'vant';

const appVersion = (window as any).jmfe.getAppVersion('jd');
interface PopOpenStyleParams {
  targetType?: number;
  heightRateFromContainer?: number;
  layoutType?: number;
  backgroundColor?: string;
  pageMultiTimes?: number;
  needCache?: number;
  needAutoClose?: number;
  hideNativeCloseBtn?: number;
}

const parseUrl = (url: string): { domain: string; params: { [key: string]: string } } => {
  const urlObj = new URL(url);
  const domain = urlObj.origin;
  const params = Object.fromEntries(urlObj.searchParams.entries());

  return { domain, params };
};
const objToStr = (obj: any) =>
  Object.entries(obj)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

export const goLinkCard = (url: any, returnUrl = null) => {
  const { domain, params } = parseUrl(url);
  const str = objToStr(params);
  window.location.href = `https://shopmember.m.jd.com/shopcard/?${str}`;
};

const addOpenMemberCallback = (url: string, callBack: any) => {
  // 半屏开卡回调处理
  window.openMemberCallback = async (result: any) => {
    try {
      let resultObj: any;
      if (typeof result === 'string') {
        resultObj = JSON.parse(result);
      } else {
        resultObj = result;
      }
      if (resultObj.status === 0 || resultObj.status === '0') {
        let typeData = resultObj.data;
        if (typeof typeData === 'string') {
          try {
            typeData = JSON.parse(typeData);
          } catch (error) {
            console.error('解析typeData失败:', error);
            throw new Error('解析typeData失败000');
          }
        }
        console.log('resultObj', typeData);
        if (typeData.type === 'layerClose') {
          try {
            showLoadingToast({
              forbidClick: true,
              duration: 0,
            });
            // 获取活动的基础信息
            const { data } = await httpRequest.post('/common/getMemberLevel');
            closeToast();
            // 会员等级不为空，则上报自定义事件并刷新页面
            if (data) {
              showLoadingToast({
                forbidClick: true,
                duration: 0,
              });
              httpRequest.post('/common/add/member').then((res) => {
                closeToast();
                console.log('🚀 ~ httpRequest.post ~ callBack:', callBack);
                callBack ? callBack(data) : window.location.reload();
              });
            } else {
              showToast({
                message: '开卡失败~',
                duration: 1000,
                forbidClick: true,
              });
            }
          } catch (error) {
            console.log('🚀 ~ window.openMemberCallback= ~ error:', error);
            window.location.reload();
          }
        }
      } else if (resultObj.status === -1 || resultObj.status === '-1') {
        console.error('半屏开卡错误:', resultObj.msg);
        throw new Error('半屏开卡错误:');
      } else if (resultObj.status === -2 || resultObj.status === '-2') {
        console.error('半屏开卡错误，方法不存在');
        throw new Error('半屏开卡错误，方法不存在');
      } else {
        throw new Error('半屏开卡错误，兜底错误');
      }
    } catch (error) {
      goLinkCard(url);
    }
  };
};

// 构建开卡样式
const popOpenStyle = (urlParams: string, { targetType, heightRateFromContainer, layoutType, backgroundColor, pageMultiTimes, needCache, needAutoClose, hideNativeCloseBtn }: PopOpenStyleParams) =>
  JSON.stringify({
    url: encodeURIComponent(`https://pages.jd.com/member/card?${urlParams}&memberSceneType=xview&closeBtn=0`),
    // targetName: window.location.href,
    targetType: targetType || 2,
    heightRateFromContainer: heightRateFromContainer || 0.7,
    layoutType: layoutType || 1,
    backgroundColor: backgroundColor || '0000009b',
    pageMultiTimes: pageMultiTimes || 1,
    needCache: needCache || 0,
    needAutoClose: needAutoClose || 1,
    hideNativeCloseBtn: hideNativeCloseBtn || 0,
  });
const popOpenCard = (url: string, styles: { targetType: any; heightRateFromContainer: any; layoutType: any; backgroundColor: any; pageMultiTimes: any; needCache: any; needAutoClose: any; hideNativeCloseBtn: any }, callBack?: any) => {
  if (!(window as any).openMemberCallback) {
    addOpenMemberCallback(url, callBack);
  }
  if ((window as any).XWebView) {
    const { domain, params } = parseUrl(url);
    if (params?.venderId) {
      if (params.returnUrl) {
        delete params.returnUrl;
      }
      const urlParams = objToStr(params);
      (window as any).XWebView.callNative('JDXViewPlugin', 'showXView2', popOpenStyle(urlParams, styles), 'openMemberCallback', '1');
    } else {
      goLinkCard(url);
    }
  } else {
    goLinkCard(url);
  }
};
/**
 *半屏开卡（仅支持京东版本大于13.6.3）
 * @param url 开卡链接
 * @param styles 弹窗样式
 * @param callBack 开卡成功回调（注意 需要门槛限制时不要传递此参数）
 */
export const openCard = (url: string, styles: any = {}, callBack?: any) => {
  if (appVersion > '13.6.3') {
    // 半屏开卡
    popOpenCard(url, styles, callBack);
  } else {
    goLinkCard(url);
  }
};

export default openCard;
