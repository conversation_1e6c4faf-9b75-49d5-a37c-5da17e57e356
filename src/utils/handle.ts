export interface EventData {
  name: string;
  data?: any;
}

type EventCallback = (event: EventData) => Promise<void> | void;

export class Handler {
  private static instance: Handler; // 单例实例

  private events: Record<string, EventCallback[]> = {}; // 事件回调存储

  private taskQueue: (() => Promise<void>)[] = []; // 任务队列

  private isRunning = false; // 是否正在处理任务

  // 获取单例实例
  public static getInstance(): Handler {
    if (!Handler.instance) {
      Handler.instance = new Handler();
    }
    return Handler.instance;
  }

  // 注册事件
  public on(eventName: string, callback: EventCallback): void {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(callback);
  }

  // 触发事件
  public trigger(eventName: string, data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const task = async () => {
        try {
          let result;
          // eslint-disable-next-line no-restricted-syntax
          for (const callback of this.events[eventName] || []) {
            // eslint-disable-next-line
            result = await callback({ name: eventName, data });
          }
          resolve(result);
        } catch (error: any) {
          console.error(`事件 ${eventName} 执行失败:`, error);
          reject(error);
        }
      };

      this.taskQueue.push(task);
      this.runTasks().then();
    });
  }

  private async runTasks(): Promise<void> {
    if (this.isRunning) return;
    this.isRunning = true;

    while (this.taskQueue.length > 0) {
      const task = this.taskQueue.shift();
      if (task) {
        // eslint-disable-next-line
        await task();
      }
    }
    this.isRunning = false;
  }
}
