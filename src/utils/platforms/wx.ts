const jsApiList = [
  'updateAppMessageShareData', // 新版
  'updateTimelineShareData', // 新版
  'onMenuShareWeibo', // 新版
  'onMenuShareAppMessage', // 废弃
  'onMenuShareTimeline', // 废弃
  'onMenuShareQQ', // 废弃
  'onMenuShareQZone', // 废弃
  'hideMenuItems',
];
const defaultShareImg = 'https://img10.360buyimg.com/imgzone/jfs/t1/163534/34/4106/101460/600e2292Ed3609887/824e50f6ac5477dd.jpg';

/**
 * 检查是否是小程序
 */
export const isMiniProgram = async (): Promise<boolean> => new Promise(((resolve) => {
  window.wx.miniProgram.getEnv((result: any) => {
    console.log('wxSDK.miniProgram.getEnv', result);
    resolve(result.miniprogram);
  });
}));

// 定义请求签名的函数
const requestSignature = async (url: any) => {
  try {
    const response = await fetch(`${process.env.VUE_APP_API_HOST}/common/wx/signature`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: url || window.location.href,
      }),
    });
    return await response.json();
  } catch (error: any) {
    console.error('请求签名失败:', error);
    throw error;
  }
};

const initWxShare = () => {
  try {
    console.log('initWxShare');
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);
    // 删除不需要的参数
    ['mockCode', 'token', 'source', 'fromType', 'isJoin'].forEach(key => params.delete(key));
    // 拼接新 URL
    const paramStr = params.toString();
    const newUrl = paramStr
      ? `${window.location.origin}?${paramStr}${url.hash}`
      : `${window.location.origin}${url.hash}`;
    const shareConfig = {
      title: '请您参与活动',
      desc: '请您参与活动',
      link: newUrl,
      imgUrl: defaultShareImg,
    };
    // 微信jssdk初始化完成后设置分享内容
    window.wx.updateAppMessageShareData(shareConfig);
    window.wx.updateTimelineShareData(shareConfig);
    console.log('initWxShare success', shareConfig);
  } catch (e) {
    console.error(e);
  }
};

// 重构后的 wxSdkConfig 函数
export const wxSdkConfig = () => new Promise((resolve) => {
  try {
    console.log('获取微信签名-开始', window.location.href);
    // 使用 async/await 包装的请求函数
    requestSignature(window.location.href)
      .then((data) => {
        console.log('获取微信签名-完成', data);
        const wxConfig = {
          // debug: window.location.href.indexOf('debug=wx') > -1,
          appId: data.data.appid,
          timestamp: data.data.timestamp,
          nonceStr: data.data.nonceStr,
          signature: data.data.signature,
          jsApiList,
          openTagList: ['wx-open-launch-weapp'],
        };
        console.log('微信config-开始', window.location.href);
        window.wx.config(wxConfig);
        console.log('微信config-完成', wxConfig);
        window.wx.ready(() => {
          console.log('wx sdk ready');
          window.wx.checkJsApi({
            jsApiList,
            success(res: any) {
              // 以键值对的形式返回，可用的api值true，不可用为false
              // 如：{"checkResult":{"chooseImage":true},"errMsg":"checkJsApi:ok"}
              console.log('checkJsApi', res);
              resolve(true);
            },
            fail(res: any) {
              console.error('wx checkJsApi fail', res);
            },
          });

          initWxShare();
          // window.wx.hideMenuItems({
          //   menuList: ['menuItem:share:appMessage', 'menuItem:share:timeline'],
          // });
        });
        window.wx.error((res: any) => {
          console.error('wx sdk error', res);
        });
        resolve(true);
      })
      .catch((e) => {
        console.log('获取微信签名-失败', e);
        resolve(true);
      });
  } catch (e) {
    console.log('获取微信签名-失败', e);
    resolve(true);
  }
});
