import useMessage from '@/hooks/useMessage';
/**
 * Author: z<PERSON>yue
 * Date: 2023/11/14 15:10
 * Description: 通用门槛判断
 */
interface List {
  btnContent: string;
  thresholdCode: number;
  thresholdContent: string;
  thresholdStatus: number;
  type: number;
  thresholdTitle: string;
}
interface Props {
  thresholdList: List[];
  className?: string;
}
/**
 * 验证门槛 分为通用门槛 定制门槛
 * 通用门槛直接Dialog展示 定制门槛组合展示
 * 通用门槛code如下
 * 1 活动未开始
 * 2 活动已结束
 * 3 人群包
 * 4 是否入会
 * 5 是否关注店铺
 * 6 付费会员
 * 7 会员等级
 * 8 关注并开卡
 * @param props 基础信息包含门槛验证
 * @response list
 *
 */
const useThreshold = (props: Props): boolean => {
  const { thresholdList, className } = props;
  // 通用门槛
  const commonWarnList: List[] = thresholdList?.filter((e: List): boolean => !e.type);
  const commonWarnLength: number = commonWarnList?.length;

  // 自定义门槛
  const thresholdWarnList: List[] = thresholdList?.filter((e: List): boolean => !!e.type);
  const thresholdWarnLength: number = thresholdWarnList?.length;

  if (commonWarnLength) {
    const warnMessage: List = thresholdList[0];
    useMessage({
      title: warnMessage.thresholdTitle,
      message: warnMessage.thresholdContent,
      className,
    });
    return false;
  }
  return !!(!commonWarnLength && thresholdWarnLength);
};
export default useThreshold;
